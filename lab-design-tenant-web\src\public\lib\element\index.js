// 按需引入element-ui组件
import {
  Row,
  Col,
  Tooltip,
  Select,
  Option,
  Input,
  InputNumber,
  Table,
  Radio,
  RadioGroup,
  RadioButton,
  Checkbox,
  MessageBox,
  Notification,
  Switch,
  DatePicker,
  Pagination,
  TableColumn,
  Drawer,
  Timeline,
  TimelineItem,
  Collapse,
  CollapseItem,
  Dialog,
  Badge,
  Loading,
  Upload,
  Progress,
  Divider,
  Menu,
  MenuItem,
  Submenu,
  Form,
  FormItem,
  Button,
  Tree,
  checkboxGroup,
  Autocomplete,
  Empty,
  Popover,
  Cascader,
  Dropdown,
  DropdownMenu,
} from 'element-ui';
import { message } from './resetMessage';
import '@/assets/styles/element-variables.scss';

export default {
  install(Vue){
    Vue.use(Row);
    Vue.use(Col);
    Vue.use(Tooltip);
    Vue.use(Select);
    Vue.use(Option);
    Vue.use(Input);
    Vue.use(InputNumber);
    Vue.use(Table);
    Vue.use(Radio);
    Vue.use(RadioGroup);
    Vue.use(RadioButton);
    Vue.use(Checkbox);
    Vue.use(Switch);
    Vue.use(DatePicker);
    Vue.use(Pagination);
    Vue.use(TableColumn);
    Vue.use(Drawer);
    Vue.use(Timeline);
    Vue.use(TimelineItem);
    Vue.use(Collapse);
    Vue.use(CollapseItem);
    Vue.use(Dialog);
    Vue.use(Badge);
    Vue.use(Loading);
    Vue.use(Upload);
    Vue.use(Progress);
    Vue.use(Divider);
    Vue.use(Menu);
    Vue.use(MenuItem);
    Vue.use(Submenu);
    Vue.use(Form);
    Vue.use(FormItem);
    Vue.use(Button);
    Vue.use(Tree);
    Vue.use(checkboxGroup);
    Vue.use(Autocomplete);
    Vue.use(Empty);
    Vue.use(Popover);
    Vue.use(Cascader);
    Vue.use(Dropdown);
    Vue.use(DropdownMenu);

    const msgbox = MessageBox;
    const { alert, confirm } = msgbox;
    Vue.prototype.$msgbox = msgbox;
    Vue.prototype.$alert = alert;
    Vue.prototype.$confirm = confirm;
    Vue.prototype.$notify = Notification;
    Vue.prototype.$message = message;
  }
}