import Vue from 'vue';
import ClipboardJS from 'clipboard';

/**
 * 复制到粘贴板方法
 */
let clipboard = '';

const clipboardFunc = ( data) => {
	Vue.prototype.$nextTick(() => {
    let target = document.getElementsByClassName('dialog-confirm')[0];
    target.setAttribute('data-clipboard-target', '#dialogMsg');
    target.setAttribute('data-clipboard-text', data);
    clipboard = new ClipboardJS('.dialog-confirm');
    clipboard.on('success', (e) => {
      e.clearSelection(); // 清除复制的样式
      Vue.prototype.$MessageAlert({
        text: Vue.prototype.i18n.t('common.copySuccessTip'),
        type: "success",
      });
    });
  });
}

const clipDestroy = () => {
  if (clipboard) {
    clipboard.destroy();
  }
}
export {
  clipboardFunc,
  clipDestroy
}
