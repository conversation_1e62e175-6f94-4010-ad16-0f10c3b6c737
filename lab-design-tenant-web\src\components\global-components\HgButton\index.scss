.hg-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: $hg-main-blue;
  border: none;
  color: $hg-primary-text;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  //outline: none;
  margin: 0;
  transition: .1s;
  font-weight: 500;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;

  &:hover {
    color: $hg-btn-primary-hover-text;
    background-color: $hg-btn-primary-hover;
  }

  &:active {
    color: $hg-btn-primary-active-text;
    background-color: $hg-btn-primary-active;
  }

  &.is-loading {
    position: relative;
    pointer-events: none;
  }
}

.hg-button.is-loading:before {
  pointer-events: none;
  content: "";
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  border-radius: inherit;
  background-color: hsla(0,0%,100%,.2);
}

.hg-button [class*=el-icon-]+span {
  margin-left: 5px;
}

.hg-button--primary {

}

.hg-button--secondary {
  color: $hg-btn-second-text;
  border: 1px solid $hg-btn-second-border;
  background-color: $hg-btn-second;

  &:hover {
    color: $hg-btn-second-text;
    border-color: $hg-btn-second-hover-border;
    background-color: $hg-btn-second;
  }

  &:active {
    color: $hg-btn-second-active-text;
    border-color: $hg-btn-second-active-border;
    background-color: $hg-btn-second;
  }
}

.hg-btn-disabled {
  cursor: not-allowed;
  pointer-events: none;
}

.hg-button--primary.hg-btn-disabled {
  color: $hg-btn-disabled-text;
  background-color: $hg-btn-disabled;

  &:hover {
    color: $hg-btn-disabled-text;
    background-color: $hg-btn-disabled;
  }
}

.hg-button--secondary.hg-btn-disabled {
  color: $hg-btn-disabled-text;
  background-color: $hg-btn-disabled;

  &:hover {
    color: $hg-btn-disabled-text;
    background-color: $hg-btn-disabled;
  }
}

.hg-button--text {
  color: $hg-main-blue;
  background-color: transparent;
  
  &:hover {
    color: $hg-btn-text-hover;
    background-color: transparent;
  }

  &:active {
    color: $hg-btn-text-active;
    background-color: transparent;
  }
}

.hg-button--text.hg-btn-disabled {
  color: $hg-btn-text-disabled-text;
  background-color: transparent;

  &:hover {
    color: $hg-btn-text-disabled-text;
    background-color: transparent;
  }
}


.hg-button--danger {
  color: $hg-btn-danger-text;
  background-color: $hg-btn-danger;
  
  &:hover {
    color: $hg-btn-danger-text;
    background-color: $hg-btn-danger-hover;
  }

  &:active {
    color: $hg-btn-danger-active-text;
    background-color: $hg-btn-danger-active;
  }
}

.hg-button--danger.hg-btn-disabled {
  color: $hg-btn-disabled-text;
  background-color: $hg-btn-disabled;

  &:hover {
    color: $hg-btn-disabled-text;
    background-color: $hg-btn-disabled;
  }
}

.hg-button--danger-secondary {
  color: $hg-error;
  background-color: transparent;
  border: 1px solid $hg-error;

  &:hover {
    color: $hg-btn-danger-hover;
    background-color: transparent;
    border: 1px solid $hg-btn-danger-hover;
  }

  &:active {
    color: $hg-btn-danger-active;
    background-color: transparent;
    border: 1px solid $hg-btn-danger-active;
  }
}

.hg-button--danger-secondary.hg-btn-disabled {
  border: none;
  color: $hg-btn-text-disabled-text;
  background-color: transparent;

  &:hover {
    border: none;
    color: $hg-btn-text-disabled-text;
    background-color: transparent;
  }
}

// button 大小
.hg-button--small {
  padding: 4px 12px;
  font-size: 12px;
}

.hg-button--middle {
  font-size: 12px;
}

.hg-button--large {
  font-size: 14px;
  padding: 10px 24px;
}
