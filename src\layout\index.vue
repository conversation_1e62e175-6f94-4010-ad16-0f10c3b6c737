<template>
  <Header :user-info="userInfo" :current-app-path="'/lab_tenant_uc'" :current-env="currentEnv" />
</template>
<script>
import Header from 'heygears-cloud-header'
import { mapState, mapActions } from 'vuex'

export default {
  name: 'Layout',
  components: {
    Header
  },
  props: {
  },
  data() {
    return {
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    currentEnv() {
      return process.env.NODE_ENV
    }
  },
  watch: {
  },
  created() {
  },
  mounted() {
    this.initI18nDictList()
  },
  methods: {
    ...mapActions('user', ['initI18nDictList']),
  }
}
</script>
<style lang="scss" scoped>
</style>
