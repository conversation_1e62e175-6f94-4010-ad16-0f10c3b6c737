<template>
  <button
    :class="[
      'component-button',
      'flex-items-center',
      'flex-center',
    ]"
    :style="{ width: width ? width + 'px' : 'auto' }"
    :disabled="disabled"
  >
    <i v-if="icon" :class="['icon', 'iconfont', icon]" />
    <slot />
  </button>
</template>

<script>
export default {
  name: 'Button',
  props: {
    width: {
      type: [String, Number],
      default: 'auto'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.component-button {
  display: inline-block;
  position: relative;
  border: 0;
  color: $hg-primary-fontcolor;
  background: $hg-main-black;
  border: 1px solid $hg-border-color;
  border-radius: $hg-border-radius2;
  cursor: pointer;
  white-space: nowrap;
  &:hover {
    border: 1px solid $hg-disable-fontcolor;
    // color: $hg-main-blue;
    // i {
    //   color: $hg-main-blue !important;
    // }
  }
  &:active {
    border: 1px solid #2f3033;
    // color: $hg-main-blue;
    // i {
    //   color: $hg-main-blue;
    // }
  }
  &:disabled {
    cursor: default;
    color: #bcbfcc;
    background: rgba(84, 86, 92, 0.2);
  }
}
.component-button[sizes="small"] {
  font-size: $hg-small-fontsize;
  padding: 0 12px;
  height: 24px;
  line-height: 22px;
  border-radius: $hg-border-radius2;
  .iconfont {
    font-size: $hg-small-fontsize;
    margin-right: 8px;
    color: $hg-active-fontcolor;
  }
}
.component-button[sizes="middle"] {
  font-size: $hg-small-fontsize;
  padding: 0 16px;
  height: 32px;
  line-height: 30px;
  border-radius: $hg-border-radius2;
  .iconfont {
    font-size: $hg-small-fontsize;
    margin-right: 12px;
    color: $hg-active-fontcolor;
  }
}
.component-button[sizes="big"] {
  font-size: $hg-normal-fontsize;
  padding: 0 24px;
  height: 40px;
  line-height: 40px;
  border-radius: $hg-border-radius4;
  .iconfont {
    font-size: $hg-large-fontsize;
    margin-right: 8px;
    color: $hg-active-fontcolor;
  }
}
.component-button[type="primary"] {
  border: 0;
  color: #e4e8f7;
  background: #3054cc;
  &:hover {
    background: #3760eb;
  }
  &:active {
    color: #c5c9d6;
    background: #2b4bb8;
  }
  &:disabled {
    cursor: default;
    color: rgba(228, 232, 247, 0.4);
    background: rgba(84, 86, 92, 0.4);
  }
}
.component-button[type="warning"] {
  border: 0;
  color: $hg-primary-fontcolor;
  background: $hg-error-color;
  &:hover {
    background: #db4747;
  }
  &:active {
    color: #c5c9d6;
    background: #b23939;
  }
  &:disabled {
    cursor: default;
    color: rgba(228, 232, 247, 0.4);
    background: rgba(84, 86, 92, 0.4);
  }
}
.component-button[type="warning-minor"] {
  border: 0;
  color: $hg-error-color;
  background: #1d1d1f;
  border: 1px solid $hg-error-color;
  &:hover {
    color: #db4747;
    border: 1px solid #db4747;
  }
  &:active {
    color: #b23939;
    border: 1px solid #b23939;
  }
  &:disabled {
    cursor: default;
    color: rgba(228, 232, 247, 0.4);
    background: rgba(84, 86, 92, 0.4);
  }
}
.component-button[disabled="true"],
.component-button[disabled] {
  color: rgba(228, 232, 247, 0.4) !important;
  background: rgba(84, 86, 92, 0.4) !important;
  cursor: not-allowed !important;
  .iconfont {
    color: rgba(228, 232, 247, 0.4) !important;
    cursor: not-allowed !important;
  }
  &:hover {
    border-color: #38393D !important;
  }
}
</style>
