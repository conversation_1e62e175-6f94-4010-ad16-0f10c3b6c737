<template>
  <div class="user-content">
    <div class="user-list">
      <!-- 左边树 -->
      <div class="list-left-tree">
        <!-- 搜索框 -->
        <div class="basic-config">
          <span>{{lang('basic')}}</span>
          <div class="config-btn-list">
            <el-button v-if="!editleft" type="primary" @click="editBasicConfig('basic')">{{lang('btnEdit')}}</el-button>
            <el-button v-if="editleft" plain @click="cancelEdit('basic')">{{lang('cancel')}}</el-button>
            <el-button v-if="editleft" type="primary" @click="saveConfig('basic')">{{lang('save')}}</el-button>
          </div>
        </div>
        <!-- 技能等级设置 -->
        <div class="set-skin-level">
          <div class="level-box">
            <span class="title">{{lang('skillLevel')}}</span>
            <div v-for="(skin, idx) in designerLevels" :key="skin.code" :class="['level-list', idx == designerLevels.length - 1 ? 'no-border' : '']">
              <span class="left-lev">{{skin.code}}{{lang('level')}}</span>
              <div v-if="editleft" class="right-level">
                <el-input type="number" :class="skin.isError ? 'error-border-tips' : ''" v-model="skin.point" @input="handeNormalInput(skin, idx)" @change="veryLevelNumber(skin, idx)"></el-input>
                <span class="append">{{lang('danwei')}}</span>
                <span v-show="skin.isError" class="errortips">{{skin.errorText}}</span>
              </div>
              <div v-else class="right-level">
                <span class="num">{{skin.point}}</span><span class="append-edit">{{lang('danwei')}}</span>
              </div>
            </div>
          </div>

          <div class="level-box">
            <span class="title">{{lang('weight')}}</span>
            <div class="level-list">
              <span class="left-lev">{{lang('groupqc')}}</span>
              <div v-if="editleft" class="right-level">
                <el-input type="number" :class="groupQcRatioError.isError ? 'error-border-tips' : ''" v-model="groupQcRatio" @change="veryThisValue(groupQcRatio, 'groupQcRatio')"></el-input>
                <span class="append">%</span>
                <span v-show="groupQcRatioError.isError" class="errortips">{{groupQcRatioError.errorText}}</span>
              </div>
              <div v-else class="right-level">
                <span class="num">{{groupQcRatio}}</span><span class="append-edit">%</span>
              </div>
            </div>

            <div class="level-list">
              <span class="left-lev">{{lang('back')}}</span>
              <div v-if="editleft" class="right-level">
                <el-input type="number" :class="rebateRatioError.isError ? 'error-border-tips' : ''" v-model="rebateRatio" @change="veryThisValue(rebateRatio, 'rebateRatio')"></el-input>
                <span class="append">%</span>
                <span v-show="rebateRatioError.isError" class="errortips">{{rebateRatioError.errorText}}</span>
                <!-- <span v-show="rebateRatio > 100 || rebateRatio < 0" class="errortips">有效值0-100</span> -->
              </div>
              <div v-else class="right-level">
                <span class="num">{{rebateRatio}}</span><span class="append-edit">%</span>
              </div>
            </div>

            <div class="level-list">
              <span class="left-lev">{{lang('overTime')}}</span>
              <div v-if="editleft" class="right-level">
                <el-input type="number" :class="timeoutRatioError.isError ? 'error-border-tips' : ''" v-model="timeoutRatio" @change="veryThisValue(timeoutRatio, 'timeoutRatio')"></el-input>
                <span class="append">%</span>
                <span v-show="timeoutRatioError.isError" class="errortips">{{timeoutRatioError.errorText}}</span>
                <!-- <span v-show="timeoutRatio > 100 || timeoutRatio < 0" class="errortips">有效值0-100</span> -->
              </div>
              <div v-else class="right-level">
                <span class="num">{{timeoutRatio}}</span><span class="append-edit">%</span>
              </div>
            </div>

            <div class="level-list no-border">
              <span class="left-lev">{{lang('free')}}</span>
              <div v-if="editleft" class="right-level">
                <el-input type="number" :class="freeOrderRatioError.isError ? 'error-border-tips' : ''" v-model="freeOrderRatio" @change="veryThisValue(freeOrderRatio, 'freeOrderRatio')"></el-input>
                <span class="append">%</span>
                <span v-show="freeOrderRatioError.isError" class="errortips">{{freeOrderRatioError.errorText}}</span>
                <!-- <span v-show="freeOrderRatio > 100 || freeOrderRatio < 0" class="errortips">有效值0-100</span> -->
              </div>
              <div v-else class="right-level">
                <span class="num">{{freeOrderRatio}}</span><span class="append-edit">%</span>
              </div>
            </div>
          </div>

        </div>  
      </div>
      <!-- 右边点数配置列表 -->
      <div class="list-right-table">
        <!-- 头部搜索 -->
        <div class="depart-table-search">
          <span>{{lang('designCodeCofig')}}</span>
          <div class="search-input">
            <hg-input v-model="keyword" prefix-icon="el-icon-search" :placeholder="lang('enterSku')" clearable @change="search"></hg-input>
          </div>
          <div class="config-btn-list">
            <el-button v-if="!editright" type="primary" @click="editBasicConfig('table')">{{lang('btnEdit')}}</el-button>
            <el-button v-if="editright" type="default" @click="cancelEdit('table')">{{lang('cancel')}}</el-button>
            <el-button v-if="editright" type="primary" @click="saveConfig('table')">{{lang('save')}}</el-button>
            <el-button v-if="editright" type="primary" @click="importPoints()">{{lang('exportPoint')}}</el-button>
          </div>
        </div>
        <!-- 列表 -->
        <div class="depart-table">
          <hg-table :header-data="headerData" :height="'auto'" class="user-table" :loading="tableLoading" :data="tableList">
            <template v-slot:no="{ row, index }">
              <span>{{ index + 1 }}</span>
            </template>
            <template #designName="scope">
              <!-- {{scope.row.chargeUnit}} -->
              <span>{{ $t(`apiCommon.${scope.row.designCode}`) }}</span>
            </template>
            <!-- <template #chargeUnit="scope">
              <span>{{ $getI18nText(scope.row.chargeUnit) }}</span>
            </template> -->
            <!-- 加急时间 -->
            <template #expeditedTime="scope">
              <div class="expedited-time" v-for="(time, index) in scope.row.urgentInfos">{{time.urgentCode}}  {{lang('hours')}}</div>
            </template>
            <!-- 物料编号 -->
            <template #designCode="scope">
              <div class="expedited-time" v-for="(erpSkuCode, index) in scope.row.urgentInfos">{{erpSkuCode.erpSkuCode}}</div>
            </template>
            <!-- 点数的值 -->
            <template #points="scope">
              <div class="points" v-for="(points, index) in scope.row.urgentInfos">
                <span v-if="!editright">{{ (points.points || points.points === 0) ? points.points : '--'}}</span>
                <el-input type="number" v-else :class="!points.points && points.points !== 0 ? 'error-border-tips' : ''" v-model="points.points" @input="handleInput(points.points, points, scope.row, scope.index)" @change="changePrice(points.points, points, scope.row)"></el-input>
              </div>
            </template>
          </hg-table>
        </div>
        <div class="depart-pagination">
          <pagination showTotal :total="page.total" :disabled="tableLoading" :initPageIndex="page.pageNo" :initPageSize="page.pageSize" @onSearch="search"></pagination>
        </div>
      </div>
    </div>
    <leftDrawer :drawer.sync="drawer" @submitPoint="submitPoint"></leftDrawer>
  </div>
</template>
<script>
import hgTable from '@/components/HgTable';
import pagination from '@/components/Pagination';
import { mapGetters, mapActions } from 'vuex';
import { deepClone } from '@/public/utils/index';
import { getDesignerSetInfo, setConfig, getList, setskuPoints } from '@/api/designPoints';
import leftDrawer from './components/leftDrawer';
import { getLang } from '@/public/utils';
export default {
  name: 'user',
  components: { hgTable, pagination, leftDrawer },
  data() {
    return {
      editleft: false,
      editright: false,
      drawer: false,
      designerLevels: [],
      freeOrderRatio: 0, //免单分配
      freeOrderRatioError: {},
      groupQcRatio: 0, //组内QC
      groupQcRatioError: {},
      rebateRatio: 0, // 返单比例
      rebateRatioError: {},
      timeoutRatio: 0, // 超时
      timeoutRatioError: {},
      errorTips: [],
      tableLoading: false,
      page: {
        pageSize: 10,
        pageNo: 1,
        total: 0,
      },
      keyword: '',
      tableList: [],
      editPointsArr: [], //编辑的值
      basicEdit: false,
      tableEdit: false,
    };
  },
  computed: {
    ...mapGetters(['orgCode', 'language', 'showTipFromDetail']),
    headerData() {
      return [
        {
          prop: 'no',
          width: '80px',
          noTip: false,
          getLabel: () => {
            return this.lang('number');
          },
        },
        {
          prop: 'designName',
          minWidth: '20%',
          noTip: false,
          getLabel: () => {
            return this.lang('designCode');
          },
        },
        // {
        //   prop: 'chargeUnit',
        //   minWidth: '20%',
        //   noTip: false,
        //   getLabel: () => {
        //     return '单位';
        //   },
        // },
        {
          prop: 'designCode',
          minWidth: '30%',
          noTip: false,
          getLabel: () => {
            return this.lang('skuCode');
          },
        },
        {
          prop: 'expeditedTime',
          minWidth: '20%',
          noTip: false,
          getLabel: () => {
            return this.lang('time');
          },
        },
        {
          prop: 'points',
          minWidth: '30%',
          noTip: false,
          getLabel: () => {
            return this.lang('points');
          },
        }];
    },
    showLeaveTip() {
      const showLeaveTip = this.basicEdit || this.tableEdit;
      return showLeaveTip;
    },
  },
  watch: {
    showLeaveTip(show, oldShow) {
      if(show !== oldShow) {
        this.updateTipFromDetail(show);
      }
    },
  },
  beforeRouteLeave(to, from, next) {
    console.log(to.path, this.showLeaveTip, 1111)
    if(to.path !== '/configuration/pointsAllocation' && this.showLeaveTip) {
      // 自动点击，避免高亮两个tab
      const cancelFn = () => { document.getElementById('/configuration/pointsAllocation').click();next(false) };
      const confirmFn = () => { this.updateTipFromDetail(false); next(); }
      setTimeout(() => {
        this.$askBeforeLeaveDetail(this.showLeaveTip, cancelFn, confirmFn);
      }, 100);
   } else {
      next();
   }
  },
  beforeDestroy () {
    window.onbeforeunload = null;
    this.updateTipFromDetail(false);;
  },
  mounted() {
    window.onbeforeunload = this.handleOnbeforeunload;
    this.getDesignerSetInfo();
    this.getList();
  },
  methods: {
    ...mapActions(['updateTipFromDetail']),
    lang: getLang('designpoints'),
    handleOnbeforeunload(){
      if(this.showTipFromDetail){
        return '弹出提示';
      }
    },
    // 获取设计师点数影响因素信息
    async getDesignerSetInfo(){
      const { code, data } = await getDesignerSetInfo();
      this.designerLevels = data.designerLevels ? data.designerLevels : [];
      this.freeOrderRatio = data.freeOrderRatio
      this.groupQcRatio = data.groupQcRatio
      this.rebateRatio = data.rebateRatio
      this.timeoutRatio = data.timeoutRatio
    },
    editBasicConfig(type){
      if(type == 'basic'){
        this.editleft = true;
      } else {
        this.editright = true;
      }
    },
    cancelEdit(type){
      if(type == 'basic'){
        this.editleft = false;
        this.getDesignerSetInfo();
        this.basicEdit = false;
      } else {
        console.log(this.editright, 11111)
        this.editright = false;
        this.search();
        this.tableEdit = false;
      }
    },
    async saveConfig(type){
      if(type == 'basic'){
        console.log(this.errorTips, 4444)
        if(this.designerLevels.some(item => {return item.isError})){
          // this.$message.error('请输入正确的技能等级')
          return
        } else if(this.freeOrderRatioError.isError || this.groupQcRatioError.isError || this.rebateRatioError.isError || this.timeoutRatioError.isError){
          // this.$message.error('请输入正确的分配指标权重')
          return
        }
        let data = {
          designerLevels: this.designerLevels,
          freeOrderRatio: this.freeOrderRatio, //免单分配
          groupQcRatio: this.groupQcRatio, //组内QC
          rebateRatio: this.rebateRatio, // 返单比例
          timeoutRatio: this.timeoutRatio, // 超时
        }
        const {code} = await setConfig(data);
        if(code == 200){
          this.editleft = !this.editleft;
          this.getDesignerSetInfo();
          this.basicEdit = false;
        }
      }
      if(type == 'table'){
        if(this.editPointsArr.some(item => item.points === '')){
          this.$message.error(this.lang('allpoint'))
          return
        } else if(this.editPointsArr.some(item => item.points < 0)){
          this.$message.error(this.lang('zhengshu'))
          return
        }
        const { code } = await setskuPoints(this.editPointsArr)
        if(code == 200){
          this.editright = !this.editright;
          this.getList();
          this.tableEdit = false;
        }
      }
    },
    // 搜索
    search(type, searchData){
      if(searchData) {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
      }else {
        this.page.pageNo = 1;
      }
      this.getList();
    },
    // 获取列表、
    async getList(){
      this.tableLoading = true;
      let parame = {
        keyword: this.keyword,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize
      }
      const { code, data } = await getList(parame);
      this.tableList = data.data;
      this.page.pageNo = data.pageNo;
      this.page.pageSize = data.pageSize;
      this.page.total = data.totalSize;
      this.tableLoading = false
    },
    // 列表的值修改
    changePrice(points, value, row){
      let have = this.editPointsArr.find((item) => {return item.erpSkuCode == value.erpSkuCode})
      if(have){
        have.points = value.points !== '' ? Number(value.points) : value.points
      } else {
        this.editPointsArr.push({
          erpSkuCode: value.erpSkuCode,
          points: value.points !== '' ? Number(value.points) : value.points
        })
      }
      let obj = {}
      this.editPointsArr = this.editPointsArr.reduce((cur, next) => {
        obj[next.erpSkuCode] ? '' : obj[next.erpSkuCode] = true && cur.push(next);
              return cur;
      }, []);
      this.tableEdit = true;
      console.log(this.editPointsArr, 7777)
    },
    // 导入点数
    importPoints(){
      this.drawer = true;
    },
    // 确认导入
    submitPoint(){
      this.drawer = false;
      this.cancelEdit('table');
    },
    // 验证
    veryLevelNumber(data, idx){
      if(!data.point && data.point !== 0){
        this.$set(this.designerLevels[idx], 'isError', true);
        this.$set(this.designerLevels[idx], 'errorText', this.lang('noEmpty'));
      } else if(data.point < 0){
        this.$set(this.designerLevels[idx], 'isError', true);
        this.$set(this.designerLevels[idx], 'errorText', this.lang('intNum'));
      } else if(String(data.point).includes('.')){
        this.$set(this.designerLevels[idx], 'isError', true);
        this.$set(this.designerLevels[idx], 'errorText', this.lang('value'));
      } else {
        this.$set(this.designerLevels[idx], 'isError', false);
        this.$set(this.designerLevels[idx], 'errorText', '');
      }
      this.basicEdit = true;
    },
    veryThisValue(data, type){
      if(type == 'groupQcRatio'){
        this.groupQcRatioError = this.veryNumber(data);
      } else if(type == 'rebateRatio'){
        this.rebateRatioError = this.veryNumber(data);
      } else if(type == 'freeOrderRatio'){
        this.freeOrderRatioError = this.veryNumber(data);
      } else if(type == 'timeoutRatio'){
        this.timeoutRatioError = this.veryNumber(data);
      }
      this.basicEdit = true;
    },
    veryNumber(data){
      if(!data && data !== 0){
        return { isError: true, errorText: this.lang('noEmpty')}
      } else if((data > 100 || data < 0) || String(data).includes('.')){
        return { isError: true, errorText: this.lang('value')}
      }
      return { isError: false, errorText: ''};
    },
    handeNormalInput(data, idx){
      if (data.point > 5) {
        this.$set(this.designerLevels[idx], 'point', data.point.slice(0,5));
      }
    },
    handleInput(points, value, row, index){
      const idx = this.tableList[index].urgentInfos.findIndex((item) => {return item.erpSkuCode == value.erpSkuCode});
      // 使用正则表达式匹配整数和小数部分
      const regex = /^(\d{0,5})(\.\d{0,2})?$/;
      const match = points.match(regex);
      let lastValue = this.tableList[index].urgentInfos[idx].points;
      if(match){
        this.$set(this.tableList[index].urgentInfos[idx], 'points', match[0])
      } else {
        let pointArr = points.split('.');
        let newPoints = 0;
        if(pointArr[0]){
          if(pointArr[1]){
            newPoints = pointArr[0].slice(0, 5) + '.' + pointArr[1].slice(0, 2)
          } else {
            newPoints = pointArr[0].slice(0, 5)
          }
          this.$set(this.tableList[index].urgentInfos[idx], 'points', newPoints)
        }
      }

    }
  }
};
</script>
<style lang="scss" scoped>
.user-content {
  height: 100%;
  .user-title {
    color: $hg-main-blue;
    font-weight: bold;
    font-size: 16px;
    line-height: 48px;
    letter-spacing: 0px;
    text-align: left;
  }
  .user-list {
    display: flex;
    height: 100%;
    /deep/.el-input.el-input--prefix .el-input__prefix {
      margin-left: 0 !important;
      left: 5px !important;
    }
    /deep/.el-button.is-plain:focus{
      background: transparent;
    }
    /deep/.el-button--primary:focus{
      background: #3760EA;
      border-color: #3760EA;
    }
    .list-left-tree {
      width: 320px;
      border-radius: 4px;
      background: #1d1d1f;
      height: 100%;
      // overflow: auto;
      .basic-config{
        display: flex;
        position: relative;
        padding: 12px 24px;
        border-bottom: 1px solid #3D4048;
        span {
          display: inline-block;
          line-height: 40px;
          margin-right: 10px;
        }
        .config-btn-list{
          line-height: 40px;
          position: absolute;
          right: 24px;
          top: 12px;
        }
      }
      .set-skin-level{
        padding: 16px 24px;
        height: calc(100% - 60px);
        overflow: auto;
        .level-box{
          .title{
            display: inline-flex;
            height: 25px;
            align-items: flex-end;
            font-weight: 700;
            margin-bottom: 10px;
          }
          .level-list{
            position: relative;
            display: flex;
            align-items: center;
            height: 68px;
            // margin-bottom: 8px;
            border-bottom: 1px dotted #3D4048;
            padding-bottom: 6px;
            .right-level{
              position: absolute;
              right: 0;
              .el-input{
                position: relative;
                width: 190px;
                background-color: #141519;
              }
              .append{
                position: absolute;
                right: 20px;
                top: 50%;
                transform: translateY(-50%);
                color: #9EA2A8;
              }
              .errortips{
                position: absolute;
                bottom: -15px;
                left: 0;
                font-size: 12px;
                color: #E55353;
              }
              /deep/.error-border-tips{
                .el-input__inner{
                  border-color: #E55353;
                }
              }
              .append-edit{
                color: #9EA2A8;
                margin-left: 10px;
              }
              /deep/.el-input__inner{
                padding-right: 70px;
              }
            }
          }
          .no-border{
            border-bottom: none;
          }
        }
      }
    }
    .list-right-table {
      position: relative;
      margin-left: 24px;
      width: 864px;
      // padding: 24px;
      flex: 1;
      background: #1d1d1f;
      height: 100%;
      display: flex;
      flex-direction: column;
      .depart-table-search {
        display: flex;
        position: relative;
        border-bottom: 1px solid #3D4048;
        padding: 12px 24px;
        span {
          display: inline-block;
          line-height: 40px;
          margin-right: 10px;
        }
        .search-input {
          width: 360px;
        }
        .skill-btn{
          position: absolute;
          right: 120px;
          top: 6px;
        }
        .config-btn-list {
          line-height: 40px;
          position: absolute;
          right: 24px;
          top: 12px;
        }
      }
      .depart-table {
        flex: 1;
        width: 100%;
        .hg-table {
          height: 100%;
          overflow: auto;
          /deep/.el-table {
            // max-height: 100% !important;
          }
        }
        .table-high-light {
          float: left;
          width: auto;
          max-width: calc(100% - 41px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .expedited-time{
          line-height: 40px;
        }
        .points{
          height: 40px;
          display: flex;
          align-items: center;
          .el-input{
            height: 24px;
          }
          /deep/.el-input__inner{
            height: 24px;
            background-color: #141519;
          }
          /deep/.error-border-tips{
            .el-input__inner{
              border-color: #E55353;
            }
          }
        }
      }
      .depart-pagination {
        z-index: 1;
        position: absolute;
        bottom: 0;
        right: 0;
        height: 60px;
        width: 100%;
      }
    }
  }
}
</style>

