<template>
  <div class="component-btn-list">
    <el-button
      v-for="(btn, index) in btns"
      :key="index"
      :class="['btn', { active: btn.id === selectBtnId }]"
      :type="btn.id === selectBtnId ? 'primary' : 'text'"
      @click="click(btn)"
      size="small"
    >
      {{ btn.name }} <span v-if="btn.list && btn.list.length">({{ btn.list.length }})</span>
    </el-button>
  </div>
</template>
<script>
export default {
  name: 'BtnList',
  props: {
    btns: {
      type: Array,
      default: () => []
    }
    // selectBtnId: {
    //   type: [Number, String],
    //   default: function () {
    //     return this.btns[0].id
    //   }
    // }
  },
  data() {
    return {
      selectBtnId: this.btns[0].id
    }
  },
  methods: {
    click(btn) {
      this.selectBtnId = btn.id
      this.$emit('click', btn)
    },
    default(){
      if(this.btns.length){
        this.click(this.btns[0])
      }
    }
  },
  mounted(){
    this.default()
  }
}
</script>

<style lang="scss" scoped>
.component-btn-list {
  padding: 4px 12px;
  display: inline-block;
  border-radius: $hg-border-radius2;
  background-color: $hg-main-black;
  margin-bottom: 4px;
}
.btn {
  font-size: $hg-medium-fontsize;
  font-weight: 600;
  color: $hg-secondary-fontcolor;
  min-width: 104px;
  width: auto;
  &.active {
    color: $hg-primary-fontcolor;
  }
}
</style>
