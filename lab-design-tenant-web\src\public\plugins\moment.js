/**
 * 按需引入moment
 * 使用文档 http://momentjs.cn/docs/
 */
import moment from 'moment';
import 'moment/locale/zh-cn';
import { getStore } from '@/public/utils/storage';
import { DEFAULT_LANGUAGE } from '@/public/constants/setting';
import { parseJson } from '@/public/utils';
import store from '@/store';
const userInfo = parseJson(getStore('userInfo'));

// 忽略moment的警告
moment.suppressDeprecationWarnings = true;

const language = getStore('lang') || DEFAULT_LANGUAGE;

switch(language){
  case 'zh': moment.locale('zh-cn'); break;
  case 'en': moment.locale('en'); break; // 默认英文
  default: break;
}

const getUtcOffset = () => {
  let value = -(new Date().getTimezoneOffset());
  if(userInfo.timezone) {
    if(userInfo.timezone.utc || userInfo.timezone.utc === 0) {
      value = userInfo.timezone.utc;
    }
  }
  return value;
}
const utcOffset = getUtcOffset();
store.commit('SET_UTC_OFFSET', utcOffset);

export default moment;

