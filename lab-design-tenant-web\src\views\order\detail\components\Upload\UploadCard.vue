<template>
  <div class="upload-card">
    <!-- 上传start -->
    <div class="upload-box">
      <span v-if="name">{{ name }}<span>{{ necessary && needUpload ? ' *' : '' }}</span></span>
      <div class="re-order-upload" v-if="needUpload">
        <el-upload
          drag
          multiple
          ref="reOrderUpload"
          action="#"
          :accept="acceptType || '*'"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleChange">
            <hg-icon icon-name="icon-upload-default-lab"></hg-icon><span>{{ $t('common.btn.upload') }}</span>
        </el-upload>
        </div>
    </div>
    <!-- 上传end -->

    <div class="file-box">
      <div v-if="uploadFileList.length > 0" class="file-ul">
        <file-item  
          class="file-li" 
          :needMask="needUpload"
          :needDownload="needDownload"
          v-for="(item, index) in uploadFileList" 
          :key="index" 
          :item="item" 
          @handleRemove="handleRemove"
          @dragstart.native.prevent
          @handleDownload="downloadFile">
          <div v-if="!needUpload" slot="eventBtn" class="file-event-btn">
            <span v-if="item.canView" @click.stop="toggleModelView(item)"><hg-icon icon-name="icon-preview-on-lab"></hg-icon>{{ $t('common.btn.preview') }}</span>
            <span v-permission="['download']" @click.stop="downloadFile(item)"><hg-icon icon-name="icon-download-lab"></hg-icon>{{ $t('common.btn.download') }}</span>
          </div>

          <div slot="loadModel">
            <model-preview 
              bg="#262629"
              class='pre-model-style'
              :errorText="$t('file.tips.loadFileFail')"
              v-if="!needUpload && item.canView && item.visible"
              :ref="`modelBox${item.fileName}`"
              :file-list='[item]'
              @error="loadError(item)"
              ></model-preview>
          </div>

        </file-item>
      </div>

      <div v-else-if="!needUpload" class="file-no-data">
        <img class="hg-icon" src="~@/assets/images/order/icon_folder.svg" alt="">
        <!-- <hg-icon icon-class="icon-file-lab" color="#5878B4"></hg-icon> 等阿里icon恢复后使用hg-icon-->
        <span>{{ $t('file.noFile') }}</span>
      </div>

      <div v-else class="no-upload">
        <hg-icon icon-name="icon-file-lab" color="#5878B4"></hg-icon>
        <span>{{ $t(uploadTip) || $t('file.tips.upload', [name]) }}</span>
      </div>
    </div>

  </div>
</template>

<script>
import SliceUpload from '@/public/utils/SliceUploadV4';
import FileItem from './FileItem';
import ModelPreview from '@/components/ModelPreview';
import { isStl } from '@/public/utils';
import { getDownloadUrl } from '@/api/file';
import { createIFrameDownLoad, getFileNameNoSuffix } from '@/public/utils/file';

export default {
  name: 'UploadCard',
  components: { FileItem, ModelPreview },
  props: {
    uploadList: {
      type: Array,
      default() {
        return [];
      }
    },
    name: String,
    needUpload: {
      type: Boolean,
      default: true,
    },
    needDownload: {
      type: Boolean,
      default: false,
    },
    fileType: {
      type: Number,
      require: true
    },
    uploadTip: String,
    acceptType: String,
    clientOrgCode: {
      type: Number,
      require: true,
    },
    necessary:Boolean,
    standarName:String,
  },
  data() {
    return {
      uploadFileList: [],
      selectFileList: [], // 用于检查的文件列表
    }
  },
  watch: {
    uploadList: {
      deep: true,
      handler(list) {
        console.log(`${this.name}-uploadList-list: `, list);
        if (list.length) {
          this.uploadFileList = [];
          this.selectFileList = [];
          list.forEach(fileItem => {
            const item = {
              percentage: 100,
              progress: 100,
              uid: fileItem.filePath,
              fileName: fileItem.fileName,
              filePath: fileItem.filePath,
              fileType: fileItem.fileType,
              fileSize: fileItem.fileSize,
              visible: false,
              orgCode: this.clientOrgCode,
              viewUrl: '', // 下载地址
              canView: isStl(fileItem.fileName) && fileItem.filePath, // STL预览
            };
            this.uploadFileList.push(item);
            this.selectFileList.push(item);
          });
        } else {
          // 当传入的uploadList为空，有可能是通过上下键切换订单重新加载数据，如果传入的为空，但组件内存在不是新上传的上传数据，则清空
          if (this.uploadFileList.length && this.uploadFileList.every(item => !item.uploadStatus)) {
            this.uploadFileList = [];
          }
        }
      },
      immediate: true,
    }
  },
  methods: {
    isStl,

    /**
     * 重置上传列表
     */
    resetUploadFileList(list) {
      this.uploadFileList = list;
    },
    /**
     * 上传
     */
    async handleChange(file, fileList) {
      try {
        console.log('handleChange-fileList', fileList)
        if (!this.verifyFile(file, fileList) ) { return false; }

        // console.log('fileList', fileList);

        const sliceUpload = new SliceUpload({file: file.raw, orgCode: this.clientOrgCode, fileType: this.fileType});
        this.uploadFileList.push(sliceUpload); //一开始就放到uploadFileList
        this.$emit('implantUploadStart')
        sliceUpload.onCheckMd5((data) => {
          const { md5 } = data;
          const hasSameMd5 = this.selectFileList.some(file => file.md5 === md5); // 上传过的文件，返回列表是没有md5的，所以无法判断是相同文件。
          if(hasSameMd5) {
            this.$hgOperateRepeatFail(this.$t('file.tips.uploadSameFile', [data.fileName])); // 重复的话需要删掉uploadFileList的，selectFileList不需要删，因为没有push
            this.handleRemove(data);
            this.$emit('implantUploadEnd')
          }else {
            this.selectFileList.push(data); // 检查通过则放到selectFileList列表
            sliceUpload.onUpload();
          }
        });

        // 上传成功，发现和上次上传的s3FileId一致，则提示重复文件; uploadStatus表示是新上传的文件；这里要旧文件和新上传的文件做id重复对比
        sliceUpload.onSuccess((data) => {
          const { filePath, fileName } = data;
          const hasSameS3FileId = this.uploadFileList.some(file => !file.uploadStatus && file.filePath === filePath);
          if(hasSameS3FileId) {
            this.$hgOperateRepeatFail(this.$t('file.tips.uploadSameFile', [fileName]));
            this.handleRemove(data);
          }
          console.log('onEnd', data);

          // 如果有上传文件，则设计文件，设计STL，设计截图为必传文件
          const errArr = ['', null, undefined];
          if (!errArr.includes(this.necessary) && this.necessary === false) {
            this.$emit('necessaryFileUploadSuccess')
          }

          this.$emit('implantUploadEnd',  this.uploadFileList)
        });

        sliceUpload.onError((data) => {
          console.error('data: ', data);
          this.$hgOperateFail(this.$t('file.tips.uploadFail'));
          this.handleRemove(data);

          this.$emit('implantUploadError')
        });
   
        sliceUpload.onStart();
        
      } catch (error) {
        console.log('error: ', error);
        this.handleRemove(file.raw);
      }
    },

    // 校验文件
    verifyFile(file, fileList) {
      if(this.acceptType === '.stl,.zip,.dcm,.obj,.ply'){
        const regRex = /\.(stl|zip|dcm|obj|ply)$/g;
        if(!regRex.test(file.name.toLowerCase())){
          this.$hgOperateFail(this.$t('file.tips.wrongFileType', ['stl/zip/dcm/obj/ply']));
          return false;
        }
      }
      
      if(this.acceptType === '.pdf'){
        const regRex = /\.(pdf)$/g;
        if(!regRex.test(file.name.toLowerCase())){
          this.$hgOperateFail(this.$t('file.tips.wrongFileType', ['pdf']));
          return false;
        }
      }

      if(this.standarName) {
        const fileName = file.name;
        const tempName = fileName.split('.');
        tempName.pop();
        const name = tempName.join('.');

        if(name !== this.standarName) {
          this.$alert(this.$t('order.detail.tips.differentFileName'),this.$t('component.title.system'),{
            confirmButtonText: this.$t('common.btn.confirm'),
            type: 'warning',
          });
        }
      }

      return true;
    },

    handleRemove(file) {
      console.log('handleRemove-file', file)

      const index = this.uploadFileList.findIndex(item => file.uid === item.uid); // 上传过的uid是md5，没上传过的是本地读取文件的uid
      if(index > -1) {
        const item = this.uploadFileList[index];
        item.cancelUpload && item.cancelUpload();
        this.uploadFileList.splice(index,1);
      }

      //如果selectFileList也有则也要删
      const selectIndex = this.selectFileList.findIndex(item => file.uid === item.uid);
      console.log('handleRemove-selectIndex', selectIndex, this.selectFileList)
      if(selectIndex > -1) {
        this.selectFileList.splice(selectIndex,1);
      }
    },

    downloadFile(file) {
      const { filePath, fileName } = file;
      if(filePath) {
        const param = {
          s3FileId: filePath,
          filename: getFileNameNoSuffix(fileName),
          orgCode: this.clientOrgCode
        };
        getDownloadUrl(param).then(res => {
          if(res.code === 200) {
            createIFrameDownLoad(res.data.url);
          }else {
            this.$hgOperateFail(this.$t('http.error.80080003'));
          }
        }).catch(err => {
          console.log('error:',err);
          // this.$hgOperateFail(this.$t('file.tips.downloadFail'));
        });
      }
    },

    /**
     * 预览模型
     */
    toggleModelView(item) {
      if(!item.visible) {
        item.visible = true;
        this.$nextTick(() => {
          console.log(item.fileName);
          this.$refs[`modelBox${item.fileName}`][0].resetVisibleStatus();
        });
      }else {
        item.visible = false;
      }
      
    },
    // 文件加载失败，viewUrl重置，再次点击打开会获取文件地址
    loadError(item) {
      item.viewUrl = '';
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-card {
  width: 100%;

  .upload-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    line-height: 24px;

    >span:first-of-type {
      font-weight: bold;
      font-size: 16px;  
      span {
        color: $hg-red;
      }
    }

    .re-order-upload {
      cursor: pointer;

      .hg-icon {
        margin-right: 8px;
        font-size: 14px;
      }
    }
  }

  .file-box {

    .file-no-data {
      display: flex;
      align-items: center;
      border-radius: 4px;
      background: $hg-border-second;

      span {
        padding-left: 16px;
        color: $hg-grey;
      }

      .hg-icon {
        margin: 8px 16px;
        margin-right: 0;
        width: 64px;
        height: 64px;
        font-size: 64px;
        background: $hg-main-black;
      }
    }

    .no-upload {
      padding: 32px 24px;
      border-radius: 4px;
      background: $hg-border-second;

      .hg-icon {
        margin-right: 8px;
      }
    }

    .file-li {
      margin-bottom: 12px;

      &:last-of-type {
        margin-bottom: 0;
      }

      .file-event-btn>span {
        cursor: pointer;
        margin-left: 32px;

        .hg-icon {
          padding-right: 8px;
        }
      }

      .pre-model-style {
        height: 320px;
        width: 100%;
      }
    }
  }
}
</style>

<style lang="scss">
.upload-card {
  .re-order-upload {
    .el-upload-dragger {
      display: flex;
      color: $hg-main-blue;
      line-height: 24px;

      height: 100%;
      width: 100%;
      background: transparent;
      border: none;
    }
  }
}
</style>