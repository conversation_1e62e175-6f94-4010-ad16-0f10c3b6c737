<template>
  <div class="heypoint-setting">
    <div class="header-tab">
      <span :class="['tab-item', clientType == 0 ? 'active' : '']" @click="changeType(0)">{{ $t('heypoint.setting.monthCustomer') }}</span>
      <span :class="['tab-item', clientType == 1 ? 'active' : '']" @click="changeType(1)">{{ $t('heypoint.setting.payCustomer') }}</span>
      <div class="add-btn" @click="addConfig">
        <div class="el-icon-plus1">+</div>
      </div>
    </div>
    <!-- 内容 -->
    <div v-if="heypointList.length == 0">
      <el-empty></el-empty>
    </div>
    <div v-loading="settingLoad" class="all-content">
      <div class="content-setting" v-if="heypointList && heypointList.length > 0">
        <div class="content-box" v-for="(setting, index) in heypointList" :key="index">
          <!-- 第一部分条件 -->
          <div class="content-box-first">
            <p v-if="nowSetting.indexOf(setting.id) == -1 && setting.noticeType === 1">{{ $t('heypoint.setting.titltOne') }} {{ setting.heypointBalance }} {{ $t('heypoint.setting.hour') }}，</p>
            <p v-else-if="nowSetting.indexOf(setting.id) == -1 && setting.noticeType === 2">
              {{ $t('heypoint.setting.titleTwo') }} {{ setting.availableCreditRate }} % {{ $t('heypoint.setting.hour') }}，
            </p>
            <p v-else>
              <el-radio-group v-model="setting.noticeType" @change="changeNewSetting(setting, index)">
                <el-radio :label="1"
                  >{{ $t('heypoint.setting.titltOne') }}
                  <el-input-number
                    class="custem-input"
                    :controls="false"
                    :min="0"
                    v-model="setting.heypointBalance"
                    onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  ></el-input-number
                  >{{ $t('heypoint.setting.hour') }},</el-radio
                >
                <el-radio :label="2"
                  >{{ $t('heypoint.setting.titleTwo') }}
                  <el-input-number
                    class="custem-input"
                    :min="0"
                    :max="100"
                    :controls="false"
                    onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                    v-model="setting.availableCreditRate"
                  ></el-input-number
                  >%{{ $t('heypoint.setting.hour') }},</el-radio
                >
              </el-radio-group>
            </p>
          </div>
          <!-- 第二部分描述 -->
          <div class="content-box-second">{{ $t('heypoint.setting.titleThird') }}</div>
          <!-- 第三部分选择 -->
          <div class="conteng-box-select">
            <el-checkbox :disabled="nowSetting.indexOf(setting.id) == -1" v-model="setting.isEmail">{{ $t('heypoint.setting.email') }}</el-checkbox>
            <el-checkbox :disabled="nowSetting.indexOf(setting.id) == -1" v-model="setting.isClient">{{ $t('heypoint.setting.client') }}</el-checkbox>
            <el-checkbox :disabled="nowSetting.indexOf(setting.id) == -1" v-model="setting.isNoticeLeader">{{ $t('heypoint.setting.leading') }}</el-checkbox>
          </div>
          <!-- 第四部分描述 -->
          <div class="content-box-third">{{ $t('heypoint.setting.titleFour') }}</div>
          <!-- 第五部分,按钮 -->
          <div class="content-box-four">
            <!-- <el-button @click="deleteBtn(setting.id)">删除</el-button> -->
            <hg-button class="btn" v-if="nowSetting.indexOf(setting.id) == -1" type="primary" @click="editHeypoint(setting, 'edit', index)">{{ $t('heypoint.customer.edit') }}</hg-button>
            <el-button class="btn" v-else type="primary" style="background-color: #1d1d1f;border: 1px solid #4477FB;" plain @click="editHeypoint(setting, 'submit')">{{
              $t('heypoint.customer.saveEdit')
            }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getHeypointSetting, addOrUpdateBalance, removeAll } from '@/api/heypoint';
export default {
  data() {
    return {
      clientType: 0,
      nowSetting: [], //正在编辑
      nowEditId: null, //新增的编辑配置
      heypointList: [],
      settingLoad: true,
    };
  },
  mounted() {
    this.getHeypointSetting();
  },
  methods: {
    // 获取余额提醒配置列表
    getHeypointSetting(type) {
      this.settingLoad = true;
      let obj = {
        settlementType: this.clientType,
      };
      getHeypointSetting(obj).then((res) => {
        if (res.code === 200) {
          this.heypointList = res.data;
          //需要转化为true和false
          this.heypointList.forEach((item) => {
            item.isEmail = item.isEmail == 1 ? true : false;
            item.isClient = item.isClient == 1 ? true : false;
            item.isNoticeLeader = item.isNoticeLeader == 1 ? true : false;
          });
          //如果时新增的时候需要把最新的id加进编辑器
          if (type == 'add') {
            this.nowEditId = this.heypointList[this.heypointList.length - 1].id;
            this.nowSetting.push(this.heypointList[this.heypointList.length - 1].id);
          }
          this.settingLoad = false;
        }
      });
    },
    changeType(type) {
      this.clientType = type;
      this.getHeypointSetting();
    },
    // 添加一个配置
    addConfig() {
      if (this.heypointList.length >= 5) {
        this.$message.error(this.$t('heypoint.setting.limitNum'));
        return;
      }
      let configObj = {
        availableCreditRate: 0,
        chEmailNote: '',
        cnClientNote: '',
        cnClientWinNote: '',
        enClientNote: '',
        enClientWinNote: '',
        enEmailNote: '',
        heypointBalance: 100,
        isClient: 0,
        isEmail: 0,
        isNoticeLeader: 0,
        noticeType: 1,
        salesmanNote: '',
        settlementType: this.clientType,
        id: null, //临时id用来处理新增即进入编辑状态,确定新增是时重置为空
      };
      this.addOrUpdateBalance(configObj, 'add');
    },
    // 改变新增的配置时需要初始化值
    changeNewSetting(setting, index) {
      if (setting.noticeType == 2) {
        if (this.heypointList[index].availableCreditRate == 0 || !this.heypointList[index].availableCreditRate) {
          this.$set(this.heypointList[index], 'availableCreditRate', 10);
        }
      } else {
        if (this.heypointList[index].heypointBalance == 0 || !this.heypointList[index].heypointBalance) {
          this.$set(this.heypointList[index], 'heypointBalance', 100);
        }
      }
    },
    // 删除配置
    deleteBtn(id) {
      removeAll(id).then((res) => {});
    },
    // 编辑余额提醒
    editHeypoint(setting, type, index) {
      if (type === 'edit') {
        this.nowSetting.push(setting.id);
        // 编辑时哪个没有就置空哪个
        if (setting.noticeType == 1) {
          this.$set(this.heypointList[index], 'availableCreditRate', 0);
        } else {
          this.$set(this.heypointList[index], 'heypointBalance', 0);
        }
      }
      if (type === 'submit') {
        this.nowSetting.splice(this.nowSetting.indexOf(setting.id), 1);
        let editConfig = this.heypointList.find((item) => {
          return item.id === setting.id;
        });
        this.addOrUpdateBalance(editConfig);
      }
    },
    // 新增余额配置
    addOrUpdateBalance(data, type) {
      data.isEmail = data.isEmail ? 1 : 0;
      data.isClient = data.isClient ? 1 : 0;
      data.isNoticeLeader = data.isNoticeLeader ? 1 : 0;
      addOrUpdateBalance(data).then((res) => {
        if (res.code === 200) {
          this.getHeypointSetting(type);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.heypoint-setting {
  display: flex;
  flex-direction: column;
  height: 100%;
  .header-tab {
    position: relative;
    height: 40px;
    margin-bottom: 24px;
    .tab-item {
      position: relative;
      display: inline-block;
      min-width: 104px;
      padding: 0 6px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: #fff;
      cursor: pointer;
      text-align: center;
      // height: 0;
      // border-width: 0px 20px 40px 0px;
      // border-style: none solid solid;
      // border-color: transparent transparent #1d1d1f;
    }
    .active {
      color: #fff;
      background: $hg-main-blue;
      // border-color: transparent transparent $hg-main-blue;
    }
    .add-btn {
      width: 36px;
      height: 36px;
      line-height: 38px;
      text-align: center;
      background: $hg-main-blue;

      position: absolute;
      right: 0px;
      top: 0;
      user-select: none;

      &:active {
        background-color: #c6e2ff;
      }
      cursor: pointer;
      .el-icon-plus1 {
        font-size: 20px;
        margin-top: -2px;
        color: #fff;
      }
    }
  }
  .all-content {
    flex: 1;
    height: 100%;
    overflow: auto;
  }
  .content-setting {
    flex: 1;
    height: 100%;
    padding-right: 20px;
    overflow: auto;
    .content-box {
      display: flex;
      height: 150px;
      padding: 24px;
      background: #1d1d1f;
      margin-bottom: 20px;
      .content-box-first {
        flex: 1.5;
        .el-radio {
          height: 44px;
        }
        .custem-input {
          width: 60px;
          height: 24px;
          margin: 0 4px;
          /deep/.el-input__inner {
            height: 24px;
            padding: 0;
            text-align: center;
          }
        }
      }
      .content-box-second {
        flex: 1;
        margin-left: 20px;
      }
      .conteng-box-select {
        flex: 1.5;
        .el-checkbox {
          display: block;
          height: 36px;
        }
        /deep/.el-checkbox__label {
          color: #fff;
        }
        /deep/.el-checkbox__input.is-checked + .el-checkbox__label {
          color: $hg-main-blue;
        }
      }
      .content-box-third {
        flex: 1;
      }
      .content-box-four {
        flex: 1;
        position: relative;
        .btn {
          position: absolute;
          right: 0;
          bottom: 0;
        }
      }
    }
    /deep/.el-checkbox__input.is-disabled .el-checkbox__inner {
      background: #54565c3f;
    }
  }
}
</style>
