// 按钮、组件相关的中文
export default {
  component: {
    title: {
      system: 'System hint',
    },
    tip: {
      pleaseInput: 'Please input',
      title: 'Prompt',
    },

    date: {
      start: 'Start Time',
      end: 'Ending Time',
      startTime: 'Start Time',
      endTime: 'End Time'
    },

    pagination: {
      sizeLabel: '/page',
      jumpLabel: 'Jump to page',
      page: 'page'
    },
    ortho: {
      iprTitle: 'IPR',
      additionTitle: 'Add Attachments',
      btnShowTooth: 'Show tooth map',
      toothTitle: 'Tooth Map-{0}',
      btnAddStep: 'More alignment trays',
      inputStep: 'Alignment Trays {0}',
      cutInput: 'Reduce {0}',
      tips: {
        steps: 'Alignment Trays {0}',
        limitValue: 'Within the range of 1~99.',
        addTip: 'Sure not to add attachment for Tooth {0}?',
        isEmpty: 'Cannot be blank.',
        stepEmpty: 'Please enter which alignment trays to perform this.',
        cutIsEmpty: 'Please enter the width to reduce.',
        cutLimit: 'Reduced width is limited at 0.01~3.00 mm.',
        cutMsg: 'Sure not to reduce enamel at Alighment Trays {0} between teeth {1}-{2}?'
      },
    },
  }
}