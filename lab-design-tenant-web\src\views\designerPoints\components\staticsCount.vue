<template>
  <div>
    <el-drawer :title="lang('exportPoint')" custom-class="statics-count-drawer" :visible.sync="staticsCount" :before-close="closeDrawer">
      <div class="draw-title" slot="title">{{lang('pointsSta')}} {{this.date}}</div>
      <div class="statics-allocation">
        <div class="statics-header">
          <el-form :model="statics" :rules="rules" :inline="true" ref="staticsRule" label-width="110px" class="demo-ruleForm">
            <el-form-item class="statics-search" :label="lang('monthDate')" prop="workday">
              <el-input type="number" v-model="statics.workday"  :placeholder="lang('pleaseInput')"></el-input>
              <span class="tips">{{lang('day')}}</span>
            </el-form-item>
            <el-form-item class="statics-search special-search" :label="lang('nightDay')" prop="nightstandard">
              <el-input type="number" v-model="statics.nightstandard" :placeholder="lang('pleaseInput')"></el-input>
              <span class="tips">%</span>
            </el-form-item>
            <el-form-item class="statics-search special-search" :label="lang('middleDay')" prop="middlestandard">
              <el-input type="number" v-model="statics.middlestandard" :placeholder="lang('pleaseInput')"></el-input>
              <span class="tips">%</span>
            </el-form-item>
          </el-form>
        </div>
        <!-- 内容 -->
        <div class="statics-content" v-loading="tableLoading">
          <div v-show="!isImportExcel" class="point-upload-box">
            <el-upload
              id="staticsUpload"
              class="upload-demo"
              drag
              ref="staticsUpload"
              action="#"
              :accept="'.xlsx'"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="uploadChange"
              :key="uploadKey"
            >
              <span class="upload-icon">+</span>
              <div class="el-upload__text">
                <p>{{lang('dragExcel')}}</p>
                <p style="margin-top: 6px;">({{lang('limitTips')}})</p>
              </div>
            </el-upload>
            <div class="download-btn" @click="downdayExcel">
              <hg-icon icon-name="icon-download-default-lab"></hg-icon
              ><span class="btn-text">{{lang('downloadMonth')}}</span>
            </div>
          </div>
        </div>
        <!-- 列表 -->
        <div class="table-list" v-loading="tableLoading">
          <div class="import-title" v-if="isImportExcel">
            <div v-if="language == 'zh'">共计{{ importTableList.length }}个，导入<span class="success">成功{{ successList.length }}</span>个，<span class="error">失败{{ errorList.length }}</span>个</div>
            <div v-else>{{ importTableList.length }} in total:<span class="success">{{ successList.length }}imported, </span>,<span class="error">{{ errorList.length }} failed.</span></div>
          </div>
          <!-- 列表 -->
          <div class="depart-table" v-if="isImportExcel">
            <hg-table :header-data="headerData" class="user-table" :data="newMountedList">
              <template #designer="scope">
                <span>{{ scope.row.designer }}</span>
              </template>
              <!-- 点数的值 -->
              <template #email="scope">
                <div class="points">
                  <span>{{ scope.row.email || "--" }}</span>
                </div>
              </template>

              <template #result="scope">
                <span v-if="scope.row.result === '成功'">{{lang('Imported')}}</span>
                <span class="error-result" v-else>{{lang('failed')}}</span>
              </template>
            </hg-table>
            <div class="depart-pagination" v-if="isImportExcel">
              <pagination showTotal :total="page.total" :isHavePageSize="false" :isHaveJump="false" :initPageIndex="page.pageNo" :initPageSize="page.pageSize" @onSearch="search"></pagination>
            </div>
          </div>
        </div>
        <!-- 按钮组 -->
        <div class="btn-list" v-if="newMountedList.length">
          <el-button plain @click="reloadUpload">{{lang('exportAgain')}}</el-button>
          <el-button type="primary" @click="creatStatics">{{lang('creat')}}</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import hgTable from "@/components/HgTable";
import pagination from '@/components/Pagination';
import { importDayExcel, getDesignerShifts, saveDesignerShifts, generate, exportMonth } from "@/api/designPoints";
import { getDownloadUrl } from '@/api/file';
import { directDown, createIFrameDownLoad } from "@/public/utils/file.js";
import { server } from "@/config";
import { mapGetters } from "vuex";
import { getLang } from '@/public/utils';
export default {
  name: "staticsCount",
  components: {hgTable, pagination},
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    date: String,
    isCreatDesignerList: Boolean
  },
  data() {
    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.lang('pleaseInput')));
      } else if(value < 0 || String(value).includes('.')) {
        callback(new Error(this.lang('intNum')));
      } else {
        callback();
      }
    };
    return {
      statics: {
        workday: '',
        middlestandard: '',
        nightstandard: ''
      },
      rules: {
        workday: [{ required: true, message: this.lang('pleaseInput'), trigger: 'blur' },{ validator: validatePass, trigger: 'blur' }],
        middlestandard: [{ required: true, message: this.lang('pleaseInput'), trigger: 'blur' },{ validator: validatePass, trigger: 'blur' }],
        nightstandard: [{ required: true, message: this.lang('pleaseInput'), trigger: 'blur' },{ validator: validatePass, trigger: 'blur' }]
      },
      uploadLoading: false,
      uploadKey: 0,
      isImportExcel: false,
      importTableList: [],
      errorList: [],
      successList: [],
      tableLoading: false,
      page: {
        pageSize: 20,
        pageNo: 1,
        total: 0,
      },
      newMountedList: []
    };
  },
  computed: {
    ...mapGetters(["language"]),
    staticsCount: {
      get() {
        return this.drawer;
      },
      set(val) {
        this.$emit("update:drawer", val);
      },
    },
    headerData() {
      return [
        {
          prop: "rowNumber",
          width: "80px",
          noTip: false,
          getLabel: () => {
            return this.lang('number');
          },
        },
        {
          prop: "email",
          minWidth: "40%",
          noTip: false,
          getLabel: () => {
            return this.lang('account');
          },
        },
        {
          prop: "designer",
          minWidth: "30%",
          noTip: false,
          getLabel: () => {
            return this.lang('desinger');
          },
        },
        {
          prop: "nightShiftDays",
          minWidth: "30%",
          noTip: false,
          getLabel: () => {
            return this.lang('night');
          },
        },
        {
          prop: "midShiftDays",
          minWidth: "20%",
          noTip: false,
          getLabel: () => {
            return this.lang('middle');
          },
        },
        {
          prop: "result",
          minWidth: "30%",
          noTip: false,
          getLabel: () => {
            return this.lang('result');
          },
        }
      ];
    },
  },
  watch: {
    staticsCount(newValue, oldValue) {
      if(newValue){
        this.designerShifts()
      } else {
        // //侧边栏关闭时保存一次标准
        // this.saveDesignerShifts();
        this.newMountedList = [];
        this.uploadKey = 0;
        this.importTableList = [];
        this.errorList = [];
        this.successList = [];
        this.isImportExcel = false;
        this.page = {
          pageSize: 20,
          pageNo: 1,
          total: 0,
        };
      }
    }
  },
  mounted () {
    
  },
  methods: {
    lang: getLang('designpoints'),
    closeDrawer(){
      this.$confirm(this.lang('istuichu'), this.lang('systemTips'), {
          confirmButtonText: this.lang('submitBtn'),
          cancelButtonText: this.lang('cancel')
        }).then(() => {
          this.$emit('successCreat');
        })
        .catch(action => {
          
        });
    },
    // 获取点数标准
    async designerShifts(){
      const { code, data } = await getDesignerShifts(this.date);
      if(code == 200){
        this.statics.workday = this.isCreatDesignerList ? data.monthlyWorkingDays : '';
        this.statics.middlestandard = this.isCreatDesignerList ? data.midShiftWorkingDays : '';
        this.statics.nightstandard = this.isCreatDesignerList ? data.nightShiftWorkingDays: '';
      }
    },
    // 保存点数标准
    async saveDesignerShifts(){
      let data = {
        monthlyWorkingDays: this.statics.workday,
        midShiftWorkingDays: this.statics.middlestandard,
        nightShiftWorkingDays: this.statics.nightstandard,
        month: this.date
      }
      await saveDesignerShifts(data)
    },
    async uploadChange(file) {
      this.newMountedList = []
      const regRex = /\.(xlsx)$/g;
      if (!regRex.test(file.name.toLowerCase())) {
        this.$message.error(this.lang("uploadError"));
        this.uploadKey++;
        return false;
      }
      let data = {
        monthlyWorkingDays: this.statics.workday,
        midShiftWorkingDays: this.statics.middlestandard,
        nightShiftWorkingDays: this.statics.nightstandard,
        month: this.date
      }
      const { code } = await saveDesignerShifts(data);
      const param = new FormData();
      param.append("uploadFile", file.raw);
      param.append("month", this.date);
      this.tableLoading = true;
      try {
        const { code, data } = await importDayExcel(param);
        if (code === 200) {
          // 获取成功和失败的总和
          // this.importTableList = data;
          this.errorList = data.filter((item) => {
            return item.result === "失败";
          });
          this.successList = data.filter((item) => {
            return item.result === "成功";
          });
          this.importTableList = this.errorList.concat(this.successList); // 错误置顶
          this.page.total = data.length;
          this.page.pageNo = 1;
          this.page.pageSize = 20;
          this.newMountedList = this.importTableList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize);
          this.isImportExcel = true;
          this.tableLoading = false;
        } else {
          this.isImportExcel = false;
          this.tableLoading = false;
          return;
        }
      } catch (error) {
        this.$message.error(this.$t('common.noData'));
        this.isImportExcel = false;
        this.tableLoading = false;
      }
      this.uploadFileList = [];
    },
    // 重新导入
    reloadUpload(){
      this.$refs.staticsUpload.$children[0].handleClick()
    },
    // 校验文件
    verifyFile(file, fileList) {
      // if(this.acceptType !== '.xlsx'){
      const regRex = /\.(xlsx)$/g;
      if (!regRex.test(file.name.toLowerCase())) {
        this.$hgOperateFail(this.lang("uploadError"));
        return false;
      }
      // }
      return true;
    },
    // 下载月值班表模板
    async downdayExcel() {
      // let url = `${server.designerPoints}/export`;
      // directDown(url, `${this.lang('template')}.xlsx`);
      this.$message.success(this.lang('downLoadWait'));
      const { code, data } = await exportMonth();
      if(code == 200){
        const param = {
          s3FileId: data,
          filename: `${this.lang('template')}`,
        };
        getDownloadUrl(param).then(res => {
          if(res.code === 200) {
            createIFrameDownLoad(res.data.url);
          }else {
            this.$hgOperateFail(this.$t('http.error.80080003'));
          }
        }).catch(err => {
          console.log('error:',err);
        });
      }
    },
    // 生成
    async creatStatics(){
      let designerDays = []
      this.successList.forEach((item) => {
        let obj = {};
        obj.midShiftDays = Number(item.midShiftDays);
        obj.nightShiftDays = Number(item.nightShiftDays);
        obj.userCode = item.userCode
        designerDays.push(obj)
      })
      let parame = {
        designerDays: designerDays,
        month: this.date
      }
      this.$refs['staticsRule'].validate(async (valid) => {
        if(valid){
          const { code, data } = await generate(parame);
          if(code == 200){
            this.$message.success(this.lang('creatSuccess'));
            this.$emit('successCreat');
          }
        } else {
          return false
        }
      })
    },
    // 列表跳下一页
    search(type, searchData){
      this.newMountedList = [];
      if(searchData) {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
        this.newMountedList = this.importTableList.slice((pageIndex - 1) * pageSize, pageIndex * pageSize)
      }
    },
  },
};
</script>

<style lang="scss">
.statics-count-drawer {
  width: 900px !important;
  background-color: $hg-main-black;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
    color: $hg-label;
  }
  .el-drawer__body {
    padding: 24px;
    overflow: hidden;
  }
  .el-button.is-plain:focus{
    background: transparent;
  }
  .el-button--primary:focus{
    background: #3760EA;
    border-color: #3760EA;
  }
  .statics-allocation{
    display: flex;
    flex-direction: column;
    height: calc(100% - 80px);
    .error-tips{
      color: #FBAA0E;
      font-size: 12px;
      padding: 4px;
      margin-left: 48px;
    }
    .statics-header{
      display: flex;
      .statics-search{
        position: relative;
        .el-input__prefix{
          display: none;
        }
        .el-input__inner{
          padding-right: 40px;
        }
        .el-input__suffix{
          padding-right: 30px;
        }
        .el-form-item__content{
          position: relative;
          .tips{
            position: absolute;
            font-size: 12px;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
      .special-search{
        .el-form-item__label{
          width: 80px!important;
        }
      }
      .points-btnlist{
        position: absolute;
        right: 0px;
        top: 0;
      }
      .el-input{
        width: 180px;
        background: #141519;
        .el-input--prefix .el-input__inner{
          padding-left: 20px;
        }
      }
      .error-border-tips{
        .el-input__inner{
          border-color: #E55353;
        }
      }
    }
    .statics-content{
      // margin-top: 24px;
      // height: calc(100% - 60px);
      .point-upload-box {
        width: 100%;
        height: 656px;
        background: $hg-hover;
        padding: 20px;
      }
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: $hg-hover;
        width: 100%;
        height: 566px;
        border-color: $hg-main-border;
        .upload-icon {
          color: #9EA2A8;
          font-size: 36px;
        }
      }
      .download-btn {
        display: flex;
        width: 100%;
        height: 40px;
        justify-content: center;
        align-items: center;
        color: $hg-main-blue;
        margin-top: 20px;
        cursor: pointer;
      }
      .btn-text {
        vertical-align: top;
        margin-left: 8px;
      }
    }
    .table-list {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: calc(100% - 60px);
      .import-title {
        line-height: 40px;
        .success {
          color: #00b860;
          margin: 0 8px;
        }
        .error {
          color: #e55353;
          margin: 0 8px;
        }
      }
      .depart-table {
        position: relative;
        // flex: 1;
        height: calc(100% - 20px);
        width: 100%;
        .hg-table {
          height: 100%;
          // height: calc(100% - 30px);
          overflow: hidden;
          .el-table {
            // height: calc(100% - 60px)!important;
            // max-height: 100% !important;
          }
        }
        .table-high-light {
          float: left;
          width: auto;
          max-width: calc(100% - 41px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .expedited-time {
          line-height: 40px;
        }
        .error-result{
          color: #E55353;
        }
      }
      .depart-pagination {
        z-index: 1;
        position: absolute;
        bottom: 0;
        right: 0;
        height: 60px;
        width: 100%;
      }
      .btn-list{
        position: absolute;
        bottom: 24px;
        right: 10px;
        display: flex;
        justify-content: flex-end;
        width: 100%;
        height: 50px;
        padding: 10px 24px 0 0;
        border-top: 1px solid $hg-main-border;
      }
    }
    .btn-list{
      position: absolute;
      bottom: 24px;
      right: 10px;
      display: flex;
      justify-content: flex-end;
      width: 100%;
      height: 56px;
      padding: 16px 24px 0 0;
      border-top: 1px solid $hg-main-border;
    }
  }
}
</style>
