import request from '../axios';
import { server } from '@/config';

const axios = request.axios;

/**
 * 获取租户端订单列表
 */
export const getOrderList = (data) => {
  return axios.post(`${server.orderServer}/list`, data);
};

/**
 * 获取批量指派列表
 */
export const getBatchList = (designCodes, nameOrGroupKeyword, staffTypeCode, orderCustomerOrgCode) => {
  const params = {
    designCodes,
    nameOrGroupKeyword,
    staffTypeCode,
    orderCustomerOrgCode
  };
  return axios.get(`${server.userServer}/group-staffs`, {params});
};

/**
 * 批量指派操作
 */
export const batchAssign = (data) => {
  return axios.post(`${server.orderServer}/batchAssign`, data);
};

/**
 * 由我审核，由我指派
 */
 export const batchOperate = (data) => {
  return axios.post(`${server.orderServer}/batchOperate`, data);
};

/**
 * 批量由我审核
 * @param {*} orderCodeList 
 */
export const batchExamineByMe = (orderCodeList) => {
  return axios.post(`${server.orderServer}/reviewedByMe`, orderCodeList);
}

/**
 * 获取订单详情
 */
export const getOrderDetail = (orderCode) =>  {
  return axios.get(`${server.orderServer}/orderInfo`, { params: { orderCode } });
};

/**
 * 获取正畸方案列表
 * @param {} orderCode 
 */
export const getOrthList = (orderCode) => {
  return axios.get(`${server.systemCommonServer}/programmes`, { params: { orderCode } });
};

/**
 * OQC：获取待审核的订单 type:1 上一条  type:2 下一条
 */
export const getUnCheckOrderForMe = ({ orderCode, operateType }) => {
  return axios.post(`${server.orderServer}/toBeCheckedOrderInfo`,{ orderCode, type: operateType });
};

// 查询QC列表
export const getUserList = (userName) => {
  return axios.get(`${server.orderServer}/getQcUsers`,{ params: { qcName: userName } });
};

/**
 * 批量获取下载预授权
 */
 export const getBatchDownloadUrl = (data) => {
  return axios({
    url: `${server.normalUploadServerV3}/getBatchDownloadUrl`,
    method: 'post',
    data:{
      downloadUrls: data,
      isAccelerate: false,
    }
  });
 };

 /**
 * 下载预授权
 * @param { Object } data 请求参数  {filename:自定义文件名, orgCode：用户组织code, s3FileId：s3FileId必传}
 */

 export const getDownloadUrl = (data, isHandlerError, timeout) => {
  return axios({
    url: `${server.normalUploadServerV3}/getDownloadUrl`,
    method: 'POST',
    data: {
      ...data,
      isAccelerate: false,
    },
    isHandlerError,
    timeout,
  });
};

 // 获取所有的设计软件
export const getDesignSoftwareAll = data => {
  return axios({
    url: `${server.orderFoler}/designSoftwareAll`,
    method: 'GET',
    data,
  });
};

// 联合订单批量指派
export const batchSetDesigner = data => {
  return axios({
    url: `${server.orderServer}/batchSetDesigner`,
    method: 'post',
    data
  });
}

// 获取设计师轮次信息
export const getorderDesigners = (orderCode) => {
  return axios({
    url: `${server.orderServer}/orderDesigners`,
    method: 'GET',
    params: {
      orderCode
    },
  });
}
// 保存常规
export const saveNewCommonQuestions = (data) => {
  return axios({
    url: `${server.orderServer}/saveNewCommonQuestions`,
    method: 'post',
    data
  });
}

// 获取租户端二级部门
export const getdeptInfos = (name) => {
  return axios({
    url: `${server.orderServer}/deptInfos`,
    method: 'GET',
    params: {
      name: ''
    },
  });
}

// 导出新增订单excel
export const exportNewOrder = (data) => {
  return axios({
    url: `${server.orderServer}/importNewOrder`,
    method: 'post',
    data
  });
}
