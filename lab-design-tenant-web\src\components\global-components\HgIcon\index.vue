<template>
  <i 
    v-if="iconName"
    :class="['hg-icon',iconfontName, iconName, popperClass]" 
    :style="{
      color: color, 
      'font-size': fontSize,
      ...popperStyle
    }"
    @click="$emit('click')">
    <slot></slot>  
  </i>
  <i 
    v-else 
    :class="['hg-icon','hg-icon-svg',popperClass,iconClass]" 
    :style="{
      color: color, 
      ...popperStyle
    }"
    @click="$emit('click')">
    <svg-icon :iconClass="iconClass"></svg-icon>
  </i>
</template>

<script>
export default {
  name: 'HgIcon',
  props: {
    iconfontName: {
      type: String,
      default: 'iconfont-lab',
    },
    iconName: {
      type: String,
      default: '',
      require: true
    },
    iconClass: {
      type: String,
      default: '',
    },
    popperClass: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: ''
    },
    fontSize: String,
    popperStyle: {
      type: Object,
      return() {
        return null;
      }
    }
  },
  methods: {
    handleClick() {
      this.$emit('click');
    }
  }
}
</script>

<style lang="scss" scoped>
.hg-icon-svg {
  display: inline-block;
}
</style>