<template>
  <div class="component-search">
    <div class="left">
      <Input
        :placeholder="placeholder ? placeholder : $t('common.searchTip')"
        padding-left="24px"
        padding-right="24px"
        size="normal"
        prefix-icon="icon-search"
        :input-content="inputContent"
        @blurInput="enterInput"
      />
    </div>
    <div v-if="searchIcons.refresh || searchIcons.add" v-permission="['addCustomer', 'delete']" :class="['right', `right_${size}`]">
      <span v-if="searchIcons.refresh" @click="research"><i class="iconfont icon-refresh" /></span>
      <span v-if="searchIcons.add" @click="add"><i class="iconfont icon-add" /></span>
    </div>
  </div>
</template>

<script>
import Input from '@/components/func-components/Input.vue'

export default {
  name: 'Search',
  components: {
    Input
  },
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'small'
    },
    searchIcons: {
      type: Object,
      default: () => {
        return {
          add: true,
          refresh: true
        }
      }
    },
    inputContent: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {

    }
  },
  methods: {
    enterInput(arg) {
      this.$emit('enterInput', arg)
    },
    research() {
      this.$emit('research')
    },
    add() {
      this.$emit('add')
    }
  }
}
</script>

<style lang="scss" scoped>
  .component-search {
    width: 100%;
    height: auto;
    background-color: $hg-main-black;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .left {
      flex: 1;
    }
    .right {
      color: $hg-primary-fontcolor;
      span {
        display: inline-block;
        border-radius: 2px;
        text-align: center;
        background-color: transparent;
        margin-left: 8px;
        .iconfont {
          cursor: pointer;
          font-size: 16px;
        }
        &:hover, &:active{
          background-color: $hg-hover-bg-color;
        }
        &:active {
          .iconfont {
            color: $hg-active-iconcolor;
          }
        }
      }
      &_medium {
        span {
          width: 40px;
          height: 40px;
          line-height: 40px;
        }
      }
      &_small {
        span {
          width: 32px;
          height: 32px;
          line-height: 32px;
        }
      }
      &_mini {
        span {
          width: 24px;
          height: 24px;
          line-height: 24px;
        }
      }
    }
  }
</style>
