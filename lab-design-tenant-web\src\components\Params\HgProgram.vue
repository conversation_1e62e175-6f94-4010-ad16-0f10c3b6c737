<template>
  <div class="hg-program-box">
    <el-row v-if="dataList && dataList.length > 0">
      <el-col class="hg-program-col" v-for="(item, index) in dataList" :key="`${item.code}-${index}`">
        <div class="program-label">
          <span>{{ getI18nName(item, i18nTitle, $getI18nText) }}</span>
        </div>

        <div class="program-content">
         
          <hg-radio 
            v-if="item.component === COMPONENT_TYPE.RADIO" 
            v-model="item.value" 
            :data="item"
            :i18nTitle="i18nTitle"
            :eventDisable="eventDisable"></hg-radio>

          <hg-img-checkbox-card
            v-else-if="item.component === COMPONENT_TYPE.IMAGE_CHECKBOX_CARD && !eventDisable" 
            v-model="item.value" 
            :data="item"
            :i18nTitle="i18nTitle"
            :parentItem="findParentItem(item)"
            :eventDisable="eventDisable"></hg-img-checkbox-card>
          
          <show-hg-img-checkbox-card
            v-else-if="item.component === COMPONENT_TYPE.IMAGE_CHECKBOX_CARD && eventDisable" 
            v-model="item.value" 
            :data="item"
            :i18nTitle="i18nTitle"></show-hg-img-checkbox-card>

          <hg-radio-select-card 
            v-else-if="item.component === COMPONENT_TYPE.RADIO_SELECT_CARD" 
            v-model="item.value" 
            :data="item"
            :i18nTitle="i18nTitle"
            :eventDisable="eventDisable"></hg-radio-select-card>

          <hg-select
            v-else-if="item.component === COMPONENT_TYPE.SELECT"
            v-model="item.value" 
            :data="item"
            :i18nTitle="i18nTitle"
            :eventDisable="eventDisable"></hg-select>

          <hg-radio-img-card
            v-else-if="item.component === COMPONENT_TYPE.RADIO_IMG_CARD"
            v-model="item.value" 
            :data="item"
            :i18nTitle="i18nTitle"
            :eventDisable="eventDisable"></hg-radio-img-card>

          <hg-img-checkbox 
            v-else
            v-model="item.value" 
            :data="item"
            :i18nTitle="i18nTitle"
            :eventDisable="eventDisable"></hg-img-checkbox>

        </div>
      </el-col>
    </el-row>

    <div v-else class="no-program">
      <span>{{ $t('param.noProgram') }}</span>
    </div>
  </div>
</template>

<script>
import { COMPONENT_TYPE } from './utils/constant';
import HgImgCheckbox from './components/HgImgCheckbox';
import HgRadio from './components/HgRadio';
import HgImgCheckboxCard from './components/HgImgCheckboxCard';
import HgRadioSelectCard from './components/HgRadioSelectCard';
import ShowHgImgCheckboxCard from './components/ShowHgImgCheckboxCard';
import HgRadioImgCard from './components/HgRadioImgCard';
import HgSelect from './components/HgSelect';
import { getI18nName } from './utils';

export default {
  components: {
    HgRadio,
    HgImgCheckbox,
    HgImgCheckboxCard,
    HgRadioSelectCard,
    ShowHgImgCheckboxCard,
    HgRadioImgCard,
    HgSelect,
  },
  provide() {
    return {
      needZoom: this.needZoom,
    }
  },
  props: {
    dataList: {
      type: Array,
      default(){
        return [];
      }
    },
    i18nTitle: {
      type: String,
      default: ''
    },
    needZoom: {
      type: Boolean,
      default: true
    },
    eventDisable: {
      type: Boolean,
      default: false,
    }
  },
  data(){
    return {
      COMPONENT_TYPE,
    }
  },
  watch: {
    dataList: {
      deep: true,
      handler(list) {
        // console.log(JSON.stringify(list[2]));
      }
    }
  },
  methods: {
    getI18nName,
    findParentItem(item) {
      if(item.connectPid) {
        const parentItem = this.dataList.find(data => data.code === item.connectPid);
        return parentItem;
      }else {
        return null;
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import './style/index.scss';
</style>