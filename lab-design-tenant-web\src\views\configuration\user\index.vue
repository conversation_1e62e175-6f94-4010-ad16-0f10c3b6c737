<template>
  <div class="user-content">
    <div class="user-list">
      <!-- 左边树 -->
      <div class="list-left-tree">
        <!-- 搜索框 -->
        <div>
          <el-input :placeholder="$t('userList.orgSearch.keywords')" v-model="departName">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <!-- 树组织 -->
        <div class="depart-tree">
          <el-tree
            class="depart-filter-tree"
            :data="orgDept"
            node-key="orgCode"
            :props="defaultProps"
            :filter-node-method="filterNode"
            :default-expanded-keys="expandKeys"
            :current-node-key="selectOrgCode"
            @node-click="selectDepart"
            ref="tree"
          >
            <template slot-scope="{ data }">
              <div :class="['custom-label', data.orgCode == selectOrgCode ? 'current' : '']">
                {{ data.orgName }}
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      <!-- 右边人员列表 -->
      <div class="list-right-table">
        <!-- 头部搜索 -->
        <div class="depart-table-search">
          <span v-if="selectOrg">{{ selectOrg.orgName }}</span>
          <div class="search-input">
            <hg-input v-model="organization" prefix-icon="el-icon-search" :placeholder="$t('userList.orgSearch.orgKeyWords')" @change="search" clearable></hg-input>
          </div>
          <div class="btn-list">
            <hg-button v-show="haveDesigner" v-permission="['skinLevelSet']" type="primary" @click="openSetSkin">{{$t('designpoints.skillLevel')}}</hg-button>
            <hg-button type="primary" popper-class="order-list-btn" @click="openSetRange">{{ $t('userList.list.rangName') }}</hg-button>
          </div>
        </div>
        <!-- 列表 -->
        <div class="depart-table">
          <hg-table :header-data="headerData" :height="'auto'" class="user-table" :needSelect="true" :loading="tableLoading" :data="userList" @update-selected-rows="selectTable">
            <!-- 负责范围 -->
            <template #rangName="scope">
              <el-tooltip class="table-high-light" effect="dark" popper-class="range-tooltip" :content="$getI18nText(scope.row.rangName)">
                <span>{{ $getI18nText(scope.row.rangName) }}</span>
              </el-tooltip>
            </template>
            <!-- 在线状态 -->
            <template #isOnline="scope">
              <div v-if="!scope.row.isOnline"><span class="login-type-noonline"></span>{{ $t('userList.list.noLine') }}</div>
              <div v-if="scope.row.isOnline"><span class="login-type-online"></span>{{ $t('userList.list.onLine') }}</div>
            </template>
            <template #roleName="scope">
              <span>{{ scope.row.roleStr }}</span>
            </template>

            <!-- 技能等级 -->
            <template #designerLevel="scope">
              <div v-if="scope.row.designerLevel">{{ $getI18nText(getNowLevel(scope.row.designerLevel)) }}</div>
              <div v-else>--</div>
            </template>
          </hg-table>
        </div>
        <div class="depart-pagination">
          <pagination :total="page.total" :disabled="tableLoading" :initPageIndex="page.pageNo" :initPageSize="page.pageSize" @onSearch="search"></pagination>
        </div>
      </div>
    </div>
    <set-range-dialog ref="setRange" @setRangeList="setRangeList"></set-range-dialog>
    <!-- 技能等级弹窗 -->
    <el-dialog :title="$t('designpoints.configDedine')" :visible.sync="setSkinDialog" width="500px">
      <div class="dialog-box">
        <el-select style="width: 100%" v-model="configSkin" :placeholder="$t('designpoints.tips')">
          <el-option v-for="item in skinList" :key="item.code" :label="$t(`apiCommon.${item.enName}`)" :value="item.code"></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSkin">{{$t('common.cancel')}}</el-button>
        <el-button type="primary" @click="submitSkin">{{$t('common.ok')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import hgTable from '@/components/HgTable';
import pagination from '@/components/Pagination';
import setRangeDialog from './components/setRangeDialog';
import { getOrgTree, getUserList, setRange, getDesignerLevels, batchSetDesignerLevel } from '@/api/user';
import { mapGetters } from 'vuex';
export default {
  name: 'user',
  components: { hgTable, setRangeDialog, pagination },
  data() {
    return {
      departName: '',
      organization: '',
      orgDept: [], //组织树
      selectOrgCode: '',
      selectOrg: null,
      expandKeys: [],
      defaultProps: {
        children: 'sonList',
        label: 'orgName',
      },
      tableLoading: true,
      page: {
        pageSize: 10,
        pageNo: 1,
        total: 0,
      },
      userList: [],
      selectSelection: [], //选择的项
      setSkinDialog: false,
      skinList: [{
        code: 1,
        zhName: '技能一等'
      }],
      configSkin: null, // 选择的技能等级
      haveDesigner: false,
    };
  },
  computed: {
    ...mapGetters(['orgCode', 'language']),
    headerData() {
      return [
        {
          prop: 'name',
          minWidth: '20%',
          noTip: false,
          getLabel: () => {
            return this.$t('userList.list.name');
          },
        },
        {
          prop: 'isOnline',
          minWidth: '15%',
          noTip: true,
          getLabel: () => {
            return this.$t('userList.list.isOnline');
          },
        },
        {
          prop: 'roleName',
          minWidth: '25%',
          noTip: false,
          getLabel: () => {
            return this.$t('userList.list.roleName');
          },
        },
        {
          prop: 'designerLevel',
          isHideHeader: 'hide',
          minWidth: '25%',
          noTip: false,
          getLabel: () => {
            return this.$t('designpoints.skillLevel');
          },
        },
        {
          prop: 'rangName',
          minWidth: '40%',
          noTip: true,
          getLabel: () => {
            return this.$t('userList.list.rangName');
          },
        },
      ];
    },
    getNowLevel(){
      return (value) => {
        let level = this.skinList.find((item) => { return item.code == value })
        return { zh: level.zhName, en: level.enName }
      };
    }
  },
  watch: {
    departName(val) {
      this.$refs.tree.filter(val);
    },
  },
  mounted() {
    this.getOrgDeptList();
  },
  methods: {
    // 获取部门组织架构树
    async getOrgDeptList() {
      let orgCode = this.orgCode ? this.orgCode : null;
      const { data } = await getOrgTree(orgCode);
      this.orgDept = data;
      this.selectOrg = this.orgDept[0];
      this.selectOrgCode = this.selectOrgCode ? this.selectOrgCode : this.orgDept[0].orgCode;
      this.expandKeys = this.orgDept.map((item) => {
        return item.orgCode;
      });
      await this.getDesignerLevels();
      await this.search();
    },
    // 改变搜索
    async search(type, searchData) {
      this.tableLoading = true;
      if (type == 'page') {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
      } else {
        this.page.pageNo = 1;
        this.page.pageSize = 10;
      }
      let data = {
        nameKeyword: this.organization,
        orgCode: this.selectOrgCode ? this.selectOrgCode : this.orgDept[0].orgCode,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
      };
      await this.getUserList(data);
    },

    // 选择部门
    selectDepart(data, node) {
      let orgCode = data.orgCode;
      this.selectOrg = data;
      this.selectOrgCode = orgCode;
      this.search();
    },
    // 获取人员列表
    getUserList(data) {
      this.selectSelection = [];
      getUserList(data).then((res) => {
        if (res.code == 200) {
          let uesrList = res.data.data;
          this.page.total = res.data.totalSize;
          this.page.pageNo = res.data.pageNo;
          this.page.pageSize = res.data.pageSize;
          this.userList = this.setUserList(uesrList);
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    // 在这里处理列表
    setUserList(list) {
      let tableDtata = [];
      list.forEach((item) => {
        let roleStr = '';
        if(item.i18nCodes){
          item.i18nCodes.split(',').forEach((item) => {
            roleStr += this.$t(`apiCommon.${item}`) + ','
          })
          roleStr = roleStr.slice(0, roleStr.length - 1);
        }
        const tableItem = {
          enRangeNames: item.enRangeNames,
          isOnline: item.isOnline,
          name: item.name,
          roleName: item.roleName,
          showRoleName: { zh: item.roleName, en: item.roleEnName },
          roleStr: roleStr,
          userCode: item.userCode,
          zhRangeNames: item.zhRangeNames,
          rangName: { zh: item.zhRangeNames, en: item.enRangeNames },
          designerLevel: item.designerLevel
        };
        tableDtata.push(tableItem);
      });
      // 判断列表是否有设计师
      let findObj = tableDtata.find((item) => {
        return item.roleName.indexOf('设计师') != -1;
      });
      if(findObj){
        this.headerData[3].isHideHeader = '';
        this.haveDesigner = true;
      } else {
        this.headerData[3].isHideHeader = 'hide';
        this.haveDesigner = false;
      }
      return tableDtata;
    },
    // 选择的table row
    selectTable(length, selection) {
      this.selectSelection = selection;
    },
    // 设置范围弹窗
    openSetRange() {
      // 当列表没有选中时需要拒绝
      if (this.selectSelection && this.selectSelection.length == 0) {
        this.$message({
          type: 'error',
          message: this.$t('userList.list.selectPeople'),
        });
        return;
      }
      // 当选择了不是IQC,OQC,设计师时需要拒绝
      let findObj = this.selectSelection.find((item) => {
        return item.roleName.indexOf('IQC') == -1 && item.roleName.indexOf('OQC') == -1 && item.roleName.indexOf('设计师') == -1;
      });
      if (findObj) {
        this.$message({
          type: 'error',
          message: this.$t('userList.list.people'),
        });
        return;
      }
      this.$refs.setRange.setRangeDialog = true;
    },
    // 设置技能等级
    openSetSkin(){
      this.configSkin = '';
      // 当列表没有选中时需要拒绝
      if (this.selectSelection && this.selectSelection.length == 0) {
        this.$message({
          type: 'error',
          message: this.$t('designpoints.designers'),
        });
        return;
      }
      // 当选择了不是设计师时需要拒绝
      let findObj = this.selectSelection.find((item) => {
        return item.roleName.indexOf('设计师') == -1;
      });
      if (findObj) {
        this.$message.error(this.$t('designpoints.designError'))
        return;
      }
      this.setSkinDialog = true;
    },
    cancelSkin(){
      this.setSkinDialog = false;
      this.configSkin = '';
    },
    // 提交技能等级
    async submitSkin(){
      let data = []
      this.selectSelection.forEach((item) => {
        let obj = {}
        obj.userCode = item.userCode;
        obj.code = this.configSkin;
        data.push(obj)
      })
      const {code} = await batchSetDesignerLevel(data);
      if(code == 200){
        this.$message.success(this.$t('designpoints.configSuccess'));
        this.setSkinDialog = false;
        this.search();
      }
    },
    // 筛选树
    filterNode(value, data) {
      if (!value) return true;
      return data.orgName.indexOf(value) !== -1;
    },
    // 负责范围
    setRangeList(list) {
      let userCodes = this.selectSelection.map((item) => {
        return item.userCode;
      });
      let data = {
        designCodes: list,
        orgCode: this.selectOrgCode,
        userCodes: userCodes,
      };
      setRange(data).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: this.$t('userList.list.success'),
          });
          this.$refs.setRange.setRangeDialog = false;
          this.search();
        } else {
          this.$message.error(this.$t('userList.list.error'));
          this.$refs.setRange.setRangeDialog = false;
        }
      });
    },
    // 获取技能列表
    getDesignerLevels(){
      getDesignerLevels().then((res) => {
        if(res.code == 200){
          this.skinList = res.data;
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.user-content {
  height: 100%;
  .user-title {
    color: $hg-main-blue;
    font-weight: bold;
    font-size: 16px;
    line-height: 48px;
    letter-spacing: 0px;
    text-align: left;
  }
  .user-list {
    display: flex;
    height: 100%;
    /deep/.el-input.el-input--prefix .el-input__prefix {
      margin-left: 0 !important;
      left: 5px !important;
    }
    .list-left-tree {
      width: 290px;
      border-radius: 4px;
      background: #1d1d1f;
      height: 100%;
      padding: 12px 16px;
      overflow: auto;
      .depart-tree {
        margin-top: 20px;
      }
      .depart-filter-tree {
        background: #1d1d1f;
        color: #fff;
        /deep/.el-tree-node__content {
          height: 36px;
        }
        /deep/.el-tree-node__content:hover {
          background-color: #262629;
        }
        /deep/.el-tree-node:focus > .el-tree-node__content {
          background-color: #262629;
        }
      }
      .current {
        color: $hg-main-blue;
      }
    }
    .list-right-table {
      position: relative;
      margin-left: 24px;
      width: 864px;
      padding: 24px;
      flex: 1;
      background: #1d1d1f;
      height: 100%;
      display: flex;
      flex-direction: column;
      .depart-table-search {
        display: flex;
        // width: 100%;
        position: relative;
        height: 62px;
        // align-items: top;
        span {
          display: inline-block;
          line-height: 40px;
          margin-right: 10px;
        }
        .search-input {
          width: 360px;
        }
        .skill-btn{
          position: absolute;
          right: 120px;
          top: 6px;
        }
        .btn-list{
          position: absolute;
          right: 0;
          top: 6px;
        }
        .order-list-btn {
          margin-left: 16px;
        }
      }
      .depart-table {
        flex: 1;
        width: 100%;
        .hg-table {
          height: 100%;
          overflow: auto;
          /deep/.el-table {
            // max-height: 100% !important;
          }
        }
        .table-high-light {
          float: left;
          width: auto;
          max-width: calc(100% - 41px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .login-type-noonline {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #737680;
          margin-right: 4px;
        }
        .login-type-online {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #72d143;
          margin-right: 4px;
        }
      }
      .depart-pagination {
        z-index: 1;
        position: absolute;
        bottom: 0;
        right: 0;
        height: 60px;
        width: 100%;
      }
    }
  }
}
.dialog-box{
  height: 100px;
}
</style>
<style lang="scss">
.range-tooltip {
  max-width: 600px;
}
</style>
