import * as THREE from 'three'
import { _vector3 } from './vector3'

export function getAttributePositionBox3Vectors(position, matrix, filter) {
  const maxs = {
    x: new THREE.Vector3(-Infinity, -Infinity, -Infinity),
    y: new THREE.Vector3(-Infinity, -Infinity, -Infinity),
    z: new THREE.Vector3(-Infinity, -Infinity, -Infinity),
    xVertexIndex: null,
    yVertexIndex: null,
    zVertexIndex: null,
  }

  const mins = {
    x: new THREE.Vector3(Infinity, Infinity, Infinity),
    y: new THREE.Vector3(Infinity, Infinity, Infinity),
    z: new THREE.Vector3(Infinity, Infinity, Infinity),
    xVertexIndex: null,
    yVertexIndex: null,
    zVertexIndex: null,
  }

  if (matrix) {
    position = position.clone().applyMatrix4(matrix)
  }

  const { count } = position

  for (let i = 0; i < count; i++) {
    _vector3.fromBufferAttribute(position, i)
    const { x, y, z } = _vector3

    if (filter) {
      const result = filter(x, y, z)
      if (!result) {
        continue
      }
    }

    if (x > maxs.x.x) {
      maxs.x.set(x, y, z)
      maxs.xVertexIndex = i
    }

    if (x > maxs.x.x) {
      maxs.x.set(x, y, z)
      maxs.xVertexIndex = i
    }

    if (x < mins.x.x) {
      mins.x.set(x, y, z)
      mins.xVertexIndex = i
    }

    if (y > maxs.y.y) {
      maxs.y.set(x, y, z)
      maxs.yVertexIndex = i
    }

    if (y < mins.y.y) {
      mins.y.set(x, y, z)
      mins.yVertexIndex = i
    }

    if (z > maxs.z.z) {
      maxs.z.set(x, y, z)
      maxs.zVertexIndex = i
    }

    if (z < mins.z.z) {
      mins.z.set(x, y, z)
      mins.zVertexIndex = i
    }
  }

  return { maxs, mins }
}

export function getAttributeFaceIndexsByVertexIndexs(index, vertexIndexs) {
  const faceIndexs = []
  const { array } = index
  const { length } = array

  for (let i = 0; i < length; i += 3) {
    if (
      vertexIndexs.includes(array[i]) &&
      vertexIndexs.includes(array[i + 1]) &&
      vertexIndexs.includes(array[i + 2])
    ) {
      faceIndexs.push(i / 3)
    }
  }
  return faceIndexs
}

export function getAttributeVertexIndexsByFaceIndexs(index, faceIndexs) {
  const vertexIndexs = []
  const { array } = index
  for (const faceIndex of faceIndexs) {
    const firstVertexIndex = faceIndex * 3
    vertexIndexs.push(
      array[firstVertexIndex],
      array[firstVertexIndex + 1],
      array[firstVertexIndex + 2],
    )
  }
  return vertexIndexs
}
