<template>
  <div class="log-content">
    <div class="log-search">
      <logSearch @getSearchList="getSearchList"></logSearch>
    </div>
    <div class="log-all">
      <span>{{$t('heypoint.operateLog.allHeypointNum')}}：{{ heypointAll.rechargePointTotal }}</span>
      <span>{{$t('heypoint.operateLog.allgitfHeypoint')}}：{{ heypointAll.giftPointTotal }}</span>
      <span>{{$t('heypoint.operateLog.allOverHeypoint')}}：{{ heypointAll.giftExpiredPointTotal }}</span>
    </div>
    <div class="log-table">
      <hg-table :header-data="headerData" :height="'auto'" :needSelect="false" :data="logList" :loading="tableLoading">
        <!-- 结算类型 -->
        <template #settlementType="scope">
          <span>{{ scope.row.settlementType | settlementType }}</span>
        </template>
        <!-- 客户编号 -->
        <template #orgSn="scope">
          <span>{{ scope.row.orgSn  }}</span>
        </template>
        <!-- 操作类型 -->
        <template #operationType="scope">
          <span>{{ scope.row.operationType | operatTypeFilter }}</span>
        </template>
        <!-- 来源 -->
        <template #rechargeSource="scope">
          <span>{{ scope.row.rechargeSource | rechargeSourceFilter }}</span>
        </template>
        <!-- 时间 -->
        <template #operationTime="scope">
          <span>{{ (scope.row.operationTime * 1000) | dateFormatInHtml }}</span>
        </template>
        <!-- 黑豆有效期 -->
        <template #expiredTime="scope">
          <span v-if="scope.row.expiredTime == 0">--</span>
          <span v-else>{{ (scope.row.expiredTime * 1000) | dateFormatInHtml }}</span>
        </template>
        <!-- 黑豆数  影响类型(1:充值黑豆;2:赠送黑豆;3:可用信用值,如果都影响就，返回1-2-3) -->
        <template #points='scope'>
          <p class='red-color'>{{ scope.row.points | capitalize}}</p>
        </template>
        <!-- 赠送黑豆余额 影响类型(1:充值黑豆;2:赠送黑豆;3:可用信用值,如果都影响就，返回1-2-3) -->
        <template #giftBalance='scope'>
          <p :class='{"red-color":scope.row.affectType == 2 }'>{{ scope.row.giftBalance | capitalize}}</p>
        </template>
        <!-- // 充值黑豆余额 影响类型(1:充值黑豆;2:赠送黑豆;3:可用信用值,如果都影响就，返回1-2-3) -->
        <template #rechargeBalance='scope'>
          <p :class='{"red-color":scope.row.affectType == 1 || scope.row.affectType == "1-2-3"}'>{{ scope.row.rechargeBalance |  capitalize}}</p>
        </template>
        <!-- 可用信用值 影响类型(1:充值黑豆;2:赠送黑豆;3:可用信用值,如果都影响就，返回1-2-3) -->
        <template #availableCredit='scope'>
          <p :class='{"red-color":scope.row.affectType == 3 || scope.row.affectType == "1-2-3"}' > {{ scope.row.availableCredit }} / {{scope.row.credit}} </p>
        </template>
      </hg-table>
    </div>
    <div class="depart-pagination">
      <pagination :total="page.total" :disabled="tableLoading" :initPageIndex="page.pageNo" :initPageSize="page.pageSize" @onSearch="search"></pagination>
    </div>
  </div>
</template>

<script>
import logSearch from './components/logSearch';
import hgTable from '@/components/HgTable';
import pagination from '@/components/Pagination';
import { searchOperationLog, operationLogTotal } from '@/api/heypoint';
import { settlementType, operatTypeFilter, capitalize, rechargeSourceFilter } from '@/filters/heypoint';
export default {
  name: 'log',
  components: { logSearch, hgTable, pagination },
  filters: {
    settlementType,
    operatTypeFilter,
    capitalize,
    rechargeSourceFilter
  },
  computed: {
    headerData() {
      return [
        {
          prop: 'serialNumber',
          width: '95%',
          className: 'orderNumber',
          oldLable: '序号',
          getLabel: () => {
            return this.$t('heypoint.operateLog.number');
          },
          fixed: 'left',
        },
        {
          prop: 'orgName',
          width: '120%',
          oldLable: '客户名',
          getLabel: () => {
            return this.$t('heypoint.customer.specialname');
          },
          fixed: 'left',
        },
        {
          prop: 'orgSn',
          width: '150%',
          oldLable: '客户编码',
          getLabel: () => {
            return this.$t('heypoint.customer.customerNo');
          },
          fixed: 'left',
        },
        {
          prop: 'no',
          minWidth: '180%',
          oldLable: '编号/订单号',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.number');
          },
          fixed: 'left',
        },
        {
          prop: 'settlementType',
          minWidth: '140%',
          oldLable: '结算类型',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.settlementType');
          },
        },
        {
          prop: 'createUserName',
          minWidth: '100%',
          oldLable: '经办人',
          getLabel: () => {
            return this.$t('heypoint.operateLog.user');
          },
        },
        {
          prop: 'operationTime',
          minWidth: '110%',
          oldLable: '时间',
          getLabel: () => {
            return this.$t('heypoint.operateLog.time');
          },
        },
        {
          prop: 'operationType',
          minWidth: '100%',
          oldLable: '操作类型',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.type');
          },
        },
        {
          prop: 'rechargeSource',
          minWidth: '100%',
          oldLable: '来源',
          getLabel: () => {
            return this.$t('heypoint.operateLog.source');
          },
        },
        {
          prop: 'points',
          minWidth: '100%',
          oldLable: '黑豆数',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.heypointTotal');
          },
        },
        {
          prop: 'expiredTime',
          minWidth: '110%',
          oldLable: '黑豆有效期',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.heypointExpired');
          },
        },
        {
          prop: 'giftBalance',
          width: '150px',
          oldLable: '赠送黑豆余额',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.giftBalance');
          },
        },
        {
          prop: 'rechargeBalance',
          width: '140px',
          oldLable: '充值黑豆余额',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.rechargeBalance');
          },
        },
        {
          prop: 'availableCredit',
          width: '150px',
          oldLable: '可用信用值',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.availableCredit');
          },
        },
        {
          prop: 'contractOrderNo',
          width: '200px',
          oldLable: 'CRM订单编号',
          getLabel: () => {
            return this.$t('heypoint.customer.crmOrderNo');
          },
        },
        {
          prop: 'contractNo',
          width: '170px',
          ldLable: 'CRM合同编号',
          getLabel: () => {
            return this.$t('heypoint.customer.crmContractNo');
          },
        },
        {
          prop: 'remark',
          width: '100px',
          oldLable: '备注',
          getLabel: () => {
            return this.$t('heypoint.customer.operate.remark');
          },
        },
      ];
    },
  },
  data() {
    return {
      logList: [],
      heypointAll: {}, //总数
      tableLoading: true, //页面loading
      page: {
        pageSize: 10,
        pageNo: 1,
        total: 10,
      },
      searchList: {
        //搜索
        createUserCode: null,
        endTime: 0,
        operationType: null,
        orgCode: null,
        settlementType: 0,
        startTime: 0,
        rechargeSource: null
      },
    };
  },
  mounted() {
    this.search();
  },
  methods: {
    // 搜索栏
    getSearchList(list) {
      let { createUserCode, endTime, operationType, orgCode, settlementType, startTime, orgName, rechargeSource } = list;
      this.searchList = {
        createUserCode: createUserCode,
        endTime: endTime,
        operationType: operationType,
        orgCode: orgCode,
        settlementType: settlementType,
        startTime: startTime,
        orgName: orgName,
        rechargeSource: rechargeSource
      };
      this.search();
    },
    search(type, searchData) {
      this.tableLoading = true;
      if (type == 'page') {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
      } else{
        this.page.pageNo = 1;
        this.page.pageSize = 10;
      }
      let data = { ...this.searchList };
      data.pageNum = this.page.pageNo;
      data.pageSize = this.page.pageSize;
      data.no = '';
      searchOperationLog(data).then((res) => {
        if (res.code === 200) {
          let logList = res.data.records;
          this.logList = this.handelTable(logList);
          this.page.pageNo = res.data.current;
          this.page.pageSize = res.data.size;
          this.page.total = res.data.total;
          this.tableLoading = false;
        }
      });
      operationLogTotal(data).then((res) => {
        if (res.code == 200) {
          this.heypointAll = res.data ? res.data : null;
        }
      });
    },
    // 处理table
    handelTable(list) {
      list.forEach((item, index) => {
        item.serialNumber = index + 1;
      });
      return list;
    },
  },
};
</script>

<style lang="scss" scoped>
.log-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  .log-all {
    height: 48px;
    display: flex;
    padding: 0 181px 0 24px;
    justify-content: space-between;
    background: $hg-main-black;
    align-items: center;
  }
  .log-table {
    flex: 1;
    .hg-table {
      height: 100%;
      /deep/.el-table {
        max-height: 100% !important;
      }
    }
    .table-high-light {
      float: left;
      width: auto;
      max-width: calc(100% - 41px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .depart-pagination {
    height: 58px;
  }
  .red-color {
    color: #dc5050;
  }
}
</style>
