<template>
  <div class="component-header">
    <audio ref="noticeAudio" :src="noticeAudio" preload="auto" muted />
    <div ref="test" class="logo finger">
      <!-- <router-link to="/"> -->
      <i class="iconfont icon-logo-heygears" />
      <!-- </router-link> -->
    </div>
    <div ref="headerMenuList" class="menu-list">
      <div ref="headerMenuItemWrap" class="menu-item-wrap"><p v-for="(item, index) in funcMenuLists" :key="index" :class="['menu-item', item.path === curCenterCode ? 'menu-active' : '']" @click="changeCenter(item.path)">{{ item.label }}</p></div>
    </div>
    <p v-if="showRightArrow" class="right-arrow" @click="goEnd"><img :src="doubleRightIcon" alt=""></p>
    <p v-if="showLeftArrow" class="left-arrow" @click="goStart"><img :src="doubleLeftIcon" alt=""></p>
    <div v-if="userInfo" class="right-part">
      <div class="nav-header">
        <p><img :src="knowledgeIcon" alt=""><span>知识库</span></p>
      </div>
      <div class="personal-center">
        <div class="personal-message">
          <!-- 消息通知 -->
          <div>
            <!-- <span v-if="Object.keys(userInfo.tenant).length" style="cursor: pointer; margin-right: 10px" @click="sendNoticeFunc(1)">发送客户消息</span>
            <span v-if="Object.keys(userInfo.tenant).length" style="cursor: pointer; margin-right: 10px" @click="sendNoticeFunc(2)">发送订单消息</span>
            <span v-if="Object.keys(userInfo.consumer).length" style="cursor: pointer; margin-right: 10px" @click="sendNoticeFunc(3)">发送黑豆消息</span> -->
            <DropDown ref="noticeDropdown" @visible-change="openNoticeBox">
              <span class="notice-box">
                <img :src="bellIcon" alt="">
                <p class="notice-count"><span v-if="totalCount">{{ totalCount >= 10 ? '···' : totalCount }}</span></p>
              </span>
              <div slot="dropdown" class="notice-dropdown-menu">
                <div class="dropdown-box">
                  <div class="dropdown-title">
                    <p class="title-text">系统消息</p>
                    <p class="count">{{ '共' + totalCount + '条新消息' }}</p>
                  </div>
                  <div ref="noticeBox" class="dropdown-content">
                    <p v-for="(item, index) in noticeList" :key="index" class="dropdown-content-item">
                      <!-- <span class="notice-text">{{ $i18n.locale === 'zh' ? item.content.text.zh : ($i18n.locale === 'en' ? item.content.text.en : '') }}</span> -->
                      <span class="notice-text">The order [1592406641428520962] was returned by the customer, please deal with it in time.</span>
                      <span class="check-btn" @click="setNoticeToReadFunc(item)">{{ '点击查看' }}</span>
                    </p>
                    <p v-if="!noticeList.length" class="dropdown-content-nodata">{{ '暂无未读消息' }}</p>
                  </div>
                  <div v-if="noticeList.length" class="dropdown-btn">
                    <span class="read-btn" @click="setAllNoticeToReadFunc">{{ noticeList.length === 1 ? '设为已读' : '全部已读' }}</span>
                  </div>
                </div>
              </div>
            </DropDown>
          </div>
          <!-- 消息通知结束 -->
          <span class="head">
            <img v-if="userInfo.avatarUrl" :src="userInfo.avatarUrl ? userInfo.avatarUrl : ''" alt="">
            <i v-else-if="userInfo.realName" class="header-text">
              {{ userInfo.realName.substr(0, 1).toUpperCase() }}
            </i>
            <!-- <img v-else :src="defaultHead" alt="" /> -->
          </span>
          <span class="user-name">{{ userInfo.realName }}</span>
        </div>
        <div class="more" @click="showCascaderFun">
          <i class="iconfont icon-more" />
          <CascaderPanel
            v-show="showCascader"
            ref="cascader"
            class="cascader"
            :options="options"
          />
        </div>
      </div>
    </div>
    <div v-else class="back-icon">
      <i class="iconfont icon-out" @click="exit" />
    </div>
  </div>
</template>
<script>
import { mapState, mapMutations } from "vuex"
import CascaderPanel from "./CascaderPanel";
import DropDown from "./DropDown";
import { redirectLogin } from "@/assets/script/token"
import { getUserInfo } from '@/api/login'
import { repeatMenusFilter } from "@/assets/script/utils"
import noticeAudio from './audio/notify.wav'
import { sendNotice, getUnreadNoticeList, setNoticeToRead, setAllNoticeToRead } from "@/api/common"
import { createSocket, sendWSPush } from "@/assets/script/websocket.js"
import knowledgeIcon from '@/assets/imgs/icon_knowledge.svg'
import bellIcon from '@/assets/imgs/icon_bell.svg'
import { currentAppPath } from '@/api/baseurl.config.js'
import doubleLeftIcon from '@/assets/imgs/doubleleft.svg'
import doubleRightIcon from '@/assets/imgs/doubleright.svg'

export default {
  name: "Header",
  components: {
    CascaderPanel,
    DropDown
  },
  props: {
  },
  data() {
    return {
      noticeAudio,
      knowledgeIcon,
      bellIcon,
      doubleLeftIcon,
      doubleRightIcon,
      funcMenuLists: [],
      options: [
        // {
        //   label: 'header.language',
        //   icon: 'language',
        //   key: 'language',
        //   menuCode: 111111,
        //   children: [
        //     {
        //       label: 'header.chinese',
        //       fun: () => {
        //         window.localStorage.setItem('lang','zh')
        //         location.reload();
        //       },
        //     },
        //     {
        //       label: 'header.english',
        //       fun: () => {
        //         window.localStorage.setItem('lang','en')
        //         location.reload();
        //       },
        //     }
        //   ]
        // },
        {
          label: 'header.out',
          icon: 'out',
          key: 'out',
          menuCode: 111111,
          fun: () => {
            redirectLogin()
          }
        }
      ],
      userMenus: [
        {
          label: 'header.center',
          icon: 'user',
          key: 'center',
          fun: () => {
            this.hrefRedirection('personal')
          },
          menuCode: 70003
        },
        {
          label: 'header.framework',
          icon: 'framework',
          key: 'framework',
          menuCode: 70005,
          fun: () => {
            this.hrefRedirection('organization')
          }
        }
      ],
      showCascader: false,
      curCenterCode: '', // 当前所在中心的code
      redirection: false,
      // 消息通知
      noticeList: [],
      // 分页
      page: 1, // 当前页
      pageSize: 10, // 每页总条数
      totalCount: 0, // 总条数
      showRightArrow: false,
      showLeftArrow: false
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    })
  },
  created() {
    this.getUserinfoFunc().then(() => {
      this.getfuncMenuList()
      this.getUnreadNoticeFunc()
    })
  },
  mounted() {
    window.addEventListener('click', () => {
      this.showCascader = false
    })
  },
  methods: {
    ...mapMutations({
      updateUserInfo: 'user/updateUserInfo'
    }),

    // 获取并处理权限菜单
    hrefRedirection(path) {
      let whiteRouter = ['customer', 'customOrg', 'setProcess']; // 客户管理模块的相关页面
      let isCustom = whiteRouter.filter(item => window.location.hash.indexOf(item) != -1);
      let folderPath = process.env.NODE_ENV == 'development' || isCustom.length ? null : (window.location.origin.indexOf('user') != -1 ? 'lab_user_uc' : 'lab_tenant_uc')
      if (folderPath) {
        window.location.href = this.$route.query.redirection ? window.location.origin + '/'+ folderPath + '/#/' + path + '?redirection=' + this.$route.query.redirection : window.location.origin + '/'+ folderPath + '/#/' + path + '?redirection=' + encodeURIComponent(window.location.pathname + window.location.hash);
      } else {
        let query = '';
        this.$route.query.redirection ? query = this.$route.query.redirection : query = encodeURIComponent(window.location.pathname + window.location.hash);
        this.$router.push({ 'path': '/' + path, query: { 'redirection': query }});
      }
    },
    getfuncMenuList() {
      var data = this.userInfo;
      let functions = [];
      data.solutions[0].roles.forEach(role => {
        functions = functions.concat(role.functions);
      });
      let menus = repeatMenusFilter(functions, 'menus');
      functions = repeatMenusFilter(data.solutions[0].roles, 'functions');
      if (data.solutions && data.solutions[0] && data.solutions[0].roles && data.solutions[0].roles.length) {
        // 判断是否拥有组织架构和个人中心的权限，并且使得两者的显示顺序是个人中心 --> 组织架构
        if (menus.length) {
          let org = menus.filter(value => value.name === '组织架构');
          let person = menus.filter(value => value.name === '个人中心');

          if (org.length && person.length) {
            this.options = this.userMenus.concat(this.options);
          } else {
            org.length ? this.options.unshift(this.userMenus[1]) : '';
            person.length ? this.options.unshift(this.userMenus[0]) : '';
          }
        }

        const curRouter = window.location.href.split('#')[1]
        this.funcMenuLists = functions.filter((item) => {
          if (item.site === 0) {
            item.label = this.$i18n.locale === 'zh' ? item.name : item.nameEn
            item.value = item.functionCode
            if (curRouter.indexOf(item.path.split('#')[1]) !== -1) {
              this.curCenterCode = item.path
            } else {
              this.curCenterCode = currentAppPath.indexOf('lab_tenant_uc') !== -1 ? '/lab_tenant_uc/#/customer' : currentAppPath
            }
            window.localStorage.setItem('currentMenuPath', this.curCenterCode)
            return item
          }
        })
        this.$nextTick(() => {
          if (this.$refs.headerMenuList.offsetWidth && this.$refs.headerMenuItemWrap.offsetWidth) {
            this.$refs.headerMenuItemWrap.offsetWidth > this.$refs.headerMenuList.offsetWidth ? this.showRightArrow = true : ''
          } else {
            this.showRightArrow = false
            this.showLeftArrow = false
          }
        })
        // this.funcMenuLists = this.funcMenuLists.filter(value => value.site === 0); // 过滤掉用户中心的显示

        // 如果点击了客户管理进入用户中心，则把当前默认选中的路径改为客户管理的路径（用户中心租户端才需要处理）
        // if (window.localStorage.getItem('currentMenuPath')) {
        //   this.curCenterCode = window.localStorage.getItem('currentMenuPath')
        // } else {
        //   this.curCenterCode = this.funcMenuLists[0].path
        //   window.localStorage.setItem('currentMenuPath', this.curCenterCode)
        // }
        // this.curCenterCode = currentAppPath.indexOf('lab_tenant_uc') != -1 ? '/lab_tenant_uc/#/customer' : currentAppPath;
      }
    },
    // 跳转中心
    changeCenter(path) {
      window.localStorage.setItem('currentMenuPath', path)
      if (path.indexOf('#') !== -1) {
        window.location.href = window.location.origin + path
      } else {
        window.location.href = window.location.origin + path + '/#/'
      }
    },
    showCascaderFun(e) {
      e.stopPropagation();
      this.showCascader = !this.showCascader;
    },
    // 获取个人信息（头像）
    getUserinfoFunc() {
      return new Promise((resolve) => {
        getUserInfo().then((res) => {
          if (res.code == 200) {
            this.userInfo.avatarUrl = res.data.headImgurl ? res.data.headImgurl : '';
            this.updateUserInfo(this.userInfo);
            resolve();
          }
        })
      });
    },
    exit() {
      // 退出方法
      redirectLogin()
      // 退出登录时卸载监听事件
      // window.removeEventListener('onmessageWS', this.getsocketData)
    },
    /** 消息通知 */
    // 发送消息
    sendNoticeFunc(num) {
      let data = {}
      if (num === 1) {
        data = {
          content: "{\"text\": {\"zh\":\"有新的客户注册，请及时查看\", \"en\":\"\"}, \"orderNo\": \"\", \"type\":6001, \"url\":\"/lab_tenant_uc/#/customer?orgCode=100778\"}", // handleType=1: 点击查看； handleType=2: 查看并处理；
          domain: "user-basic-service",
          type: 1,
          userIds: [100131]
        }
      } else if (num === 2) {
        data = {
          "content": "{\"orderNo\":\"202206271353\",\"text\":{\"en\":\"\",\"zh\":\"订单202206271353客户催单，请及时查看并处理\"},\"type\":6,\"url\":\"/lab_tenant_design/#/order?orderNo='202206271353'\"}",
          "domain": "design-basic-service",
          "type": 1,
          "userIds": [100131]
        }
      } else if (num === 3) {
        data = {
          content: "{\"text\": {\"zh\":\"收到一笔新的赠送黑豆，请及时查看\", \"en\":\"Added free credit. Please check.\"}, \"orderNo\": \"\", \"type\":10, \"url\":\"/lab_user_design/#/heyPoints\"}", // handleType=1: 点击查看； handleType=2: 查看并处理；
          domain: "design-settlement",
          type: 1,
          userIds: [100130]
        }
      }
      sendNotice(data).then((res) => {

      })
    },
    // 播放消息通知提示音
    playAudio() {
      console.log('开始播放消息通知提示音')
      const audioDom = this.$refs['noticeAudio']
      if (audioDom) {
        audioDom.currentTime = 0 // 从头开始播放提示音
        audioDom.play()
        setTimeout(() => {
          audioDom.pause()
        }, 3000)
      }
    },
    // 接收消息
    getsocketData(e) { // 创建接收消息函数
      const data = e && e.detail.data
      // 拿到任何消息都说明当前连接是正常的
      if (data === 'pong') {
        // 心跳检测的回复
        return
      }
      // 处理收到的消息
      if (data) {
        const msg = JSON.parse(data)
        if (msg.content) {
          this.playAudio()
          msg.content = JSON.parse(msg.content)
          const filterArr = this.noticeList.filter(item => item.msgId === msg.msgId)
          if (!filterArr.length) { // 如果消息列表不存在当前消息，则将当前消息插入消息列表
            this.noticeList.unshift(msg)
            this.totalCount++
          }
          console.log('接收到消息', msg)
        }
        this.ackMsg(data)
      }
    },
    // 消息确认
    ackMsg(msg) {
      if (msg.includes('connection is established')) {
        return
      }
      var data = JSON.parse(msg)
      var backData = {}
      backData.userId = data.userId
      backData.domain = 'test'
      backData.msgId = data.msgId
      backData.type = 901
      backData.content = 'ack'
      sendWSPush(backData)
    },
    // 点击打开消息通知列表
    openNoticeBox(bool) {
      if (bool) {
        this.getUnreadNoticeFunc(1)
      }
    },
    // 获取当前账号的所有未读通知消息
    getUnreadNoticeFunc(num) {
      if (num === 1) { // 点击打开通知下拉列表时重新获取数据
        this.page = 1
        this.noticeList = []
      }
      getUnreadNoticeList({
        userId: this.userInfo.userCode,
        status: 0,
        page: this.page,
        pageSize: this.pageSize
      }).then((res) => {
        if (res.code === 200 && res.data) {
          this.totalCount = res.data.count
          if (res.data.list.length) {
            res.data.list.forEach((item, index) => {
              if (item.content) {
                item.content = JSON.parse(item.content)
                // const filterArr = this.noticeList.filter(msg => msg.msgId === item.msgId)
                // if (!filterArr.length) { // 如果消息列表不存在当前消息，则将当前消息插入消息列表
                //   this.noticeList.push(item)
                // }
                // console.log('接收到消息', item)
              }
              if (index === res.data.list.length - 1) {
                this.noticeList = this.noticeList.concat(res.data.list)
                // console.log('555', this.noticeList)
              }
            })
          }
        }
      })
    },
    // 滚动加载
    lazyLoading(e) {
      const scrollTop = e.target.scrollTop // 滚动条滚动时，距离顶部的距离
      const windowHeight = e.target.clientHeight // 可视区的高度
      const scrollHeight = e.target.scrollHeight // 滚动条的总高度
      // 滚动条到底部
      if (scrollTop !== 0 && scrollTop + windowHeight === scrollHeight) {
        this.page++
        if (this.page > this.totalCount) return
        this.getUnreadNoticeFunc()
      }
    },
    // 设置为已读消息
    setNoticeToReadFunc(notice) {
      setNoticeToRead(notice.msgId).then((res) => {
        this.$refs.noticeDropdown.handleClick()
        this.totalCount ? this.totalCount-- : ''
        window.location.href = window.location.origin + notice.content.url
        // location.reload()
      })
    },
    // 设置为已读消息
    setAllNoticeToReadFunc() {
      setAllNoticeToRead(this.userInfo.userCode).then((res) => {
        if (res.code === 200) {
          this.getUnreadNoticeFunc(1)
        }
      })
    },
    goEnd() {
      const menuListDom = this.$refs.headerMenuList
      const menuItemWrapDom = this.$refs.headerMenuItemWrap
      menuListDom.scrollLeft = menuItemWrapDom.offsetWidth
      this.showRightArrow = false
      this.showLeftArrow = true
    },
    goStart() {
      const menuListDom = this.$refs.headerMenuList
      const menuItemWrapDom = this.$refs.headerMenuItemWrap
      menuListDom.scrollLeft = -(menuItemWrapDom.offsetWidth)
      this.showRightArrow = true
      this.showLeftArrow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.component-header {
  height: $hg-height-60;
  background-color: $hg-background-color;
  border-bottom-left-radius: 30px;
  border-bottom: 1px solid #38393d;
  @include box-shadow();
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  user-select: none;
  .logo {
    margin: 0 40px 0 84px;
    width: 120px;
    height: 100%;
    box-sizing: border-box;
    flex-basis: 120px;
    line-height: $hg-height-60;
    color: $hg-primary-fontcolor;
    cursor: pointer;
    i {
      font-style: normal;
      font-size: 40px;
    }
  }
  .menu-list {
    width: 60%;
    overflow: hidden;
    white-space: nowrap;
    overflow-x: scroll;
    /* 隐藏滚动条 */
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
    .menu-item-wrap {
      display: inline-block;
      .menu-item {
        display: inline-block;
        width: auto;
        height: 60px;
        line-height: 60px;
        margin-right: 12px;
        color: #E4E8F7;
        text-align: center;
        padding: 0 16px;
        box-sizing: border-box;
        cursor: pointer;
        &.menu-active {
          border-bottom: 3px solid #3760EA;
          font-weight: bold;
        }
      }
    }
  }
  .right-arrow, .left-arrow {
    color: #E4E8F7;
    margin: 0 24px;
  }
  .header-text{
    background-color: #3054cc !important;
    width: 32px;
    height: 32px;
    line-height: 32px;
    display: block;
    text-align: center;
    color: #e4e8f7;
    font-weight: bold;
    font-style: normal;
  }
  .nav-header p {
    min-width: 88px;
    height: 32px;
    line-height: 32px;
    background: linear-gradient(270deg, #3054CC 0%, #5477EB 100%);
    border-radius: 24px;
    font-size: 12px;
    color: #E4E8F7;
    padding: 0 12px;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    img {
      display: inline-block;
      width: 24px;
      height: 24px;
      background: transparent;
      vertical-align: middle;
      margin-right: 4px;
    }
  }
  .right-part {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .personal-center {
    display: inline-flex;
    justify-content: space-between;
    padding: 0 20px 0 20px;
    box-sizing: border-box;
    flex-wrap: nowrap;
    height: 100%;

    .message {
      padding-right: 8px;
      color: $hg-primary-fontcolor;
      height: 100%;
      line-height: $hg-height-60;
      cursor: pointer;

      i {
        font-size: 18px;
        position: relative;
      }

      &.active {
        i::after {
          content: "";
          position: absolute;
          right: 0px;
          top: 0px;
          height: 8px;
          width: 8px;
          background-color: $hg-error-color;
          border-radius: 50%;
        }
      }
    }
    .personal-message {
      color: $hg-secondary-fontcolor;
      font-size: $hg-normal-fontsize;
      white-space: nowrap;
      position: relative;
      display: inline-flex;
      align-items: center;
      span {
        display: inline-block;
      }
      // 消息通知ui
      .notice-box {
        display: flex;
        position: relative;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        text-align: center;
        margin: 0 16px;
        cursor: pointer;
        .img {
          width: 24px;
          height: 24px;
        }
        .notice-count {
          position: absolute;
          display: inline-block;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          right: -2px;
          top: -4px;
          background: #1D1D1F;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            box-sizing: border-box;
            background: #FF5A5A;
            color: #FFFFFF;
            font-size: 12px;
            line-height: 15px;
            text-align: center;
          }
        }
      }
      .notice-dropdown-menu {
        .dropdown-box {
          display: block;
          width: 525px;
          background: #262629;
          .dropdown-title {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #38393D;
            padding: 0 24px;
            .title-text {
              height: 64px;
              line-height: 64px;
              color: #3760EA;
              font-size: 16px;
              font-weight: 700;
            }
            .count {
              margin-left: auto;
              font-size: 12px;
              color: #FFB22C;
            }
          }
          .dropdown-content {
            height: 200px;
            overflow-y: scroll;
            &::-webkit-scrollbar, div::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            &::-webkit-scrollbar-track-piece, div::-webkit-scrollbar-track-piece{
              background-color: rgba(0, 0, 0, 0.2);
              -webkit-border-radius: 6px;
            }
            &::-webkit-scrollbar-thumb:vertical, div::-webkit-scrollbar-thumb:vertical{
              height: 6px;
              background-color:rgb(56, 57, 61);
              -webkit-border-radius: 6px;
            }
            &::-webkit-scrollbar-thumb:horizontal, div::-webkit-scrollbar-thumb:horizontal{
              width: 6px;
              background-color:rgb(56, 57, 61);
              -webkit-border-radius: 6px;
            }
            .dropdown-content-item {
              display: flex;
              align-items: center;
              color: #E4E8F7;
              padding: 10px 24px;
              box-sizing: border-box;
              border-top: 1px dashed #38393D;
              &:nth-of-type(1) {
                border-top: none;
              }
              .notice-text {
                flex: 1;
                display: block;
                font-size: 14px;
                line-height: 20px;
                white-space: normal;
                margin-right: 12px;
              }
              .check-btn {
                // flex: 1;
                white-space: nowrap;
                margin-left: auto;
                color: #3760EA;
                text-decoration-line: underline;
                cursor: pointer;
              }
              &:hover {
                background: #2D2F33;
              }
            }
          }
          .dropdown-content-nodata {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #83868F;
          }
          .dropdown-btn {
            display: flex;
            align-items: center;
            height: 80px;
            padding: 0 24px;
            border-top: 1px solid #38393D;
            .read-btn {
              margin-left: auto;
              width: 80px;
              height: 32px;
              line-height: 32px;
              text-align: center;
              background: #3760EA;
              border-radius: 2px;
              font-size: 12px;
              color: #E4E8F7;
              cursor: pointer
            }
          }
        }
      }
      // 结束
      .head {
        height: 32px;
        width: 32px;
        border-radius: 50%;
        box-sizing: border-box;
        margin: 0 10px;
        overflow: hidden;
        img {
          height: 100%;
          width: 100%;
        }
      }
      .user-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: auto;
      }
    }
    .more {
      color: $hg-primary-fontcolor;
      height: 100%;
      line-height: $hg-height-60;
      padding: 0 20px;
      cursor: pointer;
      // z-index: 1000;
    }
  }
  .more {
    color: $hg-primary-fontcolor;
    height: 100%;
    line-height: $hg-height-60;
    padding: 0 20px;
    cursor: pointer;
    position: relative;
    .cascader {
      position: absolute;
      z-index: 1;
      right: 0px;
      transform: translateY(-8px);
    }

    .empty-header {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-right: 40px;
      .iconfont {
        font-size: $hg-medium-fontsize;
        color: $hg-secondary-fontcolor;
        &:hover {
          color: $hg-primary-fontcolor;
        }
      }
    }
  }
  .back-icon {
    width: 60px;
    padding-right: 24px;
    color: $hg-secondary-fontcolor;
    text-align: center;
    font-size: $hg-medium-fontsize;
    :active,
    :hover {
      color: $hg-primary-fontcolor;
    }
    .iconfont {
      cursor: pointer;
    }
  }
}

</style>

