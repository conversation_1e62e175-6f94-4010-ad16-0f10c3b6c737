// 覆盖elementui的UI
// checkbox的覆盖样式
.el-checkbox__inner {
  border: 1px solid $hg-border;
  background-color: $hg-main-black;
  width: 16px;
  height: 16px;

}
.el-checkbox__input.is-checked {
  .el-checkbox__inner {
    background-color: $hg-main-blue;
  }
  .el-checkbox__inner::after {
    border-color: $hg-main-black;
    top: 2px;
    left: 5px;
  }
}

// el-radio的覆盖样式
.el-radio {
  &.is-checked {
    .el-radio__label {
      color: $hg-label;
    }
  }
  &.is-disabled {
    .el-radio__input {
      + span.el-radio__label {
        color: #606266;
      }
    }
  }
  .el-radio__input {
    &.is-checked {
      .el-radio__inner {
        border-color: $hg-main-blue;
        background: $hg-main-black;
      }
      .el-radio__inner::after {
        width: 6px;
        height: 6px;
        background-color: $hg-main-blue;
      }
    }
    &.is-disabled {
      .el-radio__inner {
        background: $hg-border;
      }
    }
    .el-radio__inner {
      background-color: $hg-main-black;
      border-color: $hg-border;
    }
  }
}

.el-radio-group {
  .el-radio-button {
    .el-radio-button__inner {
      background: $hg-main-black;
      border-color: $hg-border;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: $hg-main-blue;
      color: $hg-label;
    }
  }
}


//----- message 消息通知 样式覆盖 start ----
// box部分
.el-message {
  top: 86px !important;
  border-radius: 4px;
  background: $hg-hover;
  box-shadow: 0px 12px 32px 0px $hg-background,0px 8px 24px 0px $hg-background,0px 0px 16px 0px $hg-background;
  border: none;
  padding: 10px 24px;

  .el-message__content {
    color: $hg-secondary-text;
    font-weight: regular;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0px;
    text-align: left;

  }
}
// 成功的icon颜色
.el-message.el-message--success {
  .el-icon-success {
    color: $hg-success;
  }
}
// 警告的icon颜色
.el-message.el-message--warning {
  .el-icon-warning {
    color: $hg-warning;
  }
}
// 错误的icon颜色
.el-message.el-message--error {
  .el-icon-error {
    color: $hg-error;
  }
}

//----- message 消息通知 样式覆盖 end ----


//----- tooltips is-dark 样式覆盖 start ----
.el-tooltip__popper.is-dark {
  border-radius: 4px;
  background: $hg-hover;
  box-shadow: 0px 12px 32px 0px $hg-background,0px 8px 24px 0px $hg-background,0px 0px 16px 0px $hg-background;
  padding: 8px 12px;
  color: $hg-label;
  font-family:"webfont" !important;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  text-align: left;
  max-width: 600px;
}

.el-tooltip__popper.is-dark[x-placement^=top]{
  .popper__arrow {
    border-top-color: $hg-hover;
    &::after {
      border-top-color: $hg-hover;
    }
  }
}
.el-tooltip__popper.is-dark[x-placement^=bottom]{
  .popper__arrow {
    border-bottom-color: $hg-hover;
    &::after {
      border-bottom-color: $hg-hover;
    }
  }
}
.el-tooltip__popper.is-dark[x-placement^=left]{
  .popper__arrow {
    border-left-color: $hg-hover;
    &::after {
      border-left-color: $hg-hover;
    }
  }
}
.el-tooltip__popper.is-dark[x-placement^=right]{
  .popper__arrow {
    border-right-color: $hg-hover;
    &::after {
      border-right-color: $hg-hover;
    }
  }
}
//----- tooltips is-dark 样式覆盖 end ----


//----- el-select 样式覆盖 start ----
.el-select>.el-input {
  .el-input__inner{
    background-color: transparent;
    border-radius: 4px;
    border: 1px solid $hg-border;
    color: $hg-label;

    &::-webkit-input-placeholder{ // placeholder 颜色重置
      color: $hg-secondary-text;
    }
    &::-moz-placeholder {
      color: $hg-secondary-text;
    }
    &:-moz-placeholder {
      color: $hg-secondary-text;
    }
    &::-ms-input-placeholder{
      color: $hg-secondary-text;
    }

    &:focus {
      border: 1px solid $hg-label;
    }
  }
  .el-select__caret {
    color: $hg-secondary-text;
  }
}

.el-select>.el-input.is-focus { // 聚焦时边框颜色
  .el-input__inner {
    border-color: $hg-border;
  }
}

.el-select-dropdown.el-popper { 
  border-radius: 4px;            
  background: $hg-main-black;
  box-shadow: 0px 12px 32px 0px $hg-background,0px 8px 24px 0px $hg-background,0px 0px 16px 0px $hg-background;
  border: none;

  .el-scrollbar>.el-select-dropdown__wrap {
    max-height: 176px;
  }

  .el-scrollbar>.el-select-dropdown__wrap>.el-scrollbar__view{
    .el-select-dropdown__item.hover { // 下拉列表hover时样式
      background-color: $hg-hover;
      color: $hg-secondary-text;
    }

    .el-select-dropdown__item.selected {  // 下拉列表被选中时样式
      color: $hg-label;
      background-color:$hg-main-black;
    }
    .el-select-dropdown__item.is-disabled { // 下拉列表有禁选项时
      color: $hg-disable;
      &:hover {
        background: $hg-main-black;
      }
    }
  }  
}

.el-select-dropdown.el-popper[x-placement^="bottom"]{ // 下拉列表 箭头在bottom的样式
  .popper__arrow {
    border-bottom-color: $hg-main-black;
    &::after {
      border-bottom-color: $hg-main-black;
    }
  }
}

.el-select-dropdown.el-popper[x-placement^="top"]{  // 下拉列表 箭头在top的样式
  .popper__arrow {
    border-top-color: $hg-main-black;
    &::after {
      border-top-color: $hg-main-black;
    }
  }
}

// 多选时tags样式
.el-select>.el-select__tags>span {
  .el-tag {
    border-radius: 2px;
    background: $hg-main-black;
    border: none;
    margin-top: 0;
    margin-bottom: 0;
    padding: 6px 8px;
    height: 32px;

    .el-select__tags-text {
      color: $hg-label;
      font-family:"webfont" !important;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
      
    }

    .el-tag__close.el-icon-close {
      color: $hg-secondary-text;
      background-color: $hg-main-black;
      font-size: 20px;
      height: 20px;
      width: 20px;
      border-radius: 0;
    }
  }

}

// 被禁用的样式
.el-select>.el-input.is-disabled {
  .el-input__inner {
    border-radius: 4px;
    background: $hg-disable;
    border: 1px solid $hg-border;
    &:hover {
      border: 1px solid $hg-border;
    }
  }
}

//----- el-select 样式覆盖 end ----


//----- el-input el-textarea 输入框 start ----
.el-input, .el-textarea {
  .el-input__inner,
  .el-textarea__inner {
    border-radius: 2px;
    border: 1px solid $hg-border;
    background-color: transparent;
    color: $hg-label;

    &::-webkit-input-placeholder{ // placeholder 颜色重置
      color: $hg-secondary-text;
    }
    &::-moz-placeholder {
      color: $hg-secondary-text;
    }
    &:-moz-placeholder {
      color: $hg-secondary-text;
    }
    &::-ms-input-placeholder{
      color: $hg-secondary-text;
    }

    &:focus {
      border-color: $hg-label;

      &::-webkit-input-placeholder{ // placeholder 颜色重置
        color: $hg-label;
      }
      &::-moz-placeholder {
        color: $hg-label;
      }
      &:-moz-placeholder {
        color: $hg-label;
      }
      &::-ms-input-placeholder{
        color: $hg-label;
      }
    }
  }
  .el-input__count {
    background: $hg-main-black;
    color: $hg-secondary-text;
  }
}

.el-textarea>.el-textarea__inner {
  border-radius: 4px;
  padding: 12px 24px;
}

.el-input.el-input--prefix {
  .el-input__prefix {
    margin-left: 24px;
    left: 0;
  }
}

//----- el-input el-textarea 输入框 end ----


//----- el-date-editor 样式覆盖 start ----
.el-date-editor.el-range-editor {
  position: relative;
  padding: 10px 24px;
  border: 1px solid $hg-border;
  background-color: transparent;

  &:hover {
    border-color: $hg-label;
  }

  .el-input__icon.el-range__icon {
    display: none;
  }
  .el-input__icon.el-range__close-icon {
    position: absolute;
    line-height: 20px;
    right: 40px;
  }

  .el-range-input {
    text-align: left;
    background-color: transparent;
    color: $hg-label;

    &::-webkit-input-placeholder{ // placeholder 颜色重置
      color: $hg-secondary-text;
    }
    &::-moz-placeholder {
      color: $hg-secondary-text;
    }
    &:-moz-placeholder {
      color: $hg-secondary-text;
    }
    &::-ms-input-placeholder{
      color: $hg-secondary-text;
    }

    &:focus {
      border-color: $hg-label;
  
      &::-webkit-input-placeholder{ // placeholder 颜色重置
        color: $hg-label;
      }
      &::-moz-placeholder {
        color: $hg-label;
      }
      &:-moz-placeholder {
        color: $hg-label;
      }
      &::-ms-input-placeholder{
        color: $hg-label;
      }
    }
  }

  .iconfont-lab {
    margin: 0 16px;
  }

}
.el-picker-panel {
  background: $hg-main-black;
  box-shadow: 0px 0px 16px rgba(18, 19, 20, 0.32), 0px 8px 24px rgba(18, 19, 20, 0.2), 0px 12px 32px rgba(18, 19, 20, 0.12);
  border-radius: 4px;
  border: none;
  .el-picker-panel__body-wrapper {
    .el-picker-panel__sidebar {
      background: $hg-main-black;
      border-color: $hg-border;
      .el-picker-panel__shortcut {
        color: $hg-label;
        &:hover {
          color: $hg-main-blue;
        }
      }
    }
    .el-picker-panel__body {
      .el-date-range-picker__content.is-left {
        border-color: $hg-border;
      }
      .el-date-picker__header {
        border-bottom-color: $hg-border;
        .el-date-picker__header-label {
          color: $hg-label;
        }
        .el-picker-panel__icon-btn {
          color: #83868f;
          &:hover {
            color: $hg-main-blue;
          }
        }
      }
      .el-picker-panel__content {
        .el-date-range-picker__header {
          color: $hg-secondary-text;
          .el-picker-panel__icon-btn {
            color: $hg-secondary-text;
            &:hover {
              color: $hg-main-blue;
            }
          }
        }
        .el-date-table {
          color: $hg-label;
          font-size: 14px;
          th {
            color: $hg-label;
            border-bottom: 1px dashed $hg-border;
          }
          td.next-month, td.prev-month {
            color: $hg-secondary-text;
          }
          td.disabled {
            div {
              background: $hg-disable;
              
            }
          }
        }
        .el-month-table {
          td.in-range div {
            background: $hg-hover;
          }
        }
      }
    }
  }
  &.el-popper[x-placement^='bottom'] .popper__arrow {
    border-bottom-color: $hg-main-black;
    &::after {
      border-bottom-color: $hg-main-black;
    }
  }
  &.el-popper[x-placement^='top'] .popper__arrow {
    border-top-color: $hg-main-black;
    &::after {
      border-top-color: $hg-main-black;
    }
  }
}
//----- el-date-editor 样式覆盖 end ----

//----- el-message-box 样式覆盖 start ----
.el-message-box__wrapper {
  .el-message-box {
    padding: 0;
    width: 560px;
    border: none;
    border-radius: 4px;
    background: $hg-main-black;
  }
}

.el-message-box__wrapper>.el-message-box>.el-message-box__header {
  height: 60px;
  padding: 18px 24px;
  line-height: 24px;
  border-bottom: none;

  .el-message-box__title {
    color: $hg-label;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
  }

  .el-message-box__headerbtn {
    top: 18px;
    right: 24px;
  }

}

.el-message-box__wrapper>.el-message-box>.el-message-box__content {
  padding: 24px;
  border-top: 1px solid $hg-border;
  color: $hg-label;
  line-height: 20px;
}

.el-message-box__wrapper>.el-message-box>.el-message-box__btns {
  padding: 0 24px 24px;

  &>button {
    margin-left: 24px;
    min-width: 104px;
    height: 40px;
    color: $hg-label;
    font-size: 14px;
  }

  &>button.el-button--default {
    border-radius: 4px;
    border: 1px solid $hg-border;
    background-color: transparent;

    &:hover {
      color: $hg-btn-second-text;
      border-color: $hg-btn-second-hover-border;
      background-color: $hg-btn-second;
    }
  
    &:active {
      color: $hg-btn-second-active-text;
      border-color: $hg-btn-second-active-border;
      background-color: $hg-btn-second;
    }
  }

  &>button.el-button--default.el-button--primary {
    border-radius: 4px;
    background: $hg-main-blue;

    &:hover {
      color: $hg-btn-primary-hover-text;
      background-color: $hg-btn-primary-hover;
    }
  
    &:active {
      color: $hg-btn-primary-active-text;
      background-color: $hg-btn-primary-active;
    }

  }
}

//----- el-message-box 样式覆盖 end ----


//----- el-input-number 样式覆盖 start ----
.el-input-number {
  .el-input-number__decrease, .el-input-number__increase {
    background: $hg-main-black;
    color: $hg-secondary-text;
    border-color: $hg-border;
  }
  .el-input-number__increase:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled), .el-input-number__decrease:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled) {
    border-color: $hg-label;
  }
}
.el-input-number>.el-input {
  &.is-disabled {
    .el-input__inner {
      background-color: $hg-disable;
      border-color: $hg-border;
    }
  }
  .el-input__inner {
    text-align: left;
    &::-webkit-input-placeholder{ // placeholder 颜色重置
      color: $hg-secondary-text;
    }
    &::-moz-placeholder {
      color: $hg-secondary-text;
    }
    &:-moz-placeholder {
      color: $hg-secondary-text;
    }
    &::-ms-input-placeholder{
      color: $hg-secondary-text;
    }

    &:focus {
      border-color: $hg-label;

      &::-webkit-input-placeholder{ // placeholder 颜色重置
        color: $hg-label;
      }
      &::-moz-placeholder {
        color: $hg-label;
      }
      &:-moz-placeholder {
        color: $hg-label;
      }
      &::-ms-input-placeholder{
        color: $hg-label;
      }
    }
  }
}

.el-input-number.is-controls-right>.el-input-number__decrease {
  height: calc(50% - 1px);
  width: 39px;
  border-radius: 0 0 2px 0;
  background: transparent;
  border-left-color: $hg-border;

  .el-icon-arrow-down {
    position: absolute;
    left: 50%;
    top: 9px;
    transform: translate(-50%);
    display: inline-block;
    width: 0;
    height: 0;
    border: 5px solid;
    border-color: $hg-secondary-text transparent transparent;

    &::before {
      content: '';
    }
  }

  &:hover {
    background: $hg-disable;

    .el-icon-arrow-down {
      border-color: $hg-label transparent transparent;
    }
  }

}
.el-input-number.is-controls-right>.el-input-number__increase {
  height: 50%;
  width: 39px;
  border-radius: 0 2px 0 0;
  background: transparent;
  border-left-color: $hg-border;
  border-bottom-color: $hg-border;

  .el-icon-arrow-up {
    position: absolute;
    left: 50%;
    bottom: 7px;
    transform: translate(-50%);
    display: inline-block;
    width: 0;
    height: 0;
    border: 5px solid;
    border-color: transparent transparent $hg-secondary-text;

    &::before {
      content: '';
    }
  }

  &:hover {
    background: $hg-disable;
    border-bottom-color: $hg-disable;

    .el-icon-arrow-up {
      border-color: transparent transparent $hg-label;
    }
  }

}

.el-input-number.is-controls-right>.el-input-number__decrease.is-disabled,
.el-input-number.is-controls-right>.el-input-number__increase.is-disabled {
  background: $hg-disable;
  border-bottom-color: $hg-disable;

  i {
    color: $hg-disable;
  }
}

//----- el-input-number 样式覆盖 end ----

//----- el-dialog 样式覆盖 start ----
.el-dialog__wrapper>.el-dialog{
  border-radius: 4px;
  background: $hg-main-black;
}

.el-dialog__header {
  padding: 18px 24px;
  color: $hg-label;
  font-weight: bold;
  font-size: 16px;
  line-height: 24px;
  border-bottom: 1px solid $hg-border;
  .el-dialog__title {
    color: $hg-label;
    font-size: 16px;
  }
}
.el-dialog__headerbtn {
  right: 24px;
  color: #C4C4C4;
  :hover {
    color: #C4C4C4;
  }
}
.el-dialog__body {
  padding: 24px;
}

.el-dialog__footer {
  padding: 24px;
  padding-top: 0;
}
//----- el-dialog 样式覆盖 end ----

//----- el-collapse 样式覆盖 start ----
.el-collapse {
  border: none;
}
.el-collapse-item__header {
  height: auto;
  background-color: transparent;
  border: none;
  
  .el-icon-arrow-right {
    margin: 0;
    margin-left: 12px;
  }
}
.el-collapse-item__wrap {
  background-color: transparent;
  border: none;
}
.el-collapse-item__arrow {
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
}
.el-collapse-item__arrow.is-active {
  transform: rotate(-90deg);
  -webkit-transform: rotate(-90deg);
}
.el-collapse-item__content {
  padding-bottom: 0;
}

//----- el-collapse 样式覆盖 end ----

//------el-loading样式覆盖 start----
.el-loading-mask{
  background-color: rgba(18, 19, 20, 0.8);
}

//------el-loading样式覆盖 end----

//------el-form样式覆盖 start----
.el-form {
  .el-form-item {
    .el-form-item__label {
      color: $hg-default-text-color;
    }
  }
}
//------el-form样式覆盖 end----

//------el-button 样式覆盖 start----
.el-button {
  &.el-button--default {
    background: $hg-main-black;
    color: $hg-label;
    border: 1px solid $hg-border;
    &:hover {
      color: $hg-btn-primary-hover-text;
      background-color: $hg-hover;
    }
  
    &:active {
      color: $hg-btn-primary-active-text;
      background-color: $hg-btn-primary-active;
    }
  
    &.is-loading {
      position: relative;
      pointer-events: none;
    }
  }
}
//------el-button 样式覆盖 end----

.viewer-donwload.el-icon-download{
  display: inline-block;
  /* &::before {
    background-image: url('~@/assets/images/common/download.svg');
    background-repeat: no-repeat;
    background-size: 280px;
    color: transparent;
    display: block;
    font-size: 0;
    height: 20px;
    line-height: 0;
    width: 20px;
  } */
}

.el-autocomplete-suggestion.el-popper {
  background-color: #262629;
  border-color: #262629;

  li:hover {
    background-color: $hg-hover;
    color: $hg-secondary-text;
  }

  .popper__arrow {
    border-bottom-color: #262629;
    &::after {
      border-bottom-color: #262629;
    }
  }
}

// ---- el-popover 样式覆盖 start ----
.el-popover.el-popper {
  border-radius: 4px;
  background: #2F3033;
  box-shadow: 0px 24px 48px -12px rgba(0, 0, 0, 0.18);
  border: none;
  color: $hg-label;
}