<template>
  <el-dialog
    custom-class="order-detail-page return-reason" 
    append-to-body 
    width="560px" 
    :visible="isShow" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :before-close="handleClose">
    <p slot="title">{{ $t('order.ortho.title.selectImage') }}</p>
    
    <div class="content">
      <p class="tips">{{ $t('order.ortho.tips.selectImageFromOrder') }}</p>
      <div class="content-box_scroll">
        <div class="content-box_ul">
          <div 
            v-for="(item, index) in dataList" 
            :key="index"
            :class="['content-box_li', selectCurList.includes(item.id) ? 'is-select_normal': '', item.className ]" 
            @click.stop="handleClickImage(item.id)">
            <span>{{ getTypeName(item.fileType) }}</span>
            <img :src="item.pictureUrl" alt="">
          </div>
        </div>
      </div>
      <p class="select-tab">
        <span v-for="(item, index) in tabList" :key="index" :class="item.className" @click.stop="handleSelect(item)">
          {{ $t(item.name) + `(${item.fileList.length})` }}
        </span>
      </p>
    </div>

    <div slot="footer" class="footer">
      <hg-button type="secondary" @click="handleClose">{{ $t('common.btn.cancel') }}</hg-button>
      <hg-button @click="handleConfirm">{{ $t('common.btn.confirm') }}</hg-button>
    </div>
  </el-dialog>
</template>

<script>
import { getDownloadUrl } from '@/api/file';
import { FILE_TYPES } from '@/public/constants';
import { copy } from '@/public/utils';

export default {
  props: {
    clientOrgCode: {
      type: Number,
      require: true,
    },
    originList: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      isShow: false,
      dataList:[],
      tabList: [
        { name: 'order.ortho.title.face', fileType: FILE_TYPES.FACE_PIC, fileList: [], className: 'tab-item_face' },
        { name: 'order.ortho.title.intra', fileType: FILE_TYPES.INTRAORAL_PIC, fileList: [], className: 'tab-item_intr' },
        { name: 'order.ortho.title.ct', fileType: FILE_TYPES.CT_PIC, fileList: [], className: 'tab-item_ct' },
      ],
      selectCurList:[],
    }
  },
  watch: {
    'originList.length'(count) {
      if(count > 0) {
        this.getFileUrl();
      }
    }
  },
  mounted() {
    this.getFileUrl();
  },
  methods: {
    /**
     * 数据不回填，重置dataList fileType
     */
    handleClose() {
      this.isShow = false;
      this.selectCurList = [];
      this.tabList.map(tab => {
        tab.fileList = [];
        return tab;
      });
      this.dataList.map(item => {
        item.fileType = FILE_TYPES.ORTH_ORIGIN_PIC;
        item.className = '';
        return item;
      });
    },

    // 确认选择效果
    handleConfirm() {
      if(this.selectCurList.length > 0) {
        const askMsg = this.$t('order.operate.askCloseImageDailog');
        const tipTitle = this.$t('order.operate.askClose');
        this.$confirm( askMsg, tipTitle, {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
          closeOnClickModal: false,
          closeOnPressEscape: false,
          distinguishCancelAndClose: true,
        }).then(() => {
          this.handleClose();
        }).catch((action) => {
          
        });
      }else {
        this.$emit('updateSelectList', copy(this.dataList));
        this.handleClose();
      }
      
    },

    // 获取文件url
    getFileUrl() {
      this.dataList = copy(this.originList);
      this.dataList.forEach(file => {
        
        file.id = file.fileName + '-' + file.filePath;

        const param = {
          s3FileId: file.filePath,
          orgCode: this.clientOrgCode,
          filename: '',
        };
        getDownloadUrl(param).then(res => {
          const url = res.data.url;
          this.$set(file, 'pictureUrl', url);
        }).catch(err => {});
      });
    },

    getTypeName(fileType) {
      if(fileType === FILE_TYPES.FACE_PIC) {
        return this.$t('order.ortho.title.face');
      }else if (fileType === FILE_TYPES.INTRAORAL_PIC) {
        return this.$t('order.ortho.title.intra');
      }else if (fileType === FILE_TYPES.CT_PIC) {
        return this.$t('order.ortho.title.ct');
      }else {
        return '';
      }
    },
    
    // 图片是否选中状态
    handleClickImage(fileId) {
      if(this.selectCurList.includes(fileId)) {
        this.selectCurList = this.selectCurList.filter(item => item !== fileId);
      }else {
        this.selectCurList.push(fileId);
      }
    },
    
    // 点击tab
    handleSelect(tabItem) {
      const { fileType, className } = tabItem;

      this.tabList.map(tab => {
        const newFileList = tab.fileList.filter(fileId => !this.selectCurList.includes(fileId));
        tab.fileList = newFileList;
        return tab;
      });

      //给文件列表添加class
      this.dataList.map(data => {
        if(this.selectCurList.includes(data.id)) {
          data.fileType = fileType;
          data.className = className;
        }
        return data;
      });

      tabItem.fileList = this.selectCurList;
      this.selectCurList = [];
    },
  }
}
</script>


<style lang="scss" scoped>
.content {
  .tips {
    color: #D7D7D9;
    line-height: 20px;
  }
  .content-box_scroll {
    margin: 12px 0;
    height: 332px;
    overflow: hidden;
    overflow-y: auto;
    border-radius: 4px;
    background: #121314;

    .content-box_ul {
      padding: 8px;
      display: flex;
      flex-wrap: wrap;

      .content-box_li {
        cursor: pointer;
        position: relative;
        margin: 8px;
        width: 140px;
        height: 140px;
        border-radius: 4px;
        border: 1px solid #2D2F33;

        >img {
          padding: 8px;
          width: 100%;
          height: 100%;
        }

        &:hover {
          border-color: #ECECEE;
        }

        >span {
          position: absolute;
          display: inline-block;
          padding: 4px 12px;
          border-radius: 4px;
        }
      }

      .content-box_li.is-select_normal {
        border: 1px solid #ECECEE !important; // 提高优先级，选中其他类型时也让用户感知
        &:hover {
          border-color: #ECECEE;
        }
      }

      .content-box_li.tab-item_face {
        border: 1px solid #33C9FF;
        >span {
          color: #33C9FF;
          background: #33C9FF33;
          border: 1px solid #33C9FF;
        }
      }

      .content-box_li.tab-item_intr {
        border: 1px solid $hg-secondary-success;
        >span{
          color: $hg-secondary-success;
          background: #72D14333;
          border: 1px solid $hg-secondary-success;
        }
      }

      .content-box_li.tab-item_ct {
        border: 1px solid #EF9B34;
        >span {
          color: #EF9B34;
          background: #EF9B3433;
          border: 1px solid #EF9B34;
        }
      }

    }
  }

  .select-tab {

    >span {
      cursor: pointer;
      display: inline-block;
      margin-right: 16px;
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 16px;
    }

    .tab-item_face {
      color: #33C9FF;
      background: #33C9FF33;
      border: 1px solid #33C9FF;
    }

    .tab-item_intr {
      color: $hg-secondary-success;
      background: #72D14333;
      border: 1px solid $hg-secondary-success;
    }

    .tab-item_ct {
      color: #EF9B34;
      background: #EF9B3433;
      border: 1px solid #EF9B34;

    }
  } 
}
</style>

<style lang="scss">
.order-detail-page.return-reason {
  .el-dialog__body {
    padding-bottom: 16px;
    border-bottom: 1px solid #38393D;
  }

  .el-dialog__footer {
    padding-top: 24px;

    .hg-button {
      margin-left: 24px;
      min-width: 104px;
      line-height: 20px;
    }
  }
}
</style>