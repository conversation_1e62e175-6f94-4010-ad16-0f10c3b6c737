


//清除浮动
@mixin clearfix() {
&:before,
&:after {
    content: " ";
    display: table;
}
&:after {
    clear: both;
}
}

// 用于设置响应样式
$breakpoints: (
  'xs': 'only screen and ( max-width: 960px)',
  'sm': 'only screen and ( max-width: 1420px)',
  'md': 'only screen and ( max-width: 1440px)',
  'lg': 'only screen and ( max-width: 1920px)',
) !default;

@mixin respond($breakpoint) {
    $str: map-get($breakpoints, $breakpoint);
    @media #{if(type-of($str) == 'string', unquote($str), inspect($str))}
     {
      @content;
    }
  }


@function pxToVw($arg) {
    @return $arg * (100/ 1440) * 1vw;
}



$breakpoints: (
  'xs': 'only screen and ( max-width: 960px)',
  'sm': 'only screen and ( max-width: 1420px)',
  'md': 'only screen and ( max-width: 1440px)',
  'lg': 'only screen and ( max-width: 1920px)',
) !default;

@mixin respond($breakpoint) {
  $str: map-get($breakpoints, $breakpoint);
  @media #{if(type-of($str) == 'string', unquote($str), inspect($str))} {
    @content;
  }
}