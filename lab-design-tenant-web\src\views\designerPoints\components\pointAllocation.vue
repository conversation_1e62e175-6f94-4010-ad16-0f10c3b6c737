<template>
  <div>
    <el-drawer :title="lang('exportPoint')" custom-class="pointAllocation-drawer" :visible.sync="pointAllocation">
      <div class="draw-title" slot="title">{{this.date}} {{lang('allocation')}}</div>
      <div class="point-allocation">
        <div class="points-header">
          <div class="time-search">
            <span class="label">{{lang('orderNo')}}</span>
            <el-input v-model="orderNo" :placeholder="lang('pleaseInput')" suffix-icon="el-icon-search"></el-input>
          </div>
          <div class="points-btnlist">
            <el-button type="primary" @click="searchOrder">{{lang('search')}}</el-button>
          </div>
          <div class="error-tips" v-if="noOrder">{{lang('noSearch')}}</div>
        </div>
        <!-- 内容 -->
        <div class="order-content" v-show="selectOrderId || noOrder">
          <div class="order-list">
            <span class="label">{{lang('orgsn')}}</span>
            <el-input :disabled="!(!selectOrderId)" v-model="selectOrder.orgSn" :placeholder="lang('orgsn')" clearable @change="changeOrgSn"></el-input>
            <span v-show="isNoFindsn" class="find-error"><span style="margin-right: 4px;" class="el-icon-warning"></span>{{lang('noOrgNo')}}</span>
          </div>
          <div class="order-list">
            <span class="label"><span style="color: #E55353;margin-right: 4px;">*</span>{{lang('desinger')}}</span>
            <!-- <el-input v-model="selectOrder.designUserName" placeholder="设计师姓名" clearable></el-input> -->
            <el-select v-model="selectOrder.designUser" filterable collapse-tags :placeholder="lang('designPlaceholder')">
              <el-option v-for="item in designerList" :key="item.userCode" :label="item.userName" :value="item.userCode"></el-option>
            </el-select>
            <el-input class="point-input" v-model="selectOrder.designUserPoints" :placeholder="lang('point')" clearable></el-input>
            <span class="tips">{{lang('point')}}</span>
          </div>
          <div class="order-list">
            <span class="label"><span style="color: #E55353;margin-right: 4px;">*</span>{{lang('groupqc')}}</span>
            <!-- <el-input v-model="selectOrder.groupQcName" placeholder="组内QC" clearable></el-input> -->
            <el-select v-model="selectOrder.groupQc" filterable collapse-tags :placeholder="lang('designPlaceholder')">
              <el-option v-for="item in designerList" :key="item.userCode" :label="item.userName" :value="item.userCode"></el-option>
            </el-select>
            <el-input class="point-input" v-model="selectOrder.groupQcPoints" :placeholder="lang('point')" clearable></el-input>
            <span class="tips">{{lang('point')}}</span>
          </div>
          <div class="order-list">
            <span class="label">{{lang('designCode')}}</span>
            <designTypeSkuSelect v-model="selectOrder.designTypeCodes" :designCodesAll="designCodesAll" needCombined @changeSearch="changeSelect"></designTypeSkuSelect>
          </div>
          <div class="order-list">
            <span class="label"><span style="color: #E55353;margin-right: 4px;">*</span>{{lang('back')}}</span>
            <el-radio v-model="selectOrder.isReturn" :label="true">{{lang('yes')}}</el-radio>
            <el-radio v-model="selectOrder.isReturn" :label="false">{{lang('no')}}</el-radio>
          </div>
          <div class="order-list">
            <span class="label"><span style="color: #E55353;margin-right: 4px;">*</span>{{lang('free')}}</span>
            <el-radio v-model="selectOrder.isFree" :label="true">{{lang('yes')}}</el-radio>
            <el-radio v-model="selectOrder.isFree" :label="false">{{lang('no')}}</el-radio>
          </div>
          <div class="order-list">
            <span class="label"><span style="color: #E55353;margin-right: 4px;">*</span>{{lang('overTime')}}</span>
            <el-radio v-model="selectOrder.isTimeout" :label="true">{{lang('yes')}}</el-radio>
            <el-radio v-model="selectOrder.isTimeout" :label="false">{{lang('no')}}</el-radio>
          </div>
        </div>
        <!-- 按钮组 -->
        <div class="btn-list">
          <el-button type="danger" v-if="selectOrderId" @click="deleteOrder()">{{lang('delete')}}</el-button>
          <el-button type="primary" v-if="selectOrderId" @click="addOrders">{{lang('submitBtn')}}</el-button>
          <el-button type="primary" v-if="noOrder" @click="addOrders">{{lang('addbtn')}}</el-button>
        </div>
      </div>
    </el-drawer>
    <selectCustomer :oderShow.async="oderShow" :orderList="orderList" @submitOrder="submitOrder"></selectCustomer>
  </div>
</template>

<script>
import designTypeSkuSelect from './designTypeSkuSelect';
import selectCustomer from './selectCustomer';
import { orders, delOrders, setOrders, getorgSn } from '@/api/designPoints'
import { mapGetters } from "vuex";
import { getLang } from '@/public/utils';
export default {
  name: "pointAllocation",
  components: {
    designTypeSkuSelect,selectCustomer
  },
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    date: String,
    designerList: Array
  },
  data() {
    return {
      orderNo: '',
      oderShow: false,
      searchOrderTime: false,
      orderList: [],
      selectOrderId: null,
      noOrder: false,
      isNoFindsn: false,
      selectOrder: {
        orderNo: '',
        orgSn: '',
        designUser: '',
        designUserName: '',
        groupQc: '',
        designUserPoints: '',
        groupQCPoints: '',
        isReturn: false,
        isFree: false,
        isTimeout: false,
        designTypeCodes: [],
      },
      designCodesAll: []
    };
  },
  computed: {
    ...mapGetters(["language", 'designTypeSkuTree']),
    pointAllocation: {
      get() {
        return this.drawer;
      },
      set(val) {
        this.$emit("update:drawer", val);
      },
    },
  },
  watch: {
    pointAllocation(newValue, oldValue) {
      if(!newValue){
        this.resetOrder();
      }
    }
  },
  mounted () {
  },
  methods: {
    lang: getLang('designpoints'),
    // 查找
    async searchOrder(){
      let parames = {
        month: this.date,
        orderNo: this.orderNo
      }
      this.resetOrder('isSearch'); // 每次点击检索都要重置
      this.searchOrderTime = true;//点击过检索
      const {code, data} = await orders(parames);
      if(code == 200){
        if(data.length > 1){
          this.orderList = data;
          this.oderShow = true;
          this.noOrder = false;
        } else if(data.length == 0) {
          this.noOrder = true;
          this.designCodesAll = [];
        } else {
          this.submitOrder(data[0].id, data[0])
        }
      }
    },
    // 查找客户编码
    async changeOrgSn(){
      const { code, data } = await getorgSn(this.selectOrder.orgSn);
      if(code == 200){
        if(!data){
          this.isNoFindsn = true;
        } else {
          this.isNoFindsn = false;
        }
      }
    },
    // 查询到多个单时需要选择
    submitOrder(orderId, nowOrder){
      this.oderShow = false;
      if(orderId && nowOrder){
        this.selectOrderId = orderId;
        if(nowOrder.designTypes){
          this.designCodesAll = this.findParentIds(this.designTypeSkuTree, JSON.parse(nowOrder.designTypes));
        } else {
          this.designCodesAll = [];
        }
        this.selectOrder = {
          orderNo: nowOrder.orderNo,
          orgSn: nowOrder.orgSn,
          designUser: nowOrder.designUser,
          designUserName: nowOrder.designUserName,
          groupQc: nowOrder.groupQc,
          groupQcName: nowOrder.groupQcName,
          designUserPoints: nowOrder.designUserPoints.toFixed(4),
          groupQcPoints: nowOrder.groupQcPoints.toFixed(4),
          isReturn: nowOrder.isReturn,
          isFree: nowOrder.isFree,
          isTimeout: nowOrder.isTimeout,
          designTypeCodes: JSON.parse(nowOrder.designTypes)
        }
      }
    },
    // 重置
    resetOrder(isSearch){
      if(!isSearch){
        this.orderNo = '';
      }
      this.searchOrderTime = false,
      this.oderShow = false;
      this.selectOrderId = '';
      this.noOrder = false;
      this.isNoFindsn = false;
      this.designCodesAll = [];
      this.selectOrder = {
        orderNo: '',
        orgSn: '',
        designUser: '',
        designUserName: '',
        groupQc: '',
        groupQcName: '',
        designUserPoints: '',
        groupQcPoints: '',
        isReturn: false,
        isFree: false,
        isTimeout: false,
        designTypeCodes: []
      }
    },
    // 删除订单
    deleteOrder(){
      let tips = this.language == 'zh' ? `确认是否删除订单${this.selectOrder.orderNo}的点数分配？删除后无法恢复，请谨慎操作。` : `Sure to delete this allocation record for Order ${this.selectOrder.orderNo}? Cannot be restored once deleted.`
      this.$confirm(tips, this.lang('systemTips'), {
        confirmButtonText: this.lang('submitBtn'), 
      }).then(async() => {
        let parame = {
          id: this.selectOrderId,
          month: this.date
        }
        const { code, data } = await delOrders(parame);
        if(code == 200){
          this.$message.success(this.lang('deleteTips'));
          this.resetOrder();
          this.$emit('addSuccess', 'delete');
        }
      })
      .catch(action => {
        
      });
    },
    // 添加订单
    addOrders(){
      // console.log(!this.selectOrder.designUser,!this.selectOrder.designUser.length, !this.selectOrder.designUserPoints, 2222)
      if(!this.selectOrder.designUser || !this.selectOrder.designUserPoints){
        this.$message.error(this.lang('blank'));
        return
      } else if(!this.selectOrder.groupQc || !this.selectOrder.groupQcPoints){
        this.$message.error(this.lang('blank'));
        return
      } else if((this.selectOrder.isFree === '' && this.selectOrder.isFree !== false) || (this.selectOrder.isReturn === '' && this.selectOrder.isReturn !== false) || (this.selectOrder.isTimeout === '' && this.selectOrder.isTimeout !== false)){
        this.$message.error(this.lang('blank'));
        return
      }
      this.$confirm(this.lang('addtips'), this.lang('systemTips'), {
        confirmButtonText: this.lang('submitBtn'),
        cancelButtonText: this.lang('cancel')
      }).then(async() => { 
        let parame = {
          "designTypes": this.selectOrder.designTypeCodes,
          "designUser": this.selectOrder.designUser,
          "designUserPoints": this.selectOrder.designUserPoints,
          "groupQc": this.selectOrder.groupQc,
          "groupQcPoints": this.selectOrder.groupQcPoints,
          "id": this.selectOrderId,
          "isFree": this.selectOrder.isFree,
          "isReturn": this.selectOrder.isReturn,
          "isTimeout": this.selectOrder.isTimeout,
          "month": this.date,
          "orderNo": this.selectOrderId ? this.selectOrder.orderNo : this.orderNo,
          "orgSn": this.selectOrder.orgSn
        }
        try {
          const { code, data } = await setOrders(parame);
          if(code == 200){
            this.$message.success(this.lang('addSuccess'));
            this.$emit('addSuccess');
          }
        } catch (error) {
          this.$message.error(error.message);
        }
      })
      .catch(action => {
        
      });
    },
    changeSelect(designType, valueList, designCodesAll){
      this.selectOrder.designTypeCodes = valueList
    },
    // 根据三级id查找一二级
    findParentIds(tree, targetIds){
      let parentIdsArray = [];
      const findParents = (node, idPath, targetId) => {
        idPath.push(node.skuCode);
          
        if (targetId == node.skuCode) {
          parentIdsArray.push([...idPath]); // 获取当前节点的父节点id
        } else if (node.children) {
          for (let child of node.children) {
            findParents(child, idPath.slice(), targetId); // 递归查找子节点
          }
        }
      };

      for (let targetId of targetIds) {
        if(targetId ==  999999){
          parentIdsArray.push([999999])
        } else {
          for (let topLevelNode of tree) {
            findParents(topLevelNode, [], targetId);
          }
        }
      }

      return parentIdsArray;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/.pointAllocation-drawer {
  width: 800px !important;
  background-color: $hg-main-black;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
    color: $hg-label;
  }
  .el-drawer__body {
    padding: 24px;
    overflow: hidden;
  }
  .el-button.is-plain:focus{
    background: transparent;
  }
  .el-button--primary:focus{
    background: #3760EA;
    border-color: #3760EA;
  }
  .point-allocation{
    display: flex;
    flex-direction: column;
    height: calc(100% - 80px);
    .error-tips{
      color: #FBAA0E;
      font-size: 12px;
      padding: 4px;
      margin-left: 48px;
    }
    .find-error{
      display: inline-flex;
      justify-content: center;
      // color: #E55353;
      color: #FBAA0E;
      font-size: 12px;
      padding: 4px;
      margin-left: 10px;
    }
    /deep/.points-header{
      .time-search{
        position: relative;
        width: 260px;
        display: flex;
        align-items: center;
        .el-input__prefix{
          display: none;
        }
        .label{
          margin-right: 10px;
        }
        .date-icon{
          position: absolute;
          right: 40px;
          font-size: 16px;
        }
      }
      .points-btnlist{
        position: absolute;
        right: 0px;
        top: 0;
      }
      .el-input{
        width: 200px;
        background: #141519;
        .el-input--prefix .el-input__inner{
          padding-left: 20px;
        }
      }
    }
    .order-content{
      flex: 1;
      background: #27292E;
      margin-top: 30px;
      overflow: auto;
      border-radius: 8px;
      padding: 28px;
      .order-list{
        position: relative;
        margin-bottom: 40px;
        .label{
          display: inline-block;
          width: 140px;
          margin-right: 10px;
        }
        .el-input{
          width: 300px;
          background: #141519;
          .el-input--prefix .el-input__inner{
            padding-left: 20px;
          }
        }
        .point-input{
          width: 160px;
          margin-left: 20px;
          .el-input__inner{
            padding-right: 40px;
          }
          .el-input__suffix{
            padding-right: 30px;
          }
        }
        .tips{
          position: absolute;
          font-size: 12px;
          right: 80px;
          top: 50%;
          transform: translateY(-60%);
        }
        .design-type-select{
          display: inline-block;
        }
        .el-radio__label{
          color: #F3F5F7;
        }
        .el-input.is-disabled .el-input__inner{
          border: 1px solid #3D4047;
          background: rgba(223, 223, 223, 0.24);
        }
        .error-border-tips{
          .el-input__inner{
            border-color: #E55353;
          }
        }
      }
    }
    .btn-list{
      position: absolute;
      bottom: 24px;
      right: 10px;
      display: flex;
      justify-content: flex-end;
      width: 100%;
      height: 56px;
      padding: 16px 24px 0 0;
      border-top: 1px solid $hg-main-border;
    }
  }
}
</style>
