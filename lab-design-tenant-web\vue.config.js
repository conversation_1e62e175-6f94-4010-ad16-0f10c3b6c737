const webpack = require('webpack');
const path = require('path');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

function resolve(dir) {
  return path.join(__dirname, dir);
}

const isDev = process.env.VUE_APP_ENVIRONMENT === 'dev' || process.env.NODE_ENV === 'local';

module.exports = {
  publicPath: './',
  assetsDir: 'static', // 打包后静态文件所在目录
  lintOnSave: isDev, // 是否开启将lint错误输出
  productionSourceMap: Boolean(isDev),

  devServer: {
    disableHostCheck: true,
    port: 9002,
    proxy: {  // 注意顺序，如果/api配在前面，/api/design会被拦截
      '/[a-zA-Z_]+login': {
        // target: 'http://127.0.0.1:8911',//代理地址，这里设置的地址会代替axios中设置的baseURL
        target: 'https://dev-tenant-lab.heygears.com/', //有线上服务时应该需要更新
        changeOrigin: true,
        pathRewrite: {
          '^/[a-zA-Z_]+login': '/'
        }
      },
      '/lab_tenant_design/': {
        target: 'http://127.0.0.1:9002',
        changeOrigin: true,
        pathRewrite: {
          '^/lab_tenant_design/': ''
        }
      },
      '/lab_tenant_uc/': {
        target: 'https://dev-tenant-lab.heygears.com/lab_tenant_uc/',
        changeOrigin: true,
        pathRewrite: {
          '^/lab_tenant_uc/': ''
        }
      },
      '/lab_tenant_device/': {
        target: 'https://dev-tenant-lab.heygears.com/lab_tenant_device/',
        changeOrigin: true,
        pathRewrite: {
          '^/lab_tenant_device/': ''
        }
      },
      '/lab_tenant_ms': {
        target: 'https://dev-tenant-lab.heygears.com/lab_tenant_ms/',
        changeOrigin: true,
        pathRewrite: {
          '^/lab_tenant_ms/': ''
        }
      },
      '/api/systemCommon': {
        // 本地联调 设计类型的服务
        target: 'https://dev-svc.heygears.com/design-basic-service/design-basic-service/systemCommon/v1',
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/api/systemCommon': '',
        }
      },
      '/api/multiS3': {
				// 本地联调 基础业务
				// target: 'https://dev-svc.heygears.com/s3-demo/demo',
				target: 'https://dev-svc.heygears.com/design-file-service/design-file-service/multipartUpload/v1',
				changeOrigin: true,
				ws: true,
				pathRewrite: {
					'^/api/multiS3': '',
				}
      },
      '/api/heypoint': {
        target: 'https://dev-svc.heygears.com/design-settlement-service/design-settlement-service',
        changeOrigin: true,
				ws: true,
				pathRewrite: {
					'^/api/heypoint': '',
				}
      },
      '/': {
        target: 'https://dev-gw.heygears.com/gw',
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/': '',
        }
      }

    },
  },

  // css 配置
  css: {
    loaderOptions: {
      sass: {
        additionalData: `@import "./src/assets/styles/variables";`,
      },
    },
  },

  chainWebpack: (config) => {
    config.resolve.alias.set('@', resolve('./src'));

    config.module // 排除其他svg loader对src/assets/icons/svg进行操作
      .rule('svg')
      .exclude.add(resolve('src/assets/icons/svg'))
      .end();

    // 使用svg-sprite-loader对src/assets/icons/svg进行操作
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons/svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        // 定义使用规则 <svg> <use xlink:href="#icon-svg文件名"></use>  </svg>
        symbolId: 'icon-[name]',
      });
  },

  configureWebpack: {
    // Ignore all locale files of moment.js
    plugins: [
      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
      // new BundleAnalyzerPlugin({ analyzerPort: 8892 })
    ],
  },
};
