$--color-primary: $hg-main-blue;


/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";

// 按需加载
@import "~element-ui/packages/theme-chalk/src/select.scss";
@import "~element-ui/packages/theme-chalk/src/select-dropdown.scss";
@import "~element-ui/packages/theme-chalk/src/date-picker.scss";
@import "~element-ui/packages/theme-chalk/src/pagination.scss";
@import "~element-ui/packages/theme-chalk/src/checkbox.scss";
@import "~element-ui/packages/theme-chalk/src/dropdown.scss";
@import "~element-ui/packages/theme-chalk/src/radio.scss";
@import "~element-ui/packages/theme-chalk/src/collapse.scss";
@import "~element-ui/packages/theme-chalk/src/table.scss";

@import './element-reset.scss';