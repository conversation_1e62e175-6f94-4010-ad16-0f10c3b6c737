<template>
  <div class="operate-log">
    <div class="filter">
      <!-- <el-form class="log-form" :inline="true" :label-width="'110px'">
        <el-form-item :label="lang('number') + '：'" :label-width="language === 'en' ? '160px' : '110px'">
          <hg-input
            @change="searchData"
            v-model="conditions.number"
            :placeholder="lang('numberTips')"
            clearable
          ></hg-input>
        </el-form-item>
        <el-form-item :label="lang('operateDate') + '：'">
          <date-range-picker valueFormat="timestamp" v-model="conditions.date" @change="searchData"></date-range-picker>
        </el-form-item>
        <el-form-item :label="lang('type') + '：'">
          <el-select
            filterable
            clearable
            @change="searchData"
            v-model="conditions.operationType"
            :placeholder="lang('typeTips')"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.label"
              :label="lang(item.label)"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form> -->
      <div class="search-list">
        <div class="search-li">
          <span>{{$t('heypoint.customer.date')}}</span>
          <date-range-picker valueFormat="timestamp" v-model="conditions.date" @change="searchData"></date-range-picker>
        </div>
        <div class="search-li">
          <!-- <span>日期</span> -->
          <hg-input
            @change="searchData"
            v-model="conditions.number"
            :placeholder="lang('numberTips')"
            clearable
          ></hg-input>
        </div>
        <div class="search-li">
          <span>{{lang('type')}}</span>
          <el-select
            filterable
            clearable
            @change="searchData"
            v-model="conditions.operationType"
            :placeholder="lang('typeTips')"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.label"
              :label="lang(item.label)"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="btn">
          <slot name="btn"></slot>
        </div>
      </div>
    </div>
    <!-- 表格 -->
    <hg-table
      v-loading="tableInfo.loading"
      :needSelect="false"
      :data="tableInfo.data"
      :header-data="tableInfo.cols"
      maxHeight="auto"
      class="operate-table"
      :class="{ 'is-edit': isEdit }"
    >
      <template #operationTime="{ row }">
        <span>{{ (row.operationTime * 1000) | dateFormatInHtml }}</span>
      </template>

      <template #expiredTime="{ row }">
        <span v-if="row.expiredTime">
          {{ row.expiredTime * 1000 | dateFormatInHtml }}
        </span>
        <span v-else>--</span>
      </template>

      <template #operationType="{ row }">
        <span>{{ row.operationType | operatTypeFilter}}</span>
      </template>

      <template #points="{ row }">
        <span class="active">
          {{ row.points | capitalize }}
        </span>
      </template>

      <template #giftBalance="{ row }">
        <span :class="{ active: row.affectType.includes('2') }">
          {{ row.giftBalance | capitalize }}
        </span>
      </template>

      <template #rechargeBalance="{ row }">
        <span
          :class="{ active: row.affectType.includes('1') }"
          >{{ row.rechargeBalance | capitalize }}</span
        >
      </template>

      <template #availableCredit="{ row }">
        <span
          :class="{ active: row.affectType.includes('3') }"
          >{{ row.availableCredit | capitalize }}</span
        >
        <span>{{ ` / ` }}</span>
        <span>{{ row.credit | capitalize }}</span>
      </template>

      <template #remark="{ row }">
        <span>
          {{ row.remark | noData }}
        </span>
      </template>
    </hg-table>

    <!-- 分页 -->
    <pagination
      class="table-footer"
      :total="tableInfo.total"
      :initPageIndex="tableInfo.page"
      :initPageSize="tableInfo.pageSize"
      @onSearch="onSearch"
    ></pagination>
  </div>
</template>

<script>
import hgTable from '@/components/HgTable';
import pagination from '@/components/Pagination';
import { getCustomerOperationLog } from '@/api/heypoint';
import { getLang } from '@/public/utils';
import {
  capitalize,
  operatTypeFilter
} from '@/filters/heypoint';
import dateRangePicker from '@/components/DateRangePicker';
import { mapGetters } from 'vuex';
export default {
  name: 'OperateLog',
  components: {
    hgTable,
    pagination,
    dateRangePicker
  },
  filters: {
    operatTypeFilter,
    capitalize,
    noData(val) {
      const errArr = ['', undefined, null];
      if (errArr.includes(val)) {
        return '--';
      } else {
        return val;
      }
    },
    noDate(val) {
      if (val === 0) {
        return '--';
      } else {
        return val;
      }
    }
  },
  props: {
    isEdit: {
      default: false,
    },
  },
  data() {
    return {
      typeOptions: [
        { name: '充值', label: 'recharge', value: 1 },
        { name: '赠送', label: 'give', value: 2 },
        { name: '过期', label: 'overdue', value: 3 },
        { name: '消费', label: 'consume', value: 4 },
        { name: '退款', label: 'refund', value: 5 },
      ],
      tableInfo: {
        page: 1,
        pageSize: 10,
        total: 0,
        loading: false,
        data: [],
        cols: [
          {
            prop: 'operationTime',
            oldlabel: '操作日期',
            getLabel: () => {
              return this.lang('operateDate');
            },
          },
          {
            prop: 'no',
            oldlabel: '编号/订单号',
            width: '240',
            getLabel: () => {
              return this.lang('number');
            },
          },
          {
            prop: 'operationType',
            oldlabel: '操作类型',
            getLabel: () => {
              return this.lang('type');
            },
          },
          {
            prop: 'points',
            oldlabel: '黑豆数',
            getLabel: () => {
              return this.lang('heypointTotal');
            },
          },
          {
            prop: 'expiredTime',
            oldlabel: '黑豆有效期',
            getLabel: () => {
              return this.lang('heypointExpired');
            },
          },
          {
            prop: 'giftBalance',
            oldlabel: '赠送黑豆余额',
            getLabel: () => {
              return this.lang('giftBalance');
            },
          },
          {
            prop: 'rechargeBalance',
            oldlabel: '充值黑豆余额',
            getLabel: () => {
              return this.lang('rechargeBalance');
            },
          },
          {
            prop: 'availableCredit',
            oldlabel: '可用信用值',
            getLabel: () => {
              return this.lang('availableCredit');
            },
          },
          {
            prop: 'remark',
            oldlabel: '备注',
            getLabel: () => {
              return this.lang('remark');
            },
          },
        ],
      },
      conditions: {
        number: '',
        startTime: null,
        endTime: null,
        date: [0, 0],
        operationType: null,
      },
    };
  },
  computed: {
    ...mapGetters(['language']),
  },
  created() {
    this.searchData();
  },
  methods: {
    lang: getLang('heypoint.customer.operate'),
    // 搜索
    searchData() {
      this.tableInfo.page = 1;
      this.getList();
    },

    /**
     * 获取充值/赠送操作记录
     */
    async getList() {
      try {
        this.tableInfo.loading = true;
        const { orgCode } = this.$route.query;
        const date = this.conditions.date;
        const params = {
          operationType: this.conditions.operationType,
          pageNum: this.tableInfo.page,
          pageSize: this.tableInfo.pageSize,
          no: this.conditions.number,
          orgCode
        };
        if (date.length) {
          params.startTime = date[0] / 1000;
          params.endTime = date[1] / 1000;
        }
        const { data } = await getCustomerOperationLog(params);
        this.tableInfo.data = (data.records || []);
        this.tableInfo.total = data.total;

      } catch (error) {
        console.error('error: ', error);
      } finally {
        this.tableInfo.loading = false;
      }
    },

    /**
     * 页数变化回调
     */
    currentChange(val) {
      this.tableInfo.page = val;
      this.getList();
    },

    /**
     * 每页条数变化回调
     */
    sizeChange(val) {
      this.tableInfo.pageSize = val;
      this.getList();
    },

    onSearch(type, conditions) {
      if (type === 'page') {
        this.tableInfo.page = conditions.pageIndex;
        this.tableInfo.pageSize = conditions.pageSize;
      }
      this.getList();
    },
  },
};
</script>

<style lang="scss">
.operate-log {
  flex: 1;
  overflow-y: auto;
  .filter {
    // position: relative;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    // margin-top: 24px;
    .date-range-picker {
      background: $hg-background;
    }
    .log-form {
      margin-right: 14px;
    }
  }
  .search-list{
    // display: flex;
    // align-items: center;
    line-height: 40px;
    width: 100%;
    background:#1B1D22;
    padding: 10px 20px 10px 20px;
    border-bottom: 0.07143rem solid #38393D;
    .search-li{
      margin: 10px 0;
      span{
        margin-right: 10px;
      }
      display: inline-flex;
      justify-content: center;
      align-items: center;
      margin-right: 20px;
    }
  }
  .btn {
    // margin-top: 10px;
    // margin-bottom: 24px;
    // border-left: 1px solid $hg-disable;
    padding: 0;
    .el-button {
      width: 120px;
      height: 40px;
      padding: 0;
    }
    .recharge-btn {
      margin-left: 12px;
    }
  }
  .operate-table {
    height: calc(100% - 190px);
    .active {
      color: #FF5A5A;
    }
  }
  .table-footer {
    border-top: 1px solid $hg-border;
  }
}
</style>
