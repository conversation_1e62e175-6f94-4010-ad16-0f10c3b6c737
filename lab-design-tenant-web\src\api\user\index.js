import request from '../axios';
import { server } from '@/config';

const axios = request.axios;

/**
 * 获取组织架构树
 */
export const getOrgTree = (orgCode) => {
  return axios({
    url: `${server.orgServer}/designTree`,
    method: 'GET',
    // data: {
    //   orgCode,
    // },
  });
};
/**
 * 获取人员列表
 */
export const getUserList = (data) => {
  return axios({
    url: `${server.userServer}/staffs`,
    method: 'GET',
    params: data,
  });
};

/**
 * 获取负责范围
 */
export const getRangList = (orgCode) => {
  return axios({
    url: `${server.userServer}/applications`,
    method: 'GET',
  });
};

export const setRange = (data) => {
  return axios({
    url: `${server.userServer}/batch-set-staff-duty-range`,
    method: 'POST',
    data,
  });
};
// 获取所有技能等级
export const getDesignerLevels = () => {
  return axios({
    url: `${server.userServer}/designerLevels`,
    method: 'get',
  });
 };
  //批量设置设计师职能等级
  export const batchSetDesignerLevel = (data) => {
    return axios({
      url: `${server.userServer}/batchSetDesignerLevel`,
      method: 'post',
      data
    });
   };
