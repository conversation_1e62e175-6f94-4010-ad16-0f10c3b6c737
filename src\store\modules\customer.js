const state = {
  customerObj: {}, // 保存当前操作的客户信息
  searchData: null,
  lastPath: '',
}
const getters = {
}

const mutations = {
  updateCustomerObj: (state, data) => {
    state.customerObj = data;
  },
  UPDATE_SEARCH_DATA: (state, data) => {
    state.searchData = data;
  },
  UPDATE_LAST_PATH: (state, path) => {
    state.lastPath = path;
  }
}
const actions = {
  updateSearchData: ({ commit }, data) => {
    commit('UPDATE_SEARCH_DATA', data);
  },
  updateLastPath: ({ commit }, path) => {
    commit('UPDATE_LAST_PATH', path);
  },
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}

