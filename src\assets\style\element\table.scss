.el-table {
    background: $hg-main-black;
    .el-table__expanded-cell{
        // background: $hg-main-black;
    }
    &::before{
        background: $hg-main-black;
    }

    table{
        border-collapse: collapse;
    }
    th,
    tr {
        background: $hg-main-black;
    }
    // 表头部分
    .el-table__header-wrapper {
        tr th.is-leaf {
            border-color: $hg-border-color;
            color: $hg-disable-fontcolor;
            font-size: 12px;
        }

        .el-table__header {
            .has-gutter {
            }
        }
    }
    .el-table__body-wrapper.is-scrolling-none {
        .el-table__body {
            background: $hg-main-black;
            border-collapse: collapse;
            tr.el-table__row {
                td {
                    border-color: $hg-border-color;
                    border-bottom-style: dashed;
                }
            }
        }
    }
}
