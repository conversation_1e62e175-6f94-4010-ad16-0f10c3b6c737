
import store from '@/store';
import { COMPONENT_TYPE } from '@/components/Params/utils/constant';

/**
 * 
 */
export const handleTextParamList = () => {

}

/**
 * 比较参数是否有不同
 * @param {*} soureItem 
 * @param {*} designCode 
 */
export const isParamDifferent = (soureItem, designCode, software, parentName, parentComponent, originParamList = []) => {
  if(!store.getters.isLoadDefaultParam) { return false; }

  const allParam = store.getters.defaultParamMap.get(designCode);
  if(allParam) {
    const { parameter } = allParam;
    const paramData = parameter.get(software);
    if(paramData) {
      // 正畸带环参数对比需要特殊处理
      if (designCode === 24406) {
        const compareItem = originParamList.find(item => item.name === soureItem.name);
        return compareItem.value !== soureItem.value
      }
      if(parentComponent === COMPONENT_TYPE.TEXT_INPUT_CARD) { // 子集之间的比较
        const parentData = paramData.find(item => item.name === parentName); // 根据父级的名字，找到父亲数据
        if(parentData && parentData.child) {
          const childList = parentData.child;
          const compareData = childList.find(item => item.name === soureItem.name);
          return soureItem.value !== compareData.value;
        }

      }else if(parentComponent === COMPONENT_TYPE.RADIO_CHILD_INPUT) {
        const parentData = paramData.find(item => item.name === parentName); // 根据父级的名字，找到父亲数据
        if(parentData && parentData.child) {
          const childData = parentData.child.find(cItem => cItem.name === parentData.value); //找到子集，然后获取对应的孙集
          if(childData && childData.child) {
            const childList = childData.child || [];
            const compareData = childList.find(item => item.name === soureItem.name);
            return soureItem.value !== compareData.value;
          }
        }

      }else {
        const compareData = paramData.find(item => item.name === soureItem.name);
        if(compareData) {
          return soureItem.value !== compareData.value;
        }
      }
    }
  }
  return false;
}

/**
 * 比较方案是否有不同（因为方案直接是高亮选中状态，所以不需要逐一对比）
 * @param {*} programList 
 * @param {*} designCode 
 */
export const isProgramHaveDifferent = (programList, designCode) => {
  if(!store.getters.isLoadDefaultParam) { return false; }
  
  const defaultList = store.getters.defaultProgramMap.get(designCode);
  if(defaultList) {
    const noUpdate = programList.every(program => {
      const { component, name, value, child } = program;
      const defualtData = defaultList.find(item => item.name === name);
      if(defualtData) {
        if([COMPONENT_TYPE.IMAGE_CHECKBOX_CARD, COMPONENT_TYPE.RADIO_SELECT_CARD, COMPONENT_TYPE.RADIO_IMG_CARD].includes(component)) { // 10. 有子集卡片 子集有不同即可
          const noUpdate = child.every(item => {
            const defaultChidData = defualtData.child.find(chilData => chilData.name === item.name);
            return defaultChidData.value === item.value;
          });
          if(!noUpdate) {
            return false;
          }
        }
        return value === defualtData.value;
      }
      return false;
      
    });
    
    return !noUpdate;
  }
  return false;
}