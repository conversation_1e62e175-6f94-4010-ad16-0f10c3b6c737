<template>
  <div class="design-type-select">
    <el-cascader
      :options="handelDesignTypeTree(designTypeSkuTree)"
      v-model="designTypeCodes"
      :placeholder="$t('orderList.searchList.designTypeHolder')"
      :props="{ multiple: true, value: 'skuCode', label: 'label' }"
      popper-class="design-type-cascader"
      @change="changeSelect"
      collapse-tags
      clearable
    ></el-cascader>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { UNION_TYPE_CODE } from '@/public/constants';

export default {
  name: 'DesignTypeSelect',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    designCodesAll: {
      type: Array,
      default() {
        return [];
      },
    },
    needCombined: Boolean,
  },
  data() {
    return {
      designTypeCodes: [],
    };
  },
  computed: {
    ...mapGetters(['language', 'designTypeSkuTree']),
  },
  watch: {
    designCodesAll(newValue, oldValue) {
      this.designTypeCodes = newValue;
    },
  },
  methods: {
    // 处理全部设计类型数据
    handelDesignTypeTree(list) {
      const copyList = JSON.parse(JSON.stringify(list));
      //在二级添加未知
      const obj = {
        children: '',
        zhName: '未知',
        label: this.$t('orderList.order.unknownorder'),
        designCode: 25001,
        skuCode: 25001,
        enName: 'Unknown',
        hasParas: 0,
        iconUrl: '',
        level: 3,
        parentCode: 99999,
      };
      const loopArr = (arr) => {
        for (let i = 0; i < arr.length; i++) {
          arr[i].label = this.$t(`apiCommon.${arr[i].skuCode}`)
          if (arr[i].children.length == 0) {
            arr[i].children = '';
          }
          if (arr[i].children.length > 0) {
            loopArr(arr[i].children);
          }
        }
        return arr;
      };
      let newList = copyList.filter((item) => {
        return item.designCode != 25001;
      });
      
      newList.forEach((item)=>{
        if ( !item.children.find((it) => { return it.designCode == 25001; }) ) {
          item.children.push(obj);
        }
      })
      loopArr(newList);
      if(this.needCombined) {
        newList.push({
          children: '',
          zhName: '联合修复',
          label: this.$t('orderList.order.union'),
          designCode: UNION_TYPE_CODE,
          enName: 'Combined Restorations',
          skuCode: UNION_TYPE_CODE,
          iconUrl: '',
          level: 1,
        });
      }
      return newList;
    },
    //select框值改变时候触发的事件
    changeSelect() {
      let valueList = [];
      let firstCode = [];//选中的一级code
      let firstStr = ''
      this.designTypeCodes.forEach((item) => {
        valueList = valueList.concat(
          item.filter((it, index) => {
            return index == item.length - 1;
          })
        );
        firstCode = firstCode.concat(
          item.filter((it, index) => {
            return index == 0;
          })
        )
      });
      firstCode = Array.from(new Set(firstCode))
      firstCode.forEach((item)=>{
        firstStr = firstStr += item + ','
      })
      if(firstStr.length > 0) firstStr = firstStr.slice(0,firstStr.length - 1)
      this.$emit('changeSearch', 'designType', valueList, this.designTypeCodes, firstStr);
    },
  },
};
</script>

<style lang="scss" scoped>
  .design-type-select {
  /deep/.el-cascader__tags .el-tag {
    width: 65%;
    background: #1d1d1f;
    color: #fff;
    &:last-child {
      width: 25%;
    }
    &:first-child {
      width: 65%;
    }
    .el-tag__close {
      background-color: #1d1d1f;
      &::before{
        font-size: 16px;
        margin-top: 1px;
      }
    }
  }
}
</style>

<style lang="scss">
.design-type-cascader {
  box-shadow: 0px 0px 16px rgba(18, 19, 20, 0.32), 0px 8px 24px rgba(18, 19, 20, 0.2), 0px 12px 32px rgba(18, 19, 20, 0.12);
  border: solid 1px #1d1d1f;
  background: #1d1d1f;
  .el-cascader-panel {
    background: #1d1d1f;
  }
  .el-cascader-menu {
    border-right: solid 1px #38393d;
    &:last-child {
      border-right: none;
    }
  }
  .el-cascader-node:not(.is-disabled):hover,
  .el-cascader-node:not(.is-disabled):focus {
    background: #262629;
  }
  .is-empty {
    height: 80px;
  }
}
.design-type-cascader[x-placement^='bottom'] .popper__arrow::after {
  border-bottom-color: #1d1d1f;
}
.design-type-cascader[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #1d1d1f;
}
</style>
