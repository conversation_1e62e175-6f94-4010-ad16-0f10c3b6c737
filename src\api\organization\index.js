import http from '../request'

/** 获取组织列表（业务层面异步，一级一级加载）
 * @params {
 *  "orgCode": 0,
 * } data
 */
export const getOrgList = (params) => {
  return http({
    url: '/user-basic/org/v1/list',
    method: 'POST',
    data: params
  })
}

/** 获取组织人员列表(包含下级组织人员)
 * @params {
 *  "orgCode": 0,  // 组织编码
 *  "pageNo": 0,  // 页码
 *  "pageSize": 0 // 每页数据条数
 * } data
 */
export const getStaffList = (params) => {
  return http({
    url: '/user-basic/org/v1/childUsersByPage',
    method: 'POST',
    data: params
  })
}

/** 添加部门
 * @params {
 *  "orgName": 0,     // 部门名称
 *  "parentCode": 0,  // 上级组织或部门的编码
 * } data
 */
export const addDept = (params) => {
  return http({
    url: '/user-basic/org/v1/addDept',
    method: 'POST',
    data: params
  })
}

/** 删除部门
 * @params {
 *  "orgCode": 0,     // 部门编码
 * } data
 */
export const deleteDept = (params) => {
  return http({
    url: '/user-basic/org/v1/delDept',
    method: 'POST',
    data: params
  })
}

/** 编辑部门（名称）
 * @params {
 *  "orgCode": 0,     // 部门编码
 *  "orgName": '', // 部门名称
 * } data
 */
export const editDept = (params) => {
  return http({
    url: '/user-basic/org/v1/editDept',
    method: 'POST',
    data: params
  })
}

/** 获取组织列表目录树（传一个组织/部门编号，可返回该组织/部门的所有下级目录结构）
 * @params {
 *  "orgCode": 0,
 * } data
 */
export const getAllOrgTree = (params) => {
  return http({
    url: '/user-basic/org/v1/tree',
    method: 'POST',
    data: params
  })
}

/** 重置用户密码
 * @params {
 *  "userCode": 0,  // 用户编码
 * } data
 */
export const resetPassword = (params) => {
  return http({
    url: '/user-basic/user/v1/resetPassword',
    method: 'POST',
    data: params
  })
}

/** 获取区号列表
 * @params {
 *  "orderBy": 0  // 排序 (0:中文名,1:英文名)
 * } data
 */
export const getAreaCodeList = (params) => {
  return http({
    url: '/user-basic/country/v1/list',
    method: 'POST',
    data: params
  })
}

/** 添加用户
 * @params {
 *  "email": "",
 *  "mobile": "",
 *  "mobilePrefix": "",
 *  "orgCode": 0,
 *  "roleCodes": [],
 *  "tenantCode": 0,
 *  "userName": ""
 * } data
 */
export const addUser = (params) => {
  return http({
    url: '/user-basic/org/v1/addUser',
    method: 'POST',
    data: params
  })
}

/** 编辑用户信息
 * @params {
 *  "email": "",
 *  "mobile": "",
 *  "mobilePrefix": "",
 *  "userCode": 0,
 *  "roleCodes": [],
 *  "realName": ""
 * } data
 */
export const editUser = (params) => {
  return http({
    url: '/user-basic/user/v1/editByUserCode',
    method: 'POST',
    data: params
  })
}

/** 删除用户
 * @params {
 *  "userCode": "", // 用户编码
 * } data
 */
export const deleteUser = (params) => {
  return http({
    url: '/user-basic/user/v1/del',
    method: 'POST',
    data: params
  })
}

/** 修改邮箱之前验证邮箱
 * @params {
 *  "email": "string"
 * } data
 */
export const regCheck = (params) => {
  return http({
    url: '/user-basic/org/v1/regCheck',
    method: 'POST',
    data: params
  })
}

/** 发送邮箱验证码
 * @params {
 *  "email": "string"
 * } data
 */
export const sendUpdateEmailCode = (params) => {
  return http({
    url: '/user-basic/user/v1/sendUpdateEmailCode',
    method: 'POST',
    data: params
  })
}
/** 修改邮箱
 * @params {
 *  "email": "string"
 * } data
 */
export const updateEmailByCode = (params) => {
  return http({
    url: '/user-basic/user/v1/updateEmailByCode',
    method: 'POST',
    data: params
  })
}
