import { getType } from "@/public/utils";
// 字符串转JSON
export const parseJson = (str) => {
  let result = str;
  if (typeof str === 'string') {
    try {
      result = JSON.parse(str);
    } catch (error) {
      console.log('parseJson str入参不是完整JSON格式的字符串',str);
      return null;
    }
  }
  return result;
};

import i18n from '@/public/i18n/index';
export const getI18nName = (item, i18nTitle, getI18nText, needReplace = true) => {
  const { zhName, name, enName, code } = item;
  if(!name) { 
    if(['yes','no'].includes(item)) {
      return i18n.t(`param.${item}`);
    }else {
      return i18n.t(`${i18nTitle}.${item}`);
    }
  }

  if (zhName && enName && getI18nText) {
    // const key = code ? code : name
    let en = enName;
    if(needReplace) {
      en = enName.replace(/\s+/g,'\n');
    }
    if (i18n.te(`apiCommon.${code}`)) {
      const content = i18n.t(`apiCommon.${code}`)
      // console.log('code-content: ', content);
      return content.replace(/\s+/g,'\n');
    }
    if (i18n.te(`apiCommon.${name}`)) {
      const content = i18n.t(`apiCommon.${name}`)
      // console.log('name-content: ', content);
      if (getType(content) === 'Object') {
        return content.default
      } else {
        return content.replace(/\s+/g,'\n');
      }
    }
    return en + '-没有翻译的'
  } else {  
    return i18n.t(`${i18nTitle}.${name}`);
  }
};

/**
 * 简单复制：当前复制是深复制，但无法对Date类型的值进行复制（会转成字符串）
 * @param {Oject} sourceObject  array也是oject的一种
 */
export const copy = (sourceObject) => {
  let result;
  if (sourceObject instanceof Object) {
    try {
      result = JSON.parse(JSON.stringify(sourceObject));
    } catch (error) {
      console.log('copy JSON.parse 转换JSON失败，sourceObject不是规范的JSON格式');
    }
  }
  return result;
};