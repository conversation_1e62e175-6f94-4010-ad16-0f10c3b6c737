import Mock from 'mockjs';
import data from './datas';
import order from './order';
import user from './user';
import heypoint from './heypoint'


import { server } from "../config";

const baseUrl = server.baseUrl;
Mock.mock(baseUrl+server.basicServer+'/getPage', data.orderList);
Mock.mock(baseUrl+server.basicServer+'/get/20202020202021','get', data.orderDetail);
// Mock.mock(baseUrl+server.orderServer+'/getOrderlist', 'post', order.getOrderlist);
// Mock.mock(baseUrl+server.orderServer+'/getStatus', 'post', order.getStatus);
Mock.mock(baseUrl+server.orderServer+'/getOrgList', 'post', user.getOrgDept);
// Mock.mock(baseUrl+server.databoardServer+'/managerDataKiosks', 'get', order.getManagerDataKiosks);

// 黑豆
// Mock.mock(baseUrl+server.heypointServer+'/getHeypointSetting', 'post', heypoint.heypointSettingList);

// 订单详情操作
Mock.mock(baseUrl+server.orderServer+'/batchOperate',data.operateOrder);
Mock.mock(baseUrl+server.orderServer+'/operate',data.operateOrder);
Mock.mock(baseUrl+server.orderServer+'/orderInfo','get',data.getOrderDetail);

export default Mock;