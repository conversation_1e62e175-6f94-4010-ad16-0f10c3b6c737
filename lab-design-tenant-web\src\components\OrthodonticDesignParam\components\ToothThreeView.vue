<template>
  <div class="three-canvas" :id="id"></div>
</template>

<script>
import ThreeView from '../js/threeView';
export default {
  name: 'toothThreeView',
  data() {
    return {};
  },
  props: {
    id: {
      type: String,
      default: ''
    },
		info: {
			type: Object,
			default(){
				return null
			}
		},
		mode: {
			type: String,
      default: 'ipr'
		}
  },
	mounted () {
		this.initModel();
	},
	watch: {
    info: {
      deep: true,
      handler(newVal, oldVal){
        if(oldVal.lower.length && this.threeView !== null){
          this.threeView.updateToothAndCard(newVal)
        }
      }
    }
  },
	methods: {
		// 加载模型
    initModel(){
      this.$nextTick(() => {
        const dom = document.getElementById(this.id);
        this.threeView = new ThreeView(dom, this.mode);
        this.threeView._getData(JSON.parse(JSON.stringify(this.info)))
      });
    },
	},
	destroyed () {
    this.threeView.destroyed();
  },
};
</script>

<style lang="scss" scoped>
.three-canvas{
	width: 100%;
	height:100%;
}
</style>
