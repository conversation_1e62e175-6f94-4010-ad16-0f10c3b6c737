<template>
  <div class="hg-img-checkbox">
    <div 
      v-for="(item, index) in data.ranges" 
      :key="item.name+index"
      :class="{'img-checkbox-item': true,'is-active': selectValue === item.name}"  
      @click="handleChange(item.name)">
      <span>{{ getI18nName(item, i18nTitle, $getI18nText) }}</span>
      <hg-pic :iconPath="item.iconPath"></hg-pic>
    </div>
  </div>
</template>

<script>
import { getI18nName } from '../utils';
import HgPic from './HgPic';

export default {
  components: { HgPic },
  model: {
    prop: 'value',
    event: 'update',
  },
  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          ranges: []
        }
      }
    },
    i18nTitle: { // i18n需要拼接成i18n国际化文本，数据库就不存太长
      type: String,
      default: '',
    },
    eventDisable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectValue: '',
    }
  },
  watch: {
    value(newValue) {
      if(newValue !== this.selectValue) {
        this.selectValue = newValue;
      }
    }
  },
  mounted() {
    this.selectValue = this.value;
  },
  methods: {
    getI18nName,
    handleChange(value) {
      if(this.eventDisable) return;

      if(this.selectValue !== value) {
        this.selectValue = value;
        this.$emit('update', value);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.hg-img-checkbox {
  display: flex;
  flex-flow: wrap;
  font-size: 12px;

  .img-checkbox-item {
    display: flex;
    flex-direction: column;
    width: 176px;

    span {
      flex: 1;
      display: flex;
      flex-direction: column-reverse;
      line-height: 16px;
    }

    .hg-pic {
      cursor: pointer;
      margin-top: 4px;
    }
  }

  .img-checkbox-item.is-active {
    color: $hg-secondary-primary;

    .hg-pic {
      border: 2px solid $hg-main-blue;
      /deep/.hg-pic-check {
        display: inline-block;
        border-color: $hg-main-blue $hg-main-blue transparent transparent;
  
        .el-icon-check {
          color:$hg-label;
        }
      }
    }
  }
}
</style>