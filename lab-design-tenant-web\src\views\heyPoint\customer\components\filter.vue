<template>
  <div class="customer-filter">
    <el-form :inline="true">
      <el-form-item :label="lang('customerNameNo')">
        <hg-input
          ref="orgName"
          class="available-credit"
          v-model="conditions.orgName"
          :placeholder="lang('customerNameNo')"
          clearable
          @change="searchData"
          @blur="searchData"
          @keyup.enter.native="searchData"
        ></hg-input>
      </el-form-item>
      <el-form-item :label="lang('availableCredit')">
        <el-select
          :placeholder="lang('availableCredit')"
          v-model="conditions.rangeType"
          filterable
          clearable
          @change="searchData"
          class="available-credit"
        >
          <el-option
            v-for="item in availableCreditOptions"
            :key="item.value"
            :label="lang(item.label)"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="lang('settlementType')">
        <el-radio-group
          v-model="conditions.settlementType"
          @change="searchData"
        >
          <el-radio :label="0">{{ lang('monthly') }}</el-radio>
          <el-radio :label="1">{{ lang('prepaidMonth') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <div class="operate-btn">
      <el-button
        type="primary"
        :loading="templateLoading"
        @click="downTemplate"
        >{{ lang('downloadImportTemplate') }}</el-button
      >
      <el-upload
        class="batch-gift"
        action="#"
        accept=".xls, .xlsx"
        :auto-upload="false"
        :show-file-list="false"
        :limit="1"
        :on-exceed="handleExceed"
        :on-change="uploadChange"
        :file-list="fileList"
      >
        <el-button type="primary" :loading="batchGiftLoading">{{
          lang('batchRechargeHeypoint')
        }}</el-button>
      </el-upload>
    </div>

    <el-dialog
      custom-class="batch-gift-dialog"
      append-to-body
      top="8vh"
      width="60vw"
      :title="lang('batchRechargeHeypoint')"
      :visible.sync="isShow"
      :close-on-click-modal="false"
      @close="onClose"
    >
      <hg-table
        class="batch-gift-table"
        maxHeight="100%"
        :hasIndex="true"
        :data="tableInfo.data"
        :header-data="tableInfo.headerData"
        :loading="tableInfo.loading"
        v-bind="$attrs"
      >
        <!-- 处理结果 -->
        <template #result="{ row }">
          <span :class="{ fail: row.status === false }">{{ row.result }}</span>
        </template>
      </hg-table>
    </el-dialog>
  </div>
</template>

<script>
import { availableCreditOptions } from '@/public/constants/heyPoint';
import { getLang } from '@/public/utils';
import { getRechargeTemplate, batchRechargeHeypoints } from '@/api/heypoint';
import { directDown } from '@/public/utils/file';
import hgTable from '@/components/HgTable';
import { mapGetters } from 'vuex';
import UploadNormalFile from '@/public/utils/uploadNormalFile';


export default {
  name: 'HeyPointCustomerFilter',
  props: {
    conditions: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    hgTable,
  },
  data() {
    return {
      availableCreditOptions: availableCreditOptions,
      templateLoading: false,
      batchGiftLoading: false,
      fileList: [],
      isShow: false,
      tableInfo: {
        loading: false,
        data: [],
        headerData: [
          {
            prop: 'orgSn',
            getLabel: () => {
              return this.lang('customerNo');
            },
          },
          {
            prop: 'num',
            getLabel: () => {
              return this.$t('heypoint.customer.operate.rechargeNum');
            },
          },
          {
            prop: 'recordNo',
            getLabel: () => {
              return this.$t('heypoint.customer.operate.remittanceRecords');
            },
          },
          // {
          //   prop: 'effectiveDate',
          //   getLabel: () => {
          //     return this.$t('heypoint.customer.operate.expiredday');
          //   },
          // },
          {
            prop: 'remark',
            getLabel: () => {
              return this.lang('remark');
            },
          },
          // {
          //   prop: 'contractOrderNo',
          //   getLabel: () => {
          //     return this.lang('crmOrderNo');
          //   },
          // },
          // {
          //   prop: 'contractNo',
          //   getLabel: () => {
          //     return this.lang('crmContractNo');
          //   },
          // },
          {
            prop: 'result',
            getLabel: () => {
              return this.lang('processResult');
            },
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(['orgCode']),
  },
  methods: {
    lang: getLang('heypoint.customer'),

    onClose() {
      this.isShow = false;
    },

    /**
     * 超过限制数的回调函数
     */
    handleExceed() {
      this.$message.warning(this.lang('batchGiveQtyLimit'));
    },

    /**
     * 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
     */
    async uploadChange(file) {
      try {
        this.batchGiftLoading = true;
        const fileIndex = file.name.lastIndexOf('.');
        const suffix = file.name.substr(fileIndex + 1);
        if (!['xls', 'xlsx'].includes(suffix)) {
          this.$message.error(this.lang('batchGiveFileLimit'));
          return false;
        }
        const uploadNormalFile = new UploadNormalFile({file: file.raw});
        uploadNormalFile.onEnd((data) => {
          file.pictureUrl = data.s3FileId;
          this.batchGift(data.s3FileId);
          this.batchGiftLoading = false;
        });
        uploadNormalFile.onError((data) => {
          console.error('data: ', data);
          this.batchGiftLoading = false;
        });
        uploadNormalFile.onStart();
      } catch (error) {
        console.log('error: ', error);
        this.fileList = [];
      }
    },

    /**
     * 下载批量赠送模板
     */
    async downTemplate() {
      try {
        this.templateLoading = true;
        const { data } = await getRechargeTemplate();
        directDown(data, `${this.lang('batchComplimentaryHeyPointsTemplate')}.xlsx`);
      } catch (error) {
        console.log('error: ', error);
      } finally {
        this.templateLoading = false;
      }
    },

    /**
     * 批量充值信用值
     */
    async batchGift(s3FileId) {
      try {
        this.batchGiftLoading = true;
        const { data } = await batchRechargeHeypoints({ fileId: s3FileId });
        this.tableInfo.data = data;
        this.isShow = true;
        console.log('data: ', data);
      } catch (error) {
        console.log('error: ', error);
      } finally {
        this.batchGiftLoading = false;
        this.fileList = [];
      }
    },

    /**
     * 搜索
     */
    searchData() {
      this.$emit('searchData', 'filter');
      if (this.$refs.orgName && this.$refs.orgName.focused) {
        this.$refs.orgName.blur();
      }
    },
  },
};
</script>

<style lang="scss" scope>
.customer-filter {
  display: flex;
  flex-wrap: wrap;
  .el-form {
    .el-form-item {
      margin-right: 24px;
      margin-bottom: 20px;
      .available-credit {
        width: 220px;
      }
    }
  }
  .operate-btn {
    display: flex;
    margin-bottom: 20px;
    .el-button {
      margin-right: 14px;
    }
  }
}
.batch-gift-dialog {
  .batch-gift-table {
    .fail {
      color: #ff5a5a;
    }
  }
}
</style>