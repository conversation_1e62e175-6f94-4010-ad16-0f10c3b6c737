import * as THREE from 'three'
import { createMatrix4 } from '../matrix4'
import { _box3 } from '../box3'
import { getAttributePositionBox3Vectors } from '../attribute'

export function getGeometryBox3(geometry) {
  if (!geometry.boundingBox) {
    geometry.computeBoundingBox()
  }

  const box3 = new THREE.Box3()
  box3.copy(geometry.boundingBox)
  return box3
}

// 获取几何体的一个方向轴的模长
export function getGeometryAxisLength(geometry, axis) {
  const box3 = getGeometryBox3(geometry)
  const { max, min } = box3
  return max[axis] - min[axis]
}

// 获取几何体的所有方向轴的模长
export function getGeometryLengths(geometry) {
  const box3 = getGeometryBox3(geometry)
  const { max, min } = box3

  return {
    x: max.x - min.x,
    y: max.y - min.y,
    z: max.z - min.z,
  }
}

// 物体坐标系几何中心(使用之前不得使用geometry.center(),因为已经居中过了)
export function getGeometryCenter(geometry) {
  const center = new THREE.Vector3()
  if (!geometry.boundingBox) {
    geometry.computeBoundingBox()
  }

  const box = _box3
  box.copy(geometry.boundingBox)
  box.getCenter(center)
  return center
}

// 得到几何体居中后变化的矩阵(使用之前不得使用geometry.center(),因为已经居中过了)
export function getGeometrySetCenterMatrix(geometry) {
  const center = getGeometryCenter(geometry)
  return createMatrix4(center.negate())
}

export function getGeometryBox3Vectors(geometry, options = {}) {
  const { matrix, range } = options

  const { attributes } = geometry
  const { position } = attributes

  const { maxs, mins } = getAttributePositionBox3Vectors(position, matrix)

  if (!range) {
    return { maxs, mins }
  }

  let startX, endX, startY, endY, startZ, endZ

  const { x, y, z } = range

  if (x) {
    const length = maxs.x.x - mins.x.x
    startX = mins.x.x + x[0] * length
    endX = mins.x.x + x[1] * length
  }

  if (y) {
    const length = maxs.y.y - mins.y.y
    startY = mins.y.y + y[0] * length
    endY = mins.y.y + y[1] * length
  }

  if (z) {
    const length = maxs.z.z - mins.z.z
    startZ = mins.z.z + z[0] * length
    endZ = mins.z.z + z[1] * length
  }

  {
    const { maxs, mins } = getAttributePositionBox3Vectors(position, matrix, () => {
      if (startX !== undefined) {
        if (x < startX || x > endX) {
          return false
        }
      }

      if (startY !== undefined) {
        if (y < startY || y > endY) {
          return false
        }
      }

      if (startZ !== undefined) {
        if (z < startZ || z > endZ) {
          return false
        }
      }

      return true
    })

    return { maxs, mins }
  }
}
