import { voidMatch, strictMatch } from '../logic'
import { hasOwn } from '../object/index'

// 模糊查询
export function fuzzyArrayFilter(list, queryParams) {
  return list.filter((item) => {
    for (const key in queryParams) {
      const value = queryParams[key]
      if (value) {
        if (!hasOwn(item, key)) {
          throw new Error(`不存在搜索字段${key}`)
        }
        if (item[key].indexOf(value) > -1) {
          return true
        } else {
          return false
        }
      }
      return true
    }
    return true
  })
}

// 匹配查询
export function matchArrayFilter(list, queryParams, strict) {
  return strict ? strictArrayMatchFilter(list, queryParams) : voidArrayMatchFilter(list, queryParams)
}

// 匹配查询(查询字段空值也匹配)
export function voidArrayMatchFilter(list, queryParams) {
  return list.filter((item) => {
    return voidMatch(item, queryParams)
  })
}

// 匹配查询(查询字段严格相等)
export function strictArrayMatchFilter(list, queryParams) {
  return list.filter((item) => {
    return strictMatch(item, queryParams)
  })
}
