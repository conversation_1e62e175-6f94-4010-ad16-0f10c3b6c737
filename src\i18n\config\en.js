/**
 * 英文文案，分模块备注
 */
module.exports = {
  // 个人中心模块
  personal: {
    back: 'Back',
    uploadHeader: 'Upload Profile Photo',
    changeHeader: 'Change Profile Photo',
    username: 'User ID',
    changeNamePlaceholder: 'Please enter your account.',
    password: 'Password',
    newPassword: 'New Password',
    oldPassword: 'Old Password',
    confrimPassword: 'Confirm Password',
    passwordPh: 'Please enter the password.',
    changeNewPwPh: 'Please enter the new password.',
    changePwPh: 'Please enter current password.',
    ensurePwPh: 'Please enter the password again.',
    change: 'Edit',
    usernamePlaceholder: 'Please enter your name.',
    name: 'Name',
    phone: 'Phone Number',
    phonePlaceholder: 'Please enter phone number.',
    areaCode: 'Zone Code',
    cancle: 'Cancel',
    save: 'Save',
    confrim: 'OK',
    email: 'Email',
    emailPlaceholder: 'Please enter your email.',
    uploadTip: 'Upload Specification:',
    uploadTip1: 'Only jpg, png, jpeg and other formats are supported;',
    uploadTip2: 'Image size must be larger than 100 * 100 pixels;',
    uploadTip3: 'Image size should not exceed 2MB;',
    phoneErro: 'Incorrect Phone Number Format.',
    phonenull: 'Cannot be empty.',
    emailErro: 'Incorrect Email Format.',
    realNameErro: 'Name is limited to 50 characters.',
    changePWSuccessTip: 'Change password successfully!',
    saveInfoSuccessTip: 'Save personal information successfully!',
    uploadFormatErro: 'Only jpg, png, jpeg and other formats are supported!',
    uploadSizeErro: 'Image size should not exceed 2MB!',
    uploadPXErro: 'Image size must be larger than 100 * 100 pixels!',
    changePasswordTitle: 'Change Password',
    passwordErro: 'Contain 8-16 characters and at least 3 of the following: lower- and upper-case letters, numbers and special characters.',
    ensurePasswordErro: 'The two passwords entered do not match.',
    newPasswordErro: 'Please enter a new password.',
    changeEmail: 'Change Email Address',
    newEmail: 'New Email',
    InputNewEmail: 'Please enter new email address.',
    emailCode: 'Verification Code',
    InputEmailCode: 'Please enter verification code.',
    sendCode: 'Send Code',
    emailError: 'Invalid email address!',
    emailExist: 'Email address existed! Please change a new one.',
    codeError: 'Wrong verification code!',
    emailNull: 'Email address is needed!',
    codeNull: 'Verification code is needed!',
    changeEmailSuccess: 'Updated e-mail successfully.'
  },
  // 组织架构模块
  org: {
    areaCode: 'Area Code',
    usernamePlaceholder: 'Please enter your name.',
    name: 'Name',
    email: 'Email',
    emailPlaceholder: 'Please enter your email.',
    phone: 'Phone Number',
    phonePlaceholder: 'Please enter your phone number.',
    orgStructure: 'Company',
    userList: 'Member List',
    searchTip: 'Enter user name, phone number, or email to search.',
    resetPassword: 'Password Reset',
    editUser: 'Edit User',
    deleteUser: 'Delete User',
    userId: 'User ID',
    userName: 'Name',
    role: 'Title',
    department: 'Department',
    operate: 'Operation',
    addNewDept: 'New Subordinate Departments',
    addDeptSuccessTip: 'Department added successfully!',
    editDeptNameSuccessTip: 'Department name edited successfully!',
    addUser: 'Add User',
    addUserSuccessTip: 'User Added successfully, the initial password is: ',
    copy: 'Copy',
    close: 'Close',
    deleteConfirm: 'Confirm to delete {orgText} [{orgName}]?',
    deleteBtn: 'Delete',
    organization: 'Organization',
    deleteDeptSuccessTip: 'Delete {orgText} successfully!',
    editUserSuccessTip: 'Edit successfully!',
    confirm: 'OK',
    resetPasswordConfirm: 'Please confirm whether you want to reset the password of [{realName}]?',
    resetSuccessTip: 'Reset successfully, the original password is: {password}',
    deleteUserConfirm: 'Confirm to delete user [{realName}]?',
    deleteUserSuccessTip: 'Delete user successfully!',
    rolePlaceholder: 'Please select the title.',
    phoneErro: 'Incorrect Phone Number Format',
    emailErro: 'Incorrect Email Format.',
    realNameErro: 'Name is limited to 50 characters.',
    deptPlaceholder: 'Please select the department.',
    departmentName: 'Department Name',
    deptNamePlaceholder: 'Please enter the name of department.',
    parentDept: 'Superior Department',
    deptNameErro: 'Please enter 1-20 digits of English, Chinese, numbers or common English special characters.'
  },
  // 客户管理模块
  customer: {
    title: 'Customer Management',
    areaCode: 'Zone Code',
    email: 'Email',
    emailPlaceholder: 'Please enter the email of principle.',
    phone: 'Phone Number',
    phonePlaceholder: 'Please enter the phone number of principle.',
    save: 'Save',
    password: 'Password',
    searchPlaceholder: 'Search by Client Name/ID.',
    addCustomerBtn: 'Add Customer',
    normal: 'Ordinary',
    directSales: 'Direct user',
    undirectSales: 'Non-direct user',
    disaster: 'Disaster recovery user',
    testUser: 'Trial user',
    approvalPending: 'Pending Review',
    statusUnpass: 'Failed',
    opened: 'Passed',
    disable: 'Disabled',
    enable: 'Enabled',
    disableText: 'disable',
    enableText: 'enable',
    allStatus: 'All Status',
    orgCode: 'Customer ID',
    orgSn: 'ID',
    orgName: 'Client Name',
    customLevel: 'Client Level',
    customType: 'Client Type',
    createdTime: 'Created Time',
    status: 'Status',
    edit: 'Edit',
    processSetting: 'Functional Configuration',
    customerOrg: 'Customer Organization',
    refresh: 'Refresh',
    refreshSuccessTip: 'List refresh successful!',
    addCustomSuccessTip: 'Adding successful!',
    addCustomErrorTip: 'Adding failed!',
    editCustomSuccessTip: 'Client basic info editing successful!',
    editCustomErrorTip: 'Client basic info editing failed!',
    companyName: 'Name',
    companyNamePlaceholder: 'Please enter your company name.',
    orgSnPlaceholder: 'Please enter number.',
    companyAddress: 'Adress',
    addressPlaceholder: 'Please enter your company adress.',
    leader: 'Principal',
    leaderPlaceholder: 'Please enter the name of the principle.',
    passwordPlaceholder: 'Please enter your password.',
    phoneErro: 'Incorrect Phone Number Format',
    emailErro: 'Incorrect Email Format.',
    passwordErro: 'Password must contail both letters,numbers and special symbols(between 6 and 20 characters).',
    goback: 'Back',
    editCutomer: 'Edit Client Info',
    regularInfo: 'Basic Information',
    customerNumber: 'Customer Account',
    number: 'Account',
    reset: 'Reset',
    detail: 'Detailed Info',
    resetPassword: 'Password Reset',
    confirm: 'OK',
    resetPasswordConfirm: 'Confirm to reset the password?',
    resetSuccessTip: 'Reset successful. Original password is: {password}',
    copy: 'Copy',
    close: 'Close',
    editCustomerTitle: 'Edit Client',
    operateConfirm: 'Confirm to {operate} the current client?',
    operateSuccessTip: '{operate} client successful!',
    designService: 'Design Service',
    import: 'Import',
    searchSKUPlaceholder: 'Please enter SKU name to saerch.',
    byTimes: 'Per Time',
    byMonth: 'Monthly',
    byYear: 'Annually',
    forever: 'Permanent',
    skuNumber: 'SKU',
    skuName: 'Name',
    currency: 'Currency',
    standardPrice: 'Standard Price',
    salePrice: 'Sale Price',
    settleTimes: 'Settlement Interval',
    updateTime: 'Update Time',
    relatePrice: 'Associated Quotation',
    open: 'Enable',
    skuConfirm: 'Confirm to {tipText} the SKU with the number [ {skuCode} ]?',
    operateSkuSuccessTip: '{tipText} successful!',
    uploadErro: 'Only excel file can be uploaded!',
    importSuccessTip: 'Import successful!',
    orgSnErro: 'Code cannot exceed 15 characters. Only lower- and upper-case letters, numbers and en dash (-) are supported.',
    s3Setting: 'S3 Configuration',
    s3Placeholder: 'Please select S3 node configuration.',
    settleTypes: 'Settlement',
    creditValue: 'Credit Value',
    creditPlaceholder: 'Please enter the credit value.',
    currencyType: 'Currency',
    currencyTypePlaceholder: 'Please select a currency.',
    settleByMonth: 'Monthly Settlement',
    settleByDeposit: 'Prepaid',
    discountRate: 'Discount Rate',
    saleman: 'Business Personnel',
    salemanPlaceholder: 'Please select the associated business person',
    pass: 'Pass',
    unpass: 'Fail',
    examineTitle: 'Review',
    examineTip: 'Confirm to {isPass} the current client?',
    discountRateErro: 'Please enter an integer from 0-100',
    pretreatment: 'Pre-processing',
    statistics: 'Data Stats',
    functionSetting: 'Design Service Function Configuration',
    priceSetting: 'Design Service Price Configuration (Unit: HeyPoints)',
    quickCreate: 'Quick Creation',
    bindDesignGroup: 'Binding Design Group',
    customerName: 'Client name: ',
    designType: 'Design Category',
    unit: 'Unit',
    urgentTime: '{num} hours',
    urgentTime24: '24 hours',
    quickSuccessTip: '{operate} the quick creation mode successfully!',
    openQuickOrderConfirm: 'Confirm to {open} the quick creation mode?',
    openQuickOrder: 'Enable',
    closeQuickOrder: 'Close',
    bindSuccessTip: 'Bind design group successfully!',
    enterTip: 'Design Category/Art No.',
    downloadPrice: 'Download price template',
    importedTip: 'Total {result} files, imported {succeed} files, failed {failed} files.',
    application: 'Application',
    materialID: 'Art No.',
    importedStatus: 'Status',
    No: 'No.',
    importPrice: 'Import price',
    price: 'Unit Price',
    urgent: 'Express Service',
    cancle: 'Cancel',
    confrim: 'OK',
    hours: 'hours',
    searchCRM: 'Search in CRM',
    syncByCRM: 'CRM number',
    syncSuccess: 'Synced',
    unSync: 'Unsynced',
    allCRMStatus: 'All',

    pay: 'Bank information',
    bankName: 'Bank Name',
    bankLocation: 'Bank Location Country',
    bankAdress: 'Bank Address',
    bankCode: 'SWIFT Code',
    bankAccouont: 'Account Name',
    bankNumber: 'Bank Account Number',
    bankError: 'Please select account.',
    remainding: 'Reminding',
    businessTips: 'Business Personnel is blank.',

    billFrom: 'Bill from',
    tel: 'Tel',
    mail: 'Mail',
    address: 'Address',

    basicconfig: 'Basic Config.',
    priceconfig: 'Price Config.',
    rush: 'Rush Permission',
    uploadPrice: 'Import price',
    drag: 'Drag file here or click to upload the price sheet.',
    uploadError: 'Please upload files in XLSL format.',
    serveice: 'Design Service',
    exportClient: 'Export Client',
    consume: 'Consumed',
    noConsume: 'No',
    pleaseSelect: 'Please select the client to export.',
    exporting: 'Exporting. Please be patient ...',
    exportError: 'Failed to export.',
    tableName: 'Client Information',
    notTax: 'Taxes not included',
    crm: 'Sync from CRM',
    designApplication: 'Design Application',
    artNo: 'Art No.',
    unitPrice: 'Unit Price',
    asycBtn: 'Sync',
    crmPrice: 'CRM Price',
    success: 'Succeeded',
    fail: 'failed',
    konw: 'OK',
    boundsCrm: 'The account has not bounded to CRM. Please bind it before syncing the price.',
    obtainPrice: 'Failed to obtain the price from CRM.',
    includesTax: 'Sure to include taxes?',
    noIncludesTax: 'Sure to not include taxes?',
    syncFaild: 'Sync failed. Please make sure this Cloud account and CRM share the same currency.',
    updatePrice: 'This is not the currency on CRM. Would you like to update the currency?',
    updateBtn: 'Update',

    categories: 'Design categories not included in the statistics',
    automatically: 'Automatically excludes the categories that do not need design',
    categoryfilter: 'Design category filter',
    designstatistics: 'Please select the categories that will not be included in the statistics',
    selected: '{num} categories selected',

    bussinessSelect: 'At most 1 business personnel can be selected.',
    area: 'Region',
    selectArea: 'Please select the region.',

    agencyuser: 'Agency user',
    memorySn: 'Account Code',
    memoryTips: 'Please enter'
  },
  // Agent Management module
  agent: {
    title: 'Agent Management',
    searchPlaceholder: 'Search by Agent Name/Code',
    addAgentBtn: 'Add Agent',
    edit: 'Edit',
    agentCode: 'Code',
    agentName: 'Name',
    agentScope: 'Agent Scope',
    deviceCount: 'Customer Count',
    operation: 'Operation',
    refresh: 'Refresh',
    refreshSuccessTip: 'List refresh successful!',
    addAgentSuccessTip: 'Agent added successfully!',
    addAgentErrorTip: 'Failed to add agent!',
    editAgentSuccessTip: 'Agent edited successfully!',
    editAgentErrorTip: 'Failed to edit agent!',
    exportTransaction: 'Export Transaction',
    noData: 'No data',
    manageAccount: 'Manage Account',
    viewDetail: 'View Details',
    getAgentInfoErrorTip: 'Failed to get agent info',
    missingOrgCode: 'Missing agent organization code',
    missingCurrency: 'Missing settlement currency',
    getAgentListErrorTip: 'Failed to get agent list',
    noScopeSet: 'Not set',
    device: 'Device',
    designService: 'Design Service',
    aiSoftware: 'AI Software',
    unknown: 'Unknown',
    agentNamePlaceholder: 'Enter keyword to select from existing CRM clients',
    agentNameRequired: 'Please enter agent name',
    agentCodePlaceholder: 'Please enter agent code',
    agentCodeRequired: 'Please enter agent code',
    agentScopePlaceholder: 'Please select agent scope',
    agentScopeRequired: 'Please select agent scope',
    agentScopeWarning: 'Cannot modify agent scope after submission',
    email: 'Email',
    emailPlaceholder: 'Please enter agent email',
    emailRequired: 'Please enter agent email',
    emailFormatError: 'Invalid email format',
    currency: 'Currency',
    currencyPlaceholder: 'Please select the currency used for agent income',
    currencyRequired: 'Please select currency',
    timezone: 'Timezone',
    timezonePlaceholder: 'Please select agent timezone',
    timezoneRequired: 'Please select timezone',
    address: 'Address',
    addressPlaceholder: 'Please enter agent address',
    leader: 'Principal',
    leaderPlaceholder: 'Please enter principal name',
    mobile: 'Phone',
    mobilePlaceholder: 'Please enter phone number',
    mobileFormatError: 'Invalid phone number format',
    areaCode: 'Area Code',
    businessUser: 'Business User',
    businessUserPlaceholder: 'Please select business user',
    businessUserRequired: 'Please select business user',
    techSupport: 'Tech Support',
    techSupportPlaceholder: 'Please select tech support',
    techSupportRequired: 'Please select tech support',
    deviceAgent: 'Device Agent',
    designServiceAgent: 'Design Service Agent',
    aiSoftwareAgent: 'AI Software Agent',
    customerList: 'Customer List',
    customerName: 'Customer Name',
    customerNamePlaceholder: 'Please enter customer name',
    getCustomerListErrorTip: 'Failed to get customer list'
  },
  // 头部模块
  header: {
    center: 'Personal Center',
    enterprise: 'Company Information',
    framework: 'Organizational Structure',
    language: 'Language Switching',
    out: 'Log-out',
    chinese: 'Chinese',
    english: 'English'
  },

  // 公共模块
  common: {
    loading: 'Loading...',
    noData: 'None',
    cancel: 'Cancel',
    submit: 'OK',
    confirm: 'OK',
    systemTip: 'Prompt',
    skipTo: 'Jump to page',
    page: '',
    searchTip: 'Please enter the keywords',
    selectPlaceholder: 'Please Choose',
    goback: 'Back',
    copySuccessTip: 'Copy Successfully!',
    loginFail: 'Login Failed',
    loginByOthers: 'Your account has been logged in, Please log in again! ({erroCode})',
    loginTimeOut: 'Login Expired',
    loginTimeOutTip: 'Your login information has expired, please log in again!',
    loginOutTip: 'Please log in again!({erroCode})',
    networkBusy: 'Access exception!({erroCode})',
    authError: 'No permission to access!({erroCode})',
    // networkBusy: 'Network busy, please try again! ({erroCode})',
    saveTip: 'Prompt',
    saveTipContext: 'The current page modifications have not been saved yet. Are you sure to leave this page? Leaving will discard these modifications',
    save: 'Save',
    reset: 'Reset'
  },

  // 时区
  timezone: {
    timezone: 'Time Zone',
    CN: 'Asia/Shanghai(UTC{utc})',
    JP: 'Asia/Tokyo(UTC{utc})',
    US: 'America/New_York(UTC{utc})',
    GB: 'Europe/London(UTC{utc})',
    RU: 'Europe/Moscow(UTC{utc})',
    AU: 'Australia/Sydney(UTC{utc})',
    CA: 'America/Vancouver(UTC{utc})',
    NE: 'Africa/Niamey(UTC{utc})',
    EG: 'Africa/Cairo(UTC{utc})',
    IN: 'Asia/Kolkata(UTC{utc})',
    BR: 'America/Sao_Paulo(UTC{utc})',
    FR: 'Paris(UTC{utc})',
    DE: 'Berlin(UTC{utc})',
    ES: 'Madrid(UTC{utc})',
    PL: 'Warsaw(UTC{utc})',
    IT: 'Rome(UTC{utc})',
    LT: 'Vilnius(UTC{utc})',
    BE: 'Brussels(UTC{utc})',
    SL: 'Freetown(UTC{utc})',
    timezoneErr: 'Please select a time zone.'
  },

  // lab租户端角色
  50015: 'Administrator',
  50016: 'Technician',
  50017: 'Operations',
  50018: 'System Administrator',
  50019: 'System Operations',
  50020: 'Design Operations',
  50021: 'Designer',
  50022: 'Technical Support',
  50023: 'Finance',
  50024: 'Dental Assistant',
  50031: 'Designer Team Leader',
  50032: 'Business Personnel',
  50033: 'IQC',
  50034: 'OQC',
  50035: 'DeviceProducer',
  50039: 'LC Administrator',

  // 错误码对应的相关提示
  ********: 'The same login account already exists!',
  ********: 'Wrong username or password!',
  ********: 'The account cannot be found!',
  ********: 'Organization (client, department) already exists!',
  ********: 'Organization (client, department) dose not exists!',
  ********: 'Department cannot be deleted, please remove the subordinate department or the person under the department first!',
  ********: 'Do not edit customer names!',
  ********: 'Program does not exist!',
  ********: 'Design type does not exist!',
  ********: 'SKU {message} does not exist!',
  ********: 'SKU not opened!',
  ********: 'Basic design parameters are not configured!',
  ********: 'Basic information notification parameters are not configured!',
  ********: 'Failed to get user data!',
  ********: 'File does not exist!',
  ********: 'Non-excel File!',
  ********: 'Form data is empty!',
  ********: 'Excel Parsing Failed!',
  ********: 'Format of the SKU {message} is wrong!',
  ********: 'Currency in SKU {message} does not exist!',
  ********: 'Template of SKU may be wrong!',
  ********: 'SKU code is repeated!',
  60027008: 'Discounted price of SKU {message} is empty!',
  60027009: 'Price of SKU {message} is empty!',
  65000056: 'Customer code already exists!',
  70100005: 'Log in from other places, please log out first!',
  65000009: 'Phone number existed！',
  // 信用值
  11010032: 'The maximum credit value cannot exceed {message}',
  65000014: 'New password cannot be the same as the old one.',
  65000015: 'Wrong password.'
  // 70100011, // 该账号不存在
  // 70100012, // 密码错误
  // 70100015, // 账号被禁用
}
