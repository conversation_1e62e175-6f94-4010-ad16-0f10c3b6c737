<template>
  <div v-if="show" class="set-process-box">
    <div class="header">
      <p class="back-btn border finger" @click="goBack"><i class="iconfont icon-arrow-back" />{{ $t('customer.goback') }}</p>
      <div class="nav-box">
        <span>{{ $t('customer.title') }}</span>
        <span>></span>
        <span>{{ $t('customer.processSetting') }}</span>
      </div>
    </div>
    <div class="header-title-box">
      <div class="header-title">
        <p
          v-for="(item, index) in funcList"
          :key="index"
          :class="['header-title-item', 'finger', curFunc === item.id ? 'active' : '']"
          @click="changeTab(item)"
        >
          {{ item.label }}
        </p>
      </div>
      <!-- v-permission="['editCustomer', 'disabled']" -->
      <!-- <VueButton v-if="curFunc===1" v-permission="['editCustomer', 'disabled']" type="primary" sizes="big" class="edit-btn" :width="110" :disabled="false" @click.native="editDesignService">{{ !designEditing ? $t('customer.edit') : $t('customer.save') }}</VueButton> -->
    </div>
    <DesignService ref="designServiceBox" :show.sync="curFunc" :customer="curCustomerObj" :editing.sync="designEditing" />
  </div>

</template>

<script>
import DesignService from './DesignService'
import { getCustomerInfo, clearNewFlag } from '@/api/customer'

export default {
  name: 'SetProcess',
  components: {
    DesignService
  },
  data() {
    return {
      show: false,
      funcList: [
        {
          label: this.$t('customer.designService'),
          id: 1
        },
        {
          label: this.$t('customer.pretreatment'),
          id: 2
        },
        {
          label: this.$t('customer.statistics'),
          id: 3
        }
        // {
        //   label: '其他',
        //   id: 0,
        // },
      ],
      curFunc: 1, // 当前功能配置项（设计服务1；前处理2；数据统计3）
      curCustomerObj: {},
      // 设计服务
      designEditing: false // false非编辑状态， true编辑状态
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    setTimeout(() => {
      const customerCode = this.$route.query.orgCode;
      const isBackupCrmOrg = this.$route.query.isBackupCrmOrg;
      this.isBackupCrmOrg = isBackupCrmOrg;
      clearNewFlag({ orgCode: customerCode })
      this.getCustomerInfoFunc(customerCode).then(() => {
        this.show = true
      })
    }, 50)
  },
  methods: {
    goNext(cb) {
      this.$Dialog({
        title: this.$t('common.saveTip'),
        message: this.$t('common.saveTipContext'),
        ensureBtnText: this.$t('customer.confirm'),
        confirmAction: () => {
          cb()
        }
      })
    },
    // 切换功能菜单
    changeTab(item) {
      if (item.id !== 1 && this.curFunc === 1 && this.designEditing) {
        const cb = () => {
          this.curFunc = item.id
          this.designEditing = false
        }
        this.goNext(cb)
      } else {
        this.curFunc = item.id
      }
    },
    goBack() {
      const cb = () => {
        this.show = false
        this.$router.push({ path: '/customer' })
      }
      this.designEditing ? this.goNext(cb) : cb()
    },
    // 获取客户信息
    getCustomerInfoFunc(code) {
      return new Promise((resolve) => {
        getCustomerInfo({
          'orgCode': code
        }).then((res) => {
          if (res.code === 200) {
            this.curCustomerObj = res.data;
            this.curCustomerObj.isBackupCrmOrg = this.isBackupCrmOrg
            resolve()
          }
        })
      })
    },
    // 点击编辑设计服务
    editDesignService() {
      this.designEditing = !this.designEditing
      if (!this.designEditing) {
        this.$refs.designServiceBox.saveData()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.set-process-box {
  // z-index: 2001;
  // height: 100%;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0;
  bottom: 0;
  background: $hg-background-color;
  padding: 14px 24px 24px 24px;
  // padding-bottom: auto;
  overflow: hidden;
  box-sizing: border-box;
  .header {
    display: flex;
    align-items: center;
    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 12px;
      height: $hg-height-32;
      color: $hg-secondary-fontcolor;
      i {
        margin-right: 8px;
      }
    }
    .nav-box {
      margin-left: 24px;
      span {
        color: $hg-secondary-fontcolor;
        &:nth-of-type(2) {
          margin: 0 12px;
        }
        &:nth-of-type(3) {
          color: $hg-primary-fontcolor;
        }
      }
    }
  }
  .header-title-box {
    display: flex;
    align-items: center;
    margin-top: 16px;
    .header-title {
      display: inline-block;
      background: $hg-main-black;
      height: 40px;
      padding: 0 12px;
      overflow: hidden;
      .header-title-item {
        float: left;
        margin-top: 4px;
        color: $hg-secondary-fontcolor;
        font-weight: bold;
        font-size: 16px;
        height: 32px;
        line-height: 32px;
        padding: 0 20px;
        background: $hg-main-black;
        border-radius: 2px;
        &.active {
          color: $hg-primary-fontcolor;
          background: $hg-main-blue;
        }
      }
    }
    .edit-btn {
      margin-left: auto;
    }
  }
}
</style>
