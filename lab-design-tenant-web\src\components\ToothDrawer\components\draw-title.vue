<template>
  <div class="draw-title">
    <span @click="handleClick"></span>
    <span class="text">{{ text }}</span>
    <!-- <span>&nbsp;</span> -->
  </div>
</template>
<script>
export default {
  name: 'DrawTitle',
  components: {},
  data() {
    return {}
  },
  props: {
    text: {
      type: String,
      default: '',
    },
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleClick() {
      this.$emit('close')
    },
  },
}
</script>
<style lang="scss" scoped>
.el-drawer__header {
  .draw-title {
    span:first-of-type {
      float: right;
      cursor: pointer;
      width: 24px;
      height: 24px;
      background: url(~@/assets/images/common/icon_close.svg) no-repeat;
    }
    .text {
      line-height: 24px;
      font-size: 16px;
      color: #E4E8F7;
    }
  }
}
</style>
