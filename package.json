{"name": "user_uc-center-web", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "heygears.com", "scripts": {"serve": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "dev": "cross-env NODE_ENV=dev vue-cli-service build --mode dev", "sit": "cross-env NODE_ENV=sit vue-cli-service build --mode sit", "uat": "cross-env NODE_ENV=uat vue-cli-service build --mode uat", "prod": "cross-env NODE_ENV=prod vue-cli-service build --mode prod", "usprod": "cross-env NODE_ENV=usprod vue-cli-service build --mode usprod", "tkyprod": "cross-env NODE_ENV=tkyprod vue-cli-service build --mode tkyprod", "parprod": "cross-env NODE_ENV=parprod vue-cli-service build --mode parprod", "cnprod": "cross-env NODE_ENV=cnprod vue-cli-service build --mode cnprod", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@types/overlayscrollbars": "^1.12.0", "aws-sdk": "^2.886.0", "axios": "0.18.1", "clipboard": "^2.0.8", "core-js": "^3.20.2", "cross-env": "^7.0.3", "crypto-js": "^4.0.0", "echarts": "^5.0.2", "element-ui": "2.13.2", "heygears-cloud-header": "^1.0.14", "heygears-ui": "^1.0.23", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "normalize.css": "7.0.0", "nprogress": "0.2.0", "overlayscrollbars": "^1.13.1", "overlayscrollbars-vue": "^0.2.2", "path-to-regexp": "2.4.0", "save": "^2.4.0", "vue": "2.6.10", "vue-i18n": "^8.24.2", "vue-router": "3.0.6", "vuex": "3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "cache-loader": "^4.1.0", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "postcss-pxtorem": "^5.1.1", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}