<template>
  <div>
    <el-dialog
      :title="lang('systemTips')"
      :visible.sync="selectCustomerDialog"
      width="600px"
      custom-class="select-customer"
      :before-close="cancelselect"
    >
      <div class="custom-content">
        <div class="tips"><span class="fuhao">*</span>{{lang('moreOrder')}}</div>
        <div class="custom-box">
          <el-radio-group v-model="selectOrder">
            <el-radio v-for="(order, index) in orderList" :label="order.id"><span class="custom">{{order.designUserName}}</span><span class="no">{{order.orderNo}}</span></el-radio>
          </el-radio-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelselect">{{lang('cancel')}}</el-button>
        <el-button :disabled="!selectOrder" type="primary" @click="selectOrderPage"
          >{{lang('submitBtn')}}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
export default {
  name: "selectCustomer",
  props: {
    oderShow: {
      type: Boolean,
      default: false
    },
    orderList: Array
  },
  data() {
    return {
      selectOrder: ''
    }
  },
  computed: {
    selectCustomerDialog: {
      get() {
        return this.oderShow;
      },
      set(val) {
        this.$emit("update:oderShow", val);
      },
    },
  },
  methods: {
    lang: getLang('designpoints'),
    selectOrderPage() {
      let nowOrder = this.orderList.find((item) => {return item.id == this.selectOrder})
      this.$emit('submitOrder', this.selectOrder, nowOrder);
      this.selectOrder = '';
    },
    cancelselect(){
      this.selectOrder = '';
      this.$emit('submitOrder')
    }
  },
};
</script>

<style lang="scss">
.select-customer{
  .custom-content{
    .tips{
      margin-bottom: 20px;
      color: #F3F5F7;
    }
    .fuhao{
      color: #E55353;
    }
    .custom-box{
      min-height: 200px;
      max-height: 350px;
      overflow: auto;
      width: 100%;
      background: #141519;
      border-radius: 8px;
      padding: 16px;
      .el-radio{
        display: flex;
        margin-bottom: 16px;
      }
      .el-radio__label{
        display: flex;
        flex-direction: column;
        .custom{
          color: #F3F5F7;
          margin-bottom: 10px;
        }
        .no{
          color: #9EA2A8;
          font-size: 12px;
        }
      }
    }
  }
  .el-dialog__footer{
    border-top: 1px solid #38393D;
    padding-top: 10px;
  }
}
</style>
