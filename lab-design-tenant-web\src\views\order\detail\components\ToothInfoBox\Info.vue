<template>
  <div class="tooth-info">
    <div class="tooth-info-content">
      <div class="tooth-image">
        <img v-if="showToothImg" :src="toothImageBase64? toothImageBase64 : toothImageUrl">
        <hg-icon v-else icon-name="icon-logo-heygears-lab" font-size="60px"></hg-icon>
      </div>
      
      <div class="tooth-info-list" v-if="toothInfo && toothInfo.length > 0">
        <div v-if="hasMulDesigner && isResponsibleDesigner" class="has-mul-designer">
          <div class="my-tooth-info">
            <div class="design-title">{{$t('order.detail.title.myDesign')}}</div>
            <template v-if="myToothInfo && myToothInfo.length > 0">
              <div class="tooth-box" v-for="(item, idx) in myToothInfo" :key="idx">
                <div class="tooth-item">
                  <img :src="toothIconObj[item.code]" alt="">
                  <span class="design-type">{{ $getI18nText({en :item.enName, zh: item.zhName}) }}</span>
                  <!-- 4.3.19新增[其他]类型，不显示牙位 -->
                  <span v-if="item.toothValue" class="tooth-position">{{item.isJaw ? '' : $t('order.detail.toothPosition') + '：'}}{{item.toothValue}}</span>
                </div>
                <!-- 这边一般是桥体 -->
                <template v-if="item.children && item.children.length">
                  <div class="tooth-child" v-for="child in item.children" :key="child.code">
                    <img :src="toothIconObj[child.code]" alt="">
                    <span class="design-type">{{ $getI18nText({en :child.enName, zh: child.zhName}) }}</span>
                    <span class="tooth-position" v-if="child.toothValue">{{ $t('order.detail.toothPosition') + '：'}}{{ child.toothValue }}</span>
                  </div>
                </template>
              </div>
            </template>
            <div v-else class="no-data">{{ $t('order.detail.noData') }}</div>
          </div>

          <div class="other-tooth-info">
            <div class="design-title">{{$t('order.detail.title.otherDesign')}}</div>
            <template v-if="otherToothInfo && otherToothInfo.length > 0">
              <div class="tooth-box" v-for="(item, idx) in otherToothInfo" :key="idx">
                <div class="tooth-item">
                  <img :src="toothIconObj[item.code]" alt="">
                  <span class="design-type">{{ $getI18nText({en :item.enName, zh: item.zhName}) }}</span>
                  <!-- 4.3.19新增[其他]类型，不显示牙位 -->
                  <span v-if="item.toothValue" class="tooth-position">{{item.isJaw ? '' : $t('order.detail.toothPosition') + '：'}}{{item.toothValue}}</span>
                </div>
                <!-- 这边一般是桥体 -->
                <template v-if="item.children && item.children.length">
                  <div class="tooth-child" v-for="child in item.children" :key="child.code">
                    <img :src="toothIconObj[child.code]" alt="">
                    <span class="design-type">{{ $getI18nText({en :child.enName, zh: child.zhName}) }}</span>
                    <span class="tooth-position" v-if="child.toothValue">{{ $t('order.detail.toothPosition') + '：'}}{{ child.toothValue }}</span>
                  </div>
                </template>
              </div>
            </template>
            <div v-else class="no-data">{{ $t('order.detail.noData') }}</div>
          </div>
        </div>
        <div v-else class="tooth-box" v-for="(item, idx) in toothInfo" :key="idx">
          <div class="tooth-item">
            <img :src="toothIconObj[item.code]" alt="">
            <span class="design-type">{{ $getI18nText({en :item.enName, zh: item.zhName}) }}</span>
            <!-- 4.3.19新增[其他]类型，不显示牙位 -->
            <span v-if="item.toothValue" class="tooth-position">{{item.isJaw ? '' : $t('order.detail.toothPosition') + '：'}}{{item.toothValue}}</span>
          </div>
          <!-- 这边一般是桥体 -->
          <template v-if="item.children && item.children.length">
            <div class="tooth-child" v-for="child in item.children" :key="child.code">
              <img :src="toothIconObj[child.code]" alt="">
              <span class="design-type">{{ $getI18nText({en :child.enName, zh: child.zhName}) }}</span>
              <span class="tooth-position" v-if="child.toothValue">{{ $t('order.detail.toothPosition') + '：'}}{{ child.toothValue }}</span>
            </div>
          </template>
        </div>
      </div>

      <div v-else class="no-data">
        {{ $t('order.detail.noData') }}
      </div>
    </div>
  </div>
</template>

<script>
import { getDownloadUrl } from '@/api/file';
import { TOOTH_DETAIL_ICON } from '@/public/constants/tooth';

export default {
  name: 'ToothInfo',
  props: {
    downLoadImagePath: String,
    imagePath: String,
    toothDesignList: Array,
    orgCode: Number,
    toothInfo: Array,
    isEdit: Boolean,
    toothImageBase64: String,
    hasMulDesigner: Boolean,
    isResponsibleDesigner: Boolean,
    myToothInfo: Array,
    otherToothInfo: Array
  },
  data() {
    return {
      toothImageUrl: '',
      downloadFail: false,
      toothIconObj: TOOTH_DETAIL_ICON
    }
  },
  computed: {
    showToothImg() {
      return (!this.downloadFail && this.toothImageUrl) || this.toothImageBase64;
    }
  },
  watch: {
    imagePath(value) {
      if(value) {
        this.initToothImage();
      }else {
        this.toothImageUrl = '';
      }
    }
  },
  mounted() {
    this.initToothImage();
  },
  methods: {
    initToothImage() {
      if(this.imagePath) {
        if(this.downLoadImagePath) {
          this.toothImageUrl = this.downLoadImagePath;
          return;
        }
        const param = {
          s3FileId: this.imagePath,
          orgCode: this.orgCode,
        };
        getDownloadUrl(param).then(res => {
          this.toothImageUrl = res.data.url;
          this.downloadFail = false;
          this.$emit('updateImagePath', this.toothImageUrl);
        }).catch(err => {
          this.downloadFail = true;
        });
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.tooth-info>.tooth-info-content {
  display: flex;

  .tooth-image {
    display: flex;
    margin-right: 24px;
    width: 400px;
    height: 400px;
    background: $hg-hover;

    img {
      margin: auto;
      width: 100%;
    }

    .hg-icon {
      margin: auto;
    }
  }

  .tooth-info-list {
    flex: 1;
    max-height: 400px;
    overflow-y: auto;
    .has-mul-designer {
      .my-tooth-info, .other-tooth-info {
        margin-bottom: 12px;
        .design-title {
          color: #AAADB3;
          margin-bottom: 12px;
          line-height: 16px;
        }
      }
    }
    .tooth-box {
      margin-bottom: 12px;
      .tooth-item {
        display: flex;
        align-items: center;
        padding: 18px 26px;
        height: 64px;
        background: $hg-hover;
        border-radius: 4px;
        .design-type {
          font-size: 16px;
          color: $hg-label;
          font-weight: bold;
          margin-left: 14px;
        }
        .tooth-position {
          color: $hg-label;
          margin-left: 12px;
        }
      }
      .tooth-child {
        display: flex;
        align-items: center;
        padding: 18px 26px 18px 42px;
        height: 64px;
        background: $hg-hover;
        border-radius: 4px;
        border-top: 1px dashed $hg-border;
        .design-type {
          font-size: 16px;
          color: $hg-grey;
          font-weight: bold;
          margin-left: 14px;
        }
        .tooth-position {
          color: $hg-grey;
          margin-left: 12px;
        }
      }
    }
  }

  .no-data {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 18px 26px;
    border-radius: 4px;
    height: 64px;
    background: $hg-hover;
  }
}

.tooth-info {
  .order-title {
    display: flex;
    justify-content: space-between;

    .edit-param {
      cursor: pointer;
      display: flex;
      font-size: 14px;
      .hg-icon {
        margin-right: 8px;
      }
    }
  }
}

</style>