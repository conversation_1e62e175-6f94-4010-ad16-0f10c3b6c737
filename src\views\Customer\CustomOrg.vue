<template>
  <div class="custom-org-box">
    <div class="header">
      <p class="back-btn border finger" @click="goBack"><i class="iconfont icon-arrow-back"></i>{{ $t('customer.goback') }}</p>
    </div>
    <OrgContent :curFatherOrgId="curCustomerOrg" :show.sync="showOrg" :customerOrg="curCustomerObj" :tableHeight="'calc(100vh - 340px)'"></OrgContent>
  </div>

</template>

<script>
import OrgContent from "@/views/Organization/OrgContent.vue";
import { getCustomerInfo, clearNewFlag } from "@/api/customer";

export default {
  name: "CustomOrg",
  components: {
    OrgContent
  },
  data() {
    return {
      showOrg: false,
      show: false,
      curCustomerObj: {},
      curCustomerOrg: ''
    }
  },
  computed: {
  },
  watch: {
    // show(val) {
    //   console.log('7777', val)
    //   setTimeout(() => {
    //     this.showOrg = val
    //   }, 10)
    // }
  },
  created() {
    // setTimeout(() => {
    this.curCustomerOrg = this.$route.query.orgCode
    if (this.curCustomerOrg) {
      this.getCustomerInfoFunc(this.curCustomerOrg).then(() => {
        // this.show = true
        this.showOrg = true
        clearNewFlag({ orgCode: this.curCustomerOrg })
      })
    }
    // }, 50)
  },
  mounted() {
  },
  methods: {
    getCustomerInfoFunc(code) {
      return new Promise((resolve) => {
        getCustomerInfo({
          "orgCode": code
        }).then((res) => {
          if (res.code === 200) {
            this.curCustomerObj = res.data
            resolve()
          }
        })
      })
    },
    goBack() {
      // this.show = false
      this.showOrg = false
      this.$router.push({ path: '/customer' })
      // this.$emit('update:show', false);
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-org-box {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 1px;
  left: 0;
  background: $hg-background-color;
  padding: 14px 24px 70px 24px;
  box-sizing: border-box;
  .header {
    margin-bottom: 14px;
    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 12px;
      height: $hg-height-32;
      color: $hg-secondary-fontcolor;
      i {
        margin-right: 8px;
      }
    }
  }
}
</style>
