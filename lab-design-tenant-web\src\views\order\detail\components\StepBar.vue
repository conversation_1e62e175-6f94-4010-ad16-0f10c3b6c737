<template>
  <hg-card class="order-step-bar">
    <div class="step-ul">
      <div 
        class="step-li"
        v-for="(item, index) in stepList" 
        :style="{'flex-basis': (100/stepCounts) +'%'}"
        :key="index" >
        <div :class="['step-li-box', item.class]">
          <div class="step-li-icon">
            <!-- <img :src="require(`@/assets/images/order/${item.icon}.svg`)" alt=""> -->
            <hg-icon :icon-name="item.icon" font-size="40px"></hg-icon>
            <div :class="'step-line'"></div>
          </div>
          <p>{{ $t(item.name) }} </p>
        </div>
      </div>
    </div>
  </hg-card>
</template>

<script>
import { ORDER_TYPES } from '@/public/constants';

export default {
  name: 'StepBar',
  props: {
    orderStatus: {
      type: [String,Number],
      require: true,
    },
  },
  data(){
    return {
      STEP_MAP: {
        ADD: { name: 'order.detail.step.new', icon: 'icon-step-add-lab', class: 'is-finish' },
        TRANSLATE: { name: 'order.detail.step.translate', icon: 'icon-step-translate-lab', class: 'is-process' },
        TRANSLATED: { name: 'order.detail.step.translate', icon: 'icon-step-translate-lab', class: 'is-finish' },
        DESIGN_WAIT: { name: 'order.detail.step.design', icon: 'icon-step-design-lab', class: 'is-wait' },
        DESIGNING: { name: 'order.detail.step.design', icon: 'icon-step-design-lab', class: 'is-process' },
        DESIGNED: { name: 'order.detail.step.design', icon: 'icon-step-design-lab', class: 'is-finish' },
        EXAMINE_WAIT: { name: 'order.detail.step.examine', icon: 'icon-step-examine-lab', class: 'is-wait' },
        EXMAINING: { name: 'order.detail.step.examine', icon: 'icon-step-examine-lab', class: 'is-process' },
        EXMAINED: { name: 'order.detail.step.examine', icon: 'icon-step-examine-lab', class: 'is-finish' },
        COMPLETE_WAIT: { name: 'order.detail.step.finish', icon: 'icon-step-complete-lab', class: 'is-wait' },
        COMPLETING: { name: 'order.detail.step.finish', icon: 'icon-step-complete-lab', class: 'is-wait' },
        COMPLETED: { name: 'order.detail.step.finish', icon: 'icon-step-complete-lab', class: 'is-finish' },
        REBACK: { name: 'order.detail.step.reback', icon: 'icon-step-reback-lab', class: 'is-finish__reback' },
        CONFIRM_REBACK_WAIT: { name: 'order.detail.step.rebacked', icon: 'icon-step-cofirm-back-lab', class: 'is-wait' },
        CONFIRM_REBACK: { name: 'order.detail.step.rebacked', icon: 'icon-step-cofirm-back-lab', class: 'is-process__reback' },
        CLIENT_EDIT: { name: 'order.detail.step.edit', icon: 'icon-step-reEdit-lab', class: 'is-wait' },
        CLIENT_RESUBMIT:  { name: 'order.detail.step.resubmit', icon: 'icon-step-reSubmit-lab', class: 'is-wait' },
        CLINET_ASK_FOR_FREE: { name: 'order.detail.step.askForFree', icon: 'icon-step-ask-free', class: 'is-ask__wait' },
        CLINET_ASK_FOR_FREE_FINISH: { name: 'order.detail.step.askForFree', icon: 'icon-step-ask-free', class: 'is-ask__finish' },
        FREE_FOR_CLIENT_WAIT: { name: 'order.detail.step.isFree', icon: 'icon-step-complete-lab', class: 'is-wait' },
        FREE_FOR_CLIENT:  { name: 'order.detail.step.isFree', icon: 'icon-step-complete-lab', class: 'is-finish' },
      },
      stepList: [],
    }
  },
  computed: {
    stepCounts(){
      return this.stepList.length;
    },

  },
  watch: {
    orderStatus(){
      this.init();
    }
  },
  mounted(){
    this.init();
  },
  methods: {
    init(){
      let stepList = [];
      const { ADD, TRANSLATE, TRANSLATED, DESIGN_WAIT, DESIGNING, DESIGNED, EXAMINE_WAIT, EXMAINING, 
        EXMAINED, COMPLETE_WAIT, COMPLETING, COMPLETED, REBACK, CONFIRM_REBACK_WAIT, CONFIRM_REBACK, CLIENT_EDIT, CLIENT_RESUBMIT,
        CLINET_ASK_FOR_FREE, CLINET_ASK_FOR_FREE_FINISH, FREE_FOR_CLIENT_WAIT, FREE_FOR_CLIENT } = this.STEP_MAP;
      switch(this.orderStatus) {
        case ORDER_TYPES.PENDING_TRANSLATE: stepList = [ADD, TRANSLATE, DESIGN_WAIT, EXAMINE_WAIT, COMPLETE_WAIT]; break;
        case ORDER_TYPES.PENDING_ACCEPT: 
        case ORDER_TYPES.PENDING_DESIGN:
        case ORDER_TYPES.DESIGNING: stepList = [ADD, TRANSLATED, DESIGNING, EXAMINE_WAIT, COMPLETE_WAIT]; break;
        case ORDER_TYPES.PENDING_REVIEW: stepList = [ADD, TRANSLATED, DESIGNED, EXMAINING, COMPLETE_WAIT]; break;
        case ORDER_TYPES.PENDING_CONFIRM: stepList = [ADD, TRANSLATED, DESIGNED, EXMAINED, COMPLETING]; break;
        case ORDER_TYPES.COMPLETED: stepList = [ADD, TRANSLATED, DESIGNED, EXMAINED, COMPLETED]; break;
        case ORDER_TYPES.PENDING_RETURN: stepList = [REBACK, CONFIRM_REBACK_WAIT, CLIENT_EDIT, CLIENT_RESUBMIT]; break;
        case ORDER_TYPES.RETURNED: stepList = [REBACK, CONFIRM_REBACK, CLIENT_EDIT, CLIENT_RESUBMIT]; break;
        case ORDER_TYPES.REQUEST_FREE: stepList = [CLINET_ASK_FOR_FREE, FREE_FOR_CLIENT_WAIT]; break;
        case ORDER_TYPES.APPLY_FREE: stepList = [CLINET_ASK_FOR_FREE_FINISH, FREE_FOR_CLIENT]; break;
        default: break;
      }
      this.stepList = stepList;
    }
  },
}
</script>

<style lang="scss" scoped>
.order-step-bar>.step-ul {
  display: flex;
  white-space: nowrap;

  .step-li{
    position: relative;
    flex-shrink: 1;
    .step-li-box {
      text-align: center;
      .step-li-icon {
        position: relative;
        width: 100%;
        >img{
          width: 40px;
          height: 40px;
        }
        .step-line {
          position: absolute;
          left: 50%;
          right: -50%;
          top: 75%;
          height: 2px;
          margin: 0 15%;
          background: linear-gradient(to right, $hg-main-blue, $hg-main-blue 5px, transparent 5px, transparent);
          background-size: 10px 100%;
        }    
      }

      &>p{
        padding-top: 10px;
        font-weight: bold;
      }
    }

    &:last-of-type{
      .step-line {
        display: none;
      }
    }
  }

  // 成功
  .is-finish.step-li-box{
    font-weight: bold;
    color: $hg-main-blue;
    .step-li-icon>.step-line {
      background: linear-gradient(to right, $hg-main-blue, $hg-main-blue 5px, transparent 5px, transparent);
      background-size: 10px 100%;
    }
  }
  .is-process.step-li-box {
    font-weight: bold;
    color: $hg-main-blue;
    .step-li-icon>.step-line {
      background: linear-gradient(to right, $hg-border-second, $hg-border-second 5px, transparent 5px, transparent);
      background-size: 10px 100%;
    }
  }
  // 等待中
  .is-wait.step-li-box {
    color: $hg-border-second;
    .step-li-icon>.step-line {
      background: linear-gradient(to right, $hg-border-second, $hg-border-second 5px, transparent 5px, transparent);
      background-size: 10px 100%;
    }
  }
  // 回退
  .is-process__reback.step-li-box{
    color: $hg-red;
    .step-li-icon>.step-line {
      background: linear-gradient(to right, $hg-border-second, $hg-border-second 5px, transparent 5px, transparent);
      background-size: 10px 100%;
    }
  }

  .is-ask__finish.step-li-box,
  .is-finish__reback.step-li-box {
    color: $hg-red;
    .step-li-icon>.step-line {
      background: linear-gradient(to right, $hg-red, $hg-red 5px, transparent 5px, transparent);
      background-size: 10px 100%;
    }
  }

  .is-ask__wait.step-li-box {
    color: $hg-red;
    .step-li-icon>.step-line {
      background: linear-gradient(to right, $hg-border-second, $hg-border-second 5px, transparent 5px, transparent);
      background-size: 10px 100%;
    }
  }
}
</style>