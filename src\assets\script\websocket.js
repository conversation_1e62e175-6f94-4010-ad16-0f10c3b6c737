let Socket = ''
let setIntervalWesocketPush = null
const wsUrl = 'wss://dev-svc.heygears.com/design-notify-service/design-notify-service/websocket/'
const token = window.localStorage.getItem('AccessToken') + '/1'
/**
 * 建立websocket连接
 * websocket状态：CONNECTING-0；OPEN-1；CLOSING-2；CLOSED-3
 * @param {string} url ws地址
 */
export const createSocket = () => {
  Socket && Socket.close() // 如果已经存在了连接则断开连接，重新创建一个长连接
  if (!Socket && wsUrl) {
    console.log('建立websocket连接')
    const url = wsUrl + token
    Socket = new WebSocket(url)
    Socket.onopen = onopenWS // 长连接已连上，可在onopenWS()做一些操作，例如定时发送心跳，检测连接是否还有效
    Socket.onmessage = onmessageWS // 接收websocket信息，可在onmessageWS()里处理信息
    Socket.onerror = onerrorWS  // 连接失败，可在onerrorWS()里重连
    Socket.onclose = oncloseWS // 连接关闭
  } else {
    console.log('websocket已连接，无需重连')
  }
}

/**打开WS之后发送心跳 */
const onopenWS = () => {
  sendPing()
}

/**WS数据接收统一处理（由于该方法需要监测实时获取数据，所以自定义成一个事件供调用） */
const onmessageWS = e => {
  window.dispatchEvent(new CustomEvent('onmessageWS', {
    detail: {
      data: e.data
    }
  }))
}

/**连接失败重连 */
const onerrorWS = () => {
  Socket.close()
  setIntervalWesocketPush ? clearInterval(setIntervalWesocketPush) : ''
  console.log('连接失败重连中')
  if (Socket.readyState !== 3) {
    Socket = null
    createSocket()
  }
}

/**
 * 发送数据但连接未建立时进行处理等待重发
 * @param {any} message 需要发送的数据
 */
const connecting = message => {
  setTimeout(() => {
    if (Socket.readyState === 0) {
      connecting(message)
    } else {
      Socket.send(JSON.stringify(message))
    }
  }, 1000)
}

/**
 * 发送数据（例如收到消息后需要做消息确认的时候会用到）
 * @param {any} message 需要发送的数据
 */
export const sendWSPush = message => {
  if (Socket !== null && Socket.readyState === 3) {
    Socket.close()
    createSocket()
  } else if (Socket.readyState === 1) {
    Socket.send(JSON.stringify(message))
  } else if (Socket.readyState === 0) {
    connecting(message)
  }
}

/**断开重连 */
const oncloseWS = () => {
  console.log('websocket已断开....正在尝试重连')
  setIntervalWesocketPush ? clearInterval(setIntervalWesocketPush) : ''
  Socket = null
  createSocket()
}
/**发送心跳
 * @param {number} time 心跳间隔毫秒 默认3000
 * @param {string} ping 心跳名称 默认字符串ping
 */
export const sendPing = (time = 3000, ping = 'ping') => {
  setIntervalWesocketPush ? clearInterval(setIntervalWesocketPush) : ''
  Socket.send(ping)
  setIntervalWesocketPush = setInterval(() => {
    if (Socket.readyState !== 2 && Socket.readyState !== 3) { // 连接状态才发ping
      Socket.send(ping)
      console.log('ping')
    }
  }, time)
}