import request from '../axios';
import { server } from '@/config';
const axios = request.axios;
/**
 * 获取上传文件url(非分片上传)
 * @param {Object} { md5:string 文件生成md5 , suffix:string 文件后缀suffix}
 */
export const getUploadUrl = ({ md5, suffix, orgCode, expirationInDays }) => {
  let param = {
    contentMd5: md5,
    objectSuffix: suffix,
    orgCode,
    isAccelerate: false,
  }
  if(expirationInDays !== undefined) {
    param.expirationInDays = expirationInDays;
  }
  return axios({
    url: `${server.normalUploadServer}/getUploadUrl`,
    method: 'POST',
    data: param,
    isRepeat: true
  });
};

/**
 * 完成上传操作
 * @param {sring} data 请求参数 
 */

export const completeUpload = (data) => {
  return axios({
    url: `${server.normalUploadServer}/completeUpload`,
    method: 'POST',
    data: {
      ...data,
      isAccelerate: false,
    }
  });
};

/**
 * 下载预授权
 * @param { Object } data 请求参数  {filename:自定义文件名, orgCode：用户组织code, s3FileId：s3FileId必传}
 */

 export const getDownloadUrl = (data, isHandlerError, timeout) => {
  return axios({
    url: `${server.normalUploadServerV3}/getDownloadUrl`,
    method: 'POST',
    data: {
      ...data,
      isAccelerate: false,
    },
    isHandlerError,
    timeout,
  });
};

/**
 * 批量获取下载预授权
 */
 export const getBatchDownloadUrl = (data) => {
  return axios({
    url: `${server.normalUploadServerV3}/getBatchDownloadUrl`,
    method: 'post',
    data:{
      downloadUrls: data,
      isAccelerate: false,
    }
  });
 };

/**
 * 获取分片上传预授权
 * @param {*} param0 
 */
export const getMultipartUploadInfo = ({fileName, md5, suffix, orgCode, size}) => {
  let param = {
    filename: fileName,
    md5,
    objectSuffix: suffix,
    orgCode,
    size,
    isAccelerate: false,
  };

  /* if(process.env.VUE_APP_ENVIRONMENT === 'dev') {
    param.expirationInDays = 1;
  } */

  return axios({
    url: `${server.multiUploadServer}/getMultipartUploadUrls`,
    method: 'POST',
    data: param,
  });
};

/**
 * 完成分片上传动作
 */
export const completeMultipartUpload = ({fileName, orgCode, s3Uid}) => {
  return axios({
    url: `${server.multiUploadServer}/completeMultipartUpload`,
    method: 'POST',
    data: {
      filename: fileName,
      orgCode,
      urlUuid: s3Uid,
      isAccelerate: false,
    }
  });
};

/**
 * 完成[片段]上传
 */
export const completePartUpload = ({ partMd5, partIndex, s3Uid }) => {
  return axios({
    url: `${server.multiUploadServer}/finishPart`,
    method: 'POST',
    data: {
      md5: partMd5,
      partIndex,
      urlUuid: s3Uid
    }
  });
};


/**
 * 终止分片上传动作
 * @param {*} s3Uid 
 */
export const abortMultipartUpload = (s3Uid) => {
  return axios({
    url: `${server.multiUploadServer}/abortMultipartUpload/${s3Uid}`,
    method: 'POST'
  });
};

/**
 * 新版：获取分片上传信息
 */
export const getMultipartUploadInfoV4 = ({ md5, size, fileName, sliceSize, suffix, partList, expireTime }) => {
  let data = {
    md5,
    size,
    filename: fileName,
    multipartSize: sliceSize,
    objectSuffix: suffix,
    partMd5s: partList,
    isAccelerate: false,
  };
  if(expireTime) {
    data.expirationInDays = expireTime;
  }
  return axios({
    url: `${server.multiUploadServerV4}/getMultipartUploadUrls`,
    method: 'POST',
    data,
  });
};

/**
 * 全部完成上传
 */
export const completeMultipartUploadV4 = ({fileName, s3Uid}) => {
  return axios({
    url: `${server.multiUploadServerV4}/completeMultipartUpload`,
    method: 'POST',
    data: {
      filename: fileName,
      urlUuid: s3Uid,
      isAccelerate: false,
    }
  });
};