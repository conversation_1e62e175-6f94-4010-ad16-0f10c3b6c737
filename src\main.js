import Vue from 'vue'
import '@/assets/style/reset.css'
import i18n from '@/i18n/index.js'
import locale from 'element-ui/lib/locale'
import App from './App'
import store from './store'
import router from './router'
import '@/lib/register.config.js' // 引入组件注册
import '@/assets/style/elementui-reset.scss' // css
import { getStore } from '@/assets/script/storage'
import { setTimezone } from '@/assets/script/formatDate.js'
import permission from '@/assets/script/permission.js'
// import './rem.js'

// 设置语言
locale.i18n((key, value) => i18n.t(key, value)) // 兼容element
Vue.prototype.i18n = i18n;

// 设置用户时区
const timezone = getStore('userInfo') ? (getStore('userInfo').timezone ? getStore('userInfo').timezone.tzName : null) : null;
setTimezone(timezone)

// 自定义指令，限制按钮点击事件，在结果返回以前不能继续点击
Vue.directive('btn-control', {
  // 插入dom 时做的事情
  inserted: function(el) {
    el.addEventListener('click', (e) => {
      if (!el.disabled) {
        el.style.pointerEvents = 'none'
        setTimeout(() => {
          el.style.pointerEvents = 'auto'
        }, 2000)
      } else {
        // disabled为true时，阻止默认的@click事件
        e.preventDefault()
        e.stopPropagation()
      }
    })
  }
})

// 自定义指令，设置按钮的权限
Vue.directive('permission', permission)
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

Vue.config.productionTip = false

new Vue({
  el: '#app',
  store,
  router,
  i18n,
  render: h => h(App)
})
