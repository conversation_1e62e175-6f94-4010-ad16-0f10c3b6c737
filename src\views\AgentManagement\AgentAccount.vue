<template>
  <div v-if="show" class="agent-account-box">
    <!-- 头部导航栏 -->
    <div class="header">
      <p class="back-btn border finger" @click="goBack">
        <i class="iconfont icon-arrow-back" />返回
      </p>
      <p class="header-title">账户<em>{{ agentInfo.agentName || '代理商' }}</em></p>
    </div>

    <!-- 自定义选项卡导航 -->
    <div class="header-tab">
      <span :class="['tab-item', activeTab === 'a2d_hd_4k' ? 'active' : '']" @click="changeTab('a2d_hd_4k')">A2D HD 4K</span>
      <!-- 预留其他选项卡 -->
      <!-- <span :class="['tab-item', activeTab === 'design_service' ? 'active' : '']" @click="changeTab('design_service')">设计服务</span>
      <span :class="['tab-item', activeTab === 'ai_software' ? 'active' : '']" @click="changeTab('ai_software')">AI软件</span> -->
    </div>

    <!-- 选项卡内容 -->
    <div class="tabs-content">
      <!-- A2D HD 4K 选项卡内容 -->
      <div v-if="activeTab === 'a2d_hd_4k'" class="tab-content">
        <!-- 主体内容 -->
        <div class="content-box">
          <!-- 左侧统计卡片区域 -->
          <div class="content-left">
            <div class="stats-container">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="iconfont icon-user"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-label">剩余授权数</div>
                  <div class="stat-value">{{ currentTabData.remainingAuth }}</div>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="iconfont icon-shopping-cart"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-label">总购买数</div>
                  <div class="stat-value">{{ currentTabData.totalPurchased }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧表格区域 -->
          <div class="content-right">
            <div class="custom-table">
              <!-- 表头 -->
              <div class="table-header">
                <div class="header-cell">操作时间</div>
                <div class="header-cell">事件类型</div>
                <div class="header-cell">终端客户</div>
                <div class="header-cell">设备SN</div>
              </div>
              <!-- 数据行 -->
              <div class="table-body">
                <div v-for="(record, index) in currentTabData.recentRecords" :key="index" class="table-row">
                  <div class="table-cell">{{ record.operationTime }}</div>
                  <div class="table-cell">{{ record.eventType }}</div>
                  <div class="table-cell">{{ record.customerName }}</div>
                  <div class="table-cell">{{ record.deviceSN }}</div>
                </div>
              </div>
              <!-- 查看更多 -->
              <div class="table-more">
                <span class="show-more" @click="openDrawer">查看更多 ></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预留其他选项卡内容 -->
      <!-- <div v-if="activeTab === 'design_service'" class="tab-content">
        <div class="coming-soon">
          <p>设计服务功能即将开放</p>
        </div>
      </div>
      <div v-if="activeTab === 'ai_software'" class="tab-content">
        <div class="coming-soon">
          <p>AI软件功能即将开放</p>
        </div>
      </div> -->
    </div>

    <!-- 授权记录抽屉组件 -->
    <AgentAuthorizationRecord
      :show.sync="showAuthRecord"
      :agent-info="agentData"
      :device-type="activeTab"
      :auth-records="currentTabData.allRecords || []"
    />
  </div>
</template>

<script>
import AgentAuthorizationRecord from './AgentAuthorizationRecord.vue'

export default {
  name: 'AuthRecord',
  components: {
    AgentAuthorizationRecord
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    agentData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      agentInfo: {
        orgCode: '',
        agentName: '',
        agentCode: '',
        userCode: '',
        email: '',
        mobile: '',
        mobilePrefix: '',
        status: '',
        tzCode: ''
      },
      // 当前激活的选项卡
      activeTab: 'a2d_hd_4k',
      // 授权记录抽屉显示状态
      showAuthRecord: false,
      // 各选项卡的数据
      tabsData: {
        a2d_hd_4k: {
          remainingAuth: 20,
          totalPurchased: 100,
          // 最近3条记录（在主界面显示）
          recentRecords: [
            {
              operationTime: '2024-01-15 14:30:25',
              eventType: '授权',
              customerName: '客户名称A',
              deviceSN: 'SN A2D001234567',
              operator: '张三'
            },
            {
              operationTime: '2024-01-15 13:25:18',
              eventType: '授权',
              customerName: '客户名称B',
              deviceSN: 'SN A2D001234568',
              operator: '李四'
            },
            {
              operationTime: '2024-01-15 11:45:32',
              eventType: '撤销授权',
              customerName: '客户名称C',
              deviceSN: 'SN A2D001234569',
              operator: '王五'
            }
          ],
          // 完整记录（在抽屉中显示）
          allRecords: [
            {
              operationTime: '2024-01-15 14:30:25',
              eventType: '授权',
              customerName: '客户名称A',
              deviceSN: 'SN A2D001234567',
              operator: '张三'
            },
            {
              operationTime: '2024-01-15 13:25:18',
              eventType: '授权',
              customerName: '客户名称B',
              deviceSN: 'SN A2D001234568',
              operator: '李四'
            },
            {
              operationTime: '2024-01-15 11:45:32',
              eventType: '撤销授权',
              customerName: '客户名称C',
              deviceSN: 'SN A2D001234569',
              operator: '王五'
            },
            {
              operationTime: '2024-01-14 16:20:45',
              eventType: '授权',
              customerName: '客户名称D',
              deviceSN: 'SN A2D001234570',
              operator: '赵六'
            },
            {
              operationTime: '2024-01-14 10:15:30',
              eventType: '授权',
              customerName: '客户名称E',
              deviceSN: 'SN A2D001234571',
              operator: '系统'
            }
          ]
        }
        // 预留其他选项卡数据结构
        // design_service: { ... },
        // ai_software: { ... }
      }
    }
  },
  computed: {
    // 当前选项卡的数据
    currentTabData() {
      return this.tabsData[this.activeTab] || {}
    }
  },
  watch: {
    show(val) {
      if (val && this.agentData) {
        // 从 props 接收数据，参考 EditAgent 的方式
        this.agentInfo = {
          orgCode: this.agentData.orgCode || '',
          agentName: this.agentData.orgName || this.agentData.agentName || '',
          agentCode: this.agentData.orgSn || this.agentData.agentCode || '',
          userCode: this.agentData.userCode || '',
          email: this.agentData.email || '',
          mobile: this.agentData.mobile || '',
          mobilePrefix: this.agentData.mobilePrefix || '+86',
          status: this.agentData.status || '',
          tzCode: this.agentData.tzCode || ''
        }

        console.log('代理商账户管理弹窗接收到的数据:', this.agentInfo)

        // 数据验证
        if (!this.agentInfo.orgCode) {
          console.warn('缺少必要的代理商组织编码')
          if (this.$MessageAlert) {
            this.$MessageAlert({
              text: '缺少代理商信息，请重新进入',
              type: 'warning'
            })
          }
        }
      }
    }
  },
  methods: {
    // 关闭弹窗，参考 EditAgent 的方式
    goBack() {
      this.$emit('update:show', false);
    },
    // 切换选项卡
    changeTab(tabType) {
      this.activeTab = tabType;
      console.log('切换到选项卡:', tabType);
      // 切换选项卡时关闭授权记录抽屉
      this.showAuthRecord = false;
    },
    // 打开授权记录抽屉
    openDrawer() {
      this.showAuthRecord = true;
      console.log('打开授权记录抽屉');
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '1': '正常',
        '0': '待审核',
        '-1': '审核不通过',
        '2': '禁用'
      }
      return statusMap[status] || '未知状态'
    }
  }
};
</script>

<style lang="scss" scoped>
.agent-account-box {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0;
  background: $hg-background-color;
  padding: 14px 24px 70px 24px;
  overflow-x: auto;
  overflow-y: hidden;

  .header {
    margin-bottom: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 12px;
      height: $hg-height-32;
      color: $hg-secondary-fontcolor;
      i {
        margin-right: 8px;
      }
    }

    .header-title {
      color: $hg-primary-fontcolor;
      font-size: 16px;
      font-weight: bold;
      flex: 1;
      margin-left: 24px;

      em {
        color: $hg-main-blue;
        font-style: normal;
        margin-left: 8px;
      }
    }

    .header-actions {
      .action-btn {
        background-color: $hg-main-blue;
        border: none;
        color: $hg-primary-fontcolor;
        font-size: 12px;
        height: 32px;
        padding: 0 16px;
      }
    }
  }

  .header-tab {
    position: relative;
    height: 40px;
    margin-bottom: 24px;

    .tab-item {
      position: relative;
      display: inline-block;
      min-width: 104px;
      padding: 0 16px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: $hg-secondary-fontcolor;
      cursor: pointer;
      text-align: center;
      border-radius: 4px;
      margin-right: 8px;
      transition: all 0.3s ease;

      &:hover {
        color: $hg-primary-fontcolor;
        background: $hg-hover-bg-color;
      }

      &.active {
        color: $hg-primary-fontcolor;
        background: $hg-main-blue;
      }
    }
  }

  .tabs-content {
    height: calc(100% - 64px);

    .tab-content {
      height: 100%;
    }

    .coming-soon {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: $hg-secondary-fontcolor;
      font-size: 16px;
    }
  }

  .content-box {
    display: flex;
    height: 100%;
    gap: 24px;

    .content-left, .content-right {
      height: 50%;
      border-radius: 4px;
      background: $hg-main-black;
      box-shadow: 0px 12px 32px 0px $hg-background-color,0px 8px 24px 0px $hg-background-color,0px 0px 16px 0px $hg-background-color;
      padding: 16px 24px;

      .title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        height: 40px;

        .title-text {
          color: $hg-primary-fontcolor;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }

    .content-left {
      width: 300px;
      flex-shrink: 0;

      .stats-container {
        height: 240px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 20px;

        .stat-card {
          display: flex;
          align-items: center;
          padding: 20px;
          background: $hg-background-color;
          border-radius: 8px;
          border: 1px solid $hg-border-color;
          height: 80px;

          .stat-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: $hg-main-blue;
            border-radius: 8px;
            margin-right: 20px;

            i {
              font-size: 24px;
              color: white;
            }
          }

          .stat-content {
            flex: 1;

            .stat-label {
              color: $hg-secondary-fontcolor;
              font-size: 14px;
              margin-bottom: 8px;
            }

            .stat-value {
              color: $hg-primary-fontcolor;
              font-size: 36px;
              font-weight: bold;
              line-height: 1;
            }
          }
        }
      }
    }

    .content-right {
      flex: 1;

      .custom-table {
        width: 100%;
        height: 240px;
        background: $hg-background-color;
        border-radius: 8px;
        padding: 16px;
        position: relative;

        .table-header {
          display: flex;
          margin-bottom: 8px;

          .header-cell {
            flex: 1;
            color: $hg-secondary-fontcolor;
            font-size: 14px;
            font-weight: normal;
            padding: 0 8px;

            &:first-child {
              flex: 1.2;
            }
            &:last-child {
              flex: 1.5;
            }
          }
        }

        .table-body {
          .table-row {
            display: flex;
            height: 48px;
            align-items: center;
            border-radius: 4px;
            margin-bottom: 4px;

            &:hover {
              background: $hg-hover-bg-color;
            }

            .table-cell {
              flex: 1;
              color: $hg-primary-fontcolor;
              font-size: 14px;
              padding: 0 8px;

              &:first-child {
                flex: 1.2;
              }
              &:last-child {
                flex: 1.5;
              }
            }
          }
        }

        .table-more {
          position: absolute;
          bottom: 16px;
          right: 16px;

          .show-more {
            color: $hg-main-blue;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;

            &:hover {
              color: $hg-button-hover-fontcolor;
            }
          }
        }
      }
    }
  }
}
</style>
