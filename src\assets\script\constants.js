/**
 * 公共常量
 */
 export const COMMON_CONSTANTS = {
  MAX_HEAD_IMG_SIZE: 2 * 1024 * 1024, // 上传头像大小最大为2MB
  MAX_HEAD_IMG_WIDTH: 100, // 上传头像最大宽度为100px
  MAX_HEAD_IMG_HEIGHT: 100, // 上传头像最大高度为100px
  IS_IMAGE_RULE: /image+/, // 判断是否是图片的正则表达式
  IS_EXCEL_RULE: /\xl.{1,2}$/, // 判断是否是excel的正则表达式
  // PHONE_RULE: /^(\+?0?86\-?)?1[345789]\d{9}$/, // 中国手机号码正则表达式
  PHONE_RULE: /^\d{0,11}$/, // 国际手机号码正则表达式（国家太多无法一一校验格式，只需要保证输入的是数字，不需要校验真实性，因此限制最多输入11位数字即可）
  EMAIL_RULE: /^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*\.[a-z]{2,}$/, // 邮箱正则表达式
  // PASSWORD_RULE: /^[a-zA-Z0-9!@#$%^&*()_+=|{}?><:,;."'`~\-\]\\[\/]{6,16}$/, // 密码正则表达式：6~16位，限制大小写英文、数字或常用字符(或关系)
  // PASSWORD_RULE: /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.])[\da-zA-Z~!@#$%^&*()_+`\-={}:";'<>?,.]{6,20}$/, // 密码正则表达式：6~20位，限制必须要英文+数字+特殊符号
  PASSWORD_RULE:/^(?![A-Za-z]+$)(?![A-Z\d]+$)(?![A-Z\W]+$)(?![a-z\d]+$)(?![a-z\W]+$)(?![\d\W]+$)\S{8,16}$/,
  NAME_RULE: /^[\u4e00-\u9fa5a-zA-Z0-9\s!@#$%^&*()_+=|{}?><:,;."'`~\-\]\\[\/]{1,20}$/, // 名称（部门名称、部门编号）正则表达式：必填大小写英文+中文+数字+常用英文特殊字符，限20位（或关系）
  CUSTOMER_CODE_RULE: /^[a-zA-Z0-9-]{1,15}$/, // 客户编码正则表达式：1~15位，限制大小写英文、数字和横杠-
  /** 特殊字符的正则 */
  SPECIAL_CHAR_REGEX: /[^a-zA-Z0-9！@￥……（）——【】；·‘’、：“|，。《》？_.`~!@#$%^&*()+\-=[\]{};':"|,.<>?/\\ \u4e00-\u9fa5]/g,
}
