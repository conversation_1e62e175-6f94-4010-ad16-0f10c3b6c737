<template>
  <div>
    <el-drawer class="tooth-dailog" 
      :modal-append-to-body="false" 
      :visible="isShow" 
      center 
      size="80%" 
      :show-close="false" 
      :append-to-body="true" 
      @open="openToothDialog" 
      @close="closeToothDialog">
      <template slot="title">
        <draw-title @close="closeToothDialog" :text="$t('order.add.tooth.title')"></draw-title>
      </template>
      <div class="wrapper">
        <div class="tab">
            <slot name="tab"></slot>
        </div>
        <div class="top-info">
          <div id="tooth" ref="tooth" class="tooth" @contextmenu.prevent="openMenu($event)">
            <ul v-show="menu.visible" :style="{ left: menu.left + 'px', top: menu.top + 'px' }" class="contextmenu">
              <li @click="deleteSelectList">{{$t('order.add.tooth.clear')}}</li>
            </ul>
            <span id="del" @click="deleteAllList">{{ $t('order.add.tooth.clearall') }}</span>
            <span id="num">index:{{ hoverToothNum | showToothNumBySystemIdx }}</span>
          </div>
          <div class="selection">
            <!-- language === 'zh' ? firstLevel.cnName : firstLevel.enName  $t(`apiCommon.${item.designCode}`) -->
            <right-select
              @select="selectDesignType"
              :canSelectCodes="canSelectCodes"
              :otherDesignerType="otherDesignerType"
              :firstLevelCode="firstLevel.designCode"
              :firstLevelName="$t(`apiCommon.${firstLevel.designCode}`)"
              :firstLevelIcon="firstLevel.iconUrl"
              :secondLevel="firstLevel.children"
              :key="firstLevel.uid"
              :hasOtherCategory="hasOtherCategory"
              v-for="firstLevel in DesignTypeList"
            ></right-select>
          </div>
        </div>
        <div class="bottom-detail">
          <tooth-info 
            ref="toothInfo"
            @deleteDesign="handleDelete"
            @updatePlantOptions="updatePlantOptions"
            v-if="toothDesign.length" 
            :toothSchemefromToothDesign="toothSchemefromToothDesign" 
            :otherDesignerType="otherDesignerType"
            :implantSystemObj="implantSystemObj"
          ></tooth-info>
          <no-tooth-info v-else></no-tooth-info>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import DrawTitle from './components/draw-title';
import ToothInfo from './components/tooth-info';
import NoToothInfo from './components/no-tooth-info';
import RightSelect from './components/right-select';

import { lowerNumbers, toothNumbers, upperNumbers } from './js/constant';
import rules from './js/rule';
import ThreeView from './js/threeView';
import { TOOTH_SELECT_ICON, TOOTH_DETAIL_ICON } from '@/public/constants/tooth.js';
import { getDesignTypeByDelivery } from '@/api/common';

export default {
  name: 'ToothDrawer',
  components: { RightSelect, DrawTitle, ToothInfo, NoToothInfo },
  props: {
    propToothDesign: Array,
    propCategoryCode: Number,
    orgCode: Number,
    deliveryCode: Number,
    hasOtherCategory: Boolean,
    otherDesignerType: Array,
    implantSystem: Object
  },
  data() {
    return {
      toothDesign: [],
      //右击牙位图菜单
      menu: {
        visible: false,
        top: 0,
        left: 0,
      },

      isShow: false,

      hoverToothNum: '',

      selectedTooths: [],
      canSelectCodes: [],
      currentSelectImplantForm: {},
      implantSystemObj: {}
    };
  },
  mixins: [rules], // rules存放牙位的校验规则
  computed: {
    ...mapState({
      designTypeTree: state => state.user.designTypeTree, //所有一级设计类型
      designCategory: state => state.order.designCategory, //当前一级设计类型
    }),
    /**
     * 一级类code
     */
    firstCategoryCode(){
      return this.propCategoryCode || this.designCategory.designCategoryCode
    },
    /**
     * 用于监听toothDesign数组长度变化
     */
    isChangeKey() {
      const len = this.toothDesign.length;
      const key = String(Math.random()) + String(len);
      return key;
    },

    /**
     *所有二级类型
     */
    DesignTypeList() {
      const designTypeTree = JSON.parse(JSON.stringify(this.designTypeTree));
      const list = designTypeTree.find(ele => ele.designCode === this.firstCategoryCode)?.children;
      // 过滤不显示的三级设计类型
      const thirdDesignTypeFilterList = [21301]
      if (!list) return [];
      const result = list.map(item => {
        item.iconUrl = TOOTH_SELECT_ICON[item.designCode];
        if (item.children) {
          item.children = item.children.filter(subItem => !thirdDesignTypeFilterList.includes(subItem.designCode))
          item.children.map(subItem => {
            subItem.iconUrl = TOOTH_SELECT_ICON[subItem.designCode] || TOOTH_SELECT_ICON['default'];
            return subItem;
          });
        }
        return item;
      });

      if (this.firstCategoryCode === 21000) {
        //如果一级是固定修复，二级按照特定规则排序
        const sortCode = [21100, 21200, 21500, 21400, 21300];
        result.sort((a, b) => {
          return sortCode.indexOf(a.designCode) - sortCode.indexOf(b.designCode);
        });
      }
      if (this.firstCategoryCode === 24000) {
        //如果一级是正畸
        let orthoItem = result.find(item => item.designCode === 24100); // 正畸项
        if (orthoItem) {
          //只显示 隐形正畸（简单）
          orthoItem.children = orthoItem.children.filter(chilItem => chilItem.designCode === 24102);
        }
      }
      console.log('result: ', result);
      return result;
    },
    /**
     * 所有三级设计类型
     */
    allThreeDesign() {
      return this.DesignTypeList.reduce((total, cur) => {
        return total.concat([...cur.children]);
      }, []);
    },
    // 获取牙位方案数据
    toothSchemefromToothDesign() {
      // 获取所有三级
      const allThreeDesign = this.allThreeDesign;
      const fullDenetureCodeList = [22201, 22202, 22203, 22204, 22301, 22302, 22303];

      let toothDesign = JSON.parse(JSON.stringify(this.toothDesign));
      toothDesign = toothDesign.map(item => {
        item.iconUrl = TOOTH_DETAIL_ICON[item.code];
        return item;
      });
      // 所有缺失位类型 牙冠-内冠-临冠-解剖型内冠 TODO:去除活动修复的牙冠缺失位和解剖型内冠缺失位
      const allMissingBit = [21106, 21107, 21203, 21204];

      // 冠code对应缺失位code
      const crownToMissing = {
        21101: 21106,
        21102: 21107,
        21201: 21203,
        21202: 21204,
        // TODO:去除活动修复的牙冠缺失位和解剖型内冠缺失位
        // 22601: 22602,
        // 22701: 22702,
      };

      // 缺失位code对应冠code
      const missingToCrown = {
        21106: 21101,
        21107: 21102,
        21203: 21201,
        21204: 21202,
        // TODO:去除活动修复的牙冠缺失位和解剖型内冠缺失位
        // 22602: 22601,
        // 22702: 22701,
      };
      const notToBrige = [21501, 23501];
      // 用来保存所有缺失位的信息
      let allMissingBitObjArr = [];
      // 遍历orderlist
      toothDesign.forEach(ele => {
        // 如果是缺失位类型
        if (!notToBrige.includes(ele.code)) {
          const toothArr = ele.tooth;
          // 遍历缺失位对象里面的牙齿
          toothArr.forEach(tooth => {
            let obj = {
              code: ele.code,
              pidCode: ele.pidCode,
              number: tooth,
              enName: ele.enName,
              zhName: ele.zhName,
              pidEnName: '',
              pidZhName: '',
            };
            allMissingBitObjArr.push(obj);
          });
        }

        if (fullDenetureCodeList.includes(ele.code)) {
          ele.jawList = []
          if (ele.tooth.some(item => upperNumbers.includes(item))) {
            ele.jawList.push({
              jaw: 'upper',
              tooth: ele.tooth.filter(item => upperNumbers.includes(item))
            })
          }
          if (ele.tooth.some(item => lowerNumbers.includes(item))) {
            ele.jawList.push({
              jaw: 'lower',
              tooth: ele.tooth.filter(item => lowerNumbers.includes(item))
            })
          }
        }
      });

      //所有牙桥的摇号
      let allInBrigeNum = [];

      // 找到牙桥，然后把牙桥里面做缺失位的拼接进去
      let tempUnshift = [];

      toothDesign.forEach(order => {
        if ([21501, 23501].includes(order.code)) {
          const toothArr = order.tooth;
          order.tooth2 = allMissingBitObjArr.filter(ele => {
            if (toothArr.includes(ele.number)) {
              allInBrigeNum.push(ele.number);
            }
            return toothArr.includes(ele.number);
          });
        }
        // 如果是冠就取冠的code并且取对应的缺失位code
        // code
        const code = order.code;
        // 缺失位code 有冠
        const missingCode = crownToMissing[code];

        // 根据缺失位取冠 有缺失位
        const crownCode = missingToCrown[code];

        // 判断有没有这个冠
        let hasThisCrown = true;

        if (crownCode) {
          hasThisCrown = toothDesign.some(ele => {
            return ele.code == crownCode;
          });
        }

        // 如果有缺失位但是没有冠
        if (!hasThisCrown) {
          let item = allThreeDesign.find(ele => {
            return ele.designCode == crownCode;
          });
          const toothItem = toothDesign
            .filter(ele => {
              return String(ele.code) == String(code);
            })
            .map(ele => {
              return ele;
            });

          item.code = item.designCode;
          item.zhName = item.cnName;
          item.tooth2 = toothItem;
          item.tooth = [];
          item.img = TOOTH_DETAIL_ICON[crownCode];
          item.sortTimeStamp = Date.now();
          tempUnshift.push(item);
        }
        // 如果有冠类型就拼接对应的缺失位到冠类型数据的tooth2字段
        if (missingCode) {
          const toothItem = toothDesign
            .filter(ele => {
              return String(ele.code) == String(missingCode);
            })
            .map(ele => {
              return ele;
            });

          // 缺失位类型数组
          order.tooth2 = toothItem || [];
        }
      });

      if (tempUnshift.length > 0) {
        toothDesign.unshift(...tempUnshift);
      }

      //将桥上得牙号去除
      toothDesign.forEach(order => {
        if (!notToBrige.includes(order.code) && ![23601].includes(order.code)) {
          order.tooth = order.tooth.filter(number => !allInBrigeNum.includes(number));
        }
      });

      // 把缺失位的对象过滤掉
      let temp = toothDesign.filter(ele => {
        return !allMissingBit.includes(ele.code);
      });

      let temp1 = [];
      temp1 = temp.filter(order => {
        if (order.tooth && order.tooth.length > 0) {
          return true;
        } else if (order.tooth2 && order.tooth2[0] && order.tooth2[0].tooth.length > 0) {
          return true;
        } else {
          return false;
        }
      });
      return temp1;
    },
  },
  watch: {
    toothDesign: {
      handler(newVal) {
        this.$nextTick(() => {
          this.viewerDraw(newVal);
        });
      },
      immediate: true,
      deep: true,
    },
    propToothDesign: {
      immediate: true,
      handler(newVal) {
        this.toothDesign = newVal.filter(item => item.code !== 25001); // 过滤未知
      }
    },
    deliveryCode: {
      immediate: true,
      handler(code) {
        if(code && this.orgCode) {
          const param = {
            orgCode: this.orgCode,
            deliveryCode: code
          };
          getDesignTypeByDelivery(param).then(res => {
            this.canSelectCodes = this.getChildren(res.data);
          });
        }
      }
    },
    implantSystem: {
      
      handler(val) {
        console.log('tooth,index,implantSystem-val', val)
        if (val && val.implantSystem) {
          this.implantSystemObj = val;
          this.currentSelectImplantForm = { ...val }
        }
      },
      immediate: true
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      const dom = document.getElementsByClassName('tooth')[0];
      this.threeView = new ThreeView(dom);
      this.threeView.addEventListener('selectTooth', tooths => {
        this.selectedTooths = tooths;
      });
      this.threeView.addEventListener('hoverTooth', number => {
        this.hoverToothNum = number;
      });
      // viewer的场景+模型加载完毕触发的回调, 加载完毕后才可以绘制场景
      this.threeView.addEventListener('viewerLoaded', () => {
        this.viewerDraw(this.toothDesign);
      });
    });
  },
  destroyed() {
    this.threeView.destroyed();
  },
  methods: {
    /**
     * 更新种植体数据
     * @param {*} options 
     */
    updatePlantOptions(options) {
      this.currentSelectImplantForm = {...options}
    },
    /**
     * 场景绘制
     */
    viewerDraw(toothDesign) {
      this.threeView && this.threeView.draw(toothDesign);
    },
    getChildren(dataList) {
      let resultList = [];
      dataList.forEach(data => {
        const { level, isEnable, children, designCode } = data;
        if(level === 3 && isEnable) {
          resultList.push(designCode);
        }
        if(children && children.length > 0) {
          resultList = resultList.concat(this.getChildren(children));
        }
      });
      return resultList;
    },
    /**
     * 
     * @param {Array} params 参数
     */
    handleDelete(params) {
      console.log('handleDelete-params: ', params);
      // 联合修复订单中，otherDesignerType为指派给其他设计师的设计类型，当前设计师不可编辑(删除)指派给其他设计师的设计类型
      if (params.some(item => this.otherDesignerType.includes(item.code))) {
        this.$message({
          type: 'warning',
          message: this.$t('order.detail.tips.otherDesignTips')
        })
        return
      }

      if (params.some(item => item.code === 23601)) {
        if (this.$refs.toothInfo) {
          this.$refs.toothInfo.resetImplantForm();
        }
        this.currentSelectImplantForm = {}
        this.$emit('resetImplantForm')
      }

      let toothDesign = this.toothDesign;
      // 冠code对应缺失位code
      const crownToMissing = {
        21101: 21106,
        21102: 21107,
        21201: 21203,
        21202: 21204,
      };
      params.forEach(param => {
        let { code: delCode, tooth: delToothList, tooth2 } = param;
        // 删除的选项中是否选择了固定修复的缺失位
        const missingDelCode = crownToMissing[delCode];
        const hasMissingTooth = missingDelCode && tooth2 && tooth2.length;
        const delIndex = toothDesign.findIndex(item => item.code === delCode);
        if (delIndex > -1) {
          if ([21501,23501].includes(delCode)) { // 如果是桥体
            // 找到包含桥体牙位的类型list
            let inBrideList = toothDesign.filter(item => item.tooth.some(tooth => delToothList.includes(tooth)));
            inBrideList.forEach(item => {
              item.tooth = item.tooth.filter(tooth => !delToothList.includes(tooth)); // 这里会直接把toothDesign里面对应条目的tooth改变
              if(item.tooth.length === 0) {
                const bDelIndex = toothDesign.findIndex(dItem => dItem.code === item.code); // 如果删完则直接删条目
                if(bDelIndex > -1) {
                  toothDesign.splice(bDelIndex, 1);
                }
              }
            });
          } else { // 删除其他
            const delItem = toothDesign[delIndex];
            if (delItem.tooth.length === delToothList.length) {
              toothDesign.splice(delIndex, 1);
            } else {
              delItem.tooth = delItem.tooth.filter(tooth => !delToothList.includes(tooth));
            }
          }
        }
        // 删除的选项中如果选择了缺失位，则从toothDesign找到缺失位的设计类型进行过滤
        if (hasMissingTooth) {
          this.toothDesign = this.toothDesign.filter(item => item.code !== missingDelCode)
        }
      });
    },

    /**
     * 模型绑定的右键鼠标事件
     * @param event 事件对象
     */
    openMenu(event) {
      this.menu.left = event.offsetX;
      this.menu.top = event.offsetY;
      this.menu.visible = true;
    },

    /**
     * 打开牙位图侧边弹窗
     */
    openToothDialog() {
      this.$nextTick(() => {
        const dom_container = document.getElementsByClassName('tooth')[0];
        dom_container.appendChild(this.threeView.renderer.domElement);
        // this.camera.aspect = parseInt(this.renderer.domElement.style.width) / parseInt(this.renderer.domElement.style.height);
        // this.camera.updateProjectionMatrix();
      });
    },

    /**
     * 关闭牙位图侧边弹窗
     */
    closeToothDialog() {
      this.toothDesign = this.toothDesign.filter(item => item.tooth.length > 0);

      const toothDesignCodes = this.toothDesign.map(item => item.code);
      // 如果选择了单孔牙支持式导板，但没有选择种植体系统，则不能关闭侧拉弹窗
      if (toothDesignCodes.some(item => [23601].includes(item)) && !this.currentSelectImplantForm.implantSystem) {
        this.$message.error(this.$t('order.detail.guide.implantSystemTips'));
        return
      }
      //  截图
      const base64 = this.threeView.getImage();
      // 关闭弹窗
      // this.isShow = false;
      this.$emit('updateToothDesign', this.toothDesign, base64, toothDesignCodes, this.currentSelectImplantForm);
    },

    setCurrentInfo() {
      const toothDesign = this.toothDesign.filter(item => item.tooth.length > 0);

      const toothDesignCodes = toothDesign.map(item => item.code);
      //  截图
      const base64 = this.threeView.getImage();
      console.log('setCurrentInfo-this.currentSelectImplantForm', this.currentSelectImplantForm)
      this.$emit('setCurrentInfo', toothDesign, base64, toothDesignCodes, this.currentSelectImplantForm);
    },

    /**
     * 删除数组
     * @param arr1 删除的目标数组
     * @param arr2 对应关系数组
     */
    cutArr(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        let temp = arr2[i];
        for (let j = 0; j < arr1.length; j++) {
          if (temp === arr1[j]) {
            arr1.splice(j, 1); // remove clone[j]
          }
        }
      }
    },

    /**
     * 模型右键点击清除
     */
    deleteSelectList() {
      let toothNameList = this.getToothNameList(this.selectedTooths);

      //桥体特殊删除
      for (let i = this.toothDesign.length - 1; i >= 0; i--) {
        let item = this.toothDesign[i];
        if (item.code == 21501 || item.code == 23501) {
          item.tooth.forEach((number, index) => {
            if (toothNameList.includes(number)) {
              toothNameList = [...toothNameList, ...item.tooth];
            }
          });
        }
      }

      for (let i = 0; i < this.toothDesign.length; i++) {
        this.cutArr(this.toothDesign[i].tooth, toothNameList);
      }
      
      this.toothDesign = this.toothDesign.filter(item => item.tooth.length > 0);

      this.menu.visible = false;
    },

    /**
     * 点击模型中的全部去除
     */
    deleteAllList() {
      console.log('this.toothDesign', this.toothDesign)
      // 联合修复订单中，otherDesignerType为指派给其他设计师的设计类型，当前设计师不可编辑(删除)指派给其他设计师的设计类型
      if (this.toothDesign.some(item => this.otherDesignerType.includes(item.code))) {
        this.$message({
          type: 'warning',
          message: this.$t('order.detail.tips.otherDesignLimitTips')
        })
        return
      }
      this.toothDesign = [];
      this.threeView.handleCurrentSelect();
    },
    /**
     * 获取牙号数组
     * @param selectedTooths 选择的牙位mesh数据
     */
    getToothNameList(selectedTooths) {
      let result = [];
      selectedTooths.forEach(mesh => {
        result.push(mesh.name);
      });
      return result;
    },
    /**
     * 用于对牙齿的牙号进行排序
     * @param list 牙齿数据
     */
    sortTooth(list) {
      if (!Array.isArray(list)) {
        return false;
      }
      let indexArr = [];
      list.forEach(ele => {
        const index = toothNumbers.indexOf(ele);
        if (index !== -1) {
          indexArr.push(index);
        }
      });

      let tooths = [];

      indexArr.sort((a, b) => a - b);
      indexArr.forEach(ele => {
        const tooth = toothNumbers[ele];
        tooths.push(tooth);
      });
      return tooths;
    },
    /**
     * 点击右边设计分类的图标
     * @param {Object} threeLevelDesignItem 点击的类型数据
     *
     * eg
     *  {
     *  children:[]
        cnName:"牙冠"
        designCode:21100
        enName:"Crown"
        hasParas:1
        iconUrl:"https://xxx/icon_牙位图_已选标签_解剖_牙冠.png"
        level:3
        parentCode:21100
       }
     */
    selectDesignType(threeLevelDesignItem) {
      // 牙号数组
      let toothNameList = this.getToothNameList(this.selectedTooths);
      const { designCode } = threeLevelDesignItem;

      if (designCode === 22101) {
        //半口支架等类型
        if (this.check4(toothNameList)) return;
        this.check1(threeLevelDesignItem, toothNameList);
      } else if (designCode === 22102) {
        // 1/4支架等类型
        if (this.check4(toothNameList)) return;
        this.check3(threeLevelDesignItem, toothNameList);
      } else if (designCode === 23403) {
        // 放射导板
        if (this.check4(toothNameList)) return;
        if (this.check18(threeLevelDesignItem,toothNameList)) return;
        this.check11(threeLevelDesignItem, toothNameList);
      } else if (designCode === 24406) {
        // 正畸带环
        if (this.check4(toothNameList)) return;
        if (this.check16(threeLevelDesignItem,toothNameList)) return;
        if (this.check17(threeLevelDesignItem,toothNameList)) return;
        this.check3(threeLevelDesignItem, toothNameList);
      }else if ([21501, 23501].includes(designCode)) {
        // 桥体
        if (this.check4(toothNameList)) return;
        if (this.check21(threeLevelDesignItem,toothNameList)) return;
        if (this.check24(threeLevelDesignItem,toothNameList)) return;
        this.check6(threeLevelDesignItem, toothNameList);
      } else if ([21403].includes(designCode)) {
        // 基牙
        if (this.check4(toothNameList)) return;
        this.check7(threeLevelDesignItem, toothNameList);
      } else if ([22501, 21303, 23401, 24102, 24301, 24302, 24402, 23106, 23404].includes(designCode)) {
        //托盘等上下颚类型
        if (this.check4(toothNameList)) return;
        if (this.check15(threeLevelDesignItem,toothNameList)) return;
        if (this.check16(threeLevelDesignItem,toothNameList)) return;
        if (this.check17(threeLevelDesignItem,toothNameList)) return;
        if (this.check18(threeLevelDesignItem,toothNameList)) return;
        if (this.check19(threeLevelDesignItem,toothNameList)) return;
        if (this.check20(threeLevelDesignItem,toothNameList)) return;
        if (this.check22(threeLevelDesignItem,toothNameList)) return;
        if (this.check23(threeLevelDesignItem,toothNameList)) return;
        this.check11(threeLevelDesignItem, toothNameList);
      } else if ([21402, 21404, 22401, 23301 , 24303, 24401, 24501, 22502].includes(designCode)) {
        //处理 不分割模型相关类型 活动修复模型等不需要选择牙齿相关类型
        if (this.check16(threeLevelDesignItem,toothNameList)) return;
        if (this.check18(threeLevelDesignItem,toothNameList)) return;
        if (this.check20(threeLevelDesignItem, toothNameList)) return;

        this.check2(threeLevelDesignItem);
      } else if ([22201, 22202, 22203, 22204].includes(designCode)) {
        //处理 全口义齿
        if (this.check4(toothNameList)) return;
        if (this.check5(threeLevelDesignItem,toothNameList)) return;
        this.check8(threeLevelDesignItem, toothNameList);
      } else if ([22301, 22302, 22303].includes(designCode)) {
        //处理 局部义齿
        if (this.check4(toothNameList)) return;
        if (this.check55(toothNameList)) return;
        this.check8(threeLevelDesignItem, toothNameList);
      } else if ([23104, 23105].includes(designCode)) {
        //处理 局部义齿
        if (this.check4(toothNameList)) return;
        if (this.check66(threeLevelDesignItem,toothNameList)) return;
        if (this.check18(threeLevelDesignItem,toothNameList)) return;
        this.check8(threeLevelDesignItem, toothNameList);
      } else if([25002,22503,23402,24405].includes(designCode)){
        // 杂项 其他
        this.checkOther(threeLevelDesignItem, toothNameList);
      } else if([23204].includes(designCode)){
        // 基台定位器
        if (this.check4(toothNameList)) return;
        if (this.check23(threeLevelDesignItem,toothNameList)) return;
        if (this.check24(threeLevelDesignItem,toothNameList)) return;
        this.check8(threeLevelDesignItem, toothNameList);
      } else if ([23601].includes(designCode)) {
        //单孔牙支持式导板
        if (this.check4(toothNameList)) return;
        if (this.check16(threeLevelDesignItem,toothNameList)) return;
        if (this.check17(threeLevelDesignItem,toothNameList)) return;
        if (this.check18(threeLevelDesignItem,toothNameList)) return;
        if (this.check19(threeLevelDesignItem,toothNameList)) return;
        if (this.check20(threeLevelDesignItem,toothNameList)) return;
        if (this.check21(threeLevelDesignItem,toothNameList)) return;
        if (this.check22(threeLevelDesignItem,toothNameList)) return;
        this.check8(threeLevelDesignItem, toothNameList);
      } else {
        //通用设计类型
        if (this.check4(toothNameList)) return;
        if (this.check16(threeLevelDesignItem,toothNameList)) return;
        if (this.check17(threeLevelDesignItem,toothNameList)) return;
        if (this.check18(threeLevelDesignItem,toothNameList)) return;
        if (this.check19(threeLevelDesignItem,toothNameList)) return;
        if (this.check20(threeLevelDesignItem,toothNameList)) return;
        if (this.check21(threeLevelDesignItem,toothNameList)) return;
        if (this.check22(threeLevelDesignItem,toothNameList)) return;
        this.check8(threeLevelDesignItem, toothNameList);
      }
      this.toothDesign = this.toothDesign.filter(item => item.tooth.length > 0); //过滤tooth为空得设计类型
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';
@import '~@/assets/styles/mixin.scss';
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-top: 0;
  overflow-y: auto;
}
.top-info {
  display: flex;
  flex-wrap: wrap;
  // margin-bottom: 20px;
  .tooth {
    margin-right: 24px;
    position: relative;
    margin-bottom: 20px;

    .contextmenu {
      position: absolute;
      left: 0;
      top: 0;
      background-color: #fff;
      font-size: 12px;
      color: #6f7376;
      box-shadow: 0 2px 16px 0;
      border-radius: 4px;
      li {
        padding: 7px 24px;
      }
    }
    #del {
      position: absolute;
      bottom: 20px;
      right: 20px;

      font-size: 12px;
      color: $hg-secondary-primary;
      cursor: pointer;
      &:after {
        position: absolute;
        left: -20px;
        top: -2px;
        content: '';

        display: block;
        width: 16px;
        height: 16px;
        background: url('../../assets/images/order/clearall.svg');
      }
    }

    #num {
      position: absolute;
      bottom: 20px;
      left: 20px;

      padding: 4px 10px;
      font-size: 12px;
      color: #83868f;
      background: #e4e8f714;
      border-radius: 12px;
    }
  }

  .selection {
    flex: 1;
    margin-bottom: 20px;
    min-width: 400px;
    // width: 46%;
    height: 400px;

    background-color: $hoverBgcColor;
    padding: 24px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.bottom-detail {
  flex: 1;
  min-height: 400px;
  margin-bottom: 50px;
  border-radius: 2px;
  overflow-x: auto;
  box-sizing: border-box;
  color: #6f7376;
  background-color: $hoverBgcColor;
  ul {
    @include clearfix;
    li {
      float: left;
    }
  }
  .card {
    .card-item {
      margin-bottom: 20px;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}
</style>
<style lang="scss">
.tooth-dailog {
  .el-drawer {
    .el-drawer__header {
      margin-bottom: 24px;
      background: #262629;
      font-size: 16px;
      padding: 18px 24px;
    }
    background-color: $mainColorDark;
    .el-drawer__body {
      height: calc(100vh - 44px);
      flex: 0 0 auto;
    }
  }
}
</style>
