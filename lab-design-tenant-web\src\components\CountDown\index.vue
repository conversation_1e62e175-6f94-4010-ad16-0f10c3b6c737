/** 用法：<CountDown :completeTime="" :createdTime="" :stamp="1654669519" :orderState="6" idMsTime="s"></CountDown>
参数：completeTime: 完成时间，createdTime： 创建时间，stamp：外部传入的时间戳，orderState：订单状态(注意订单状态需要去除设计师,简化业务判断)  
idMsTime: 传进来的时间戳是毫秒还是秒数*/ 

/* 倒计时组件 */
<template>
  <div class="count-down">
    <div v-if="isPage" class="count-down-in-page">
      <span :style="getColor"> <hg-icon icon-name="icon-reminder-lab"></hg-icon> {{ getText }} {{ countTime }}</span>
    </div>

    <div v-else :style="getColor">
      <span>{{ getText }}{{ countTime }}</span>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'countDown',
  data() {
    return {
      // 当订单状态在useTimCost里面就显示用时 7：待确认 8：已完成 9：已退回 11：已免单
      useTimeCost: [7, 8, 9, 11],
    };
  },
  props: {
    // 外部传入的时间戳，交期
    stamp: {
      type: Number,
      default: () => {
        return 0;
      },
    },
    completeTime: {
      // 外部传入的时间戳,完成时间
      type: Number,
      default: () => {
        return 0;
      },
    },
    createdTime: {
      // 外部传入的时间戳，创建时间
      type: Number,
      default: () => {
        return 0;
      },
    },
    // 传进来的是毫秒还是秒数 ms,s
    idMsTime: {
      type: String,
      default: 'ms',
    },
    // 订单状态key值，用于获取颜色以及文字
    orderState: {
      type: [Number],
      default: () => {
        return '';
      },
    },
    isPage: Boolean,
    isurgent: { //是否是数据看板得加急订单
      type: Boolean, 
      default: false
    }
  },
  computed: {
    ...mapGetters(['currentTime', 'language']),
    /**
     * 倒计时
     */
    countTime() {
      if (!this.stamp) {
        return '';
      }

      // 如果订单状态是useTimeCost里面的并且有用时就直接在页面显示用时
      const orderState = this.orderState;

      // 有用时字段，但是没完成时间
      if (this.useTimeCost.indexOf(orderState) > -1 && this.completeTime == 0) {
        return this.isPage ? '--' : '';
      }

      let result = 0;
      if (this.useTimeCost.indexOf(orderState) > -1 && this.completeTime != 0) {
        // [显示用时] 完成时间 - 创建时间
        result = Math.abs(this.completeTime - this.createdTime);
      } else {
        result = this.idMsTime == 'ms' ? Math.abs(this.currentTime - this.stamp) : Math.abs(this.currentTime - this.stamp * 1000);
      }

      //   获取时间戳相减的秒数
      const seconds = result / 1000;
      //   获取小时 分钟 秒数
      const hour = parseInt(seconds / 3600);
      let minute = parseInt((seconds % 3600) / 60);
      let second = parseInt(((seconds % 3600) % 60) % 60);
      minute = String(minute).padStart(2, 0);
      second = String(second).padStart(2, 0);

      if (hour > 2000) {
        return '00:00:00';
      }
      // 处理好之后返回，渲染页面
      return `${hour} : ${minute} : ${second}`;
    },
    /**
     * 获取颜色
     */
    getColor() {
      const colorObj = {
        out: '#FD5C64',
        rest: '#FFB22C',
        complete: '#F7F8FA',
      };

      // 符合显示‘用时’条件
      const orderState = this.orderState;

      if (this.useTimeCost.indexOf(orderState) > -1) {
        return { color: colorObj.complete };
      }
      let newStamp = this.idMsTime == 'ms' ? this.stamp : this.stamp * 1000;
      const isOverTime = newStamp < this.currentTime;
      if (isOverTime) {
        return { color: colorObj.out };
      }
      return this.isurgent ? { color: '#ffffff' } : { color: colorObj.rest };
    },
    // 获取文本
    getText() {
      let textObj = {};
      if (this.language === 'en') {
        textObj = {
          out: 'Delayed ',
          rest: 'Remaining ',
          complete: 'TotalTime ',
        };
      } else {
        textObj = {
          out: '超时 ',
          rest: '剩余 ',
          complete: '用时 ',
        };
        if(this.isPage) {
          if(this.language === 'en') {
            textObj = {
              out: 'Order Delayed: ',
              rest: 'Order Time Left: ',
              complete: 'Order Time Used: ',
            };
          }else {
            textObj = {
              out: '订单时间超时：',
              rest: '订单剩余时间：',
              complete: '订单已用时间：',
            };
          }
        }
      }

      const orderState = this.orderState;

      if (this.useTimeCost.indexOf(orderState) > -1 && this.completeTime == 0) {
        // 没有完成时间
        return this.isPage ? textObj.complete : '';
      }

      // 符合显示‘用时’条件
      if (this.useTimeCost.indexOf(orderState) > -1) {
        return textObj.complete;
      }
      let newStamp = this.idMsTime == 'ms' ? this.stamp : this.stamp * 1000;
      const isOverTime = newStamp < this.currentTime;
      if (isOverTime) {
        return textObj.out;
      }
      // 显示超时
      return textObj.rest;
    },
  },
};
</script>

<style lang="scss" scoped>
.count-down {
  width: 100%;
}
.count-down>.count-down-in-page {

  span {
    display: flex;
    align-items: center;
    font-weight: bold;
    .hg-icon {
      margin-right: 12px;
      font-size: 24px;
    }
  }
}
</style>
