import Vue from 'vue'
import axios from 'axios'
import { getStore, setStore } from '@/assets/script/storage.js'
import i18n from '@/i18n/index.js'
import { NETWORK_STATUS, AUTH_STATUS, LOGIN_ERR_STATUS } from '@/assets/script/map'
import { redirectLogin } from '@/assets/script/token.js'
import { baseUrl } from './baseurl.config'

const $t = (key, value) => i18n.t(key, value)

// 需要弹窗提示重新登录的错误状态
const loginErrors = [
  LOGIN_ERR_STATUS.REMOTE_LOGIN,
  LOGIN_ERR_STATUS.TOKEN_TIMEOUT,
  LOGIN_ERR_STATUS.TOKEN_CHECK_FAILURE,
  LOGIN_ERR_STATUS.ACCESSTOKEN_TIMEOUT,
  LOGIN_ERR_STATUS.ACCESSTOKEN_NULL,
  LOGIN_ERR_STATUS.REFRESHTOKEN_TIMEOUT,
  LOGIN_ERR_STATUS.USER_CANCEL_1,
  LOGIN_ERR_STATUS.USER_CANCEL_2,
  LOGIN_ERR_STATUS.USER_INFO_TIMEOUT,
  LOGIN_ERR_STATUS.TOKEN_NULL
]

// 需要弹窗提示重新登录的错误状态
const authErrors = [
  AUTH_STATUS.TOKEN_AUTHORITY_FAILURE_1,
  AUTH_STATUS.TOKEN_AUTHORITY_FAILURE_2,
  AUTH_STATUS.TOKEN_AUTHORITY_FAILURE_3
]

// 网络错误提示
const networkStatusError = [
  NETWORK_STATUS.FAILURE,
  NETWORK_STATUS.SERVICE_ERROR,
  NETWORK_STATUS.PARAM_ERROR,
  NETWORK_STATUS.PERMISSION_DENIED,
  NETWORK_STATUS.DABS_ERROR,
  NETWORK_STATUS.REDIS_ERROR,
  NETWORK_STATUS.SERVICE_FAILURE,
  NETWORK_STATUS.LOGIN_FAIL,
  NETWORK_STATUS.REFERER_FAILURE,
  NETWORK_STATUS.CONNECT_FAILURE,
  NETWORK_STATUS.REFERER_NULL,
  NETWORK_STATUS.BUSINESS_ERRO,
  NETWORK_STATUS.SERVICE_ERROR_2
]

// 防止连续展示的不好体验
let loginError = false
let authError = false
let networkError = false

const http = axios.create({
  baseURL: baseUrl,
  withCredentials: true,
  headers: {
    'content-type': 'application/json;charset=utf-8',
    'Accept': 'application/json'
    // 'x-hg-user': 'eyJ1c2VyQ29kZSI6MTAwMDA4LCJ0ZW5hbnRDb2RlIjoxMDAwLCJzb2x1dGlvbkNvZGUiOjIwMDAwNSwicm9sZUNvZGVzIjpbNTAwMDEsNTAwMDBdLCJtZW51Q29kZXMiOls3MDAwNiw3MDAwMl0sImRhdGFDb250cm9sIjpbOSw5XSwiY29uc3VtZXJDb2RlIjoxMDAwMDEsIm9yZ0luZm8iOlt7Im9yZ0NvZGUiOjEwMDAwMSwicHJlT3JnQ29kZSI6bnVsbCwic3ViT3JnQ29kZXMiOlsxMDAwMDcsMTAwMDExLDEwMDAwNl0sImJyb09yZ0NvZGVzIjpbXX0seyJvcmdDb2RlIjoxMDAwMDcsInByZU9yZ0NvZGUiOjEwMDAwMSwic3ViT3JnQ29kZXMiOlsxMDAwMDgsMTAwMDA5XSwiYnJvT3JnQ29kZXMiOlsxMDAwMTEsMTAwMDA2XX0seyJvcmdDb2RlIjoxMDAwMDksInByZU9yZ0NvZGUiOjEwMDAwNywic3ViT3JnQ29kZXMiOltdLCJicm9PcmdDb2RlcyI6WzEwMDAwOF19XX0='
  },
  timeout: 1000 * 90
})

// 添加请求拦截器,在发送请求之前做些什么
http.interceptors.request.use(config => {
  config.headers.authorization = 'Bearer ' + (getStore('AccessToken') || '')
  return config
}, error => {
  // 对请求错误做些什么
  return Promise.reject(error)
})

// 添加响应拦截器,并对响应数据做点什么
http.interceptors.response.use((response) => {
  /* 处理状态码 */
  // 请求响应成功：
  if (response.status === NETWORK_STATUS.SUCCESS) {
    // 如果AccessToken更新了则保存最新的AccessToken（最新的AccessToken会在接口响应头里的authorization返回）
    if (('Bearer ' + response.headers.authorization) !== response.config.headers.authorization) {
      if (response.headers.authorization) { // 有些接口不需要校验token的就不会返回authorization，所以没有返回就不需要保存
        setStore('AccessToken', response.headers.authorization)
      }
    }
    // 根据返回的状态码做相应的处理
    if (response.data.code === NETWORK_STATUS.SUCCESS) { // 请求成功
      return Promise.resolve(response.data)
    } else if (loginErrors.includes(response.data.code)) { // 登录问题(弹窗提示异地登录或登录过期，请重新登录)
      if (getStore('AccessToken')) {
        setStore('AccessToken', '')
        if (!loginError) {
          loginError = true
          const tipLang = response.data.code === LOGIN_ERR_STATUS.REMOTE_LOGIN ? 'common.loginByOthers' : 'common.loginOutTip'
          Vue.prototype.$MessageAlert({
            text: $t(tipLang, { erroCode: response.data.code }),
            type: 'error',
            duration: 2000
          })
          const timer = setTimeout(() => {
            loginError = false
            redirectLogin()
            clearTimeout(timer)
          }, 2000)
          return Promise.reject({ msg: response.data.message })
        }
      }
    } else if (authErrors.includes(response.data.code)) { // 权限问题，消息提示访问权限异常后跳转到登录页
      if (!authError) {
        authError = true
        Vue.prototype.$MessageAlert({
          text: $t('common.authError', { erroCode: response.data.code }),
          type: 'error',
          duration: 2000
        })
        const timer = setTimeout(() => {
          authError = false
          clearTimeout(timer)
        }, 2000)
        return Promise.reject({ msg: response.data.message })
      }
    } else if (networkStatusError.includes(response.data.code)) { // 网络有关的问题，提示网络繁忙
      if (!networkError) {
        networkError = true
        Vue.prototype.$MessageAlert({
          text: $t('common.networkBusy', { erroCode: response.data.code }),
          type: 'error',
          duration: 2000
        })
        const timer = setTimeout(() => {
          networkError = false
          clearTimeout(timer)
        }, 2000)
        return Promise.reject({ msg: response.data.message })
      }
    } else if (response.data.code === 65000006) { // 修改邮箱处不做拦截
      return Promise.reject(response.data)
    } else if([60010045, 60010046].includes(response.data.code)){ // 未返回crm价格
      console.log(response.data, 4444)
      return Promise.reject({ msg: response.data.message, code: response.data.code })
    } else {
      const name = response.data.code === 11010032 ? 'detailMessage' : 'message'
      // 其他的错误码响应，则提示对应的错误信息
      Vue.prototype.$MessageAlert({
        text: $t(response.data.code, { 'message': response.data[name] }),
        type: 'error'
      })
      return Promise.reject({ msg: response.data.message, code: response.data.code })
    }
  } else {
    return Promise.reject(response.data)
  }
}, (error) => {
  // 对响应错误做点什么
  console.log(error)
})
export default http
