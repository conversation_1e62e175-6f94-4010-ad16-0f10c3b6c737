<template>
  <div v-if="show" class="test-layout-box">
    <div class="header">
      <p class="back-btn border finger" @click="goBack">
        <i class="iconfont icon-arrow-back" />返回
      </p>
      <p class="header-title">测试新布局</p>
    </div>
    
    <div class="designer-points" v-loading="loadingList">
      <!-- 导航栏 -->
      <div class="points-header">
        <div class="time-search">
          <span class="label">搜索</span>
          <el-input 
            v-model="searchKey" 
            placeholder="请输入编码/代理商名称搜索" 
            suffix-icon="el-icon-search" 
            clearable
            @change="handleSearch"
          />
        </div>
        <div class="points-btnlist">
          <el-button type="primary" @click="handleRefresh">刷新</el-button>
          <el-button type="primary" @click="handleAdd">添加代理商</el-button>
          <el-button type="primary" @click="handleExport">导出交易明细</el-button>
        </div>
      </div>
      
      <!-- 内容 -->
      <div class="points-table">
        <!-- 搜索栏 -->
        <div class="search-list">
          <div class="one-search design-group">
            <span class="label">代理范围</span>
            <el-select v-model="agentScope" placeholder="请选择代理范围" clearable>
              <el-option label="设备" value="0"></el-option>
              <el-option label="设计服务" value="1"></el-option>
              <el-option label="AI软件" value="2"></el-option>
            </el-select>
          </div>
          <div class="one-search design-group">
            <span class="label">状态</span>
            <el-select v-model="status" placeholder="请选择状态" clearable>
              <el-option label="正常" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </div>
        </div>
        
        <!-- 列表 -->
        <div class="depart-table">
          <hg-table 
            :header-data="headerData" 
            class="user-table" 
            :loading="tableLoading" 
            :data="testTableData"
          >
            <template v-slot:no="{ row, index }">
              <span>{{ index + 1 }}</span>
            </template>
            
            <template #agentCode="scope">
              <span>{{ scope.row.agentCode }}</span>
              <span v-if="scope.row.isNew" class="new-flag">NEW</span>
            </template>
            
            <template #agentName="scope">
              <span>{{ scope.row.agentName }}</span>
            </template>
            
            <template #agentScope="scope">
              <span>{{ scope.row.agentScope }}</span>
            </template>
            
            <template #deviceCount="scope">
              <span class="device-count clickable" @click="handleViewCustomers(scope.row)">
                {{ scope.row.deviceCount }}
              </span>
            </template>
            
            <template #edit="scope">
              <i class="iconfont icon-edit iconfont-24" @click="handleEdit(scope.row)" />
            </template>
            
            <template #user="scope">
              <i class="iconfont icon-manage_accounts iconfont-24" @click="handleManageUsers(scope.row)" />
            </template>
            
            <template #detail="scope">
              <i class="iconfont icon-price_change iconfont-24" @click="handleViewDetails(scope.row)" />
            </template>
          </hg-table>
          
          <div class="depart-pagination">
            <pagination 
              showTotal 
              :total="page.total" 
              :pageSizes="[10, 20, 50, 100]" 
              :initPageIndex="page.pageNo" 
              :initPageSize="page.pageSize" 
              @onSearch="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import hgTable from "@/components/HgTable/index.vue";
import pagination from '@/components/func-components/Pagination';

export default {
  name: "TestLayout",
  components: { hgTable, pagination },
  props: {
    show: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      loadingList: false,
      tableLoading: false,
      searchKey: '',
      agentScope: '',
      status: '',
      page: {
        pageSize: 20,
        pageNo: 1,
        total: 100,
      },
      testTableData: [
        {
          agentCode: 'AG001',
          agentName: '测试代理商1',
          agentScope: '设备',
          deviceCount: 25,
          isNew: true
        },
        {
          agentCode: 'AG002',
          agentName: '测试代理商2',
          agentScope: '设计服务',
          deviceCount: 18,
          isNew: false
        },
        {
          agentCode: 'AG003',
          agentName: '测试代理商3',
          agentScope: 'AI软件',
          deviceCount: 32,
          isNew: false
        }
      ]
    };
  },
  computed: {
    headerData() {
      return [
        {
          prop: "no",
          width: "60px",
          noTip: false,
          getLabel: () => {
            return '序号';
          },
        },
        {
          prop: "agentCode",
          minWidth: "20%",
          noTip: false,
          getLabel: () => {
            return '代理商编码';
          },
        },
        {
          prop: "agentName",
          minWidth: "25%",
          noTip: false,
          getLabel: () => {
            return '代理商名称';
          },
        },
        {
          prop: "agentScope",
          minWidth: "20%",
          noTip: false,
          getLabel: () => {
            return '代理范围';
          },
        },
        {
          prop: "deviceCount",
          minWidth: "15%",
          noTip: false,
          getLabel: () => {
            return '设备数量';
          },
        },
        {
          prop: "edit",
          width: "60px",
          noTip: false,
          getLabel: () => {
            return '编辑';
          },
        },
        {
          prop: "user",
          width: "60px",
          noTip: false,
          getLabel: () => {
            return '用户';
          },
        },
        {
          prop: "detail",
          width: "60px",
          noTip: false,
          getLabel: () => {
            return '详情';
          },
        }
      ];
    },
  },
  methods: {
    goBack() {
      this.$emit("update:show", false);
    },
    
    handleSearch() {
      console.log('搜索:', this.searchKey);
    },
    
    handleRefresh() {
      console.log('刷新');
    },
    
    handleAdd() {
      console.log('添加代理商');
    },
    
    handleExport() {
      console.log('导出交易明细');
    },
    
    handleViewCustomers(row) {
      console.log('查看客户:', row);
    },
    
    handleEdit(row) {
      console.log('编辑:', row);
    },
    
    handleManageUsers(row) {
      console.log('管理用户:', row);
    },
    
    handleViewDetails(row) {
      console.log('查看详情:', row);
    },
    
    handlePageChange(type, searchData) {
      if(searchData) {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
      } else {
        this.page.pageNo = 1;
        this.page.pageSize = 20;
      }
      console.log('分页变化:', this.page);
    }
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/style/variables.scss";

.test-layout-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #0B0C0D; // 使用最深的背景色
  z-index: 9999;
  
  .header {
    display: flex;
    align-items: center;
    height: 60px;
    padding: 0 24px;
    border-bottom: 1px solid $hg-border;
    background: $hg-main-black; // 头部使用稍浅的背景
    
    .back-btn {
      display: flex;
      align-items: center;
      color: $hg-label;
      cursor: pointer;
      margin-right: 24px;
      
      i {
        margin-right: 8px;
      }
      
      &:hover {
        color: $hg-main-blue;
      }
    }
    
    .header-title {
      color: $hg-label;
      font-weight: bold;
      font-size: 16px;
    }
  }
  
  .designer-points {
    padding: 24px;
    height: calc(100% - 60px);
    overflow: hidden;
    // 不设置背景色，继承父级的深色背景
    
    .points-header {
      position: relative;
      margin-bottom: 24px;
      
      .time-search {
        position: relative;
        width: 560px;
        display: flex;
        align-items: center;
        
        .label {
          margin-right: 20px;
          color: $hg-label;
        }
        
        .date-icon {
          position: absolute;
          right: 40px;
          font-size: 16px;
        }
      }
      
      .points-btnlist {
        position: absolute;
        right: 0px;
        top: 0;
      }
    }
    
    .points-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: calc(100% - 64px);
      background: #1B1D22; // 关键：与设计师点数管理页面一致的背景色
      margin-top: 20px; // 与设计师点数管理页面一致
      
      .search-list {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 12px 24px; // 添加内边距
        border-bottom: 1px solid #3D4047; // 添加底部边框
        
        .one-search {
          display: flex;
          align-items: center;
          margin-right: 20px;
          
          .label {
            margin-right: 10px;
            white-space: nowrap;
            color: $hg-label;
          }
          
          .el-select {
            width: 200px;
          }
        }
      }
      
      .depart-table {
        position: relative;
        flex: 1;
        height: calc(100% - 20px);
        width: 100%;
        background: #27292E; // 表格容器背景
        border-radius: 8px;
        
        .user-table {
          height: 100%;
          background: #27292E;
          overflow: hidden;
          
          .new-flag {
            display: inline-block;
            background: rgba(246, 76, 76, 0.24);
            padding: 2px 4px;
            border-radius: 3px;
            color: $hg-secondary-danger;
            font-size: 10px;
            margin-left: 8px;
            font-weight: bold;
            vertical-align: middle;
          }
          
          .device-count {
            color: $hg-main-blue;
            cursor: pointer;
            font-size: 14px;
            
            &:hover {
              color: $hg-btn-primary-hover;
            }
          }
          
          .iconfont-24 {
            font-size: 20px;
            color: $hg-grey;
            cursor: pointer;
            line-height: 1;
            
            &:hover {
              color: $hg-main-blue;
            }
          }
        }
        
        .table-high-light {
          float: left;
          width: auto;
          max-width: calc(100% - 41px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .expedited-time {
          line-height: 40px;
        }
        
        .error-tips {
          color: $hg-secondary-danger;
        }
      }
      
      .depart-pagination {
        z-index: 1;
        position: absolute;
        bottom: 0;
        right: 0;
        height: 60px;
        width: 100%;
      }
    }
  }
}

// 深度选择器样式 - 统一Element UI组件样式
::v-deep .el-button.is-plain:focus {
  background: transparent;
}

::v-deep .el-button--primary:focus {
  background: $hg-main-blue;
  border-color: $hg-main-blue;
}

::v-deep .el-input {
  width: 400px;
  background: $hg-background;
  
  .el-input__inner {
    background: $hg-background;
    border-color: $hg-border;
    color: $hg-label;
    padding-left: 20px;
    
    &::placeholder {
      color: $hg-grey;
    }
    
    &:focus {
      border-color: $hg-main-blue;
    }
  }
  
  .el-input__prefix {
    display: none;
  }
  
  .el-input--prefix .el-input__inner {
    padding-left: 20px;
  }
}

::v-deep .el-select {
  .el-input__inner {
    background: $hg-background;
    border-color: $hg-border;
    color: $hg-label;
    
    &:focus {
      border-color: $hg-main-blue;
    }
  }
  
  .el-input__suffix {
    .el-select__caret {
      color: $hg-grey;
    }
  }
}

// 表格样式 - 重点优化表头和表体
::v-deep .el-table {
  background: #27292E;
  color: $hg-label;
  border-radius: 4px;
  
  &::before {
    height: 0;
  }
  
  // 表头样式
  .el-table__header-wrapper {
    .el-table__header thead tr {
      background: transparent;
      
      th {
        height: 48px;
        font-size: 12px;
        color: $hg-disable-fontcolor;
        background: transparent;
        border-bottom: 1px solid $hg-border;
        padding: 0;
        
        &:first-of-type {
          padding-left: 24px;
        }
        
        &:last-of-type {
          padding-right: 24px;
        }
        
        .cell {
          padding: 0 8px;
          line-height: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          font-weight: normal;
        }
      }
    }
  }
  
  // 固定表头样式
  .el-table__fixed-header-wrapper {
    .el-table__header thead tr {
      background: transparent;
      
      th {
        height: 48px;
        font-size: 12px;
        color: $hg-disable-fontcolor;
        background: #27292E;
        border-bottom: 1px solid $hg-border;
        
        .cell {
          padding: 0 8px;
          line-height: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          font-weight: normal;
        }
      }
    }
  }
  
  // 表体样式
  .el-table__body-wrapper {
    .el-table__body tbody tr {
      background: transparent;
      
      &:hover {
        background-color: $hg-main-black;
        
        td {
          background-color: $hg-main-black;
        }
      }
      
      td {
        cursor: pointer;
        height: 56px;
        color: $hg-label;
        font-size: 14px;
        border-bottom: 1px dashed $hg-border;
        padding: 0;
        
        &:first-of-type {
          padding-left: 24px;
        }
        
        &:last-of-type {
          padding-right: 24px;
        }
        
        .cell {
          padding: 0 8px;
          line-height: 56px;
          height: 56px;
          display: flex;
          align-items: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  
  // 空状态
  .el-table__empty-block {
    background: #27292E;
    
    .el-table__empty-text {
      color: $hg-grey;
      line-height: 56px;
    }
  }
}

::v-deep .el-pagination {
  .el-pagination__total {
    color: $hg-label;
  }
  
  .el-pager li {
    background: $hg-background;
    color: $hg-label;
    border: 1px solid $hg-border;
    
    &:hover {
      color: $hg-main-blue;
    }
    
    &.active {
      background: $hg-main-blue;
      color: $hg-label;
    }
  }
  
  .btn-prev, .btn-next {
    background: $hg-background;
    color: $hg-label;
    border: 1px solid $hg-border;
    
    &:hover {
      color: $hg-main-blue;
    }
    
    &:disabled {
      color: $hg-grey;
      background: $hg-background;
    }
  }
  
  .el-pagination__sizes .el-select .el-input .el-input__inner {
    background: $hg-background;
    border-color: $hg-border;
    color: $hg-label;
  }
  
  .el-pagination__jump .el-input .el-input__inner {
    background: $hg-background;
    border-color: $hg-border;
    color: $hg-label;
  }
}

// 下拉选择框样式
::v-deep .el-select-dropdown {
  background: $hg-main-black;
  border: 1px solid $hg-border;
  
  .el-select-dropdown__item {
    background: $hg-main-black;
    color: $hg-label;
    
    &:hover {
      background: $hg-hover;
    }
    
    &.selected {
      background: $hg-main-blue;
      color: $hg-label;
    }
  }
}

// 按钮样式
::v-deep .el-button {
  &--primary {
    background: $hg-main-blue;
    border-color: $hg-main-blue;
    color: $hg-label;
    
    &:hover {
      background: $hg-btn-primary-hover;
      border-color: $hg-btn-primary-hover;
    }
    
    &:active {
      background: $hg-btn-primary-active;
      border-color: $hg-btn-primary-active;
    }
  }
}
</style>










