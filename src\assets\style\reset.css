/* 这里路径为相对于reset.css的font-face.css的路径 */
@import "./font-face.css";
html{
  /* 标准字体大小可以，在移动端使用的rem适配的话会动态改变。 */
  font-size: 14px;
  /*  使用IE盒模型（个人取舍，我一般设置width是这是盒子的真实大小，包括padding和border） */
  box-sizing: border-box;

}

html,body{
   /* 注意：webfont需要跟上一步的font-family一致，而!important保证该字体优先级最高 */
   font-family: "webfont" !important;
  /* 在有些手机浏览器中点击一个链接或着可点击元素的时候，会出现一个半透明的灰色背景； */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    /* 与浏览器窗口高度一致 */
  width: 100%;
  height: 100%;
  overflow: hidden !important;
  font-size: 14px;
  /* min-width: 1440px; */
}

body{
  display: block;
  /* 有些背景默认为浅灰色，所以统一设置为纯白 */
  background: #fff;
  font-size: 14px;
  color: #000;
    /* 使字体更加顺滑 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 去除浏览器默认的margin和padding, 自行删减一些不必要的标签 */
body,
p,
h1,
h2,
h3,
h4,
h5,
h6,
dl,
dd,
ul,
ol,
th,
td,
button,
figure,
input,
textarea,
form,
pre,
blockquote,
figure{
  margin: 0;
  padding: 0;
}

a{
  /* 小手 */
  cursor: pointer;
    /* 取消超链接的默认下划线 */
  text-decoration:none;
    /* antd里面还做了 ， 看你团队需不需要这样的风格 */
  transition: color 0.3s ease;
}

ol,
ul,
li {
  /* 去除自带的ugly样式。 */
  list-style:none
}

/* 这些节点部分属性没有继承父节点样式，所有继承一下，并取消outline，外轮廓的效果 */
a,
h1,
h2,
h3,
h4,
h5,
h6,
input,
select,
button,
textarea {
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  font-style: inherit;
  line-height: inherit;
  color: inherit;
  outline: none;
}

button,
input[type='submit'],
input[type='button'] {
/* 可点击小手 */
  cursor: pointer;
}
/* 取消部分浏览器数字输入控件的操作按钮 apperance可以改变控件的外观。 */
input[type='number'] {
  -moz-appearance: textfield;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
/**
* 删除Firefox中的内边框和填充。
*/
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.el-carousel__indicators--horizontal {
  bottom: 25px!important;
  left: 80%!important;
  transform: translateX(-50%);
}

::-webkit-scrollbar{
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track-piece{
  -webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:vertical{
  height: 6px;
  background-color:rgb(56, 57, 61);
  -webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:horizontal{
  width: 6px;
  background-color:rgb(56, 57, 61);
  -webkit-border-radius: 6px;
}

@import "//at.alicdn.com/t/font_2273289_en02ux0lr99.css";
@import "//at.alicdn.com/t/c/font_3213322_fp40y4he3ji.css";

