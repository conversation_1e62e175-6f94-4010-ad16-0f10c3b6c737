import * as THREE from 'three'
import { _matrix4 } from '../matrix4'

export function getObjectBox3(object) {
  const box3 = new THREE.Box3()
  box3.setFromObject(object)
  return box3
}

export function showObjectBoxHelper(object) {
  const boxHelper = new THREE.BoxHelper(object, 0xffff00)
  boxHelper.applyMatrix4(_matrix4.copy(object.parent.matrixWorld).invert())
  object.parent.add(boxHelper)
  return boxHelper
}

// 获取网格模型的一个方向轴的模长
export function getObjectAxisLength(object, axis) {
  const box = getObjectBox3(object)
  const { max, min } = box
  return max[axis] - min[axis]
}

// 获取网格模型的所有方向轴的模长
export function getObjectLengths(object) {
  const box = getObjectBox3(object)
  const { max, min } = box
  return {
    x: max.x - min.x,
    y: max.y - min.y,
    z: max.z - min.z,
  }
}

// 计算网格模型中心(世界坐标系)
export function getObjectCenter(object) {
  const center = new THREE.Vector3()
  const box = getObjectBox3(object)
  const { max, min } = box
  center.addVectors(max, min)
  center.multiplyScalar(0.5)
  return center
}
