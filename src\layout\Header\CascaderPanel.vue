<template>
  <div class="cascader-panel" @mouseleave="enterItem(null)">
    <ul class="content">
      <li
        v-for="(item, index) in options"
        :key="index"
        :ref="item.key"
        @mouseenter="enterItem(item)"
        :class="{ 'is-hover': isHoverItem && isHoverItem.key === item.key }"
         @click="itemFun(item)"
      >
        <span class="head">
          <i :class="['iconfont', `icon-${item.icon}`]"></i>
        </span>
        <span class="label">
          {{ $t(item.label) }}
        </span>
        <span class="arrow">
          <i v-if="item.children && item.children.length" class="iconfont icon-arrow-right"></i>
        </span>
      </li>
    </ul>
    <span class="up-arrow">
      <i class="iconfont icon-arrow-up"> </i>
    </span>
  </div>
</template>
<script>
export default {
  name: "CascaderPanel",
  props: {
    options: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isHoverItem: null,
      childrenDomElements: {},
    };
  },
  watch: {
    isHoverItem(val, oldVal) {
      if (oldVal) {
        const childNode = this.childrenDomElements[oldVal.key];
        if (childNode) {
          childNode.style.display = 'none';
        }
      }
      if (val) {
        const newChildNode = this.childrenDomElements[val.key];
        if (newChildNode) {
          newChildNode.style.display = 'block';
        } else {
          this.createChildUL(val);
        }
      }
    },
  },
  methods: {
    itemFun(item) {
      if (item.fun) {
        item.fun();
      }
    },
    createChildUL(item) {
      if (item.children) {
        const dom = this.$refs[item.key][0];
        const ul = document.createElement('ul');
        ul.setAttribute('class', 'cascader-panel-second');
        item.children.forEach((node) => {
          const li = document.createElement('li');
          li.innerHTML = `<span>${ this.$t(node.label)}</span>`;
          if (node.fun) {
            li.addEventListener('click', () => {
              node.fun();
            });
          }
          this.$refs[node.key] = li;
          ul.appendChild(li);
        });
        dom.appendChild(ul);
        this.childrenDomElements[item.key] = ul;
      }
    },
    enterItem(item) {
      this.isHoverItem = item;
    },
  },
};
</script>
<style lang="scss" scoped>
.cascader-panel {
  background-color: $hg-main-black;
  width: auto;
  padding: 12px 0;
  z-index: 30;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.4);
  ul {
    font-size: $hg-normal-fontsize;
    li {
      padding: 0 28px;
      height: 40px;
      line-height: 40px;
      color: $hg-secondary-fontcolor;
      display: flex;
      // justify-content: space-between;
      position: relative;
      .head {
        i {
          font-size: 18px;
          color: $hg-secondary-fontcolor;
        }
      }
      .label {
        min-width: 60px;
        max-width: 165px;
        margin: 0 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &.is-hover {
        background-color: $hg-hover-bg-color;
      }

      .arrow {
        margin-left: auto;
        color: $hg-secondary-fontcolor;
        i {
          font-size: $hg-small-fontsize;
        }
      }
    }
  }
  .up-arrow {
    position: absolute;
    top: -32px;
    right: 10px;
    z-index: 1;

    i {
      font-size: 10px;
      color: $hg-main-black;
    }
  }
}
</style>
<style lang="scss">
.cascader-panel-second {
  background-color: $hg-main-black;
  position: absolute;
  right: 100%;
  width: 112px;
  border-radius: 4px;
  li {
    height: 40px;
    padding-left: 24px;
    &:hover {
      background-color: $hg-hover-bg-color;
    }
  }
}
</style>
