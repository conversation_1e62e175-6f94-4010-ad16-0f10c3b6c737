import { getUploadUrl, completeUpload } from '@/api/file';
import { getFileInfo, getSuffix } from '@/public/utils/file';
import axios from 'axios';

class UploadNormalFile {
  constructor(option) {
    this.s3FileId = null;
    this.fileName = option.file.name || '';
    this.fileSize = option.file.size || 0;
    this._file = option.file;
    this.needOrgCode = option.needOrgCode || false;
    this.orgCode = option.orgCode || 0;
    this.expirationInDays = option.expirationInDays;
    this.md5 = null;
    this.fileArrayBuffer = null;
    this.url = '';
    this.callbackFn = null;
    this.progressFn = null;
    this.errFn = null;
    this.progress = 0;
  }
  async getMd5() {
    try {
      const { md5, fileArrayBuffer } = await getFileInfo(this._file);
      this.md5 = md5;
      this.fileArrayBuffer = fileArrayBuffer;
    } catch (error) {
      this.errFn && this.errFn(error);
    }
  }

  async onStart() {
    await this.getMd5();
    this.init();
  }

  onUploadProgress(fn) {
    this.progressFn = fn;
  }

  onEnd(fn) {
    this.callbackFn = fn;
  }

  onError(fn) {
    this.errFn = fn;
  }

  async init() {
    try {
      let uploadParam = { md5: this.md5, suffix: getSuffix(this.fileName), expirationInDays: this.expirationInDays };
      if(this.needOrgCode) {
        uploadParam.orgCode = this.orgCode;
      }
      const { data } = await getUploadUrl(uploadParam);
      await axios({
        method: 'put',
        url: data.url,
        data: this.fileArrayBuffer,
        headers: {
          'Content-Type': 'multipart/form-data',
          'Content-MD5': this.md5,
        },
        onUploadProgress: progressEvent => {
          // 原生获取上传进度的事件
          if (progressEvent.lengthComputable) {
            this.progressFn && this.progressFn(progressEvent);
          }
        },
      });
      let param = { urlUuid: data.urlUuid };
      if(this.needOrgCode) {
        param.orgCode = this.orgCode;
      }
      const { code, data: result } = await completeUpload(param);
      if (code === 200) {
        this.s3FileId = result.s3FileId;
        this.url = result.url;
        this.progress = 100;
        this.callbackFn && this.callbackFn(this);
      }
    } catch (error) {
      this.errFn && this.errFn(error);
    }
  }
}

export default UploadNormalFile;
