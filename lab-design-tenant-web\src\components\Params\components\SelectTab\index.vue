<template>
  <div class="parameter-select-tab">
    <div :class="['tab-btn__prev', prevEnd && 'is-disabled']" @click="scrollPrev"></div>

    <div class="tab-box" ref="tabBox">
      <div class="tab-box-nav" ref="tabBoxNav" :style="tabBoxNavStyle">
        <div 
          v-for="item in tabList" 
          :key="item.designCode"
          :class="['tab-item', 'pointer', item.designCode === selectCode && 'is-active-tab']"
          @click="openTab(item.designCode)">
          <span class="tab-item-text">{{ $t(`apiCommon.${item.designCode}`) }}</span>
        </div>
      </div>
    </div>

    <div :class="['tab-btn__next', nextEnd && 'is-disabled']" @click="scrollNext"></div>
  </div>
</template>

<script>
import ScrollEvent from './index.js';

export default {
  name: 'SelectTab',
  mixins: [ScrollEvent],
  model: {
    prop: 'value',
    event: 'update'
  },
  data(){
    return {
      selectCode: -1
    }
  },
  props: {
    tabList: {
      type: Array
    },
    value: Number,
  },
  watch:{
    value(newValue) {
      if(this.selectCode !== newValue) {
        this.selectCode = newValue;
      }
    }
  },
  mounted(){
    this.selectCode = this.value;
  },
  methods: {
    openTab(designCode) {
      if(designCode !== this.selectCode) {
        this.selectCode = designCode;
        this.$emit('update', designCode);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.parameter-select-tab {
  display: flex;
  padding: 8px;
  background-color: #27292E;

  .tab-btn__prev {
    cursor: pointer;
    width: 40px;
    background-image: url(~@/assets/images/order/icon_page_sel.svg);
  }
  .tab-btn__next {
    cursor: pointer;
    width: 40px;
    transform: rotate(180deg);
    background-image: url(~@/assets/images/order/icon_page_sel.svg);

    &:hover {
      background-image: url(~@/assets/images/order/icon_page_sel.svg);
    }
  }

  .tab-btn__prev.is-disabled,
  .tab-btn__next.is-disabled {
    background-image: url(~@/assets/images/order/icon_page_sel.svg);
    &:hover {
      background-image: url(~@/assets/images/order/icon_page_sel.svg);
    }
  }

  .tab-box {
    width: calc(100% - 80px);
    overflow: hidden;
    background: #27292E;
  }

  .tab-box-nav {
    display: flex;
    position: relative;
    transition: transform .3s;
    float: left;
    z-index: 2;
    text-align: center;
    border-radius: 4px 0 0 4px;
    white-space: nowrap;

    .tab-item {
      cursor: pointer;
      position: relative;
      display: inline-block;     
      min-width: 96px;
      height: 40px;
      border-radius: 2px;
      color: $hg-secondary-text;

      &-text {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 12px;
        height: 100%;
        white-space: pre-wrap;
        font-size: 12px;
        // line-height: 16px;
      }

      &:hover {
        color: $hg-label;
        background: $hg-hover;
      }
    }

    .is-active-tab {
      color: $hg-white;
      border-radius: 4px;
      background: rgba(61, 64, 71, 0.36);

      &:hover {
        color: $hg-white;
        background: rgba(61, 64, 71, 0.36);
      }

      .tab-item-text {
        border-bottom: 3px solid #3760EA;
      }
    }

  }
}
</style>
