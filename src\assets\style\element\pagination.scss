@mixin pagination($height: 32px, $hg-background-color: transparent) {
    button.btn-prev,
    button.btn-next {
        height: $height;
        @content;
        background-color: transparent;
        &:disabled {
            background-color: $hg-background-color;
            .el-icon.el-icon-arrow-left,
            .el-icon.el-icon-arrow-right {
                color: $hg-disable-fontcolor;
            }
        }
    }
    .el-icon.el-icon-arrow-left,
    .el-icon.el-icon-arrow-right {
        color: $hg-primary-fontcolor;
    }

}
.el-pagination {
    @include pagination;
    .el-pager {
        li.number,
        li.el-icon.more {
            min-width: 32px;
            height: 32px;
            background-color: transparent;
            color: $hg-primary-fontcolor;
            font-weight: 400;
            line-height: 32px;
            &:hover {
                color: $hg-main-blue;
            }
        }
        li.number.active {
            background-color: $hg-main-blue;
            color: $hg-primary-fontcolor;
        }
    }
    &.is-background {
        @include pagination(32px, $disabledhg-background-color) {
            border: 1px solid $hg-border-color;
        };
        .el-pager{
            li.number,
            li.el-icon.more{
                min-width: 32px;
                height: 32px;
                border: 1px solid $hg-border-color;
                background: transparent;
                color: $hg-primary-fontcolor;
                &:not(.disabled):hover{
                    border-color: $hg-main-blue;
                    color: $hg-main-blue;
                }
            }

            li.number:not(.disabled).active{
                border-color: $hg-main-blue;
                background-color: $hg-main-blue;
                color: $hg-primary-fontcolor;
            }
        }
    }
    &.el-pagination--small{
        @include pagination(24px);
        .el-pager {
            li.number,
            li.el-icon.more {
                min-width: 24px;
                height: 24px;
                background-color: transparent;
                color: $hg-primary-fontcolor;
                font-weight: 400;
                line-height: 24px;
                &:hover {
                    color: $hg-main-blue;
                }
            }
            li.number.active {
                background-color: $hg-main-blue;
                color: $hg-primary-fontcolor;
            }
        }
    }
    .el-pagination__jump, .el-pagination__total{
        color: $hg-primary-fontcolor;
    }
}
