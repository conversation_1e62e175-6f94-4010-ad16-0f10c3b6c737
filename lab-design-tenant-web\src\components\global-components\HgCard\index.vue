<template>
  <div :class="['hg-card', needShadow && 'information-card']">
    <div v-if="isInfo" class="hg-card-header"></div>
    <slot></slot>
  </div>
</template>

<script>
// 无内容卡片
export default {
  name: 'HgCard',
  props: {
    isInfo: <PERSON><PERSON><PERSON>,
    needShadow: <PERSON><PERSON><PERSON>,
  }
}
</script>

<style lang="scss" scoped>
.hg-card {
  position: relative;
  height: auto;
  padding: 24px;
  border-radius: 4px;
  background: #1B1D22;

  .hg-card-header {
    position: absolute;
    top: 0px;
    left: 0;
    height: 8px;
    width: 100%;
    border-radius: 4px 4px 0px 0px;
    background: $hg-main-blue;
  }
}

.hg-card.information-card {
  box-shadow: 0px 12px 32px 0px $hg-background,0px 8px 24px 0px $hg-background,0px 0px 16px 0px $hg-background;
}
</style>