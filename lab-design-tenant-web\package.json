{"name": "lab-designcenter-tenant-web", "version": "0.1.0", "private": true, "scripts": {"start": "npm run serve", "local": "cross-env NODE_ENV=local vue-cli-service serve --mode local", "serve": "vue-cli-service serve --mode development", "dev": "vue-cli-service build --mode dev", "sit": "vue-cli-service build --mode sit", "uat": "vue-cli-service build --mode uat", "prod": "vue-cli-service build --mode production", "usprod": "vue-cli-service build --mode usprod", "tkyprod": "vue-cli-service build --mode tkyprod", "parprod": "vue-cli-service build --mode parprod", "cnprod": "vue-cli-service build --mode cnprod", "lint": "vue-cli-service lint"}, "dependencies": {"aws-sdk": "^2.1163.0", "axios": "^0.21.1", "core-js": "^3.6.5", "echarts": "^5.5.0", "element-ui": "^2.15.8", "heygears-cloud-header": "1.0.14", "js-cookie": "^2.2.1", "luckyexcel": "^1.0.1", "moment": "~2.29.1", "postcss-pxtorem": "5.1.1", "qs": "^6.10.5", "spark-md5": "^3.0.1", "style-rem-loader": "^1.0.0", "three": "^0.142.0", "viewerjs": "^1.11.2", "vue": "^2.6.11", "vue-i18n": "^8.22.4", "vue-lazyload": "^1.3.3", "vue-router": "^3.2.0", "vuex": "^3.4.0", "dexie": "^3.2.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "cross-env": "^7.0.3", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "hat": "0.0.3", "mockjs": "^1.1.0", "node-sass": "^5.0.0", "prettier": "^1.19.1", "sass-loader": "^10.1.1", "svg-sprite-loader": "^5.2.1", "vue-loader": "15.7.0", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.8.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}