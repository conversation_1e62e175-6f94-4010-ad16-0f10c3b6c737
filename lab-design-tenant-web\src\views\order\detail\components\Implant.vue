<template>
  <hg-card class="implant-scheme">
    <order-title langName="implantScheme" class="title-box">
      <span v-show="showNewTip" class="new-file-icon">NEW</span>
      <span class="tips" v-if="!implantsScheme">{{ $t('order.detail.implant.tips') }}</span>
      <span class="complete-btn" v-if="isDesign">
        <el-button type="primary" v-if="!implantsScheme || implantsScheme.currentState === -1" :loading="submitLoading" @click="submitImplant">{{$t('heypoint.customer.operate.submit')}}</el-button>
      </span>
      <!-- 待审核 -->
      <div v-if="implantsScheme && implantsScheme.currentState === 0" class="pending-approval">
        <span class="point"></span>
        {{ $t('order.detail.implant.pendingApproval') }}
      </div>
      <div v-if="implantsScheme && implantsScheme.currentState === 1" class="approved">
        <span class="point"></span>
        {{ $t('order.detail.implant.approved') }}
      </div>
      <div v-if="implantsScheme && implantsScheme.currentState === -1" class="not-pass">
        <span class="point"></span>
        {{ $t('order.detail.implant.notPass') }}
      </div>
    </order-title>

    <div class="upload-ul">
      <!-- :name="$t(item.name)" -->
      <!-- @necessaryFileUploadSuccess="necessaryFileUploadSuccess" -->
      <upload-card
        ref="uploadCard"
        :name="$t(implantUpload.name)"
        :uploadList="implantUpload.list"
        :fileType="implantUpload.fileType"
        :uploadTip="implantUpload.uploadTip"
        :acceptType="implantUpload.acceptType"
        :necessary.sync="implantUpload.necessary"
        :clientOrgCode="clientOrgCode"
        :needUpload="editUpload"
        :needDownload="true"
        @implantUploadStart="implantUploadStart"
        @implantUploadEnd="implantUploadEnd"
      ></upload-card>
    </div>

    <div v-if="implantsScheme && implantsScheme.currentState === -1" class="return-order">
      <div class="title">
        <i class="el-icon-info"></i>
        <span class="text">{{ $t('order.detail.implant.returnTitle') }}</span>
      </div>

      <div class="reason">
        {{ implantsScheme.returnReasons }}
      </div>

      <div class="img-list">
        <div class="img-li" v-for="url in returnImageList" :key="url">
          <img :src="url" @click.stop="openView(url, returnImageList)" />
        </div>
      </div>
    </div>
  </hg-card>
</template>

<script>
import OrderTitle from './OrderTitle';
import UploadCard from './Upload/UploadCard';
import { ORDER_TYPES, FILE_TYPES } from '@/public/constants';
import { submitImplantsScheme } from '@/api/order/operate'
import { getDownloadUrl } from '@/api/file';

export default {
  name: 'OrderDetail',
  components: { OrderTitle, UploadCard },
  props: {
    needUpload: Boolean,
    showNewTip: Boolean,
    clientOrgCode: {
      type: Number,
      require: true,
    },
    implantsScheme: Object,
    orderCode: String,
    orderStatus: [Number, String],
  },
  data() {
    return {
      editUpload: this.needUpload,
      implantUpload: {
        name: 'order.detail.implant.implantScheme',
        compType: 1,
        fileType: FILE_TYPES.IMPLANT_FILE,
        list: [],
        uploadTip: 'file.tips.implant',
        acceptType: '.pdf',
        necessary: true,
      },
      returnImageList: [],
      submitLoading: false,
      uploadObj: {},
    }
  },
  computed: {
    isDesign({ orderStatus }) {
      return ORDER_TYPES.DESIGNING === orderStatus
    }
  },
  watch: {
    needUpload(val) {
      if (this.implantUpload.list.length > 0) {
        this.editUpload = false
      } else {
        this.editUpload = val;
      }
    },
    implantsScheme: {
      handler(val) { 
        if (val) {
          console.log('implantsScheme-val', val)
          this.implantUpload.list = val.schemeFiles || [];
          if (val.schemeFiles && val.schemeFiles.length > 0) {
            this.editUpload = false
          }

          if (val.currentState === -1) {
            this.editUpload = true
            if (val.files) {
              const imageS3List = JSON.parse(val.files)
              this.handleImage(imageS3List)
            }
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    openView(url, imageList) {
      const index = imageList.findIndex(img => img === url);
      this.$hgViewer.open({
        imgList: imageList,
        initialViewIndex: index,
      });
    },
    async handleImage(imageS3List = []) {
      if(imageS3List.length === 0) return;

      let reqList = [];
      imageS3List.forEach(s3FileId => {
        const req = new Promise(resovle => {
          const param = {
            s3FileId,
            orgCode: this.clientOrgCode,
            filename: '',
          };
          getDownloadUrl(param, true).then(res => {
            resovle(res.data.url);
          }).catch(err => {});
        });
        reqList.push(req);

      });

      await Promise.all(reqList).then(res => {
        this.returnImageList = res;
      });
    },
    implantUploadStart() {
      this.submitLoading = true;
    },
    implantUploadEnd() {
      this.submitLoading = false;
    },
    /**
     * 父组件调用的方法：获取当前组件的文件列表
     */
    getCompList() {
      let compList = [];
      const dom = this.$refs.uploadCard;
      console.log('dom', dom)
      if(dom) {
        const { necessary, fileType, uploadFileList } = dom;
        const data = {
          necessary,
          fileType,
          fileList: uploadFileList,
        }

        compList.push(data);
      }
      return compList;
    },

    /**
     * 提交种植方案
     */
    async submitImplant() {
      try {
        this.$nextTick(() => {
          const orderFiles = this.getCompList();
          console.log('orderFiles', orderFiles);
          if (!orderFiles || (orderFiles[0] && orderFiles[0].fileList.length === 0)) {
            this.$message.error(this.$t('order.detail.implant.submitLimit'));
            return;
          }

          this.$confirm(this.$t('order.detail.implant.submitConfirm'), this.$t('common.systemTips'), {
            confirmButtonText: this.$t('common.btn.confirm'),
            cancelButtonText: this.$t('common.btn.cancel'),
            distinguishCancelAndClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
          }).then(async () => {
            this.submitLoading = true;
            const params = {
              orderCode: this.orderCode,
              orderFiles: orderFiles[0].fileList.map(item => {
                return {
                  fileName: item.fileName,
                  filePath: item.filePath,
                  fileSize: item.fileSize,
                  fileTime: item.fileTime,
                  fileType: item.fileType
                }
              }),
            }
            const { data } = await submitImplantsScheme(params);
            if (data) {
              this.$message.success(this.$t('order.detail.implant.submitSuccess'));
              this.$emit('submitSuccess')
            }
          }).catch((action) => {
            console.log(action);
          });
        })
      } catch (error) {
        console.error(error)
      } finally {
        this.submitLoading = false;
      }
    }
  },
}
</script>

<style lang="scss">
.implant-scheme {
  .title-box {
    .tips {
      font-size: 16px;
      color: #FE9B0E;
      margin-left: 8px;
    }
    .pending-approval {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      border-radius: 9999px;
      border: 1px solid #FE9B0E;
      background: rgba(254, 155, 14, 0.24);
      color: #FE9B0E;
      font-weight: normal;
      margin-left: 16px;
      .point {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: #FE9B0E;
        border-radius: 50%;
        margin-right: 6px;
      }
    }
    .approved {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      border-radius: 9999px;
      border: 1px solid #00AA6D;
      background: rgba(0, 170, 109, 0.24);
      color: #00AA6D;
      font-weight: normal;
      margin-left: 16px;
      .point {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: #00AA6D;
        border-radius: 50%;
        margin-right: 6px;
      }
    }
    .not-pass{
      display: flex;
      align-items: center;
      padding: 6px 12px;
      border-radius: 9999px;
      border: 1px solid #F64C4C;
      background: rgba(246, 76, 76, 0.24);
      color: #F64C4C;
      font-weight: normal;
      margin-left: 16px;
      .point {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: #F64C4C;
        border-radius: 50%;
        margin-right: 6px;
      }
    }
  }
  .upload-ul {
    margin-top: 24px;
    & > .upload-li {
      margin-bottom: 24px;

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
  & > .order-title {
    display: flex;
    align-items: center;
    position: relative;

    .new-file-icon {
      color: $hg-white;
      background: $hg-new-tag;
      border-radius: 7px;
      padding: 0 5px;
      margin-left: 10px;
      font-size: 12px;
      height: 14px;
      line-height: 14px;
    }
    .complete-btn{
      position: absolute;
      right: 0;
      /deep/.el-button--primary.is-plain{
        background: transparent;
        color: #fff;
      }
    }
  }
  .return-order {
    margin-top: 8px;
    padding: 32px 24px;
    border-radius: 4px;
    border: 1px solid #F64C4C;
    background: rgba(246, 76, 76, 0.10);
    .title {
      color: #F64C4C;
      .text {
        font-weight: 700;
        margin-left: 8px;
      }
    }
    .reason {
      margin-top: 16px;
      line-height: 20px;
      color: #F64C4C;
    }
    .img-list {
      display: flex;
      .img-li {
        cursor: pointer;
        margin: 16px 16px 0 0;
        width: 80px;
        height: 80px;
        background: #262629;
        border-radius: 2px;
        img {
          padding: 4px;
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>