<template>
  <div class="main">
    展示系统自定义的全局组件

    <el-row>
      【可用状态】按钮事件为click
      <hg-button>默认按钮</hg-button>
      <hg-button type="primary">主要按钮</hg-button>
      <hg-button type="secondary">次要按钮</hg-button>
      <hg-button type="text">文本按钮</hg-button>
      <hg-button type="danger">危险按钮</hg-button>
      <hg-button type="danger-secondary">危险次要按钮</hg-button>
    </el-row>

    <el-row>
      【禁用状态】按钮事件为click
      <hg-button disabled>默认按钮</hg-button>
      <hg-button type="primary" disabled>主要按钮</hg-button>
      <hg-button type="secondary" disabled>次要按钮</hg-button>
      <hg-button type="text" disabled>文本按钮</hg-button>
      <hg-button type="danger" disabled>危险按钮</hg-button>
      <hg-button type="danger-secondary" disabled>危险次要按钮</hg-button>
    </el-row>

    <el-row>
      【按钮大小】按钮事件为click
      <hg-button type="primary" size="small">小</hg-button>
      <hg-button type="primary" size="middle">中</hg-button>
      <hg-button type="primary">默认大</hg-button>
    </el-row>

    <el-row>
      卡片的使用
      <div>
        默认状态
        <hg-card></hg-card>
      </div>

      <div>

        
        有阴影 needShadow
        <hg-card needShadow></hg-card>
      </div>

      <div>
        有蓝色线条 isInfo
        <hg-card isInfo></hg-card>
      </div>
    </el-row>

    <el-row>
      图标组件
      <hg-icon icon-name="icon-del" color="red" popper-class="test">11111</hg-icon>
      
      <hg-icon icon-class="arrow-back-lab" color="red"></hg-icon>不支持在图片里面加东西

      <hg-icon icon-class="icon_enter" :popperStyle="{width:'24px',height:'24px'}"></hg-icon>
    </el-row>

  </div>
</template>

<script>
export default {
  
}
</script>

<style lang="scss" scoped>
.main {
  padding: 24px;
  .el-row {
    margin-bottom: 10px;

    div {
      margin-top: 10px;
    }

    .test {
      font-size: 64px;
    }

    .arrow-back-lab {
      width: 28px;
      height: 28px;
    }

    .icon_enter {
      color: chocolate;
    }
  }
  button {
    margin-right: 10px;
  }
}
</style>