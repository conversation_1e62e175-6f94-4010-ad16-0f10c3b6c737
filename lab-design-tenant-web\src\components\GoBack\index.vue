<template>
  <hg-button class="go-back" type="secondary" @click="handleClick">
    <hg-icon icon-name="icon-arrow-back-lab"></hg-icon>
    <span>{{$t('common.back')}}</span>
  </hg-button>
</template>

<script>
export default {
  name: 'GoBack',
  methods: {
    handleClick(){
      this.$emit('handleGoBack');
    }
  }
}
</script>

<style lang="scss" scoped>
.go-back {
  width: 82px;
  height: 34px;
  padding: 8px 16px;
  font-size: 12px;
  line-height: 16px;
  color: $hg-secondary-text;

  .hg-icon {
    margin-right: 8px;
  }
}
</style>
