<template>
  <div class="hg-pic">
    <div class="hg-pic-check">
      <i class="el-icon-check"></i>
    </div>
    <div v-if="iconPath" class="hg-pic-item">
      <vue-photo-zoom-pro 
        v-if="needZoom"
        :is-zoom="isZoom"        
        :out-zoomer="true"
        :url="`${PARAM_ICON_PATH}/${iconPath}.svg`">
      </vue-photo-zoom-pro>

      <img v-else v-lazy="`${PARAM_ICON_PATH}/${iconPath}.svg`" alt="" srcset="">
    </div>
    
    <div v-else class="no-img">
      <img v-lazy="`${PARAM_ICON_PATH}/no_img.svg`" alt="">
    </div>
  </div>
</template>

<script>
import { PARAM_ICON_PATH } from '../utils/constant';

export default {
  inject: ['needZoom'],
  props: {
    isActive: Boolean,
    iconPath: String,
    isZoom: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      PARAM_ICON_PATH,
    }
  },
}
</script>

<style lang="scss" scoped>
.hg-pic {
  position: relative;
  height: 112px;
  width: 176px;
  border: 2px solid $hg-border;
  border-radius: 4px;

  .hg-pic-check {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    width: 0;
    height: 0;
    border-width: 14px;
    border-style: solid;

    .el-icon-check {
      position: absolute;
      right: -12px;
      top: -10px;
    }
  }

  &:hover {
    background: rgba(56, 57, 61, 0.4);

    .hg-pic-check {
      display: inline-block;
      border-color: $hg-border $hg-border transparent transparent;

      .el-icon-check {
        color: $hg-grey;
      }
    }
  }

  >div {
    display: flex;
    width: 100%;
    height: 100%;

    img {
      margin: auto;
    }
  }
  .hg-pic-item>img {
    width: 100%;
    height: 100%;
  }
}
</style>