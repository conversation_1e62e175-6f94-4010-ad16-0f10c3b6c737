<template>
  <hg-card class="base-info">
    <el-row>
      <el-col :span="6">
        <span class="label">{{ $t(`${i18nTitle}.designer`) }}</span>
        <el-tooltip :disabled="designerContent.length <= 10" :content="designerContent" placement="top">
          <span class="value">{{ designerContent || $t(`${i18nTitle}.noDesigner`) }}</span>
        </el-tooltip>
      </el-col>
      <el-col :span="6">
        <span class="label">{{ $t(`${i18nTitle}.clientName`) }}</span>
        <span class="value">{{ baseInfo.client }}</span>
      </el-col>
      <el-col :span="6">
        <span class="label">{{ $t(`${i18nTitle}.expectTime`) }}</span>
        <span class="value">{{ baseInfo.expectTime ? baseInfo.expectTime : '--' | dateFormatInHtml('yyyy.MM.DD HH:mm')}}</span>
      </el-col>
      <el-col :span="6">
        <span class="label">{{ $t(`${i18nTitle}.createdTime`) }}</span>
        <span class="value">{{ baseInfo.createdTime ? baseInfo.createdTime : '--' | dateFormatInHtml('yyyy.MM.DD HH:mm')}}</span>
      </el-col>
      <el-col :span="6">
        <span class="label">{{ $t(`${i18nTitle}.finishTime`) }}</span>
        <span class="value">{{ baseInfo.finishTime ? baseInfo.finishTime : '--' | dateFormatInHtml('yyyy.MM.DD HH:mm')}}</span>
      </el-col>

      <el-col :span="6">
        <span class="label" :class="canOperateQC ? 'special-label' : ''"><span v-if="canOperateQC" style="color: #ea3147;margin-right: 2px;">*</span>{{$t(`${i18nTitle}.software`)}}</span>
        <el-select v-if="canOperateQC" class="design-soft" :class="canOperateQC ? 'special-design-soft' : ''" v-model="baseInfo.designSoftware" :placeholder="$t(`${i18nTitle}.placeholder`)" @change="changeSoftware">
          <el-option v-for="item in designSoftwareOptinos" :key="item.code" :label="translateTextByLang(item)" :value="item.code"></el-option>
        </el-select>
        <span v-else>{{ translateTextByLang(baseInfo.designSoftwareInfo) }}</span>
      </el-col>
      <el-col :span="6">
        <span class="label" :class="canOperateQC ? 'special-label' : ''"><span v-if="canOperateQC" style="color: #ea3147;margin-right: 2px;">*</span>{{$t(`${i18nTitle}.version`)}}</span>
        <el-select v-if="canOperateQC" class="design-soft" :class="canOperateQC ? 'special-design-soft' : ''" v-model="baseInfo.softwareVersion" :placeholder="$t(`${i18nTitle}.placeholder`)">
          <el-option v-for="item in versionOptinos" :key="item.code" :label="translateTextByLang(item)" :value="item.code"></el-option>
        </el-select>
        <span v-else>{{ translateTextByLang(baseInfo.softwareVersionInfo) }}</span>
      </el-col>

      <el-col :span="24">
        <span class="label">{{ $t(`${i18nTitle}.patientName`) }}</span>
        <span class="value">{{ baseInfo.patientName || '--' }}</span>
      </el-col>
      <el-col :span="24" class="select-qc">
        <span class="label">{{ $t(`${i18nTitle}.teamQC`) }}</span>
        <el-select
          v-if="canOperateQC"
          v-model="baseInfo.groupQC"
          filterable
          :placeholder="$t(`${i18nTitle}.placeholder`)"
          clearable>
          <el-option
            v-for="item in userList"
            :key="item.userCode"
            :label="item.userName"
            :value="item.userCode">
          </el-option>
        </el-select>
        <span v-else>{{ baseInfo.groupQCName }}</span>
      </el-col>

      <el-col :span="24" class="remark">
        <span clase="label">{{ $t(`${i18nTitle}.remark`) }}</span>

          <div class="client-remark-box">
            <div :class="['remark-content', baseInfo.isUpdateClientRemark && 'remark-content-unread']">
              <span
                class="client-remark-box-remark"
                :style="
                  (baseInfo.translateContent ||
                    orderStatus !== 1) && {
                    borderBottom: 'none'
                  }
                ">
                {{ $t(`${i18nTitle}.clientRemark`) }}
                {{ baseInfo.clientRemark }}
              </span>

              <div class="remark-image-box" v-show="imageList.length > 0">
                <img v-for="(imageItem, urlIndex) in imageList" :key="urlIndex" :src="imageItem.url" @click.stop="openView(imageItem.url, imageList)" alt="">
              </div>

            </div>
            
            <!-- 待译单 designUser=0 IQC 才能显示 -->
            <el-input
              v-if="orderStatus === 1"
              type="textarea"
              :placeholder="$t('order.detail.tips.remarkTips')"
              :resize="'none'"
              maxlength="800"
              show-word-limit
              :autosize="{ minRows: 3, maxRows: 10 }"
              v-model="translateMsg"
              @change="handleChangeTranslate"></el-input>

            <div class="client-remark-box-show" v-else>
              <pre v-show="baseInfo.translateContent">{{ baseInfo.translateContent }}</pre>
            </div>

          </div>
      </el-col>

    </el-row>
  </hg-card>
</template>

<script>
import { parseJson } from '@/public/utils';
import { getDownloadUrl } from '@/api/file';
import { getUserList, getDesignSoftwareAll } from '@/api/order';
import { mapGetters } from 'vuex';

export default {
  name: 'BaseInfo',
  props: {
    clientOrgCode: Number,
    remarkImageList: {
      type: Array,
      default() {
        return []
      }
    },
    designSoftwareOptinos: {
      type: Array,
      default() {
        return []
      }
    },
    baseInfo: {
      type: Object,
      require: true,
      defalut(){
        return {
          designer: '',
          expectTime: '',
          createdTime: '',
          client: '',
          finishTime: '',
          levelText: '',
          clientRemark: '',
          translateContent: '',
          isUpdateClientRemark: false,
          patientName: '',
          groupQC: 0, // 组内QC
          groupQCName: '',
          designSoftware: '', 
          softwareVersion: ''
        }
      }
    },
    orderStatus: Number,
    canOperateQC: Boolean,
    hasMulDesigner: Boolean,
    isResponsibleDesigner: Boolean,
  },
  data() {
    return {
      translateMsg: '',
      imageList: [],
      i18nTitle: 'order.detail.info',
      // searchQCCode: '',
      userList: [],
      versionOptinos: []
    }
  },
  computed: {
    ...mapGetters(['language', 'userCode']),
    designerContent() {
      if (this.hasMulDesigner) {
        const designerTypes = this.baseInfo.designerTypes
        console.log('designerTypes: ', designerTypes);
        if (designerTypes) {
          if (this.isResponsibleDesigner) {
            return designerTypes.find(item => item.designUser === this.userCode)?.designUserName
          }
          return designerTypes.map(item => item.designUserName).join(',')
        }
        return ''
      } else {
        return this.baseInfo.designer || ''
      }
    }
  },
  watch: {
    remarkImageList: {
      deep: true,
      handler(list) {
        if(list.length > 0) {
          this.getImageList();
        }
      }
    },
    // 'baseInfo.groupQC'(code) {
    //   this.searchQCCode = code;
    // },
    canOperateQC(canSearch) {
      if(canSearch) {
        this.queryUserList();
      }
    },
    // 获取到软件列表后处理版本
    designSoftwareOptinos: {
      deep: true,
      handler(value){
        if(value && value.length > 0){
          // 处理当前的版本信息显示
          const versionOptinos = this.designSoftwareOptinos.find((item) => { return item.code == this.baseInfo.designSoftware})
          this.versionOptinos = versionOptinos && versionOptinos.versions ? versionOptinos.versions : [];
          this.$set(this.baseInfo, 'designSoftwareInfo', versionOptinos ? versionOptinos : { 'code': 0, 'zhName': '未知', 'enName': 'Unknown'});
          if(this.versionOptinos && this.versionOptinos.length){
            const softwareVersionInfo = this.versionOptinos.find((item) => { return item.code == this.baseInfo.softwareVersion});
            this.$set(this.baseInfo, 'softwareVersionInfo', softwareVersionInfo);
          } else {
            this.$set(this.baseInfo, 'softwareVersionInfo', { 'code': 0, 'zhName': '未知', 'enName': 'Unknown'});
          }
        }
      }
    }
  },
  mounted() {
    this.getImageList();
  },
  methods: {
    handleChangeTranslate() {
      this.$emit('getTranslateContent',this.translateMsg)
    },
    changeSoftware(){
      // 处理当前的版本信息显示
      const versionOptinos = this.designSoftwareOptinos.find((item) => { return item.code == this.baseInfo.designSoftware})
      this.baseInfo.softwareVersion = '';
      this.versionOptinos = versionOptinos && versionOptinos.versions ? versionOptinos.versions : [];
      this.$set(this.baseInfo, 'designSoftwareInfo', versionOptinos && versionOptinos.length ? versionOptinos : { 'code': 0, 'zhName': '未知', 'enName': 'Unknown'});
      if(this.versionOptinos && this.versionOptinos.length){
        const softwareVersionInfo = this.versionOptinos.find((item) => { return item.code == this.baseInfo.softwareVersion});
        this.$set(this.baseInfo, 'softwareVersionInfo', softwareVersionInfo);
      } else {
        this.$set(this.baseInfo, 'softwareVersionInfo', { 'code': 0, 'zhName': '未知', 'enName': 'Unknown'});
      }
    },
    getImageList() {
      this.imageList = [];
      this.remarkImageList.forEach(item => {
        const filePath = item.filePath;
        const data = {
          key: filePath,
          url: ''
        };
        this.imageList.push(data);

        const param = {
          s3FileId: filePath,
          orgCode: this.clientOrgCode,
          filename: '',
        };
        getDownloadUrl(param, true).then(res => {
          const url = res.data.url;
          const item = this.imageList.find(item => item.key === filePath);
          if(item) {
            this.$set(item, 'url',url);
          }
        }).catch(err => {
          this.$hgOperateFail(this.$t('order.detail.tips.loadImageFail'));
        });
      });
    },

    openView(url, imageList) {
      const showImageList = imageList.map(item => item.url);
      const index = showImageList.findIndex(img => img === url);
      this.$hgViewer.open({
        imgList: showImageList,
        initialViewIndex: index,
      });
    },
    
    // 先用前端去筛选，看看效果如何
    queryUserList() {
      getUserList('').then(res => {
        this.userList = res.data || [];
        console.log('this.userList: ', this.userList);
      });
    },
    translateTextByLang(item) {
      let lang = this.language;
      if (!item) {
        return lang == 'en' ? 'Unknown' : '未知';
      }
      if (lang == 'en') {
        return item.enName;
      }
      return item.zhName ? item.zhName : item.name;
    },
    async setDesignSoftware(){
      // 获取所有的设计软件列表
      const designSoftwareOptinos = await getDesignSoftwareAll();
      const obj = {
          'code': 0,
          'zhName': '未知',
          'enName': 'Unknown',
          'versions': [
              {
                  'code': 0,
                  'zhName': '未知',
                  'enName': 'Unknown'
              }
          ]
      }
      designSoftwareOptinos.data.push(obj)
      this.designSoftwareOptinos = designSoftwareOptinos.data || [];
    }
  }
}
</script>

<style lang="scss" scoped>
.base-info {

  .el-col {
    height: 56px;
    line-height: 56px;
    border-bottom: 1px dashed $hg-border-second;

    >span {
      display: inline-block;
      color: $hg-default-text-color;
      &:nth-of-type(2) {
        line-height: 16px;
        color: #D7D7D9;
      }
    }

    .label {
      width: 180px;
    }
    .value {
      max-width: calc(100% - 200px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      vertical-align: middle;
    }
    .special-label{
      width: 140px;
    }
    /deep/.design-soft{
      .el-input .el-input__inner{
        width: 120px;
      }
    }
    /deep/.special-design-soft{
      .el-input .el-input__inner{
        width: 180px;
      }
    }
  }

  .el-col.select-qc {
    .el-input {
      max-width: 200px;
      /deep/.el-input__inner {
        
      }
    }
  }

  .design-level {
    display: flex;
    .level-box {
      margin: auto 0;
      line-height: 20px;
      color: #D7D7D9;
    }
  }

   .remark {
    display: flex;
    padding: 15px 0;
    height: auto;
    border-bottom: none;

    &>span:first-of-type {
      min-width: 200px;
    }
    .client-remark-box {
      border: none;
      border-radius: 4px;
      width: 100%;
      padding: 5px;
      line-height: 20px;

      &-show {
        padding-top: 8px;
         
        pre {
          white-space: pre-wrap;
          word-wrap: break-word;
          word-break: keep-all;
          font-family:"webfont";
        }
      }

      .remark-content {
        border-bottom: 1px dashed $hg-border-second;
        .client-remark-box-remark {
          display: inline-block;
          width: 100%;
          padding-bottom: 8px;
        }

        .remark-image-box {
          img {
            margin-right: 8px;
            margin-bottom: 8px;
            padding: 8px;
            width: 84px;
            height: 84px;
            background: $hg-background;
            // border: 1px solid $hg-border-second;
            border-radius: 4px;
          }
        }
      }

      .remark-content.remark-content-unread {
        color: $hg-update;
        .remark-image-box>img {
          border-color: $hg-update;
        }
      }

      .el-textarea {
        /deep/.el-textarea__inner {
          border: none;
          padding: 5px 0;
          padding-top: 8px;
        }
      }
      // 禁用输入后的样式
      .el-textarea.is-disabled{
        /deep/.el-textarea__inner {
          color: $hg-white;
          background-color: $hg-white;
          cursor: default;
        }
      }
    }
  }
}
</style>