import * as THREE from 'three'

import { createMatrix4 } from '../matrix4'
import { getPremultiplyQuaternion } from '../quaternion'

export function rotateObjectByTwoVector3(object, originAxis, resultAxis) {
  const originQuaternion = object.quaternion.clone()

  const quaternion = new THREE.Quaternion()
  quaternion.setFromUnitVectors(originAxis, resultAxis)

  object.quaternion.multiply(quaternion)

  object.updateMatrix()

  const applyQuaternion = getPremultiplyQuaternion(originQuaternion, object.quaternion)

  const matrix = createMatrix4(null, applyQuaternion.invert())

  object.geometry.applyMatrix4(matrix)
}

export function rotateObjectAxis(object, twoGroupAxis) {
  if (twoGroupAxis.length !== 2) {
    return
  }

  const originX = twoGroupAxis[0][0].clone()
  const resultX = twoGroupAxis[0][1].clone()

  const originY = twoGroupAxis[1][0].clone()
  const resultY = twoGroupAxis[1][1].clone()

  const originQuaternion = object.quaternion.clone()

  const quaternion1 = new THREE.Quaternion()
  quaternion1.setFromUnitVectors(originX, resultX)

  object.quaternion.multiply(quaternion1)

  const quaternion2 = new THREE.Quaternion()
  quaternion2.setFromUnitVectors(originY, resultY)

  object.quaternion.multiply(quaternion2)

  object.updateMatrixWorld()

  const applyQuaternion = getPremultiplyQuaternion(originQuaternion, object.quaternion)

  const matrix = createMatrix4(null, applyQuaternion)

  object.geometry.applyMatrix4(matrix.clone().invert())
}
