<template>
  <div>
    <div v-if="lists.length && !redirection" :class="['nav-box', $i18n.locale == 'en' ? 'nav-box-en' : '']">
      <Select ref="menuSelect" :value="curValue" :select-options="lists" @change="goUrl" />
      <i class="iconfont icon-list-default iconfont-24" />
    </div>
    <div v-if="redirection" class="goback fl" @click="redirectionUrl">
      <div>
        <i class="el-icon-sort-down" />
        <span>{{ $t('common.goback') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import Select from './Select'

export default {
  name: 'FuncMenu',
  components: {
    Select
  },
  props: {
    lists: {
      type: Array,
      default: () => {
        return []
      }
    },
    curValue: {
      type: [String, Number, Array, Object],
      default: ''
    }
  },
  data() {
    return {
      // redirection: ''
    }
  },
  computed: {
    redirection() {
      return this.$route.query.redirection ? this.$route.query.redirection : ''
    }
  },
  methods: {
    redirectionUrl() {
      window.location.href = window.location.origin + decodeURIComponent(this.redirection)
    },
    goUrl(path) {
      if (path.indexOf('customer') !== -1) {
        window.location.href = window.location.origin + path
      } else {
        window.location.href = window.location.origin + path + '/#/'
      }
    }
  },
  mounted () {
    // this.redirection = this.$route.query.redirection
  }
}
</script>
<style lang="scss" scoped>
.goback {
  cursor: pointer;
  display: inline-block;
  width: 80px;
  height: 32px;
  border: 1px solid rgba(56, 57, 61, 1);
  margin-right: 24px;
  color: rgba(131, 134, 143, 1);
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  .el-icon-sort-down {
    transform: rotate(90deg);
    margin-right: 8px;
  }
}
.nav-box {
  position: relative;
  width: 160px;
  cursor: pointer;
  i {
    position: absolute;
    top: 7px;
    right: 24px;
    color: $hg-primary-fontcolor;
    z-index: 1;
  }
  ::v-deep .el-select{
    z-index: 2;
    .el-input {
      .el-input__suffix {
        display: none;
      }
    }

  }
  &.nav-box-en {
    width: 245px;
  }
}

</style>
