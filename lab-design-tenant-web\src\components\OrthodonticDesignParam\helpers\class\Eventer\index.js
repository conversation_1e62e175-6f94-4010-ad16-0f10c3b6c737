import hat from 'hat'

export default class Eventer {
  constructor() {
    this.eventStore = new Map()
    this.eventer = this
  }

  destroy() {
    this.eventStore.clear()
  }

  on(name, callback) {
    if (!this.eventStore.has(name)) {
      this.eventStore.set(name, [])
    }

    const eventNameList = this.eventStore.get(name)
    const id = hat()
    const event = {
      id,
      callback,
    }
    eventNameList.push(event)

    return () => {
      this.off(id)
    }
  }

  once(name, callback) {
    if (!this.eventStore.has(name)) {
      this.eventStore.set(name, [])
    }

    const eventNameList = this.eventStore.get(name)
    const id = hat()
    const event = {
      id,
      callback,
      type: 'once',
    }
    eventNameList.push(event)

    return () => {
      this.off(id)
    }
  }

  only(name, callback) {
    const eventNameList = []
    this.eventStore.set(name, eventNameList)
    const id = hat()
    const event = {
      id,
      callback,
      type: 'only',
    }
    eventNameList.push(event)

    return () => {
      this.off(id)
    }
  }

  emit(name, ...args) {
    if (!this.eventStore.has(name)) {
      return
    }

    let result

    const eventNameList = this.eventStore.get(name)
    eventNameList.forEach((event) => {
      const { id, callback, type } = event
      const res = callback(...args)

      switch (type) {
        case 'once': {
          this.off(id)

          break
        }

        case 'only': {
          result = res

          break
        }
      }
    })

    return result
  }

  has(name) {
    return this.eventStore.has(name)
  }

  get(name) {
    const eventNameList = this.eventStore.get(name)
    return eventNameList
  }

  off(id) {
    if (!arguments.length) {
      this.eventStore.clear()
      return
    }

    for (const [name, eventNameList] of this.eventStore) {
      for (let i = 0; i < eventNameList.length; i++) {
        const event = eventNameList[i]
        if (event.id === id) {
          eventNameList.splice(i, 1)
          if (!eventNameList.length) {
            this.eventStore.delete(name)
          }
          break
        }
      }
    }
  }
}

// export function afterAopEventer(instance, aop) {
//   const origin = instance.on

//   instance.on = function () {
//     origin.apply(this, arguments)
//     aop.apply(this, arguments)
//   }
// }

export function mergeOn(instanceList, eventName, cb) {
  const length = instanceList.length
  const data = []
  const eventOffList = []

  for (let i = 0; i < length; i++) {
    const instance = instanceList[i]

    const eventOff = instance.on(eventName, function () {
      eventOffList.push(eventOff)

      for (let j = 0; j < arguments.length; j++) {
        if (!data[j]) {
          data[j] = []
        }
        data[j].push(arguments[j])
      }

      if (i === length - 1) {
        cb(data)

        for (const eventOff of eventOffList) {
          eventOff()
        }
      }
    })
  }
}
