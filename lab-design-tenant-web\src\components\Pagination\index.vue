<template>
  <div v-show="total > 0" class="hg-pagination">
    <el-pagination
      background
      ref="paginationPage"
      class="pagination-context"
      @current-change="handleCurrentChange"
      :current-page.sync="currentPage"
      :page-size="currentPageSize"
      :layout=" `${showTotal ? 'total,' : ''} prev, pager, next, slot`"
      :total="total"
      :disabled="disabled"
      >
      <!-- 每页个数 -->
      <div class="page-size-box">
        <el-select ref="paginaSelect" v-model="currentPageSize" @change="changePageSize" :disabled="disabled" filterable remote :remote-method="remoteMethod" @keyup.enter.native="handleEnter" @blur="handleBlur">
          <el-option 
            v-for="number in pageSizeList" 
            :key="number"
            :value="number"
            :label="number+$t('component.pagination.sizeLabel')">
          </el-option>
        </el-select>
      </div>
      <!-- 跳至按钮 -->
      <div class="jumper-box">
        <span>{{ $t('component.pagination.jumpLabel') }}</span>
        <el-input v-model="jumpValue" @change="changeJumpNumber" :disabled="disabled"></el-input>
        <span>{{ $t('component.pagination.page') }}</span>
      </div>
    </el-pagination>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'pagination',
  props: {
    pageSizes: {  // 每页显示个数选择器 可配置
      type: Array,
      default(){
        return [5,10,20,25,50,100]
      }
    },
    total: Number,  // 总数
    initPageIndex: Number,  // 当前页数
    initPageSize: Number, // 当前每页条数
    disabled: {
      type: Boolean,
      default: false
    },
    isHavePageSize: {
      type: Boolean,
      default: true
    },
    isHaveJump: {
      type: Boolean,
      default: true
    },
    showTotal: Boolean
  },
  data(){
    return {
      pageSizeList: this.pageSizes,
      currentPage: 1, // 当前页数
      jumpValue: 1,  // jump 跳至x页
      currentPageSize: 10,  // 当前条数
      inputValue: '', // 用于存储用户输入的值
      inputValueList: []
    }
  },

  computed: {
    ...mapGetters(['language']),
    pageCounts(){ // 总页数
      if(this.total) {
        return Math.ceil(this.total/this.currentPageSize); // 向上取整
      }
      return 0;
    },
  },
  watch: {
    initPageIndex: {
      immediate: true,
      handler(value){
        if(value && value !== this.currentPage) {
          this.currentPage = value;

          // fix element-ui internalCurrentPage 不同步的问题 （暂时）
          // total 一开始初始化为null 也能解决
          // issues https://github.com/elemefe/element/issues/3188#issuecomment-429197298
          this.$nextTick(() => {
            this.$refs.paginationPage.internalCurrentPage = this.currentPage;
          });

          if(value === 1) { // 页数回到第一页则jumpValue也回归
            this.jumpValue = 1;
          }
        } 
      }
    },
    initPageSize: {
      immediate: true,
      handler(value){
        if (value && value !== this.currentPageSize) {
          this.currentPageSize = value;
        }
      }
    }
  },

  methods: {
    // 初始化 当前页数 跳转页数
    init(){
      this.currentPage = 1;
      this.jumpValue = 1;
    },

    // 切换页数
    handleCurrentChange() {
      this.filterData();
    },

    /**
     * 改变每页数量
     * @param {Number} value 每页个数选择器 change事件，当前页数 大于总页数，两值需要回到1
     */
    changePageSize(value){
      //console.log(value, this.currentPage, this.pageCounts, this.jumpValue);
      if(this.currentPage > this.pageCounts){
        this.init();
      }

      // if(this.inputValueList.length) {
      //   this.pageSizeList = this.pageSizeList.concat(this.inputValueList)
      // }
      this.filterData();
    },

    /**
     * 跳到指定页
     * @param {Number} value 【跳至】输入框change事件
     */
    changeJumpNumber(value){
      const number = Number(value);
      if(number && number > 0){
        const currentNumber = number > this.pageCounts ? this.pageCounts : number;
        this.currentPage = currentNumber;
        this.jumpValue = currentNumber;
      }else {
        this.init();
      }
      this.filterData();
    },

    /**
     * 搜索
     */
    filterData(){
      const param = {
        pageIndex: this.currentPage,
        pageSize: this.currentPageSize
      }
      this.$emit('onSearch', 'page', param);
    },

    // 自定义条数部分代码 ++++++++++++++
    remoteMethod(query){
      this.inputValue = query
    },
    handleEnter() {
      this.$refs.paginaSelect.blur();
      this.handleBlur()
    },
    handleBlur(){
      this.addPageSize();
    },
    addPageSize() {
      let num = Number(this.inputValue);
      // 检查输入是否为有效数字且不在 pageSizes 中
      if (!isNaN(num) && num > 0 && !this.pageSizeList.includes(num)) {
        if(num > 200) num = 200;
        this.inputValueList.push(num)
        this.pageSizeList.push(num); // 添加到 pageSizes
        this.currentPageSize = num; // 选中最新输入的值
        this.changePageSize();
      }
      // 如果输入的值是页面有的
      if(this.pageSizeList.includes(num)){
        this.currentPageSize = num;
        this.changePageSize();
      }
      this.inputValue = '';
    },

  }
}
</script>

<style lang="scss" scoped>

.hg-pagination {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  width: auto;
  height: 58px;
  background: $hg-main-black;
  box-shadow: 0px -1px 0px 0px $hg-border;
}


.hg-pagination {
  .pagination-context {
    display: flex;
    margin-right: 20px;
    /deep/ .el-pagination__total{
      color: $hg-label;
    }
    /deep/.btn-prev, /deep/.btn-next {
      width: 32px;
      height: 32px;  
      border-radius: 1px;
      background: transparent;
      border: 1px solid $hg-border;
      color: $hg-label;
      .el-icon {
        font-size: 14px;
      }

      /* &:hover {
        border-radius: 1px;
        background: #3054CC;
        border: 1px solid #3054CC;
      } */

      &:disabled {
        border-radius: 1px;
        background: rgba(84, 86, 92, 0.25);
        border: 1px solid $hg-border;
        color: $hg-secondary-text;
      }
    }

    /deep/.el-pager {
      >li {
        min-width: 32px;
        margin: 0 4px;
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        border-radius: 1px;
        border: 1px solid $hg-border;
        color: $hg-label;
        background: transparent;
        &:hover {
          border-radius: 1px;
          border: 1px solid $hg-main-blue;
        }
      }

      >li.active {
        border-radius: 1px;
        background: $hg-main-blue;
        color: $hg-label;
      }

      >.el-icon.more{
        border-radius: 1px;
        border: none;
      }

    }

  }
}

.hg-pagination>.pagination-context .page-size-box {
  margin-left: 20px;
  .el-select {
    /deep/.el-input {
      margin: 0;
      width: auto;
    }

    /deep/.el-input__inner {
      padding-left: 10px;
      width: 129px;
      height: 32px !important;
      line-height: 32px;
      border-radius: 1px;
    }
    /deep/.el-input__suffix {
      .el-select__caret.el-input__icon {
        line-height: 32px;
      }
    }
  }
}

.hg-pagination>.pagination-context .jumper-box {
  margin-left: 12px;
  color: $hg-label;

  .el-input {
    margin: 0 6px;
    width: auto;

    /deep/.el-input__inner {
      padding: 6px;
      width: 44px;
      height: 32px;
      text-align: left;
      border-radius: 1px;
    }
  }

  >span {
    font-size: 14px;
    line-height: 32px;
    min-width: auto;
  }
}
</style>