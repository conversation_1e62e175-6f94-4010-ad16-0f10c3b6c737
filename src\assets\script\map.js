/**
 * 网络状态码的映射（消息提示访问失败）
 */
export const NETWORK_STATUS = {
  SUCCESS: 200, //"操作成功"
  FAILURE: 400, //"操作失败"
  SERVICE_ERROR: 500,//"服务器内部错误"
  PARAM_ERROR: 501,//"请求参数错误"),
  PERMISSION_DENIED: 502,// "无权限访问"
  DABS_ERROR: 503,//"数据库操作失败"
  REDIS_ERROR: 504, //"redis错误"
  SERVICE_FAILURE: 505, //"服务调用失败"
  LOGIN_FAIL: 506, //"登录失败"
  REFERER_FAILURE: 70100003, // "访问透传信息失败"
  CONNECT_FAILURE: 70100004, // "连接访问服务失败"
  REFERER_NULL: 70120001, // 访问透传信息失败:Referer为空，请确认！
  BUSINESS_ERRO: 70100006, // ${业务错误提示}
  SERVICE_ERROR_2: 70100000, // 服务异常，请稍后再尝试
}

/**
 * 权限问题的映射(消息提示无权限访问)
 */
export const AUTH_STATUS = {
  TOKEN_AUTHORITY_FAILURE_1: 70100002, // 无权限访问
  TOKEN_AUTHORITY_FAILURE_2: 70140002, // 无权限访问
  TOKEN_AUTHORITY_FAILURE_3: 70140001, // null
}

/**
 * 登录问题的映射(弹窗提示异地登录或登录过期，请重新登录)
 */
export const LOGIN_ERR_STATUS = {
  REMOTE_LOGIN: 70110006, // "异地登录"
  TOKEN_TIMEOUT: 70110001, // 校验失败:Token已过期
  TOKEN_CHECK_FAILURE: 70110002, // 校验失败:Token验证失败
  ACCESSTOKEN_TIMEOUT: 70110003, // 校验失败:accessToken已过期，请重新登录！
  REFRESHTOKEN_TIMEOUT: 70110004, // 校验失败:refreshToken已过期，请重新登录！
  ACCESSTOKEN_NULL: 70110005, // 校验失败:accessToken无效，请重新登录！
  USER_CANCEL_2: 70110007, // 校验用户信息失败
  USER_CANCEL_1: 70100001, // 校验用户信息失败
  USER_INFO_TIMEOUT: 70130001, // 服务异常，请稍后再尝试:用户信息已过期，请重新登录！
  TOKEN_NULL: 70130002, // 服务异常，请稍后再尝试: token为空，请重新登录！
}

/**
 * 时区号与地区的映射(目前heygears只涉及下面12个地区)
 */
 export const TIMEZONE = {
  10: {countryCode: "CN", tzCode: 10, tzName: "Asia/Shanghai", utc: 480},
  11: {countryCode: "JP", tzCode: 11, tzName: "Asia/Tokyo", utc: 540},
  12: {countryCode: "US", tzCode: 12, tzName: "America/New_York", utc: -300},
  13: {countryCode: "GB", tzCode: 13, tzName: "Europe/London", utc: 0},
  14: {countryCode: "RU", tzCode: 14, tzName: "Europe/Moscow", utc: 180},
  15: {countryCode: "AU", tzCode: 15, tzName: "Australia/Sydney", utc: 600},
  16: {countryCode: "CA", tzCode: 16, tzName: "America/Vancouver", utc: -480},
  17: {countryCode: "NE", tzCode: 17, tzName: "Africa/Niamey", utc: 60},
  18: {countryCode: "EG", tzCode: 18, tzName: "Africa/Cairo", utc: 120},
  19: {countryCode: "IN", tzCode: 19, tzName: "Asia/Kolkata", utc: 330},
  20: { countryCode: "BR", tzCode: 20, tzName: "America/Sao_Paulo", utc: -180 },
  21: { countryCode: "FR", tzCode: 21, tzName: "Europe/Paris", utc: 60 },
  22: { countryCode: "DE", tzCode: 22, tzName: "Europe/Berlin", utc: 60 },
  23: { countryCode: "ES", tzCode: 23, tzName: "Europe/Madrid", utc: 60 },
  24: { countryCode: "PL", tzCode: 24, tzName: "Europe/Warsaw", utc: 60 },
  25: { countryCode: "IT", tzCode: 25, tzName: "Europe/Rome", utc: 60 },
  26: { countryCode: "LT", tzCode: 26, tzName: "Europe/Vilnius", utc: 120 },
  27: { countryCode: "BE", tzCode: 27, tzName: "Europe/Brussels", utc: 60 },
  28: { countryCode: "SL", tzCode: 28, tzName: "Africa/Freetown", utc: 0 },
}
