import * as THREE from 'three'
import { traverseArrayByCombination } from '@/components/OrthodonticDesignParam/helpers/utils'
import { createMatrix4 } from './matrix4'
import { VIEW_MATRIXS } from './constants'

export function createCameraMatrixFactor(frontEye, viewValue) {
  for (const key in VIEW_MATRIXS) {
    if (key === viewValue) {
      const matrix = VIEW_MATRIXS[key].clone()

      const position = new THREE.Vector3()
      position.copy(frontEye).applyMatrix4(matrix)

      const up = new THREE.Vector3(0, 1, 0)
      matrix.multiply(createMatrix4(null, new THREE.Euler(Math.PI / 2, 0, 0)))
      up.applyMatrix4(matrix)

      return {
        position,
        up,
      }
    }
  }
}

export function setCameraMatrixByEye(camera, frontEye, viewValue) {
  const { position, up } = createCameraMatrixFactor(frontEye, viewValue)
  camera.position.copy(position)
  camera.up.copy(up)
  camera.updateMatrix()
}

export function getCameraUpDirectionKey(camera) {
  let cameraUpDirectionKey

  const up = camera.up

  traverseArrayByCombination(['x', 'y', 'z'], (left, right) => {
    cameraUpDirectionKey = Math.abs(up.left) > Math.abs(up.right) ? left : right

    return true
  })

  return cameraUpDirectionKey
}
