<template>
  <hg-card v-show="showSidebar" :class="['sidebar', isCollapse ? 'expand-sidebar' : '']">

    <el-menu
      background-color="#1B1D22"
      :default-active="currentPath"
      :unique-opened="false"
      :collapse="isCollapse"
      :collapse-transition="false"
      :text-color="variables.hgSecondaryText"
      @close="closeMenu"
      :default-openeds="defaultOpeneds"
      ref="myMenu"
      @select="handleMenuSelect">

      <template v-for="(menuItem, index) in sidebarList">

        <!-- 展开 -->
        <el-submenu 
          v-if="menuItem.isDrop" 
          popper-class="lab-sub-menu"
          :index="menuItem.path" 
          :key="index">
          <div slot="title" :class="{'sidebar-item': true, 'sidebar-item_en': language === 'en', 'is-active': currentPath.indexOf(menuItem.path) > -1}">
            <hg-icon :icon-name="menuItem.icon" font-size="24px"></hg-icon>
            <span v-show="!isCollapse">{{ menuItem.label }}</span>
          </div>

          <router-link 
            v-for="(cMenu, cIndex) in menuItem.children" 
            :key="cIndex" 
            :to="cMenu.path">
            <el-menu-item 
              :index="cMenu.path" 
              :id="cMenu.path"
              :class="['sidebar-item_link', language === 'en' ? 'sidebar-item_link_en' : '', currentPath === cMenu.path ? 'is-active' : '']">
              <span>{{ cMenu.label }}</span>   
              <span style="margin-left: 40px;color:#3760EA;" v-show="isCollapse && currentPath === cMenu.path" class="el-icon-check"></span>
            </el-menu-item>  
          </router-link>

        </el-submenu>

        <router-link v-else :key="index" :to="menuItem.path">
          <el-menu-item 
            :index="menuItem.path"
            :class="{'sidebar-item': true, 'sidebar-item_en': language === 'en', 'is-active': currentPath === menuItem.path}">
            <hg-icon :icon-name="menuItem.icon" font-size="24px"></hg-icon>
            <span v-show="!isCollapse">{{ menuItem.label }}</span>
          </el-menu-item>
        </router-link>

      </template>
      
    </el-menu>
    <span class="expang-icon" @click="expandMenu"><hg-icon :icon-name="isCollapse ? 'icon-icon_unfold' : 'icon-icon_fold'" font-size="24px"></hg-icon></span>
  </hg-card>
</template>

<script>
import variables from '@/assets/styles/export.scss';
import { mapGetters } from 'vuex';
import { ROUTE_PATH } from '@/public/constants';

export default {
  name: 'Sidebar',
  data(){
    return {
      variables,
      ROUTE_PATH,

      // 需要隐藏的菜单
      hidSidebarList: [ROUTE_PATH.ORDER_DETAIL, ROUTE_PATH.BILL, ROUTE_PATH.BILL_DETAIL, ROUTE_PATH.HEYPOINT_CUSTOMER_INFO],

      // 设计中心
      designMenuPath: [ 
        ROUTE_PATH.DATA_BOARD,
        ROUTE_PATH.ORDER_LIST, 
        ROUTE_PATH.MANAGE_USER, 
        ROUTE_PATH.ORDER_UNVERIFIED, 
        ROUTE_PATH.POINTS_ALLOCATION,
        ROUTE_PATH.DESIGNER_POINTS,
        ROUTE_PATH.MY_DESIGN_POINTS],

      heyPointPath: [
        ROUTE_PATH.HEYPOINT_CUSTOMER,
        ROUTE_PATH.HEYPOINT_SETTING,
        ROUTE_PATH.HEYPOINT_LOG
      ],

      menuIcon: new Map([
        [ROUTE_PATH.ORDER_LIST, 'icon-design-menu-order-lab'],
        [ROUTE_PATH.MANAGE_USER, 'icon-design-menu-user-lab'],
        [ROUTE_PATH.HEYPOINT_CUSTOMER, 'icon-design-menu-heypoint-lab'],
        [ROUTE_PATH.HEYPOINT_SETTING, 'icon-design-menu-setting-lab'],
        [ROUTE_PATH.HEYPOINT_LOG, 'icon-design-menu-log-lab'],
        [ROUTE_PATH.DATA_BOARD, 'icon-design-menu-data-lab'],

        [ROUTE_PATH.DESIGNER_POINTS, 'icon-recent_actors'],
        [ROUTE_PATH.MY_DESIGN_POINTS, 'icon-recent_actors'],
      ]),
      isCollapse: false,
      defaultOpeneds: [ROUTE_PATH.ORDER_LIST, ROUTE_PATH.CONFIGURATION],
    }
  },
  computed: {
    ...mapGetters(['menuList', 'language', 'changeRoute']),
    isHeyPoint(){
      return this.heyPointPath.some(item => this.$route.path.includes(item));
    },
    showSidebar(){
      return !this.hidSidebarList.some(item => this.$route.path.includes(item));
    },
    sidebarList() {
      let resultList = [];
      console.log('this.menuList1: ', this.menuList);
      if(this.isHeyPoint) {
        resultList = this.menuList.filter(item => this.heyPointPath.includes(item.path));
        resultList.forEach((item) => {
          item.label = this.$t(`apiCommon.${item.i18nCode}`)
        })
      }else {
        // resultList = this.menuList.filter(item => this.designMenuPath.includes(item.path));
        resultList = this.initDesignMenus();
      }
      resultList.forEach(item => {
        item.icon = this.menuIcon.get(item.path) || item.icon;
      });
      // console.log('resultList: ', resultList);
      return resultList;
    },

    currentPath() {
      // const url = window.location.href;
      // if(url.includes(this.$route.path)){
      //   return this.$route.path;
      // }
      return this.$route.path;
    }
  },

  watch: {
    $route(route) {
      // console.log('route变化：',route);
      // this.changeRoute = route.fullPath;
    }
  },
  mounted () {
    console.log(this.$refs.myMenu, 6666);
    // console.log(document.getElementById('/configuration/pointsAllocation'))
  },

  methods: {
    initDesignMenus() {
      const menuList = this.menuList.filter(item => this.designMenuPath.includes(item.path));
      let resultList = [];

      const orderParentMenu = {
        icon: 'icon-design-menu-order-lab',
        name: '订单管理',
        label: this.language === 'zh' ? '订单管理' : 'Orders',
        nameEn: 'Orders',
        children: [],
        isDrop: true,
        path: ROUTE_PATH.ORDER_LIST,
      }
      const configParentMune = {
        icon: 'icon-settings_suggest',
        name: '配置管理',
        label: this.language === 'zh' ? '配置管理' : 'Staff & Config',
        nameEn: 'Staff & Config',
        children: [],
        isDrop: true,
        path: ROUTE_PATH.CONFIGURATION,
      }
      menuList.forEach(menu => {
        menu.label = this.$t(`apiCommon.${menu.i18nCode}`)
        if([ROUTE_PATH.ORDER_LIST, ROUTE_PATH.ORDER_UNVERIFIED].includes(menu.path)) {
          const parentIndex = resultList.findIndex(item => item.name === '订单管理');
          if(parentIndex> -1) {
            resultList[parentIndex].children.push(menu);
          }else {
            orderParentMenu.children.push(menu);
            resultList.push(orderParentMenu);
          }
          // 20240418 新增配置管理，将之前的人员管理也和点数配置放一起
        } else if([ROUTE_PATH.MANAGE_USER, ROUTE_PATH.POINTS_ALLOCATION].includes(menu.path)){
          const parentIndex = resultList.findIndex(item => item.name === '配置管理');
          if(parentIndex> -1) {
            resultList[parentIndex].children.push(menu);
          }else {
            configParentMune.children.push(menu);
            resultList.push(configParentMune);
          }
        } else {
          resultList.push(menu);
        }
      });

      return resultList;
    },
    expandMenu(){
      this.isCollapse = !this.isCollapse;
    },
    // 关闭父级菜单要清掉默认展开
    closeMenu(index, indexPath){
      let deleteindex = this.defaultOpeneds.indexOf(indexPath)
      if(deleteindex != -1){
        this.defaultOpeneds.splice(deleteindex, 1)
      }
    },
    handleMenuSelect(index){
      // this.currentPath = '/configuration/pointsAllocation'
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar {
  position: relative;
  width: 240px;

  .el-menu {
    border: none;
  }

  .sidebar-item {
    margin-bottom: 16px;
    padding-left: 20px !important;
    height: 48px;
    line-height: 48px;

    .hg-icon {
      margin-right: 12px;
    }

    &:hover {
      background: transparent !important;
    }

    &>span {
      font-weight: bold;
    }
  }

  .sidebar-item_en {
    padding-left: 24px !important;
  }

  .sidebar-item.is-active {
    border-radius: 4px;
    background: $hg-main-blue !important;
    color: $hg-default-text-color !important;

    .hg-icon {
      color: $hg-default-text-color;
    }
  }

  // 有子节点的title
  .el-submenu {
    margin-bottom: 16px;

    /deep/.el-submenu__icon-arrow {
      
      &::before {
        content: "\e790";
      }
    }
  }

  .el-submenu.is-active {
    /deep/.el-submenu__icon-arrow {
      color: $hg-default-text-color;
    }
  }
  /deep/.el-submenu__title {
    padding: 0 !important;
    height: 48px;
    line-height: 48px;

    &:hover {
      background: transparent !important;
    }
  }

  .sidebar-item_link {
    margin: 8px 0;
    border-radius: 4px;
    height: 36px;
    line-height: 36px;
    min-width: auto;

    &>span {
      margin-left: 16px;
    }

    &:hover {
      background: transparent !important;
    }
  }

  .sidebar-item_link_en {
    &>span {
      margin-left: 20px;
    }
  }

  .sidebar-item_link.is-active {
    background: rgba(243, 245, 247, 0.12) !important;
    color: $hg-default-text-color !important;
  }
  .expang-icon{
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
}
.expand-sidebar{
  width: 115px;
}
</style>
<style lang="scss">
.lab-sub-menu{
  .el-menu-item.is-active{
    // background: rgba(243, 245, 247, 0.12) !important;
    color: $hg-default-text-color !important;
  }
  .el-menu-item:hover{
    background: rgba(243, 245, 247, 0.12) !important
  }
}
</style>