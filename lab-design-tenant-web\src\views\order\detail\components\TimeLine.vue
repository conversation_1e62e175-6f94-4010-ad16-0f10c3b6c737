<template>
  <hg-card class="time-line">
    <el-collapse accordion value="process">
      <el-collapse-item name="process" >
        <order-title slot="title" langName="process"></order-title>
        <hg-card class="time-line-box">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in contentList"
              :key="index">
              <span class="time">{{ item.timestamp | dateFormatInHtml('yyyy.MM.DD HH:mm') }}</span>
              <div class="content-box">
                <span class="content">{{item.content}}</span>
                <div class="return-image-box" v-show="item.imageList.length > 0">
                  <img v-for="url in item.imageList" :key="url" :src="url" @click.stop="openView(url, item.imageList)" alt="">
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </hg-card>
      </el-collapse-item>
    </el-collapse>
  </hg-card>
</template>

<script>
import OrderTitle from './OrderTitle';
import { PROCESS_TYPE } from '@/public/constants';
import { parseJson } from '@/public/utils';
import { getDownloadUrl } from '@/api/file';
import { mapGetters } from 'vuex';

export default {
  name: 'TimeLine',
  components: { OrderTitle },
  props: {
    processList: {
      type: Array,
      retuqire: true,
      default(){
        return []
      }
    },
    clientOrgCode: {
      type: Number,
      require: true,
    }
  },
  data() {
    return {
      i18nTitle: 'order.detail.timeLine',
      contentList: [],
    }
  },
  watch: {
    processList(){
      this.initData();
    }
  },
  computed: {
    ...mapGetters(['language', 'oneDesignList']),
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      let contentList = [];
      let contentIndex = 0;
      this.processList.forEach((processItem, index) => {
        const { type, realName:user, content, reasonImage, time, userName, operator, groupQcName } = processItem;
        let processContent = '';
        let imageList = [];

        // 用switch是为了能更加直观了解对应的type是什么操作
        switch(type) {
          case PROCESS_TYPE.CREATED_ORDER:
            processContent = this.$t(`${this.i18nTitle}.1`, [ user ]);
            break;
          case PROCESS_TYPE.CLIENT_UPDATE_ORDER:
            processContent = this.$t(`${this.i18nTitle}.2`, [ user ]);
            break;
          case PROCESS_TYPE.CLINET_UPDATE_REMARK:
            processContent = this.$t(`${this.i18nTitle}.3`, [ user ]);
            break;
          case PROCESS_TYPE.CLIENT_CONFIRM_ORDER:
            processContent = this.$t(`${this.i18nTitle}.4`, [ user ]);
            break;
          case PROCESS_TYPE.REVOKE_ORDER_BY_CLIENT: 
            processContent = this.$t(`${this.i18nTitle}.6`, [ user ]);
            break;
          case PROCESS_TYPE.DOWNLOAD_RESULT: 
            processContent = this.$t(`${this.i18nTitle}.7`, [ user ]);
            break;
          case PROCESS_TYPE.RETURN_BY_CLIENT: 
            processContent = this.$t(`${this.i18nTitle}.8`, [ user, content ]);
            break;
          case PROCESS_TYPE.BATCH_TRANSLATE_ORDER:
            processContent = this.$t(`${this.i18nTitle}.13`, [ user ]);
            break;
          case PROCESS_TYPE.REBACK_TO_CLIENT:
            processContent = this.$t(`${this.i18nTitle}.14`,[user, content]);
            break;
          case PROCESS_TYPE.TRANSLATED_ORDER:
            processContent = this.$t(`${this.i18nTitle}.15`, [ user ]);
            break;
          case PROCESS_TYPE.CONTINUE_DESIGN:
            processContent = this.$t(`${this.i18nTitle}.16`,[user, content]);
            break;
          case PROCESS_TYPE.START_TO_DESIGN:
            processContent = this.$t(`${this.i18nTitle}.17`,[user]);
            break;
          case PROCESS_TYPE.FINISH_DESIGN:
            processContent = groupQcName ? this.$t(`${this.i18nTitle}.32`, [user, groupQcName]) : this.$t(`${this.i18nTitle}.18`, [user]);
            // processContent = this.$t(`${this.i18nTitle}.18`, [user]);
            break;
          case PROCESS_TYPE.REBACK_TO_IQC:
            processContent = this.$t(`${this.i18nTitle}.19`, [user, content]);
            break;
          case PROCESS_TYPE.REVOKE_BY_DESIGNER:
            processContent = this.$t(`${this.i18nTitle}.20`, [user])
            break;
          case PROCESS_TYPE.CONFIRM_PASS:
            processContent = this.$t(`${this.i18nTitle}.21`, [operator, user]);
            break;
          case PROCESS_TYPE.NOT_PASS:
            processContent = this.$t(`${this.i18nTitle}.22`,[content, user]);
            break;
          case PROCESS_TYPE.REVOKE_FROM_CLIENT:
            processContent = this.$t(`${this.i18nTitle}.23`,[user]);
            break;
          case PROCESS_TYPE.REVOKE_RETURNORDER:
            processContent = this.$t(`${this.i18nTitle}.25`,[user]);
            break;
          case PROCESS_TYPE.EDIT_ORDER_BY_DESIGN:
            processContent = this.$t(`${this.i18nTitle}.26`,[user, userName]);
            break;
          case PROCESS_TYPE.CLINET_ASK_FOR_FREE:
            processContent = this.$t(`${this.i18nTitle}.27`,[user, content]); 
            break;
          case PROCESS_TYPE.APPROVE_FOR_FREE:
            processContent = this.$t(`${this.i18nTitle}.28`,[user]); 
            break;
          case PROCESS_TYPE.DISAPPROVE_FOR_FREE:
            processContent = this.$t(`${this.i18nTitle}.29`,[user]) + `：${content}`; 
            break;
          case PROCESS_TYPE.IS_NORMAL_QUESTION: 
            processContent = this.$t(`${this.i18nTitle}.31`,[user]); 
            break;
          case PROCESS_TYPE.FILL_DESIGN:
            processContent = this.$t(`${this.i18nTitle}.33`,[this.getDesignCategory(content)]); 
            break;
          // 设计师提交了种植方案
          case PROCESS_TYPE.SUBMIT_IMPLANT:
            processContent = this.$t(`${this.i18nTitle}.36`,[user]); 
            break;
          // 客户确认了种植方案
          case PROCESS_TYPE.CONFIRM_IMPLANT:
            processContent = this.$t(`${this.i18nTitle}.37`,[user]);
            break;
          // 客户返单种植方案
          case PROCESS_TYPE.RETURN_IMPLANT:
            processContent = this.$t(`${this.i18nTitle}.38`,[user]);
            break;
          default: break;

        }
        if(processContent){
          contentIndex++;
          contentList.push({
            key: contentIndex,
            content: processContent,
            timestamp: time,
            imageList,
          });

          if(reasonImage) {
            imageList = parseJson(reasonImage) || [];
            if(imageList.length > 0) {
              imageList = imageList.filter(item => item);
              this.handleImage(imageList, contentIndex);
            }
          }

        }
      });
      
      this.contentList = contentList;
    },

    async handleImage(imageS3List = [], index) {
      if(imageS3List.length === 0) return;

      let reqList = [];
      imageS3List.forEach(s3FileId => {
        const req = new Promise(resovle => {
          const param = {
            s3FileId,
            orgCode: this.clientOrgCode,
            filename: '',
          };
          getDownloadUrl(param, true).then(res => {
            resovle(res.data.url);
          }).catch(err => {});
        });
        reqList.push(req);

      });

      let imageList = [];
      await Promise.all(reqList).then(res => {
        imageList = res;
      });

      this.contentList.forEach((item) => {
        if(item.key === index) {
          this.$set(item,'imageList', imageList);
        }
      });
    },

    openView(url, imageList) {
      const index = imageList.findIndex(img => img === url);
      this.$hgViewer.open({
        imgList: imageList,
        initialViewIndex: index,
      });
    },
    // 获取当前设计品类
    getDesignCategory(code){
      let codeList = code.split(',')
      let category = ''
      // if(this.language == 'zh'){
      //   codeList.forEach((it) => {
      //     category += this.oneDesignList.find((item) => {return item.designCode == it}).cnName + '、'
      //   })
      // } else {
      //   codeList.forEach((it) => {
      //     category += this.oneDesignList.find((item) => {return item.designCode == it}).enName + '、'
      //   })
      // }
      codeList.forEach((it) => {
        category += this.$t(`apiCommon.${it}`) + '、'
      })
      category = category.slice(0, category.length - 1)
      return category
    },
  }

}
</script>

<style lang="scss" scoped>
.time-line .time-line-box {
  margin-top: 24px;
  padding: 24px;
  border-top: 1px dashed #38393D;

  .el-timeline {
    .el-timeline-item {
      padding-bottom: 0;
      min-height: 48px;

      &:first-of-type {
        /deep/.el-timeline-item__node {
          border: 2px solid $hg-secondary-primary;
        }
      }

      &:last-of-type {
        height: auto;
      }
    }

    /deep/.el-timeline-item__tail {
      border-left: 1px dashed rgba(243, 245, 247, 0.24);
      top: 18px;
      left: 4px;
      height: 75%;
    }
    /deep/.el-timeline-item__node {
      width: 10px;
      height: 10px;
      left: 0;
      top: 7px;
      background-color: transparent !important;
      border: 2px solid rgba(243, 245, 247, 0.24);
    }
    /deep/.el-timeline-item__wrapper {
      //padding-left: 60px;
      .el-timeline-item__content {
        display: flex;
      }
    }
  }

  .time {
    width: 120px;
  }

  .content-box {
    flex: 1;
    padding-left: 120px;
    word-break:normal; 
    width:auto; 
    display:block; 
    white-space:pre-wrap;
    word-wrap : break-word ;
    overflow: hidden ;
  }

}

.return-image-box {
  display: flex;
  flex-direction: row;
  margin-top: 8px;
  padding-bottom: 12px;

  img {
    margin-right: 8px;
    padding: 8px;
    width: 54px;
    height: 54px;
    border-radius: 4px;
    background: $hg-background;
  }

}
</style>

<style lang="scss">
.time-line .time-line-box {
  .el-timeline {
    .el-timeline-item:first-of-type {
      .el-timeline-item__content {
        color: $hg-secondary-primary;
        .time {
          font-weight: bold;
        }
      }
    }
  }

  .el-timeline-item__wrapper {
    top: 0;
  }

  .el-timeline-item__content {
    padding-left: 14px;
    color: #E1E8FF;
  }

  .el-timeline-item__timestamp.is-bottom {
    display: none;
  }
}
.time-line {
  .order-title {
    padding-bottom: 0;
    border: none;
  }
  .el-collapse-item__arrow {
    color: $hg-default-text-color;
  }
}
</style>