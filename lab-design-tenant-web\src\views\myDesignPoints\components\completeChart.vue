<template>
  <div class="month-points">
    <div class="month-header">
      <span class="title">{{lang('completeChange')}}</span><span class="date">{{monthsArray[0]}}~{{monthsArray[monthsArray.length - 1]}}</span>
      <!-- <div class="all-points">
        <p class="list"><span class="point1"></span>月总指标点数</p>
        <p class="list"><span class="point2"></span>月完成点数</p>
      </div> -->
    </div>
    <div id="compltet-chart" class="month-chart"></div>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
  export default {
    name: 'completeChart',
    props: {
      monthsArray: Array,
      completionRate: Array
    },
    data() {
      return {
        
      }
    },
    mounted () {
      
    },
    watch: {
      completionRate: {
        handler(newVal, oldVal) {
          this.getMonthChart();
        }
      }
    },
    methods: {
      lang: getLang('designpoints'),
      getMonthChart() {
        let completeChart = this.$echarts.init(document.getElementById('compltet-chart'));
        //配置图表
        let option = {
          tooltip: {
            trigger: "axis",
            formatter: (param) => {
              let content = '';
              param.forEach((item) => {
                content += `<p>${item.axisValue}</p>${item.marker}${item.seriesName}<span style="margin-left: 20px;">${item.data}%</span></br>`
              })
              return content;
            },
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.monthsArray,
          },
          yAxis: {
            type: 'value',          
            show: true
          },
          series: [
            {
              data: this.completionRate,
              type: 'line',
              label: {
                show: true,
                position: 'top',
                color: '#AAADB3',
                formatter: (param) => {
                  if(param.value == 0){
                    return 0;
                  } else {
                    return `${param.value}%`;
                  }
                }
              },
              name: this.lang('completeChange'),
              color: ['#00B860'],
            }
          ]
        };
        completeChart.setOption(option);
      }
    },
  }
</script>

<style lang="scss" scoped>
.month-points{
  margin-top: 24px;
  min-width: 554px;
  height: 400px;
  padding: 20px;
  .month-header{
    position: relative;
    display: flex;
    align-items: center;
    .title{
      color: #AAADB3;
      margin-right: 8px;
    }
    .date{
      color: #AAADB3;
    }
    .all-points{
      position: absolute;
      right: 38px;
      display: flex;
      .list{
        margin-right: 6px;
        padding: 4px;
      }
      .point1{
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #5F8AFF;
        margin-right: 8px;
      }
      .point2{
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #00B860;
        margin-right: 8px;
      }
    }
  }
  .month-chart{
    width: 100%;
    height: 100%;
  }
}
</style>