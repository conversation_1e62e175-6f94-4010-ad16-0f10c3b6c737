<template>
  <div class="header-select-box">
    <el-select
      :value="selectedValue"
      :multiple="isMultiple"
      :placeholder="placeholder ? placeholder : $t('common.selectPlaceholder')"
      popper-class="header-app-slt"
      @focus="focus"
      @change="change"
    >
      <el-option
        v-for="item in selectOptions"
        :key="item.path"
        :label="item.label"
        :value="item.path"
      />
    </el-select>
  </div>
</template>
<script>
export default {
  name: "Select",
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    isMultiple: { // 是否多选
      type: Boolean,
      default: false
    },
    value: {
      // type: String,
      default: ""
    },
    selectOptions: {
      type: Array,
      default: []
    },
  },
  data() {
    return {
      selectedValue: this.value,
    };
  },
  created() {
  },
  watch: {
    value(val) {
      this.selectedValue = val;
    },
  },
  methods: {
    focus() {
      this.$emit('focus');
    },
    change(value) {
      this.$emit('change', value);
    },

  },
};
</script>
<style lang="scss">
.header-select-box  {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  ::v-deep .el-select {
    width: 100%;
    .el-input {
      width: 100%;
      display: block;
      .el-input__inner {
        width: 100%;
        border: 1px solid $hg-border-color;
        border-radius: $hg-border-radius2;
        box-sizing: border-box;
        background-color: transparent;
        height: 40px;
        line-height: 40px;
        color: $hg-primary-fontcolor;
        font-weight: normal;
        padding: 0 50px 0 24px;
        &::placeholder {
          color: $hg-secondary-fontcolor;
        }
      }
      &:hover {
        .el-input__inner {
          border: 1px solid $hg-disable-fontcolor;
        }
      }
      &.is-focus {
        .el-input__inner {
          border-color: $hg-primary-fontcolor;
        }
      }
      .el-input__suffix {
        right: 8px !important;
        .el-select__caret{
          color: $hg-secondary-fontcolor;
        }
      }
    }
    .el-select__tags {
      padding: 0 8px;
      .el-tag {
        height: 32px;
        line-height: 32px;
        border-radius: 2px;
        background: $hg-hover-bg-color;
        border: none;
        padding: 0 8px;
        color: $hg-primary-fontcolor;
        .el-icon-close {
          background-color: transparent;
          right: 0;
          font-size: 20px;
          color: $hg-secondary-fontcolor;
          margin-left: 8px;
        }
      }
    }
  }
}

.header-app-slt {
  box-shadow: 0px 12px 32px 0px #121314,0px 8px 24px 0px #121314,0px 0px 16px 0px #121314 !important;
   border: none !important;
  .popper__arrow, .popper__arrow::after {
    border-top-color: #1D1D1F !important;
    border-bottom-color: #1D1D1F !important;
  }
  .el-scrollbar {
    background-color: #1d1d1f;
    .el-select-dropdown__wrap.el-scrollbar__wrap {
      .el-scrollbar__view.el-select-dropdown__list {
        li.el-select-dropdown__item {
          height: 40px;
          padding: 0 24px;
          // color: #e4e8f7;
          font-size: 14px;
          line-height: 40px;
          font-weight: normal;
        }
        li.el-select-dropdown__item.selected {
          color: #e4e8f7;
        }
        // 禁用选项样式
        li.el-select-dropdown__item.selected,
        li.el-select-dropdown__item:hover,
        li.el-select-dropdown__item.hover {
          background-color: #262629;
        }
      }
    }
  }
}
</style>
