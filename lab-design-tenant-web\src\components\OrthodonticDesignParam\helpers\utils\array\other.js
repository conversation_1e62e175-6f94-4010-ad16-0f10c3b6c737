// 删除数组符合条件的元素
export function removeArrayItem(arr, handler, isUnion = true) {
  if (isUnion) {
    const length = arr.length
    for (let i = 0; i < length; i++) {
      const item = arr[i]
      const remove = handler(item)
      if (remove) {
        arr.splice(i, 1)
        return arr
      }
    }
  } else {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      const remove = handler(item)
      if (remove) {
        arr.splice(i, 1)
        i--
      }
    }
    return arr
  }
}
