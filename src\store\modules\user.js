import { setStore, getStore } from '@/assets/script/storage.js';
import { getI18nDict } from '@/api/role/index.js'

const state = {
  personInfo: {}, // 个人中心页获取个人信息
  userInfo: getStore('userInfo') || {}, // 各中心共用的个人信息
  i18nDictList: [], // i18n字典表
}
const getters = {
}

const mutations = {
  updatePersonInfo: (state, data) => {
    state.personInfo = data;
  },
  updateUserInfo: (state, data) => {
    state.userInfo = data;
    setStore('userInfo', data);
  },
  initI18nDictList: (state, data) => {
    state.i18nDictList = data;
    setStore('i18nDictList', data);
  },
}
const actions = {
  initI18nDictList: ({commit}) => {
    getI18nDict().then((res) => {
      if(res.code == 200){
        let dictList = res.data;
        let apiZh = {};
        let apiEn = {};
        dictList.forEach((item) => {
          apiZh[item.code] = item.lang['zh-CN'];
          apiEn[item.code] = item.lang['en-US']
        })
        commit('initI18nDictList', {apiZh, apiEn});
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}

