<template>
  <div class="business-board" v-loading="loadingBsiness">
    <div class="title">{{$t('databoard.businessboard.statistics')}}</div>
    <div class="type-content">
      <div class="other-box">
        <div class="normal-box" v-for="(other, index) in otherDataList" :key="index">
          <span :class="['box1', other.type == 3 ? 'box-blue' : '']"
            >{{ other.type != 3 ? $t(other.name) : `${$t(other.name)}···` }}<span v-if="language == 'zh'">{{ $t(other.danwei) }}</span></span
          >
          <span class="box2" v-if="other.type == 1">{{ businessOrder[other.number] | computeNumber('isOver') }}</span>
          <span class="box2" v-if="other.type == 3">{{ businessOrder[other.number] }}</span>
          <span class="box3" v-if="other.type == 2">{{ businessOrder[other.number] }}%</span>
          <span class="box4" v-if="other.type == 2 || index == 0 || index == 2"
            >{{$t('databoard.adminboard.rate')}}<span><i class="el-icon-caret-top top-icon"></i>{{ businessOrder[other.numberRate] }}%</span></span
          >
          <!-- <img class="arrow-img" src="@/assets/images/databoard/icon-arrow-lab.svg" alt="" /> -->
        </div>
      </div>
    </div>
    <div class="title">{{$t('databoard.businessboard.urgent')}}</div>
    <div class="urgent-order" v-if="expeditedList.length > 0">
      <div class="order-list" v-for="(expedited, index) in expeditedList" :key="index">
        <p class="order-no">{{$t('databoard.businessboard.orderNo')}}：{{ expedited.orderNo }}</p>
        <span class="name">{{ expedited.orgName }}</span>
        <span class="name">
          <CountDown :completeTime="0" :createdTime="expedited.createdTime" :stamp="expedited.deliveryTime" :orderState="Number(expedited.status)" :isurgent="true"> </CountDown
        ></span>
        <span class="name">{{ $t(`apiCommon.${expedited.statusName.en}`) }}</span>
        <span class="details" @click="jumpDetails(expedited, 'expedited')">{{$t('databoard.businessboard.details')}}</span>
      </div>
    </div>
    <div class="urgent-order" v-else>
      <div class="order-list no-list">
        {{$t('common.noData')}}
      </div>
    </div>
    <div class="title">{{$t('databoard.businessboard.warningAndOver')}}</div>
    <div class="urgent-order" v-if="warningList.length > 0">
      <div class="order-list" v-for="(warning, index) in warningList" :key="index">
        <p class="order-no">{{$t('databoard.businessboard.orderNo')}}：{{ warning.orderNo }}</p>
        <span class="name">{{ warning.orgName }}</span>
        <span class="name"> <CountDown :completeTime="0" :createdTime="warning.createdTime" :stamp="warning.deliveryTime" :orderState="Number(warning.status)"> </CountDown></span>
        <span class="name"> {{ $t(`apiCommon.${warning.statusName.en}`) }}</span>
        <span class="details" @click="jumpDetails(warning, 'warning')">{{$t('databoard.businessboard.details')}}</span>
      </div>
    </div>
    <div class="urgent-order" v-else>
      <div class="order-list no-list">
        {{$t('common.noData')}}
      </div>
    </div>
  </div>
</template>

<script>
import CountDown from '@/components/CountDown';
import { businessDataKiosks } from '@/api/databoard';
import { ROUTE_NAME } from '@/public/constants';
import { mapGetters } from 'vuex';
import {computeNumber} from '@/filters'
export default {
  name: 'businessBoard',
  components: { CountDown },
  computed: {
    ...mapGetters(['language']),
  },
  data() {
    return {
      loadingBsiness: true,
      businessOrder: {
        newOrder: 0, //本月新订单
        lastOrder: 0, //上月订单
        activeCustomers: 0, //活跃客户
        outboundReturnRate: 0, //外返率
        outboundReturnRingRate: 0, //外返率环比
        working: 0, //正在处理,
        newOrderRingRate: 0,//本月新订单环比
        activeCustomersRingRate: 0//我的活跃客户环比
      },
      otherDataList: [
        {
          type: 1,
          name: 'databoard.adminboard.newOrder',
          danwei: 'databoard.businessboard.singel',
          number: 'newOrder',
          numberRate: 'newOrderRingRate',
        },
        {
          type: 1,
          name: 'databoard.adminboard.lastOrder',
          danwei: 'databoard.businessboard.singel',
          number: 'lastOrder',
        },
        {
          type: 1,
          name: 'databoard.businessboard.myCustomer',
          danwei: 'databoard.businessboard.bit',
          number: 'activeCustomers',
          numberRate: 'activeCustomersRingRate',
        },
        {
          type: 2,
          name: 'databoard.adminboard.outRate',
          number: 'outboundReturnRate',
          numberRate: 'outboundReturnRingRate',
        },
        {
          type: 3,
          name: 'databoard.businessboard.handeling',
          number: 'working',
        },
      ],
      // 加急订单
      expeditedList: [],
      // 预警订单
      warningList: [],
    };
  },
  mounted() {
    this.businessDataKiosks();
  },
  methods: {
    businessDataKiosks() {
      this.loadingBsiness = true;
      businessDataKiosks().then((res) => {
        let data = res.data;
        Object.keys(this.businessOrder).forEach((item) => {
          this.businessOrder[item] = data[item] ? data[item] : 0;
        });
        this.expeditedList = data.expeditedList ? data.expeditedList : [];
        this.warningList = data.warningList ? data.warningList : [];
        this.loadingBsiness = false;
      });
    },
    // 跳转
    jumpDetails(item, type) {
      let orderCode = item.orderCode;
      this.$router.push({ name: ROUTE_NAME.ORDER_DETAIL, query: { auth:'Y', id: orderCode } });
    },
  },
};
</script>

<style lang="scss" scoped>
.business-board {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  .title {
    color: $hg-secondary-primary;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .type-content {
    margin-bottom: 22px;
    width: 100%;
    display: flex;
    .other-box {
      margin-top: 12px;
      width: 100%;
      display: flex;
      .normal-box {
        flex: 1;
        min-width: 100px;
        height: 128px;
        display: flex;
        flex-direction: column;
        padding: 24px;
        background: #1d1d1f;
        border-radius: 4px;
        position: relative;
        margin-right: 24px;
        background-image: url('../../../assets/images/databoard/icon-arrow-lab.svg');
        background-repeat: no-repeat;
        background-position: bottom right;
        
        &:last-child {
          margin-right: 0;
        }
        .box1 {
          color: #fff;
          font-size: 16px;
          font-weight: blod;
          span {
            color: #9EA2A8;
          }
        }
        .box-blue {
          color: $hg-main-blue;
        }
        .box2 {
          margin-top: 16px;
          color: #fff;
          font-size: 20px;
          font-weight: blod;
        }
        .box3 {
          color: #ff5a5a;
          font-weight: bold;
          font-size: 16px;
          margin-top: 16px;
        }
        .box4 {
          font-size: 14px;
          margin-top: 16px;
          color: #9EA2A8;
          span {
            color: #ff5a5a;
          }
          .top-icon {
            color: #ff5a5a;
            margin: 0 4px;
          }
        }
        .arrow-img {
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
    }
  }
  .urgent-order {
    margin-top: 12px;
    border-radius: 4px;
    padding: 24px 24px 24px 24px;
    background: #1d1d1f;
    max-height: 266px;
    width: 100%;
    margin-bottom: 22px;
    overflow: auto;
    &:last-child {
      margin-bottom: 0;
    }
    .order-list {
      display: flex;
      justify-content: space-between;
      line-height: 24px;
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
      .details {
        color: $hg-main-blue;
        cursor: pointer;
      }
      span {
        flex: 0.5;
      }
      .name {
        flex: 2;
      }
      .order-no {
        flex: 3;
      }
    }
    .no-list{
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
