<template>
  <ul class="right-select-item" @mouseleave="handleToggle(false)" v-if="secondLevel && secondLevel.length > 0">
    <li :class="[isDisable(firstLevelCode, 'left')]" >
      <div :class="['left-box',  'pointer']" @mouseenter="handleToggle(true)">
        <div class="first-img-box">
          <img :src="firstLevelIcon" alt />
        </div>
        <div :class="['name', language !== 'zh' ? 'unique' : 'normal-line-height']">
          <span>{{ firstLevelName }}</span>
        </div>
      </div>
    </li>
    <ul class="right-box" v-show="active">
      <li class="row top">
        <div :class="['second-img-box', isDisable(second.designCode)]" :key="second.uid" v-for="second in secondLevel">
          <img class="pointer" @mousemove="altTipCode = second.designCode" @mouseleave="altTipCode = 0" @click="handleClick(second)" :src="second.iconUrl" />
          <div v-show="second.designCode == altTipCode" class="alt-tip" slot="content">
            <!-- {{ language === 'zh' ? second.cnName : second.enName }} -->
            {{ $t(`apiCommon.${second.designCode}`) }}
          </div>
        </div>
        <div style="min-width: 1px;  margin-left: 16px;"></div>
      </li>
      <li class="select-text">{{ selectText }}</li>
    </ul>
  </ul>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  name: 'right-select',
  components: {},
  data() {
    return {
      altTipCode: 0,
      active: false,
      selectText: '',
      leftActive: false,
    };
  },
  props: {
    firstLevelCode: {
      type: [Number, String],
      default: -9999,
    },
    firstLevelIcon: {
      type: String,
      default: '',
    },
    firstLevelName: {
      type: [String],
      default: '',
    },
    secondLevel: {
      type: [Array,String],
      default: () => {
        return [];
      },
    },
    activeBracket: {
      type: Array,
      default: () => {
        return [];
      },
    },
    orderList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    canSelectCodes: {
      type: Array,
      defalut() {
        return [];
      }
    },
    hasOtherCategory: Boolean,
    otherDesignerType: Array
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 禁用二级 //TODO 除了新增的禁选桥体，其他都是从旧Design复制过来的用不上，暂时保存是等4.3.36测试通过之后删除
     * @param {*} code
     */
    secondLevelDis(code) {
      const hasIndividualTray = this.activeBracket.some(item => this.individualTrayList.includes(item));
      const hasClearAligners = this.activeBracket.some(item => this.clearAligners.includes(item));
      const hasSegmentation = this.activeBracket.some(item => this.segmentationList.includes(item));
      const hasyaohedian = this.activeBracket.some(item => this.orthodontic_bracket_removal_up_half2List.includes(item));
      const hasyemoyadian = this.activeBracket.some(item => this.nightguardList.includes(item));

      const hasStudyModel = this.orderList.some(item => item.code === 20027); //记存模型
      const hasyaoheban = this.orderList.some(ele => ele.code === 20089); //咬合板
      const hasDesign = this.orderList.some(ele => ele.code === 20086); //活动义齿（设计牙）
      const hasfinished = this.orderList.some(ele => ele.code === 20085); //活动义齿（成品牙）
      const hasTryOn = this.orderList.some(ele => ele.code === 20087); //试戴
      const hasOthers = this.orderList.some(ele => ele.code === 20092); //其他类型
      const hasplant1 = this.orderList.some(ele => ele.code === 20097); //种植-种植全冠类型
      const hasplant2 = this.orderList.some(ele => ele.code === 20098); //种植-种植解剖型内冠类型
      const hasplant3 = this.orderList.some(ele => ele.code === 20099); //种植-全冠桥架类型

      const segAndOrthCode = [20056, 20057];
      if (hasIndividualTray && code !== 20054) {
        return 'disable-box';
      } else if (hasClearAligners && code !== 20055) {
        return 'disable-box';
      } else if (hasStudyModel) {
        if (code !== 20027) {
          return 'disable-box';
        }
      } else if (hasSegmentation && !segAndOrthCode.includes(code)) {
        return 'disable-box';
      }
      if (code == 20026 && this.onlyBracket) {
        return 'disable-box';
      }

      const normalNotAllowCodes = [20054, 20092]; //个性化托盘  其他

      if (!this.hasIndividualTrayList && code === 20054) {
        return 'disable-box';
      }

      if (!this.hasIndividualTrayList && code === 20054) {
        return 'disable-box';
      }
      const modelAllowCode = [20024, 20025, 20026, 20033];

      if (hasDesign && code !== 20086 && !modelAllowCode.includes(code)) {
        return 'disable-box';
      }
      if (hasfinished && code !== 20085 && !modelAllowCode.includes(code)) {
        return 'disable-box';
      }

      if (hasTryOn && code !== 20087 && !modelAllowCode.includes(code)) {
        return 'disable-box';
      }

      const normalNotAllowCode = [20054, 20092];
      if (this.onlyTooth && normalNotAllowCode.includes(code)) {
        return 'disable-box';
      }

      if (hasyaoheban && code !== 20089 && !modelAllowCode.includes(code)) {
        return 'disable-box';
      }
      if (hasyaohedian && code !== 20088 && !modelAllowCode.includes(code)) {
        return 'disable-box';
      }
      if (hasyemoyadian && code !== 20090 && !modelAllowCode.includes(code)) {
        return 'disable-box';
      }

      const otherAllowCode = [20024, 20025, 20026, 20092];
      if (hasOthers && !otherAllowCode.includes(code)) {
        return 'disable-box';
      }

      // 禁选&不是桥体-其他[25002,22503,23402,24405]
      if(this.canSelectCodes.length > 0 &&!this.canSelectCodes.includes(code) && ![21501,23501, 25002,22503,23402,24405].includes(code)) {
        return 'disable-box';
      }

      // 联合修复订单中，otherDesignerType为指派给其他设计师的设计类型，当前设计师不可编辑指派给其他设计师的设计类型
      if (this.canSelectCodes.length > 0 && this.otherDesignerType.length > 0 && this.otherDesignerType.includes(code)) {
        return 'other-designer-disable-box';
      }
      return '';
    },

    // 禁选
    isDisable(code, type) {
      if(type === 'left') {
        if(code === 24100 && this.hasOtherCategory) {
          return 'disable-box';
        }
      }else {
        // 禁选&不是桥体-其他
        if(this.canSelectCodes.length > 0 &&!this.canSelectCodes.includes(code) && ![21501,23501, 25002,22503,23402,24405].includes(code)) {
          return 'disable-box';
        }
        // 联合修复订单中，otherDesignerType为指派给其他设计师的设计类型，当前设计师不可编辑指派给其他设计师的设计类型
        if (this.canSelectCodes.length > 0 && this.otherDesignerType.length > 0 && this.otherDesignerType.includes(code)) {
          return 'other-designer-disable-box';
        }
      }
      return '';
    },

    /** //TODO暂时用不上，是旧Design的规则，暂时保存是等4.3.36测试通过之后删除
     * 禁用一级
     */
    firstLevelDis() {
      const hasIndividualTray = this.activeBracket.some(item => this.individualTrayList.includes(item));
      const hasClearAligners = this.activeBracket.some(item => this.clearAligners.includes(item));
      const hasSegmentation = this.activeBracket.some(item => this.segmentationList.includes(item));
      const hasyaohedian = this.activeBracket.some(item => this.orthodontic_bracket_removal_up_half2List.includes(item));
      const hasyemoyadian = this.activeBracket.some(item => this.nightguardList.includes(item));
      const hasyeheban = this.orderList.some(ele => ele.code === 20089);

      const hasDesign = this.orderList.some(ele => ele.code === 20086);
      const hasfinished = this.orderList.some(ele => ele.code === 20085);
      const hasTryOn = this.orderList.some(ele => ele.code === 20087); //试戴
      const hasOthers = this.orderList.some(ele => ele.code === 20092); //其他类型
      const hasplant1 = this.orderList.some(ele => ele.code === 20097); //种植-种植全冠类型
      const hasplant2 = this.orderList.some(ele => ele.code === 20098); //种植-种植解剖型内冠类型
      const hasplant3 = this.orderList.some(ele => ele.code === 20099); //种植-全冠桥架类型

      if (hasIndividualTray && this.firstLevelCode !== 20030) {
        return 'disable-box';
      } else if (hasClearAligners && this.firstLevelCode !== 20035) {
        return 'disable-box';
      } else if (hasSegmentation && this.firstLevelCode !== 20035) {
        return 'disable-box';
      }

      const arr = [20037, 20032]; //可选项一级（活动修复  模型）
      if (hasfinished && !arr.includes(this.firstLevelCode)) {
        //是否选择成品牙
        return 'disable-box';
      }
      if (hasTryOn && !arr.includes(this.firstLevelCode)) {
        //是否选择试戴
        return 'disable-box';
      }

      const designAllowArr = [20031, 20032, 20037]; //可选项一级（牙桥 模型 活动修复 ）
      if (hasDesign && !designAllowArr.includes(this.firstLevelCode)) {
        //是否选择了活动义齿（设计牙）
        return 'disable-box';
      }

      if (this.onlyBracket && !arr.includes(this.firstLevelCode)) {
        return 'disable-box';
      }

      if (this.onlyTooth && [20035, 20037, 20093].includes(this.firstLevelCode)) {
        return 'disable-box';
      }
      const yahedianAllowCode = [20035, 20032]; //可选项一级（正畸  模型）
      if (hasyaohedian && !yahedianAllowCode.includes(this.firstLevelCode)) {
        return 'disable-box';
      }
      if (hasyemoyadian && !yahedianAllowCode.includes(this.firstLevelCode)) {
        return 'disable-box';
      }
      if (hasyeheban && !yahedianAllowCode.includes(this.firstLevelCode)) {
        return 'disable-box';
      }

      const otherAllowCodes = [20030, 20032];
      if (hasOthers && !otherAllowCodes.includes(this.firstLevelCode)) {
        return 'disable-box';
      }

      const plant12Allow = [20031, 20032, 20093];
      if (hasplant1 && !plant12Allow.includes(this.firstLevelCode)) {
        return 'disable-box';
      }
      if (hasplant2 && !plant12Allow.includes(this.firstLevelCode)) {
        return 'disable-box';
      }

      const plant3Allow = [20032, 20093];
      if (hasplant3 && !plant3Allow.includes(this.firstLevelCode)) {
        return 'disable-box';
      }
      return '';
    },

    /**
     * 切换高亮 显示
     * @param {Boolean} status
     */
    handleToggle(status) {
      this.leftActive = status;
      this.active = status;
      this.selectText = '';
    },

    /**
     * 点击的时候
     */
    handleClick(second) {
      if (this.secondLevelDis(second.designCode) === 'disable-box') {
        return false;
      }
      // 联合修复订单中，otherDesignerType为指派给其他设计师的设计类型，当前设计师不可编辑指派给其他设计师的设计类型
      if (this.secondLevelDis(second.designCode) === 'other-designer-disable-box') {
        this.$message({
          type: 'warning',
          message: this.$t('order.detail.tips.otherDesignTips')
        })
        return false;
      }
      // this.selectText = this.language === 'zh' ? second.cnName : second.enName;
      this.selectText = this.$t(`apiCommon.${second.designCode}`);
      this.$emit('select', second);
    },
  },
};
</script>
<style lang="scss" scoped>
.right-select-item {
  display: flex;
  //   cursor: pointer;
  margin-bottom: 16px;
  .active {
    border-color: #3760ea !important;
  }
  .row {
    display: flex;
  }
  .left-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    margin-right: 16px;
    padding-top: 12px;
    padding-bottom: 8px;
    width: 100px;
    height: 92px;
    border: 2px solid #2F3238; 
    border-radius: 4px;
    color: #e4e8f7;
    background-color: #2F3238;
    font-size: 14px;

    cursor: pointer;
    &:hover {
      background-color: #3D4047;
      border-color: #3760ea;
    }
    .first-img-box {
      width: 32px;
      height: 32px;
      img {
        width: 100%;
      }
    }
    .name {
      width: 85px;
      text-align: center;
      font-size: 14px;
    }
    .unique {
      line-height: 14px !important;
    }
    .normal-line-height {
      line-height: 22px;
    }
  }
  .disable-box { // 父节点的disablebox
    opacity: 0.2;
    cursor: not-allowed;

    &>div {
      pointer-events: none;
    }
  }
  .right-box {
    background: #3D4047;
    flex: 1;
    min-width: 250px;
    overflow: auto;
    .top {
      margin-top: 12px;
    }
    .second-img-box {
      position: relative;
      margin-left: 16px;
      border: 2px solid #3D4047;
      border-radius: 4px;
      min-width: 32px;
      min-height: 32px;
      width: 32px;
      height: 32px;
      cursor: pointer;
      .alt-tip {
        content: '';
        font-size: 12px;
        white-space: nowrap;
        border-radius: 4px;
        position: absolute;
        margin-top: 3px;
        padding: 8px 12px;
        color: #e4e8f7;
        background: #262629;
        margin-left: -14px;
        // z-index: 12;
        cursor: pointer;
      }
      &:hover {
        border-color:#3760ea;
      }
      img {
        width: 100%;
      }
    }
    .disable-box {
      opacity: 0.2;
      cursor: not-allowed;
      > img {
        pointer-events: none;
      }
      &:hover {
        border-color: transparent;
      }
    }
    .other-designer-disable-box {
      > img {
        opacity: 0.2;
      }
      cursor: not-allowed;
    }
    .select-text {
      margin-left: 16px;
      margin-top: 10px;
      color: #6f7376;
      font-size: 14px;
    }
    /* .match-wrapper {
      min-width: 140px;
      display: flex;
      align-items: center;
      margin-left: 13px;
      font-size: 14px;
      color: #333333;
      .match-box {
        width: 14px;
        height: 14px;
        margin-right: 6px;
      }
      .match-bg {
        background: url('~@/components/ToothDrawer/img/matchIcon.png') center center no-repeat;
        background-size: contain;
      }
      .no-match-bg {
        background: url('~@/components/ToothDrawer/img/noMatchIcon.png') center center no-repeat;
        background-size: contain;
      }
    } */
  }
}
</style>
<style lang="scss">
.el-tooltip__popper.is-light.tooth-pop {
  margin-top: 3px;
  border: 1px solid #eee;
  padding: 3px 8px 3px 8px;
  .popper__arrow {
    display: none;
  }
}
</style>
