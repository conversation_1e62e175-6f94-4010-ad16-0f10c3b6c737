/**
 * 获取一些用户信息
 */
import { getStore } from '@/assets/script/storage'
import { repeatMenusFilter } from '@/assets/script/utils'

// 获取用户信息
function getMenus() {
  const userInfo = getStore('userInfo')
  if (!userInfo) return []
  let functions = []

  userInfo.solutions[0].roles.forEach(role => {
    functions = functions.concat(role.functions)
  })

  const menus = repeatMenusFilter(functions, 'menus')
  return menus
}

// 静态路由（用户中心里的一些不需要动态获取的路由信息）
function loadStaticMenu(menus, routers) {
  let menuChildren = [{
    path: '/404',
    name: '404'
  }]
  let customer = menus.filter(item => item.path.indexOf('customer') !== -1)
  if (customer.length) {
    customer = routers.filter(item => item.path.indexOf('customer') !== -1)
    menuChildren = menuChildren.concat(customer[0].children)
  }

  return menuChildren
}
export {
  getMenus,
  loadStaticMenu
}
