import * as THREE from 'three'

export function createArrowHelper(properties) {
  const {
    direction,
    origin,
    size = 1,
    color = 0x00ff00,
    depthTest = true
  } = properties

  const arrowHelper = new THREE.ArrowHelper(
    direction,
    origin,
    10 * size,
    color,
    size,
    size,
  )

  arrowHelper.traverse((child) => {
    if (child.geometry) {
      child.material.depthTest = depthTest
    }
  })

  return arrowHelper
}

export function createSphereHelper(properties) {
  const {
    size = 1,
    color = 0x00ff00,
    position = new THREE.Vector3(),
    depthTest = true
  } = properties

  const segments = Math.max(size * 32, 32) | 0

  const geometry = new THREE.SphereGeometry(size, segments, segments)
  const material = new THREE.MeshBasicMaterial({
    color,
    side: THREE.DoubleSide,
    depthTest,
  })
  const sphere = new THREE.Mesh(geometry, material)

  sphere.position.copy(position)

  return sphere
}
