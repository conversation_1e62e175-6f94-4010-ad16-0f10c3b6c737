/**
 * 国际化js
 * 使用：$t(your i18n name )
 * 拓展语言：
 *    在messages中添加，如  "zh-HK": require("./zh-HK").defalut
 */
import VueI18n from 'vue-i18n';
import Vue from 'vue';
import { getStore } from '@/public/utils/storage';
import { DEFAULT_LANGUAGE } from '@/public/constants/setting';
import zhLang from './zh-cn/index';
import enLang from './en-us/index';
import zhLocale from 'element-ui/lib/locale/lang/zh-CN';
import enLocale from 'element-ui/lib/locale/lang/en';
import ElementLocale from 'element-ui/lib/locale';

Vue.use(VueI18n);
const lang = getStore('lang') || DEFAULT_LANGUAGE;
let i18nDictList = JSON.parse(getStore('i18nDictList')) || {apiZh: {}, apiEn: {}};
const i18n = new VueI18n({
  locale: lang,
  silentFallbackWarn: true,//抑制警告（线上）
  // silentTranslationWarn: true,// 去除开发环境警告
  // missingWarn: false,// 去除开发环境警告
  // fallbackWarn: false,// 去除开发环境警告
  fallbackLocale: 'en',
  messages: {
    'zh': {
      ...zhLang,
      ...zhLocale,
    },
    'en': {
      ...enLang,
      ...enLocale,
    },
  }
});
ElementLocale.i18n((key, value) => i18n.t(key,value));

export default i18n;
