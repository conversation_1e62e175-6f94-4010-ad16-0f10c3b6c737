<template>
  <div class="month-points">
    <div class="month-header">
      <span class="title">{{lang('monthStatics')}}</span><span class="date">{{monthsArray[0]}}~{{monthsArray[monthsArray.length - 1]}}</span>
      <div class="all-points">
        <p class="list"><span class="point1"></span>{{lang('monthPoints')}}</p>
        <p class="list"><span class="point2"></span>{{lang('monthComplete')}}</p>
      </div>
    </div>
    <div id="month-chart" class="month-chart"></div>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
  export default {
    name: 'monthPointsChart',
    props: {
      monthsArray: Array,
      monthlyPoints: Object
    },
    data() {
      return {
        hookToolTip: null
      }
    },
    mounted () {
      
    },
    watch: {
      hookToolTip: {
        handler(newVal, oldVal) {
          let tooltipButton = document.querySelectorAll("#month-tooltip");
          //通过onclick注册事件 querySelectorAll获取的元素是一个数组
          if (tooltipButton.length > 0) {
            tooltipButton[0].onclick = this.pointNameClick;
          }
          // 通过addEventListener注册事件
          for (let i = 0; i < tooltipButton.length; i++) {
            tooltipButton[i].addEventListener("click", this.chartClick);
          }
        },
        deep: true,
      },
      monthlyPoints(val){
        this.getMonthChart();
      }
    },
    methods: {
      lang: getLang('designpoints'),
      getMonthChart() {
        let monthChart = this.$echarts.init(document.getElementById('month-chart'));
        //配置图表
        let option = {
          tooltip: {
            trigger: "axis",
            enterable: 'true',
            position: (point)=> ([point[0], point[1]]),
            axisPointer: {
              type: "shadow"
            },
            formatter: (param) => {
              this.hookToolTip = param; // 这个是用来监听
              let content = '';
              param.forEach((item) => {
                content += `${item.marker}${item.seriesName}<span style="margin-left: 20px;">${item.data}</span></br>`
              })
              content += `<p id="month-tooltip" style="color: #5F8AFF;text-align: right;cursor: pointer;margin-top: 10px;">${this.lang('scanDetail')}</p>`
              return content;
            },
          },
          xAxis: {
            type: 'category',
            data: this.monthsArray,
          },
          yAxis: {
            type: 'value',          
            show: true,
            axisLine: {
              lineStyle: {
                type: 'dotted',
              }
            },
            axisTick: {
              length: 6,
              lineStyle: {
                type: 'dotted'
              }
            }
          },
          series: [
            {
              data: this.monthlyPoints.monthlyIndicatorPointsArr,
              type: 'bar',
              name: this.lang('monthPoints'),
              color: ['#5F8AFF'],
            },
            {
              data: this.monthlyPoints.monthlyCompletionPointsArr,
              type: 'bar',
              name: this.lang('monthComplete'),
              color: ['#00B860']
            }
          ]
        };
        monthChart.setOption(option);
      },
      chartClick() {
        this.$emit('click', this.hookToolTip)
      },
    },
  }
</script>

<style lang="scss" scoped>
.month-points{
  margin-top: 24px;
  min-width: 554px;
  height: 400px;
  padding: 20px;
  .month-header{
    position: relative;
    display: flex;
    align-items: center;
    .title{
      color: #AAADB3;
      margin-right: 8px;
    }
    .date{
      color: #AAADB3;
    }
    .all-points{
      position: absolute;
      right: 38px;
      display: flex;
      .list{
        margin-right: 6px;
        padding: 4px;
        color: #AAADB3;
      }
      .point1{
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #5F8AFF;
        margin-right: 8px;
      }
      .point2{
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #00B860;
        margin-right: 8px;
      }
    }
  }
  .month-chart{
    width: 100%;
    height: 100%;
  }
}
</style>