<template>
  <div class="switch-box">
    <el-switch
      v-model="switchValue"
      :disabled="disable"
      active-color="#3054cc"
      inactive-color="#54565C"
      @change="change"
    />
  </div>
</template>
<script>
export default {
  name: 'VueSwitch',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    disable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // switchValue: this.value
    }
  },
  computed: {
    switchValue: {
      get() {
        return this.value
      },
      set() {}
    }
  },
  watch: {
    value(val) {
      // this.switchValue = val
    }
  },
  created() {
  },
  methods: {
    change(value) {
      this.$emit('change', value)
    }
  }
}
</script>
<style lang="scss" scoped>
.switch-box {
}
</style>
