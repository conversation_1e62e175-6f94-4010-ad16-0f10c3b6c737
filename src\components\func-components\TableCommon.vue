<!--
  * 组件名称 TableCommon
  * @desc 公用表格组件
  * <AUTHOR>
  * 组件说明: 组件的大小、表头信息、表格信息、表格高度、表格的新增/刷新/删除操作由父组件自定义控制
-->
<template>
  <div class="component-table" :style="{'background': tableStyle.background ? tableStyle.background : '#1D1D1F'}">
    <div v-if="isRefresh" class="table-box">
      <div class="table-header">
        <!-- <div class="th checkbox"><slot name="checkbox"></slot></div> -->
        <div
          v-for="(thItem, thIndex) in tableData.title"
          :key="thIndex"
          :style="{
            'width': theaderWObject[`td${thIndex}`] + 'px'
          }"
          :title="thItem || ''"
          class="th"
        >
          {{ thItem }}
        </div>
      </div>
      <overlay-scrollbars
        ref="scroll"
        class="table-body"
        :style="{'height': tableStyle.height}"
        :options="{
          paddingAbsolute: true,
          scrollbars: { autoHide: 'scroll' } }"
      >
        <div class="inner-scroll" :style="{'min-height': `${parseFloat(tableStyle.height) + 1}px`}">
          <div class="table">
            <ul v-if="tableData.data.length > 0" class="tbody">
              <li v-for="(tr, trIdx) in tableData.data" :key="trIdx" class="tr" @mouseenter="setHoverBg" @mouseleave="removeHoverBg">
                <!-- <div class="td checkbox"><slot name="checkbox"></slot></div> -->
                <div v-for="(keyItem, keyIndex) in keys" :key="keyIndex" :ref="`td${keyIndex}`" :class="['td',`td_${size}` ]">
                  <p class="texts" :title="tr[keyItem] || ''">
                    <slot :name="keyItem" :data="tr" :index="trIdx">{{ tr[keyItem] || '' }}</slot>
                  </p>
                </div>
              </li>
            </ul>
          </div>
          <div v-if="tableData.data.length === 0" class="empty-table" style="height: 100px;">
            <span>{{ $t('common.noData') }}</span>
          </div>
        </div>
      </overlay-scrollbars>
    </div>
  </div>
</template>

<script>
import Tooltip from '@/components/func-components/Tooltip'

export default {
  name: 'TableCommon',
  components: {
    Tooltip
  },
  props: {
    size: {
      type: String,
      default: 'normal'
    },
    keys: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Object,
      default: () => {
        return {
          key: [],
          data: null
        }
      }
    },
    tableStyle: {
      type: Object,
      default: () => {
        return { height: '100%', background: '#1D1D1F', hoverBackground: '#262629' }
      }
    }
  },
  data() {
    return {
      theaderWObject: {},
      isRefresh: true,
      scrollPosition: {
        x: 0, y: 0
      }
    }
  },
  watch: {
    tableData: {
      handler(newVal, oldVal) {
        if (newVal.data.length) {
          setTimeout(() => {
            this.getHeadersWidth()
          }, 0)
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getHeadersWidth()
    window.addEventListener('resize', () => {
      this.getHeadersWidth()
    })
  },
  destroyed() {
    window.removeEventListener('resize', () => {})
  },
  methods: {
    getHeadersWidth() {
      // this.$nextTick(() => {
      //   Object.keys(this.$refs).forEach(key => {
      //       this.theaderWObject[key] = (this.$refs[key][0] && this.$refs[key][0].offsetWidth || 0)
      //     })
      // });
      return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
          Object.keys(this.$refs).forEach(key => {
            this.theaderWObject[key] = (this.$refs[key] && this.$refs[key][0] && this.$refs[key][0].offsetWidth || 0)
          })
          this.tableRefresh().then(() => {
            resolve()
          })
          clearTimeout(timer)
        }, 0)
      })
    },
    tableRefresh() {
      return new Promise((resolve, reject) => {
        try {
          const { scroll } = this.$refs
          const instance = scroll.osInstance()
          const { position } = instance.scroll()
          this.scrollPosition = position
          this.isRefresh = false
          this.$nextTick().then(() => {
            this.isRefresh = true
            const timer = setTimeout(() => {
              const { y } = this.scrollPosition
              this.setScrollTop(y)
              resolve()
              clearTimeout(timer)
            }, 0)
          })
        } catch (err) {
          reject(err)
        }
      })
    },
    setScrollTop(val) {
      const { scroll } = this.$refs
      const instance = scroll.osInstance()
      instance.scroll(val)
    },
    setHoverBg(e) {
      this.tableStyle.hoverBackground ? e.target.style.background = this.tableStyle.hoverBackground : e.target.style.background = '#262629'
    },
    removeHoverBg(e) {
      this.tableStyle.background ? e.target.style.background = this.tableStyle.background : e.target.style.background = '#1D1D1F'
    }
  }
}
</script>

<style lang="scss" scoped>
  .component-table {
    width: 100%;
    height: auto;
    position: relative;
    .table-header {
      display: flex;
      justify-content: flex-start;
      border-bottom: 1px solid $hg-border-color;
      .th {
        // max-width: 16%;
        display: table-cell;
        overflow: hidden;
        padding: 0 10px;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        font-size: $hg-small-fontsize;
        color: $hg-disable-fontcolor;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .operation-title {
        flex: 1;
        text-align: center;
      }
    }
    ::v-deep .os-host-overflow { // 滚动条的位置设置
      overflow-y: auto !important;
      .os-scrollbar-vertical {
        margin-right: -25px;
      }
    }
    .table-body {
      width: 100%;
      height: 280px;
      overflow-x: hidden;
      overflow-y: auto;
    }
    .table {
      width: 100%;
      display: table;
      border-collapse: collapse;
    }
    .tbody {
      display: table-row-group;
      .tr {
        position: relative;
        display: table-row;
        &:not(:last-of-type) {
          border-bottom: 1px dashed $hg-border-color;
        }
        &:hover {
          // background-color: $hg-hover-bg-color;
        }
        .td {
          min-width: 50px;
          max-width: 200px;
          display: table-cell;
          overflow: hidden;
          padding: 0 10px;
          color: $hg-primary-fontcolor;
          vertical-align: middle;
          font-size: $hg-normal-fontsize;
          &_normal {
            height: 56px;
          }
          &_small {
            height: 40px;
          }
          .texts {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            cursor: pointer;
          }
        }
      }
    }

    .empty-table {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: $hg-normal-fontsize;
      color: $hg-secondary-fontcolor;
    }
  }
</style>
