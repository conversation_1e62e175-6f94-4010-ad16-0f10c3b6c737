export default {
  heypoint: {
    customer: {
      no: 'Transaction Number',
      date: 'Date',
      condition: 'Search Condition',
      availableCredit: 'Available Credits',
      totalBalance: 'Complimentary Fund',
      customerName: 'Client Name',
      customerNo:'Client ID',
      customerNameNo: 'Name/Client ID',
      inputTip:'Please enter name or client ID.',
      specialname: 'Customer Name',
      remark: 'Notes',
      settlementType: 'Settlement',
      monthly: 'By Month',
      prepaidMonth: 'Prepaid',
      creditValue:'Credits',
      discountRate: 'Discount Rate',
      availableCreditOptions1: 'Available Credits < 0',
      availableCreditOptions2: '0 ≤ Available Credits ≤ 100',
      availableCreditOptions3: '100 < Available Credits < 1000',
      availableCreditOptions4: 'Available Credits ≥ 1000',
      settlementCurrency: 'Currency',
      country: 'Country',
      credit: 'Prepaid Fund',
      free: 'Complimentary Fund',
      salesManager: 'Sales Manager',
      salesManagerNo: 'Sales Manager Phone No.',
      expiredDate: 'Expired Date',
      nextMonthDiscountRate: 'Next Month Discount Rate',
      heyPointsDetails: 'Account Details',
      edit: 'Edit',
      saveEdit: 'Save',
      giveHeypoint: 'Give Away',
      paidCredit: 'Top Up',
      crmOrderNo: 'CRM Order No.',
      crmContractNo: 'CRM Contract No.',
      processResult: 'Processing Result',
      downloadImportTemplate: 'Download Import Template',
      batchGiveHeypoint: 'Batch Complimentary HeyPoints',
      batchRechargeHeypoint: 'Multi Top up',
      batchGiveQtyLimit: 'Only one file can be uploaded!',
      batchGiveFileLimit: 'Please upload an excel file!',
      rechargeHeypoint: 'Fund Management',
      basic: 'Basic Config.',
      heypointAccountDetails: 'Account Details',
      batchComplimentaryHeyPointsTemplate: 'Batch Recharge Details',
      operate: {
        operateDate: 'Date',
        number: 'Transaction Number',
        type: 'Transaction Type',
        typeTips: 'Select Type',
        heypointTotal: 'Amount',
        heypointExpired: 'Expired Date',
        giftBalance: 'Complimentary Fund',
        rechargeBalance: 'Prepaid Fund',
        availableCredit: 'Available Credits',
        remark: 'remark',
        numberTips: "Transaction number.",
        all: 'All',
        recharge: 'Topped up',
        give: 'Complimentary',
        overdue: 'Expired',
        consume: 'Consumed',
        refund: 'Refunded',
        
        settlementType: 'Settlement',
        giveHeypoint: 'Give Away',
        paidCredit: 'Top Up',
        amount: 'Amount',
        amountBalance: 'Account Balance',
        nowBalance: 'Current Prepaid Fund ',
        availableOverdraft: 'Current Available Credits',
        overdraftLimit: 'Current Credits',
        nowLimit: 'Top-Up Amount',
        enterReson: 'Please enter the reason to top up.',
        confirm: 'Please confirm the information with Finance colleague before submitting.',
        close: 'Close',
        submit: 'Submit',
        refill: 'Please enter the refill amount!',
        zero: "Amount can't be zero.",
        input: "Please enter No. or order's No.",
        expired: 'Expired Date',
        nowfreebalance: 'Current Complimentary Fund ',
        nowAmount: 'Amount to Give',
        rechargeNum: 'Amount',
        remittanceRecords: 'Remittance records No',
        expiredday: 'Expired day',
        reson: 'Please enter the reason to give away.',
        information: 'Please confirm the information with the Business Personnel before submitting.',
        smaller: "The value cannot be smaller than 1!",
        expiration: 'Please select the date.',
        nextday: 'The next day',
        week: 'After a week',
        days: 'After 30 days',
        chargeLimitTips: 'Cannot be larger than the amount of credits already used',
      },
    },
    setting: {
      monthCustomer: 'Monthly-Settling Clients',
      payCustomer: 'Prepaying Clients',
      titltOne: 'When Prepaid Fund is below',
      titleTwo: 'When the proportion of Available Credits in total Credits is below',
      hour: "'",
      titleThird: 'the system will notify clients via',
      email: 'Email',
      client: 'User End',
      leading: 'Notifying the Business Personnel (email)',
      titleFour: 'of top-up.',
      limitNum: 'Only up to 5 balance configurations can be added.'
    },
    operateLog: {
      allHeypointNum: 'Sum of Credits',
      allgitfHeypoint: 'Sum of Complimentary Funds',
      allOverHeypoint: 'Sum of Expired Complimentary Amount',
      number: 'No.',
      user: 'Operator',
      time: 'Date',
      enterName: 'Please Input Customer Name',
      source: 'Source',
      redeemCode: 'Redemption Code',
      discountCode: 'Coupon'
    }
  }
}