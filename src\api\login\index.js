import axios from 'axios'
import http from '../request'

/** 退出登录
 * @params {*} data
 */
export const logout = (params) => {
  return http({
    url: '/uc/service/user/logout',
    method: 'POST',
    data: params,
  })
}

/** 修改密码
 * @params {
 *  "newPassword": "string",
 *  "oldPassword": "string",
 *  "userCode": 0
 * } data
 */
export const changePassword = (params) => {
  return http({
    url: '/user-basic/user/v1/changePassword',
    method: 'POST',
    data: params,
  })
}

/** 获取用户信息
 * @params {
  *  "id": "string"
  * } data
  */
 export const getUserInfo = () => {
  return http({
    url: '/user-basic/user/v1/info',
    method: 'GET',
  })
}

/** 获取用户信息(根据用户编码获取)
 * @params {
  *  "id": "string"
  * } data
  */
 export const getUserInfoByCode = (params) => {
  return http({
    url: '/user-basic/user/v1/infoByUserCode',
    method: 'POST',
    data: params,
  })
}

/** 修改个人信息
 * @params {
  *  "id": "string"
  * } data
  */
 export const changeUserInfo = (params) => {
  return http({
    url: '/user-basic/user/v1/edit',
    method: 'POST',
    data: params,
  })
}

/** 获取S3的上传预授权
 * @params {
 *  "id": "string"
 * } data
 */
 export const getUploadUrl = (params) => {
  return http({
    url: '/user-basic/platformS3/v1/getUploadUrl',
    method: 'POST',
    data: params,
  })
}

/** 完成S3的上传动作
 * @params {
 *  "id": "string"
 * } data
 */
 export const completeUpload = (params) => {
  return http({
    url: `/user-basic/platformS3/v1/completeUpload/${params}`,
    method: 'POST',
  })
}
