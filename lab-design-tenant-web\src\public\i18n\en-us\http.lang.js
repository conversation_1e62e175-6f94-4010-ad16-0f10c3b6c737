export default {
  http: {
    error: {
      '500': 'Service exception',
      remoteLogin: 'Your account has been logged in, Please log in again!',
      relogin: 'Please log in again! ({0})',
      accessUnusual: 'Access exception!({0})',
      noAccess: 'No permission to access! ({0})',
      reloginTip2: 'Please log in again!',
      accessFail: 'Access exception!({0})',
      accessUnusualStatus: 'Access exception! (status:{0})',
      
      ********: 'No operation permission.',
      ********: 'Order ID already exists, please change and resubmit.',
      ********: 'Order saved failed, please resubmit.',
      ********: 'Electronic order saved failed, please resubmit.',
      ********: 'The selected order status must be the same, please reselect.',
      ********: 'Order does not exist.',
      ********: 'Error Operation',
      ********: 'Modify order failed, please resubmit.',
      ********: 'The current order status does not support editing.',
      ********: 'Operation failed. Order/design status has changed.',

      ********: 'Order is not unknown.',
      ********: 'Operation failed, the person in charge of the order has been changed.',
      60010022: 'Operation failed, cannot submit the [ unknown ] design type.',
      60010032: "Submission failed. Certain design types couldn't meet the delivery time expectation.",
      60010034: 'File ID cannot be empty',
      60010035: 'The unit price of design types cannot be equal or less than 0.',
      60010037: 'The Combined Restorations order design file cannot be empty',

      80080003: 'This file has expired. You can obtain the file by contacting Xiao Hey.',
      80080007: 'This file has expired. You can obtain the file by contacting Xiao Hey.',

      11010027: 'Batch recharge failed.',
      11010032: 'The maximum credit value cannot exceed {num}'
    },
  }
}