<template>
  <hg-card class="origin-file">
    <order-title langName="sourceFile">
      <span v-show="showNewTip" class="new-file-icon">NEW</span>
    </order-title>
    <div class="sour-box">
      <div class="file-title">
        {{ $t('orderList.order.orderfiles') }}
      </div>
      <div class="file-box-card">
        <div class="file-box" v-if="fileItem">
          <span><hg-icon icon-name="icon-file-lab" color="#5878B4"></hg-icon> {{ getFileName(fileItem.fileName) }}</span>
          <div class="operate-btn" v-if="canDownload">
            <!-- <span><hg-icon icon-name="icon-preview-on"></hg-icon>预览</span> -->
            <span @click.stop="downloadFile(fileItem)"><hg-icon icon-name="icon-download-lab"></hg-icon>{{ $t('common.btn.download') }}</span>
          </div>
        </div>
        <div v-else>
          {{ $t('common.noData') }}
        </div>
      </div>
    </div>

    <div class="cbct-box">
      <div class="file-title">
        {{ $t('order.detail.CBCTFile') }}
      </div>
  
      <div class="file-box-card cbct-item" v-for="(item, index) in cbctFileList" :key="index">
        <div class="file-box" v-if="item">
          <span><hg-icon icon-name="icon-file-lab" color="#5878B4"></hg-icon> {{ getFileName(item.fileName) }}</span>
          <div class="operate-btn" v-if="canDownload">
            <!-- <span><hg-icon icon-name="icon-preview-on"></hg-icon>预览</span> -->
            <span @click.stop="downloadFile(item)"><hg-icon icon-name="icon-download-lab"></hg-icon>{{ $t('common.btn.download') }}</span>
          </div>
        </div>
        <div v-else>
          {{ $t('common.noData') }}
        </div>
      </div>
    </div>

    <!-- 待设计 -->
    <div class="source-file-tips" v-if="isResponsibleDesigner && orderStatus === ORDER_TYPES.PENDING_DESIGN">
      <span>{{ $t('order.detail.tips.clientUploadFile') }}</span>
      <span class="pointer" @click.stop.prevent="downloadFile(fileItem)">
        {{ $t('order.detail.tips.goToDownload') }}
        <img src="../../../../assets/images/order/download_tip.gif" alt="">
      </span>
    </div>

  </hg-card>
</template>

<script>
import OrderTitle from './OrderTitle';
import { getDownloadUrl } from '@/api/file';
import { ORDER_TYPES, ROLE_CODE } from '@/public/constants';
import { createIFrameDownLoad, getFileNameNoSuffix } from '@/public/utils/file';
import { mapGetters } from 'vuex';

export default {
  name: 'OriginFile',
  components: { OrderTitle },
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: Boolean,
      required: true,
    },
    fileItem: {
      type: Object,
    },
    cbctFileList: Array,
    isResponsibleDesigner: Boolean,
    orderStatus: Number,
    clientOrgCode: Number,
    showNewTip: Boolean,
    iqcCode: Number,
  },
  data() {
    return {
      ORDER_TYPES,
    }
  },
  computed: {
    ...mapGetters(['authList', 'roles', 'userCode']),
    //  【待设计】的订单，设计师、管理员、系统运营、设计运营、IQC、设计师组长点击下载原始文件触发订单状态变更为【设计中】。
    canStartToDesign(){
      console.log('this.iqcCode', this.iqcCode);
      if(this.authList.includes('download') && this.orderStatus === ORDER_TYPES.PENDING_DESIGN) {
        if(this.isResponsibleDesigner) {
          return true;
        }else if(this.iqcCode === this.userCode){
          return true;
        }else {
          const canChangeStatus = this.roles.some(roleItem => 
            [ROLE_CODE.ADMIN, ROLE_CODE.SYSTEM_OPER, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.DESIGN_LEADER].includes(roleItem.roleCode));
          return canChangeStatus;
        }
      }
      return false;
    },
    // [待退回][已退回]只有管理员、系统运营、设计运营、IQC、OQC还可以下载文件
    canDownload() {
      const canDonwloadCode = [ROLE_CODE.ADMIN, ROLE_CODE.SYSTEM_OPER, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.IQC, ROLE_CODE.OQC];

      if([ORDER_TYPES.PENDING_RETURN, ORDER_TYPES.RETURNED].includes(this.orderStatus)) {
        if(this.authList.includes('download') && this.roles.some(roleItem => canDonwloadCode.includes(roleItem.roleCode))) {
          return true;
        }else {
          return false;
        }
      }
      
      return true;
    }
  },
  methods: {
    getFileNameNoSuffix,
    downloadFile(fileItem) {
      const { filePath, fileName } = fileItem;
      if(filePath) {
        const param = {
          s3FileId: filePath,
          filename: getFileNameNoSuffix(fileName),
          orgCode: this.clientOrgCode
        };
        getDownloadUrl(param).then(res => {
          if(res.code === 200) {

            if(this.isResponsibleDesigner && this.orderStatus === ORDER_TYPES.DESIGNING) {
              this.$emit('update', true);
            }

            createIFrameDownLoad(res.data.url, 0, () => { 
              if(this.value) {
                this.$emit('update', false);
              }
            });
            
            if(this.canStartToDesign) {
              setTimeout(() => {
                this.$emit('handleStartToDesign');
              }, 10);
            }
          }else {
            this.$hgOperateFail(this.$t('file.tips.uploadOriginFileFail'));
          }
        }).catch(err => {
          console.log('error:',err);
          // this.$hgOperateFail(this.$t('file.tips.uploadOriginFileFail'));
        });
      }
    },
    getFileName(name) {
      const tempList = name.split('.');
      tempList.pop();
      return  tempList.join('.');
    },
  }
}
</script>

<style lang="scss" scoped>
.origin-file {
  // margin-bottom: 24px;
  .cbct-box {
    margin-top: 24px;
  }
  .file-title {
    margin: 8px 0px;
    font-size: 16px;
    font-weight: 700;
  }
  .file-box-card {
    padding: 24px;
    background-color: #27292E;
    border-radius: 4px;
  }
  .cbct-item {
    margin-bottom: 4px;
  }
  .file-box {
    display: flex;
    justify-content: space-between;

    .operate-btn>span {
      cursor: pointer;
      margin-left: 32px;

      .hg-icon {
        padding-right: 8px;
      }

      .icon-file-lab {
        color: $hg-secondary-primary;
      }
    }
  }
  /* Chrome, Safari, Opera */
  @-webkit-keyframes comeOut {
    from { bottom: 0; opacity: 0; }
    to { bottom: 20%; opacity: 1;}
  }

  /* Standard syntax */
  @keyframes comeOut {
    from { bottom: 0; opacity: 0; }
    to { bottom: 20%; opacity: 1;}
  }
  .source-file-tips {
    position: fixed;
    bottom: 20%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1999; // 要比load-mosk低
    padding: 8px 15px;
    background: $hg-main-blue;
    box-shadow: 0 2px 16px 0 rgba(119,119,119,0.08);
    border-radius: 16px;
    font-size: 12px;
    -webkit-animation: comeOut 2s infinite; /* Chrome, Safari, Opera */
    animation: comeOut 2s infinite;
    animation-iteration-count: 1;
    -webkit-animation-iteration-count: 1; /* Safari 和 Chrome */

    &>span:first-of-type{
      color: $hg-white;
    }

    &>span:nth-of-type(2){
      color: $hg-white;
    }

    .pointer {
      cursor: pointer;
    }

  }
}
.origin-file>.order-title {
  display: flex;
  align-items: center;

  .new-file-icon {
    color: $hg-white;
    background: $hg-new-tag;
    border-radius: 7px;
    padding: 0 5px;
    margin-left: 10px;
    font-size: 12px;
    height: 14px;
    line-height: 14px;
  }
}
</style>