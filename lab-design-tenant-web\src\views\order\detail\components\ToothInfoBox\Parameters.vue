<template>
  <div class="order-parameters">
 
    <el-collapse accordion value="parameters">
      <el-collapse-item name="parameters" >

      <order-title langName="parameters" slot="title">
        <div v-show="isEdit && designList && designList.length" class="edit-param" @click.stop.passive="handleEdit"> 
          <hg-icon icon-name="icon-edit-lab" font-size="24px"></hg-icon>
          <span>{{ $t('common.btn.editParam') }}</span>
        </div>
      </order-title>

      <div v-if="designList && designList.length > 0">
        <hg-card class="param-li" v-for="(data, index) in designList" :key="index">
          <el-collapse accordion :value="openIndex">
            <el-collapse-item :name="index" >
              <div :class="{'param-li_title': true, 'is-update-title': hasUpdate(data.parameter, data.program, data.versionItem, data.designCode)}" slot="title">
                <span><i><i></i></i>{{ $t(`apiCommon.${data.designCode}`) }}</span>
              </div>

              <div v-if="bothEmpty(data)" class="no-data">
                <span>{{ $t('common.noData') }}</span>
              </div>

              <div v-else class="param-li-content">
                <!-- 方案 -->
                <div class="program-box">
                  <div class="label"><i></i>{{ $t('param.title.program') }}</div>
                  <hg-program 
                    :i18nTitle="`${data.i18nTitle}.program`" 
                    :dataList="data.program" 
                    :needZoom="false" 
                    :eventDisable="true"></hg-program>
                </div>
                
                <div class="parameter-box" v-if="data.parameter && data.parameter.length > 0">
                  <div class="label"><i></i>{{ $t('param.title.parameter') }}</div>
                  <!-- 设计软件 -->
                  <div v-if="data.versionItem" class="parameter-item is-software-version">
                    <div>
                      <div class="parameter-item_label">
                        <span>{{ $t('param.version') }}</span>
                      </div>
                      <div class="parameter-item_value">
                        <span>{{ $t(SOFTWARE_I18N[data.software]) }}</span>
                      </div>   
                    </div>         
                  </div>
                  <!-- 软件版本start -->
                  <div v-if="data.versionItem" :class="{'parameter-item': true, 'is-software-version': true, 'is-update-item': data.versionItem.isUpdate}">
                    <div>
                      <div class="parameter-item_label">
                        <span>{{ getI18nName(data.versionItem, `${data.i18nTitle}.param`, $getI18nText) }}</span>
                      </div>
                      <div class="parameter-item_value">
                        <span>{{ data.versionItem.value }}</span>
                      </div> 
                    </div>         
                  </div>
                  <!-- 软件版本end -->
                  
                  <!-- 其他 -->
                  <div 
                    v-for="(param, pIndex) in data.parameter" 
                    :class="{
                      'parameter-item': true, 
                      'is-update-item': param.isUpdate, 
                      'is-line': param.component === COMPONENT_TYPE.TEXT_INPUT_CARD || (param.component === COMPONENT_TYPE.TEXT && !param.value)}" 
                    :key="param.name+pIndex">
                    
                    <div v-if="param.component === COMPONENT_TYPE.TEXT_INPUT_CARD" class="parameter-item-line">
                      <p class="parameter-item-line_label">
                        {{ getI18nName(param, `${data.i18nTitle}.param`, $getI18nText) }}
                      </p>
                      <div class="parameter-item-line_content-box">
                        <div>
                          <div v-for="(childParam, cIndex) in param.child" :key="childParam.name+cIndex" :class="{'is-update-item': childParam.isUpdate, 'content_item': true}">
                            <span class="content_item-label">{{ getI18nName(childParam,  `${data.i18nTitle}.param`, $getI18nText) }}</span>
                            <span class="content_item-value">{{ formatParamValue(childParam, data.i18nTitle) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-else>
                      <div class="parameter-item_label" :class="{'fold': (param.component === COMPONENT_TYPE.TEXT && !param.value), 'disabled': param.disabled}">
                        <span>{{ getI18nName(param, `${data.i18nTitle}.param`, $getI18nText) }}</span>
                        <el-tooltip popper-class="params-tips" effect="light" placement="bottom" v-if="param.tip">
                          <template slot="content">
                            <img :src="`${PARAM_ICON_PATH}/${item.tip}.svg`" alt="">
                          </template>
                          <img src="@/assets/images/common/icon_supplement.png" alt="">
                        </el-tooltip>
                      </div>

                      <div class="parameter-item_value" :class="{'disabled': param.disabled}">
                        <span>{{ formatParamValue(param, data.i18nTitle) }}</span>
                      </div>
                    </div>

                  </div>

                </div>

                <div v-else class="no-data">
                  <span>{{ $t('param.noParameter') }}</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </hg-card>
      </div>

      <div v-else class="no-data">
        <span>{{ $t('common.noData') }}</span>
      </div>

      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import OrderTitle from '../OrderTitle';
import HgProgram from '@/components/Params/HgProgram';
import { parseJson } from '@/public/utils';
import { SOFTWARE, I18N_TITLE, SOFTWARE_I18N, COMPONENT_TYPE, PARAM_ICON_PATH } from '@/components/Params/utils/constant';
import { isParamDifferent, isProgramHaveDifferent } from '@/public/utils/param';
import { getI18nName } from '@/components/Params/utils';

export default {
  name: 'Parameters',
  components: { OrderTitle, HgProgram },
  provide: {
    getTupdateTimes: () => 0,
  },
  props: {
    parameterContent: {
      type:String,
      default: '[]'
    },
    isEdit: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      SOFTWARE_I18N,
      COMPONENT_TYPE,
      PARAM_ICON_PATH,
      openIndex: -1,
      designList: [],

    }
  },
  computed: {
    ...mapGetters(['oneDesignList', 'isLoadDefaultParam', 'language', 'defaultParamMap']),
  },
  watch: {
    isLoadDefaultParam(value) { // 以防万一加载第二次
      if(value) {
        this.init();
      }
    },
    parameterContent() {
      this.init();
    },

  },
  mounted() {
    this.init();
  },
  methods: {
    getI18nName,
    init() {
      let resultList = [];
      const dataList = parseJson(this.parameterContent);
      dataList.forEach(data => {
        const { designCode, parameter, program, software } = data;
        console.log('init-parameter: ', parameter);
        const designItem = this.oneDesignList.find(item => item.designCode === designCode);
        const title = designItem ? { zh: designItem.cnName, en: designItem.enName } : { zh: '', en: '' }; 
        const i18nTitle = I18N_TITLE[designCode];

        let versionItem = null;
        const newParameter = this.handleParameter(parameter, designCode)
        // let paramList = this.getParamList(parameter,designCode, software);
        let paramList = this.getParamList(newParameter,designCode, software);
        console.log('init-paramList: ', paramList);
        if(software === SOFTWARE.THREE_SHAPE) {
          const { versionItem: item, targetList } = this.getVersionItemAndDataList(paramList);
          paramList = targetList;
          versionItem = item;
        }

        const item = {
          designCode,
          title,
          i18nTitle,
          software,
          parameter: paramList,
          program:program,
          versionItem,
        }
        resultList.push(item);
      });
      this.designList = resultList;
    },

    /**
     * 处理特殊正畸带环的参数数据
     */
    handleParameter(parameter = [], designCode) {
      if(!parameter || parameter.length === 0) return [];
      if(parameter.constructor !== Array) return [];
      const hasChildList = []
      const otherList = []
      parameter.forEach(item => {
        if (designCode === 24406 && item.child && item.child.length) {
          hasChildList.push(item)
        } else {
          otherList.push(item)
        }
      })      
      let childList = []
      if (hasChildList && hasChildList.length) {
        const flattenChildren = (data) => {
          // 定义一个存放结果的数组
          const flatList = [];

          // 定义一个递归函数来处理每个对象及其子对象
          function extractChildren(item) {
            // 将当前对象（移除 child 属性）加入结果数组
            const { child, ...rest } = item;
            flatList.push(item);

            // 如果有子元素，递归处理子元素
            if (child && Array.isArray(child)) {
              child.forEach(extractChildren);
            }
          }

          // 处理数据的每一项
          data.forEach(extractChildren);

          return flatList;
        }

        childList = flattenChildren(hasChildList)
        console.log('childList: ', childList);
      }
      return [...childList.filter(item => item.component !== 0), ...otherList]
    },

    /**
     * 获取参数的文本数组
     */
    getParamList(sourceList = [], designCode, software) {
      console.log('sourceList: ', sourceList);
      if(!sourceList || sourceList.length === 0) return [];
      if(sourceList.constructor !== Array) return [];

      let originParamList = []
      let disabledCodeList = []
      let hasDisabled = false
      // 正畸带环参数对比需要特殊处理
      if (designCode === 24406) {
        const allParam = this.defaultParamMap.get(designCode);
        const { parameter } = allParam;
        const paramData = parameter.get(software);
        originParamList = this.handleParameter(paramData, designCode)

        // 正畸带环参数特殊处理
        const specialList = ['removeUndercuts', 'insideSurface']
        const filterList = sourceList.filter(item => specialList.includes(item.name))
        const codeList = []
        filterList.forEach(item => {
          if (item.value.includes('no')) {
            const disabledChild = item.child.map(childItem => childItem.child.filter(obj => obj.component === 4).map(obj => obj.code))
            codeList.push(disabledChild)
            hasDisabled = true
          }
        })
        console.log('codeList: ', codeList);
        disabledCodeList = codeList.flat(2)
        console.log('disabledCodeList: ', disabledCodeList);
      }

      let targetList = [];
      sourceList.forEach(item => {
        const { value, name, unit, component, tip, zhName, enName, ranges, child, code } = item;
        let isUpdate = false;
        let handleValue = value;
        let childList = [];
      
        if(component === COMPONENT_TYPE.TEXT_INPUT_CARD) {
          childList = child.map(childItem => {
            const { value, name:childName, unit, zhName, enName, code } = childItem;
            const childisUpdate = isParamDifferent(childItem, designCode, software, name, component, originParamList );
            if(childisUpdate) { // 子节点有一个update 父节点直接isUpdate
              isUpdate = true;
            }
            return { value, name:childName, unit, zhName, enName, isUpdate: childisUpdate, code };
          });


        }else if(component === COMPONENT_TYPE.RADIO_CHILD_INPUT) {

          isUpdate = isParamDifferent(item, designCode, software, '', '', originParamList);
          const childData = child.find(cItem => cItem.name === value);
          if(childData) { 
            // 如果父节点的值没变，则需要判断子节点
            // handleValue = this.language === 'zh' ? childData.zhName : childData.enName;
            handleValue = this.$t(`apiCommon.${childData.code}`);
            childList = childData.child.map(childItem => {
              const { value, name:childName, unit, zhName, enName, code } = childItem;
              const childisUpdate = isParamDifferent(childItem, designCode, software, name, component, originParamList );
              if(childisUpdate) { // 子节点有一个update 父节点直接isUpdate
                isUpdate = true;
              }
              return { value, name:childName, unit, zhName, enName, isUpdate: childisUpdate, code };   
            });
            
          }

        } else {
          
          isUpdate = isParamDifferent(item, designCode, software, '', '', originParamList);
          if([COMPONENT_TYPE.SELECT, COMPONENT_TYPE.TEXT_IMAGE_CHECKBOX, COMPONENT_TYPE.RADIO].includes(component) && ranges) {
            const rangeList = parseJson(ranges);
            if (rangeList) {
              const rangeItem = rangeList.find(range => range.name === value);
              if(rangeItem) {
                // handleValue = this.language === 'zh' ? rangeItem.zhName : rangeItem.enName;
                handleValue = this.$t(`apiCommon.${rangeItem.name}`);
              }
            }
          }

          if (hasDisabled && disabledCodeList.includes(item.code)) {
            item.disabled = true
          }
        }

        const data = { name, value: handleValue, unit, component, tip, isUpdate, zhName, enName, child: childList, code, disabled: item.disabled };
        targetList.push(data);
      });
      return targetList;
    },

    /**
     * 提取版本号以及返回移除版本号的data list
     */
    getVersionItemAndDataList(sourceList) {
      let versionItem = null;
      const index = sourceList.findIndex(item => item.name === 'softwareVersion');
      if(index > -1) {
        const spliceList = sourceList.splice(index,1);
        versionItem = spliceList[0];
      }
      return {
        versionItem,
        targetList: sourceList
      };
    },

    /**
     * 格式化参数的值
     */
    formatParamValue({value, name, component, unit, child}, i18nTitle) {
      let formatValue = '';
      if(component === COMPONENT_TYPE.NUMBER_RANGE) {
        const valueList = parseJson(value);
        formatValue = `${valueList[0]} - ${valueList[1]} ${unit || ''}`;

      } else if([COMPONENT_TYPE.SELECT, COMPONENT_TYPE.TEXT_IMAGE_CHECKBOX, COMPONENT_TYPE.RADIO].includes(component)) {

        if(['yes','no'].includes(value)) {
          formatValue = this.$t(`param.${value}`);
        }else {
          // formatValue = this.$t(`${i18nTitle}.param.${value}`);
          formatValue = value;
        }

      } else if(component === COMPONENT_TYPE.RADIO_CHILD_INPUT) {
        // 正畸带环参数特殊处理
        const specialList = ['removeUndercuts', 'insideSurface']
        if (specialList.includes(name)) {
          formatValue = value
        } else {
          const childValue = child.map(item => { return `${item.value}${item.unit || ''}`;});
          formatValue = `${value}${childValue.length > 0 ? `,${childValue.join(',') }`: ''}`;
        }

      } else {
        if (value) {
          formatValue = `${value} ${unit || ''}`;
        }
      }

      return formatValue;
    },

    /**
     * 根据内容决定标题是否高亮
     */
    hasUpdate(paramList, programList, versionItem, designCode) {
      if(!paramList || !programList || !designCode) { return false; }

      const noUpdate = paramList.every(item => !item.isUpdate);
      if(!noUpdate) {
        return true;
      }

      if(versionItem && versionItem.isUpdate) {
        return true;
      }

      return isProgramHaveDifferent(programList, designCode);

    },


    handleEdit() {
      this.$emit('openEditParam');
    },
    bothEmpty(data) {
      return (!data.program || data.program.length === 0) && (!data.parameter || data.parameter.length === 0);
    },

  }
}
</script>

<style lang="scss" scoped>
.order-parameters {
  color: $hg-default-text-color;

  .param-li {
    margin-top: 24px;
    background-color: #27292E;

    .no-data {
      margin-top: 24px;
      padding-top: 32px;
      border-top: 1px dashed #38393D;
    }
  }

  .no-data {
    margin-top: 24px;
    span {
      color: $hg-label;
    }
  }
}

.order-parameters {
  .el-collapse /deep/.el-collapse-item__header {
    position: relative;
    padding-bottom: 24px;
    height: 100%;
    width: 100%;
    border-bottom: 1px dashed #38393D;

    .order-title {
      padding-bottom: 0;
      border: none;
    }

    .edit-param {
      cursor: pointer;
      display: flex;
      position: absolute;
      right: 0;
      top: 0;
      font-size: 14px;
      .hg-icon {
        margin-right: 8px;
      }

      &>span {
        text-decoration-line: underline;
      }
    }

    .el-collapse-item__arrow {
      color: $hg-default-text-color;
    }
  }
  

  /deep/.el-collapse-item__wrap .el-collapse-item__header {
    margin-bottom: 0;
    height: 20px;
    color: $hg-default-text-color;
    font-size: 16px;
    padding: 0;
    border: none;

    .param-li_title {
      padding-right: 12px;
      span {
        font-weight: bold;

        &>i {
          position: relative;
          display: inline-block;
          margin-right: 8px;
          padding: 4px;
          border-radius: 4px;
          background-color: rgba(247, 248, 250, 0.7);

          &>i{
            position: absolute;
            top: 2px;
            left: 2px;
            border-radius: 2px;
            border: 2px solid rgb(247, 248, 250);
          }
        }
      }
    }

    .el-collapse-item__arrow {
      font-size: 14px;
    }

    .param-li_title.is-update-title {
      span{
        color: $hg-update;
        &>i {
          background-color: #FFB22C4D;
          &>i {
            border-color: #FFB22C;
          }
        }
      }
    }
  }
}

.order-parameters .param-li-content{
  .program-box {
    margin: 24px 0;
    color: $hg-default-text-color;
    border-top: 1px dashed #38393D;

    /deep/.hg-radio-img-card>.radio-children-box {
      background: #2D2F33;
    }
  }

  .parameter-box {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -12px;
    color: $hg-default-text-color;

    .parameter-item {
      display: flex;
      padding: 0 12px;
      width: 33.3%;
      height: 58px;
      border-bottom: 1px dashed #2D2F33;

      @media screen and (max-width: 1440px) {
        width: 50%;
      }

      &>div {
        display: flex;
        width: 100%;
      }

      .parameter-item_label {
        margin: auto 0;
        padding-right: 12px;
        width: 50%;
        color: #F7F8FA;
        line-height: 20px;
        &.fold {
          span {
            font-weight: 700;
          }
        }
        &.disabled {
          span {
            color: rgba(243, 245, 247, 0.40);
          }
        }
      }

      .parameter-item_value {
        flex: 1; 
        margin: auto 0;
        color: #D7D7D9;
        line-height: 20px;
        &.disabled {
          span {
            color: rgba(243, 245, 247, 0.40);
          }
        }
      }
    }

    .parameter-item.is-software-version {
      margin-right: 24px; // 一行多个parameter-item padding重合问题，省事这样处理吧
      width: 100%;

      &>div {
        width: 33%;
        @media screen and (max-width: 1440px) {
          width: 50%;
        }
      }
      span {
        font-weight: bold;
        color: #F7F8FA;
      }
    }
    
    .parameter-item.is-line {
      width: 100%;
      padding-top: 24px;
      height: auto;
      border: none;

      .parameter-item-line {
        display: flex;
        flex-direction: column;

        &>p {
          font-weight: bold;
        }
      }

      .parameter-item-line_content-box {
        margin: 0 -12px;
        
        &>div {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
        }

        .content_item {
          display: flex;
          padding: 0 12px;
          width: 33.3%;
          height: 58px;
          border-bottom: 1px dashed #2D2F33;

          @media screen and (max-width: 1440px) {
            width: 50%;
          }

          .content_item-label {
            display: inline-block;
            margin: auto 0;
            padding-right: 12px;
            width: 50%;
            color: #F7F8FA;
            line-height: 20px;
          }

          .content_item-value {
            display: inline-block;
            flex: 1; 
            margin: auto 0;
            color: #D7D7D9;
            line-height: 20px;
          }

          span {
            font-weight: normal;
          }
        }

        .content_item.is-update-item {
          span{
            color: #FFA01E;
            font-weight: bold;
          }
        }
      }
    }

    .parameter-item.is-update-item {
      span{
        color: #FFA01E;
        font-weight: bold;
      }
    }
  }

  .program-box,
  .parameter-box {
    .label {
      display: flex;
      align-items: center;
      padding-top: 32px;
      font-size: 16px;
      font-weight: bold;
      width: 100%;

      &>i {
        display: inline-block;
        margin-right: 8px;
        width: 4px;
        height: 16px;
        border-radius: 0px 4px 4px 0px;
        background: #3760EA;
      }
    }
  }

  .parameter-box {
    .label {
      margin-left: 12px;
      padding-bottom: 24px;
    }
  }
}
</style>

<style lang="scss">

.order-parameters {
  /* 需要删的 */
  .el-collapse-item__header {
    color: $hg-default-text-color;
  }
  
  .program-label>span {
    color: $hg-white !important;
  }
  
  .hg-radio-select-card,
  .hg-radio {
    .is-disabled {
      cursor: auto;

      .el-radio__inner {
        cursor: auto;
        background-color: $hg-main-black;
        border-color: $hg-border;
      }

      .el-radio__label {
        cursor: auto;
      }
    }
    .el-radio.is-checked {
      .el-radio__label {
        color: $hg-label;
      }
    }
  }

  .hg-pic {
    cursor: auto !important;
    &:hover {
      background: transparent !important;

      .hg-pic-check {
        display: none;
      }
    }
  }
}
</style>