export default class VueWatherStorager {
  constructor(vm) {
    this.vm = vm
    this.watchStorager = Object.create(null)
    this.unwatchStore = Object.create(null)
  }

  destroy() {
    for (const express in this.unwatchStore) {
      this.unwatchStore[express]()
    }
  }

  has(express) {
    return !!this.watchStorager[express]
  }

  get(express) {
    if (!this.has(express)) {
      return null
    }

    return this.watchStorager[express]
  }

  set(express, watch, options) {
    if (this.watchStorager[express]) {
      this.unwatchStore[express]()
    }

    this.watchStorager[express] = watch
    this.unwatchStore[express] = this.vm.$watch(express, watch, options)
  }

  delete(express) {
    if (!this.has(express)) {
      return
    }

    delete this.watchStorager[express]
    this.unwatchStore[express]()
  }

  // on(express, watch, options) {
  //   if (!this.watchStorager[express]) {
  //     this.watchStorager[express] = []
  //     this.unwatchStore[express] = []
  //   }

  //   this.watchStorager[express].push(watch)

  //   const unwatch = this.vm.$watch(express, watch, options)

  //   this.unwatchStore[express].push(unwatch)

  //   const i = this.watchStorager[express].length - 1

  //   return () => {
  //     this.watchStorager[express].splice(i, 1)

  //     this.unwatchStore[express].splice(i, 1)

  //     unwatch()
  //   }
  // }

  // emit(express) {
  //   if (!this.has(express)) {
  //     return
  //   }

  //   this.watchStorager[express].forEach((watch) => {
  //     watch(this.vm[express])
  //   })
  // }

  // remove(express) {
  //   if (!this.has(express)) {
  //     return
  //   }

  //   this.unwatchStore[express].forEach((unwatch) => {
  //     unwatch()
  //   })

  //   delete this.watchStorager[express]
  // }
}
