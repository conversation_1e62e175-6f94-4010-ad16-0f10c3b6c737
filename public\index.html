<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- <link rel="icon" href="<%= BASE_URL %>favicon.ico"> -->
    <link rel="icon" href="./favicon.ico">
    <title>数字化运营服务平台</title>
  </head>
  <style>
    html,body{margin:0;padding:0;border:0}.SCREEN-LOADING{display:flex;position:fixed;top:0;left:0;width:100%;height:100vh;background:#121314}.SCREEN-LOADING>.content{margin:auto;width:100%;height:100px;text-align:center}.SCREEN-LOADING>.content>div{margin:0 auto;overflow:hidden;color:#3760EA;font-size:36px;width:36px;height:36px;border-radius:50%;transform:translateZ(0);animation:loadBox 1.7s infinite ease,round 1.7s infinite ease}@keyframes loadBox{0%{box-shadow:0 -0.83em 0 -0.4em,0 -0.83em 0 -0.42em,0 -0.83em 0 -0.44em,0 -0.83em 0 -0.46em,0 -0.83em 0 -0.477em}5%,95%{box-shadow:0 -0.83em 0 -0.4em,0 -0.83em 0 -0.42em,0 -0.83em 0 -0.44em,0 -0.83em 0 -0.46em,0 -0.83em 0 -0.477em}10%,59%{box-shadow:0 -0.83em 0 -0.4em,-0.087em -0.825em 0 -0.42em,-0.173em -0.812em 0 -0.44em,-0.256em -0.789em 0 -0.46em,-0.297em -0.775em 0 -0.477em}20%{box-shadow:0 -0.83em 0 -0.4em,-0.338em -0.758em 0 -0.42em,-0.555em -0.617em 0 -0.44em,-0.671em -0.488em 0 -0.46em,-0.749em -0.34em 0 -0.477em}38%{box-shadow:0 -0.83em 0 -0.4em,-0.377em -0.74em 0 -0.42em,-0.645em -0.522em 0 -0.44em,-0.775em -0.297em 0 -0.46em,-0.82em -0.09em 0 -0.477em}100%{box-shadow:0 -0.83em 0 -0.4em,0 -0.83em 0 -0.42em,0 -0.83em 0 -0.44em,0 -0.83em 0 -0.46em,0 -0.83em 0 -0.477em}}@keyframes round{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.SCREEN-LOADING>.content>p{margin-top:48px;color:#3760EA;font-size:16px}
  </style>
  <script language="JavaScript">
    document.title = window.localStorage.getItem('lang') === 'zh' ? '数字化运营服务平台' : 'HeyGears Cloud'
  </script>
  <body>
    <noscript>
      <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div class="SCREEN-LOADING">
      <div class="content"><div></div><p>Loading</p></div>
    </div>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
<script language="JavaScript">
  // 页面加载完成之后隐藏掉loading元素
  window.onload = function(){
    const dom = document.getElementsByClassName('SCREEN-LOADING')[0];
    if(dom) {
      dom.style = 'display:none;z-index:-1;';
    }
  };
</script>
