<template>
  <div class="operate-btn">
    <div v-for="(item, index) in btnList" :key="index">
      <hg-button 
        v-permission="[item.eventName]"
        :class="{'item': true, [item.className]: true, 'is-loading': item.isLoading}" 
        :loading="item.isLoading"
        :disabled="item.disabled || banOperate || isLoadData"
        @click="handleEvent(item)">
          {{ $t(item.name) }}
        </hg-button>
    </div>
  </div>
</template>

<script>
import { ORDER_TYPES, ROLE_CODE } from '@/public/constants';
import { mapGetters } from 'vuex';

export default {
  name: 'OperateButton',
  props: {
    orderStatus: {
      type: [String,Number],
      require: true,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isReturnFromClient: Boolean,
    designerCode: Number,
    designerTypes: Array,
    iqcCode: Number,
    oqcCode: Number,
    isClientRevokeOrder: Boolean,
    canOperate: { // 能否操作
      type: Boolean,
      require: true,
    },
    isLoadData: Boolean, // 数据加载中时按钮禁止点击
  },
  data(){
    return {
      btnList: [],
      eventObject: {
        translate: { name: 'order.detail.btn.translate', eventName: 'translate', isLoading:false, disabled: false, },
        edit: { name: 'order.detail.btn.edit', eventName: 'edit' },
        reback: { name: 'order.detail.btn.reback', eventName: 'reback', className: 'btn-cancel', isLoading:false },
        assignIQC: { name: 'order.detail.btn.assignIQC', eventName: 'assignIQC' },
        translateByMe: { name: 'order.detail.btn.translateByMe', eventName: 'translateByMe', isLoading:false },
        assignDesigner: { name: 'order.detail.btn.assignDesigner', eventName: 'assignDesigner' },
        rebackByDesigner: { name: 'order.detail.btn.rebackByDesigner', eventName: 'rebackByDesigner', className: 'btn-cancel', isLoading:false },
        assignOQC: { name: 'order.detail.btn.assignOQC', eventName: 'assignOQC' },
        finish: { name: 'order.detail.btn.finish', eventName: 'finish', isLoading:false },
        revokeByDesigner: { name: 'order.detail.btn.revokeByDesigner', eventName: 'revokeByDesigner', className: 'btn-plain', isLoading:false },
        examineByMe: { name: 'order.detail.btn.examineByMe', eventName: 'examineByMe', isLoading:false },
        pass: { name: 'order.detail.btn.pass', eventName: 'pass', isLoading:false },
        notPass: { name: 'order.detail.btn.notPass', eventName: 'notPass', className: 'btn-cancel' },
        revokeFromClient: { name: 'order.detail.btn.revokeFromClient', eventName: 'revokeFromClient', className: 'btn-plain', isLoading:false },
        confirmReback: { name: 'order.detail.btn.confirmReback', eventName: 'confirmReback', isLoading:false },
        continueDesign: { name: 'order.detail.btn.continueDesign', eventName: 'continueDesign', className: 'btn-plain', isLoading:false },
        cancelReback: { name: 'order.detail.btn.cancelReback', eventName: 'cancelReback', className: 'btn-plain', isLoading:false },
        approveForFree: { name: 'order.detail.btn.approveForFree', eventName: 'approveForFree', isLoading: false },
        disapproveForFree: { name: 'order.detail.btn.disapproveForFree', eventName: 'disapproveForFree', className: 'btn-plain', isLoading:false },
      },
      editBtnList: [
        { name: 'order.detail.btn.updateOrder', eventName: 'updateOrder', isLoading: false },
        { name: 'order.detail.btn.cancelUpdate', eventName: 'cancelUpdate', className: 'btn-cancel' }
      ]
    }
  },
  computed: {
    ...mapGetters(['userCode','roles']),
    roleCodeList() {
      return this.roles.map(role => role.roleCode);
    },
    banOperate() { // 禁止操作
      return this.btnList.some(btn => btn.isLoading);
    }
  },
  watch: {
    orderStatus() {
      this.init();
    },
    isEdit(value) {
      if(value) {
        this.btnList = this.editBtnList;
      }else {
        this.init();
      }
    },
    isClientRevokeOrder() {
      this.init();
    },
    canOperate() {
      this.init();
    },
    // isAssignDesigner() {
    //   return this.designerTypes ? this.designerTypes.some(item => item.designUser === this.userCode) : this.designerCode === this.userCode
    // }
  },
  mounted() {
    this.init();
  },
  methods: {
    hasRole(roleList) {
      return roleList.some(role => this.roleCodeList.includes(role));
    },
    init(){
      this.btnList = [];
      let btnTypeList = [];

      const isAssignDesigner = this.designerTypes ? this.designerTypes.some(item => item.designUser === this.userCode) : this.designerCode === this.userCode
      if(!this.canOperate) { //4.3.13：设计运营可以对[未认证订单-待译单]进行返单
        if(this.orderStatus === ORDER_TYPES.PENDING_TRANSLATE && this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE)) {
          btnTypeList = ['reback'];
          const rebackBtn = this.eventObject['reback'];
          this.btnList.push(Object.assign({}, rebackBtn, { className: '' }));
        }

      }else {
        switch(this.orderStatus) {
          // 待译单 iqcUser为空：由我译单；iqcUser=this.userId ： 编辑、入检通过、退回订单（同理客户返单：编辑、继续设计、返单）
          case ORDER_TYPES.PENDING_TRANSLATE: // 客户返单 -[编辑]-[继续设计]-[返单]
            {
              // 管理员\系统运营\设计运营最高
              if(this.hasRole([ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER])) {
              // if(this.roleCodeList.includes(ROLE_CODE.ADMIN) || this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) || this.roleCodeList.includes(ROLE_CODE.SYSTEM_OPER) ) {
                if(this.isReturnFromClient) { 
                  btnTypeList = [ 'edit', 'continueDesign', 'reback' ]; //客户返单：[编辑] [继续设计] [返单]
                }else {
                  btnTypeList = ['translate', 'edit', 'reback', 'assignIQC']; // 正常：[编辑][入检通过][返单][指派IQC]
                }
              }

              // IQC 角色
              if(this.roleCodeList.includes(ROLE_CODE.IQC)) {
                if(!this.iqcCode) {
                  btnTypeList = btnTypeList.concat(['translateByMe']); // iqcUser 为空：[由我检查]
                }else if(this.iqcCode === this.userCode) {
                  if(this.isReturnFromClient) {
                    btnTypeList = btnTypeList.concat([ 'edit', 'continueDesign', 'reback' ]);  //客户返单：[编辑] [继续设计] [返单]
                  }else {
                    btnTypeList = btnTypeList.concat(['translate', 'edit', 'reback']); // 正常：[编辑][入检通过][返单]
                  }
                }
              }
              
            }
          break;

          // 待指派
          case ORDER_TYPES.PENDING_ACCEPT:  
            {
              // 管理员 系统运营 设计运营：[编辑]、[指派设计师]
              if(this.hasRole([ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER])) {
              // if(this.roleCodeList.includes(ROLE_CODE.ADMIN) || this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) || this.roleCodeList.includes(ROLE_CODE.SYSTEM_OPER) ){
                btnTypeList = btnTypeList.concat(['edit', 'assignDesigner']);
              }

              if(this.roleCodeList.includes(ROLE_CODE.IQC) && this.iqcCode === this.userCode) { // IQC角色，当前iqcUser为本人：[编辑]、[指派设计师]
                btnTypeList = btnTypeList.concat(['edit', 'assignDesigner']);
              }
            } 
          break;

          // 待设计
          case ORDER_TYPES.PENDING_DESIGN: 
            { 
              // 管理员 系统运营 设计运营：[编辑]、[指派设计师] 20250217需求不允许编辑
              if(this.hasRole([ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER])) {
              // if(this.roleCodeList.includes(ROLE_CODE.ADMIN) || this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) || this.roleCodeList.includes(ROLE_CODE.SYSTEM_OPER) ){
                // btnTypeList = btnTypeList.concat(['edit', 'assignDesigner']);
                btnTypeList = btnTypeList.concat(['assignDesigner']);
              }

              if(this.roleCodeList.includes(ROLE_CODE.IQC) && this.iqcCode === this.userCode) { // IQC角色，当前iqcUser为本人：[编辑]、[指派设计师]
                // btnTypeList = btnTypeList.concat(['edit', 'assignDesigner']);
                btnTypeList = btnTypeList.concat(['assignDesigner']);
              }

              if(this.roleCodeList.includes(ROLE_CODE.DESIGN_LEADER)) { // 设计师组长 [指派设计师]（组长只能看到自己有权限操作的订单）
                // btnTypeList = btnTypeList.concat(['edit','assignDesigner']);
                btnTypeList = btnTypeList.concat(['assignDesigner']);
              }

              if((this.roleCodeList.includes(ROLE_CODE.DESIGN_LEADER) || this.roleCodeList.includes(ROLE_CODE.DESIGNER)) 
                && isAssignDesigner) { // 设计师、设计师组长角色 designerCode为本人：[退回订单]
                // btnTypeList = btnTypeList.concat(['edit','rebackByDesigner']); //2023-05-24需求：设计师、设计组长能编辑
                // btnTypeList = btnTypeList.concat(['rebackByDesigner']);
                if (this.designerTypes) {
                  const currentDesignerItem = this.designerTypes.find(item => item.designUser === this.userCode)
                  if (currentDesignerItem && !currentDesignerItem.isDesigned) {
                    btnTypeList = btnTypeList.concat(['edit','rebackByDesigner']); // 设计师、设计师组长角色 designerCode为本人：[退回订单] [完成设计]
                  }
                } else {
                  btnTypeList = btnTypeList.concat(['edit','rebackByDesigner']); // 设计师、设计师组长角色 designerCode为本人：[退回订单] [完成设计]
                }
              }
            }
          break;

          // 设计中
          case ORDER_TYPES.DESIGNING: 
            { 
              // 管理员 系统运营 设计运营：[指派设计师]
              if(this.hasRole([ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER])) {
              // if(this.roleCodeList.includes(ROLE_CODE.ADMIN) || this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) || this.roleCodeList.includes(ROLE_CODE.SYSTEM_OPER) ){
                btnTypeList = btnTypeList.concat(['assignDesigner']);
              }

              if(this.roleCodeList.includes(ROLE_CODE.IQC) && this.iqcCode === this.userCode) { // IQC角色，当前iqcUser为本人：[指派设计师]
                btnTypeList = btnTypeList.concat(['assignDesigner']);
              }
              
              if(this.roleCodeList.includes(ROLE_CODE.DESIGN_LEADER)) { // 设计师组长 [指派设计师]（组长只能看到自己有权限操作的订单）
                // btnTypeList = btnTypeList.concat(['edit','assignDesigner']); //2023-05-24需求：设计师、设计组长能编辑
                btnTypeList = btnTypeList.concat(['assignDesigner']); //2023-05-24需求：设计师、设计组长能编辑
              }
              // 原有判断逻辑：this.designerCode === this.userCode
              if( (this.roleCodeList.includes(ROLE_CODE.DESIGN_LEADER) || this.roleCodeList.includes(ROLE_CODE.DESIGNER))
              && isAssignDesigner) {
                if (this.designerTypes) {
                  const currentDesignerItem = this.designerTypes.find(item => item.designUser === this.userCode)
                  if (currentDesignerItem && !currentDesignerItem.isDesigned) {
                    btnTypeList = btnTypeList.concat(['edit','rebackByDesigner', 'finish']); // 设计师、设计师组长角色 designerCode为本人：[退回订单] [完成设计]
                  }
                } else {
                  btnTypeList = btnTypeList.concat(['edit','rebackByDesigner', 'finish']); // 设计师、设计师组长角色 designerCode为本人：[退回订单] [完成设计]
                }
              }
            }
          break;

          // 待审核
          case ORDER_TYPES.PENDING_REVIEW: 
            {
              // 管理员 系统运营 设计运营：[指派OQC] 20241115新增设计师组长
              if(this.hasRole([ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER, ROLE_CODE.DESIGN_LEADER])) {
              // if(this.roleCodeList.includes(ROLE_CODE.ADMIN) || this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) || this.roleCodeList.includes(ROLE_CODE.SYSTEM_OPER) ){
                btnTypeList = btnTypeList.concat(['assignOQC']);
              }

              // OQC 角色
              if(this.roleCodeList.includes(ROLE_CODE.OQC)) {  
                if(!this.oqcCode) {
                  btnTypeList = btnTypeList.concat(['examineByMe']); // oqcCode为空：[由我译单]
                }else if(this.oqcCode === this.userCode) {
                  // btnTypeList = btnTypeList.concat(['edit','pass', 'notPass']); // oqcCode 等于本人：[审核通过]、[审核不通过] 、 2023-05-24需求：OQC能编辑
                  btnTypeList = btnTypeList.concat(['pass', 'notPass']); //2025-2-17不能编辑
                }
              }

              if( (this.roleCodeList.includes(ROLE_CODE.DESIGN_LEADER) || this.roleCodeList.includes(ROLE_CODE.DESIGNER))
              && isAssignDesigner) { // 设计师、设计师组长角色 为本人：[撤回]
                btnTypeList = btnTypeList.concat(['revokeByDesigner']);
              }
            }
          break;

          // 待确认
          case ORDER_TYPES.PENDING_CONFIRM:  
            { 
              // 管理员 系统运营 设计运营 OQC且为oqcUser ： [撤回]
              if(this.hasRole([ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER]) || (this.roleCodeList.includes(ROLE_CODE.OQC) && this.oqcCode === this.userCode)) {
                btnTypeList = btnTypeList.concat(['revokeFromClient']);
              }
            }
          break;

          // 待退回
          case ORDER_TYPES.PENDING_RETURN:  
            {
              if(this.hasRole([ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER])) {
                btnTypeList = btnTypeList.concat(['confirmReback', 'continueDesign', 'edit']);
              }

              if(this.roleCodeList.includes(ROLE_CODE.IQC) && this.iqcCode === this.userCode) {
                btnTypeList = btnTypeList.concat(['confirmReback', 'continueDesign', 'edit']);
              }
            }
          break;

          // 已退回
          case ORDER_TYPES.RETURNED:  
            {
              // 管理员 系统运营 设计运营 IQC且为iqcUser ： [撤回]
              if(!this.isClientRevokeOrder 
                && (this.hasRole([ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER]) 
                || (this.roleCodeList.includes(ROLE_CODE.IQC) && this.iqcCode === this.userCode))) {
                btnTypeList = btnTypeList.concat(['cancelReback']);
              }
            }
          break;

          // 申请免单 4.3.13 仅设计运营处理
          case ORDER_TYPES.REQUEST_FREE: 
            {
              if(this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE)) {
                btnTypeList = btnTypeList.concat(['approveForFree', 'disapproveForFree']);
              }
            }
          break;

          default: break;
        }

        btnTypeList = Array.from(new Set(btnTypeList)); // 去重
        btnTypeList.forEach(type => {
          let eventItem = this.eventObject[type];
          // 4.3.28 未知也允许[入检通过]
          /* if(type === 'translate') {
            if(this.isUnknown) {
              eventItem.disabled = true;
            }else {
              eventItem.disabled = false;
            }
          } */
          this.btnList.push(eventItem);
          
        });
      }
    },

    // 处理事件
    handleEvent(btnItem) {
      if(btnItem.eventName) {
        const funtionName = `handle${this.toUpperFirstLetter(btnItem.eventName)}`;
        // this.$emit(funtionName,btnItem);
        this.$emit('handleEvent', { funtionName, btnItem });
      }
    },
    // 首字母大写
    toUpperFirstLetter(string){
      return string[0].toUpperCase() + string.substr(1);
    }
  }
}
</script>

<style lang="scss" scoped>
.operate-btn {
  width: 100%;
  &>div {
    float: right;
    margin-right: 16px;

    &:first-of-type {
      margin-right: 0;
    }
  } 

  .item {
    min-width: 80px;
    height: 40px;
  }

  .is-loading {
    background-color: #3760EA;
  }

  .assign-btn {
    width: auto;
  }

  .btn-plain {
    color: $hg-main-blue;
    background: transparent;
    border: 1px solid $hg-main-blue;
    &:hover {
      color: $hg-main-blue;
      border: 1px solid $hg-main-blue;
    }
  }

  .btn-cancel {
    color: $hg-grey;
    background: transparent;
    border: 1px solid $hg-grey;
    &:hover {
      color: $hg-label;
      border: 1px solid $hg-label;
    }
  }
}
</style>