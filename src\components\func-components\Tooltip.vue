<template>
  <div class="tooltip-box">
      <el-tooltip :content="content" :placement="position">
        <slot></slot>
      </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'Tooltip',
  props: {
    content: {
      type: [String, Number],
      default: ''
    },
    position: { // 提示框的位置，
      type: String,
      default: 'bottom-start'
    },
  },
  data() {
    return {}
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
</style>
