import Vue from 'vue'
import MessageAlertComponent from './MessageAlert.vue'

const MessageAlertConstructor = Vue.extend(MessageAlertComponent)

function showMessageAlert({ text, type, duration = 2000 }) {
  if (showMessageAlert.singleton) return false
  if (type !== 'success') {
    showMessageAlert.singleton = true
  }
  return new Promise((resolve, reject) => {
    if (document.getElementsByClassName('MessageAlert').length > 3) {
      return false;
    }
    try {
      let top = 86;
      top += document.getElementsByClassName('MessageAlert').length * 50;
      duration += document.getElementsByClassName('MessageAlert').length * 300;
      const MessageAlertDom = new MessageAlertConstructor({
        el: document.createElement('div'),
        data() {
          return {
            isShow: true,
            text: text,
            type: type,
            top: top
          }
        }
      });
      document.body.appendChild(MessageAlertDom.$el)
      setTimeout(() => {
        MessageAlertDom.isShow = false;
        setTimeout(() => {
          document.body.removeChild(MessageAlertDom.$el)
        }, 3500)
        showMessageAlert.singleton = false
        resolve()
      }, duration)
    } catch (err) {
      reject()
    }
  })
}

function registryMessageAlert() {
  Vue.prototype.$MessageAlert = showMessageAlert
}

export default registryMessageAlert
