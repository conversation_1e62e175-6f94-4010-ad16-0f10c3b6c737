<template>
  <Popup :show="show" :popup-title="popupTitle" :is-use-ele="true" :loading="loading" @cancel="cancel" @submit="submitForm('addUserRuleForm')">
    <div slot="popupContent" class="add-user-box custom-form">
      <el-form ref="addUserRuleForm" :model="newUserObj" :rules="rules">
        <el-form-item :label="$t('org.name')" prop="realName" class="add-user-label">
          <el-input v-model="newUserObj.realName" type="text" :placeholder="$t('org.usernamePlaceholder')" :title="newUserObj.realName ? '' : $t('org.usernamePlaceholder')" />
        </el-form-item>
        <el-form-item :label="$t('org.email')" prop="email" class="add-user-label">
          <el-input v-model="newUserObj.email" type="text" :placeholder="$t('org.emailPlaceholder')" :title="newUserObj.email ? '' : $t('org.emailPlaceholder')" />
        </el-form-item>
        <!-- 时区 -->
        <el-form-item :label="$t('timezone.timezone')" prop="tzCode" class="add-user-label">
          <div class="input-box"><Select :select-options="timezoneList" :value="newUserObj.tzCode" @change="changeTimezone" /></div>
        </el-form-item>
        <!-- 区域 -->
        <!-- <el-form-item :label="$t('customer.area')" prop="areaCode" class="add-user-label">
          <div class="input-box">
            <el-cascader style="width: 100%;" v-model="newUserObj.areaCode" :props="{label: 'label', value: 'areaCode'}" :options="areaList" @change="changeArea"></el-cascader>
          </div>
        </el-form-item> -->
        <el-form-item :label="$t('org.department')" prop="selectedDept" class="add-user-label">
          <div class="input-box"><VueCascader :value="newUserObj.selectedDept" :options="deptArr" :props-obj="propsObj" @change="changeDept" @expandChange="expandChange" /></div>
        </el-form-item>
        <el-form-item :label="$t('org.role')" prop="selectedRole" class="add-user-label">
          <div class="input-box"><Select :placeholder="$t('org.rolePlaceholder')" :select-options="updateRoleArr" :value="newUserObj.selectedRole" :is-multiple="true" @change="changeRole" /></div>
        </el-form-item>
        <el-form-item :label="$t('org.phone')" prop="mobile" class="add-user-label">
          <div class="area-code"><Select :select-options="countryListArrayComputed" :value="newUserObj.selectedAreaCode" :placeholder="$t('org.areaCode')" @change="changeAreaCode" /></div>
          <el-input v-model="newUserObj.mobile" type="text" :placeholder="$t('org.phonePlaceholder')" :title="newUserObj.mobile ? '' : $t('org.phonePlaceholder')" />
        </el-form-item>
      </el-form>
    </div>
  </Popup>
</template>

<script>
import Popup from '@/components/func-components/Popup'
import Select from '@/components/func-components/Select'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { refreshLabel } from '@/assets/script/refreshLabel.js'
import VueCascader from '@/components/func-components/VueCascader'
import { getTimezoneList, getAreaList } from '@/api/common'
import { myTimeFormat } from '@/assets/script/formatDate.js'
import { getStore } from '@/assets/script/storage.js'

export default {
  name: 'AddOrgUser',
  components: {
    Popup,
    Select,
    VueCascader
  },
  props: {
    popupTitle: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    curOrgObj: {
      type: Object,
      default: {}
    },
    deptArr: {
      type: Array,
      default: []
    },
    roleArr: {
      type: Array,
      default: []
    },
    areaCodeArr: {
      type: Array,
      default: []
    },
    fromCustomer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    var checkMobile = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.PHONE_RULE.test(value)) {
          return callback(new Error(this.$t('org.phoneErro')))
        }
      }
      callback()
    }
    var checkEmail = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.EMAIL_RULE.test(value)) {
          return callback(new Error(this.$t('org.emailErro')))
        }
      }
      callback()
    }
    var getChildrenOrg = (node) => {
      return new Promise((resolve) => {
        this.$parent.getOrgListFunc(node.value).then((nodes) => {
          resolve(nodes)
        })
      })
    }
    return {
      newUserObj: {
        selectedDept: '', // 选中的部门信息,
        selectedRole: [], // 选中的角色信息,多选，保存所选角色的角色id
        selectedAreaCode: '+86', // 选中的区号
        realName: '',
        email: '',
        mobile: '',
        tzCode: getStore('userInfo').timezone.tzCode,
      },
      rules: {
        realName: [
          { required: true, message: this.$t('org.usernamePlaceholder') },
          { max: 50, message: this.$t('org.realNameErro') }
        ],
        email: [
          { required: true, message: this.$t('org.emailPlaceholder') },
          { validator: checkEmail, trigger: 'blur' }
        ],
        tzCode: [
          { required: true, message: this.$t('timezone.timezoneErr') }
        ],
        areaCode: [
          { required: true, message: this.$t('customer.selectArea') }
        ],
        selectedRole: [
          { required: true, message: this.$t('org.rolePlaceholder') }
        ],
        selectedDept: [
          { required: true, message: this.$t('org.deptPlaceholder') }
        ],
        mobile: []
      },
      propsObj: {
        expandTrigger: 'hover',
        value: 'orgCode',
        label: 'orgName',
        children: 'sonList',
        leaf: 'isLeaf',
        emitPath: false,
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          if (!node.isLeaf) {
            getChildrenOrg(node).then((data) => {
              node.children = []
              node.children.concat(data)
              // 通过调用resolve将子节点数据返回，通知组件数据加载完成
              resolve(data)
            })
          } else {
            resolve()
          }
        }
      },
      timezoneList: [],
      areaList: [],
      updateRoleArr: [],
      loading: false
    }
  },
  computed: {
    countryListArrayComputed() { // 根据目前的中英文状态返回相对应的中英文区号
      const countryListArrayNew = []
      this.areaCodeArr.forEach((item) => {
        if (this.$i18n.locale === 'zh') {
          item.label = item.countryName + ' +' + item.mobilePrefix
        } else {
          item.label = item.countryEn + ' +' + item.mobilePrefix
        }
        item.value = item.mobilePrefix
        countryListArrayNew.push(item)
      })
      return countryListArrayNew
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.updateRoleArr = this.roleArr
        // 默认选中当前组织为部门
        this.newUserObj.selectedDept = this.curOrgObj.orgCode
        // 如果选中的部门不是一级组织，则不能选择管理员角色(没有管理员这个角色了，已经废弃)
        // this.updateRoleList(this.curOrgObj)

        refreshLabel('add-user-label')

        this.getTimezoneListFunc();
        // this.getAreaList();
      } else {
        this.resetForm('addUserRuleForm')
      }
    },
    'newUserObj.selectedRole'(value){
      // 当选择了业务人员，则电话号码要改成必填不能为空
      if(value.includes(50032)){
        this.$set(this.rules, 'mobile', [{ required: true, validator: this.checkMobile, trigger: 'blur' }]);
      } else {
        this.$set(this.rules, 'mobile', []);
        if(this.$refs && this.$refs.addUserRuleForm){
          this.$refs.addUserRuleForm.clearValidate('mobile');
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    checkMobile(rule, value, callback){
      if(!value){
        return callback(new Error(this.$t('personal.phonenull')))
      }
      if (value) {
        if (!COMMON_CONSTANTS.PHONE_RULE.test(value)) {
          return callback(new Error(this.$t('org.phoneErro')))
        }
      }
      callback()
    },
    // 根据选中的部门更新角色列表，只有一级组织才能选择管理员角色
    updateRoleList(curOrg) {
      if (this.updateRoleArr.length) {
        const admin = this.updateRoleArr.filter(item => item.value === 50015)
        if (curOrg.depth !== 1 && admin.length) {
          this.updateRoleArr = this.updateRoleArr.filter(item => item.value !== 50015)
          // 选择了非一级组织作为部门，则去掉已选中的管理员角色
          this.newUserObj.selectedRole.includes(50015) ? this.newUserObj.selectedRole.splice(this.newUserObj.selectedRole.findIndex(item => item === 50015), 1) : ''
        } else if (curOrg.depth === 1 && !admin.length && this.fromCustomer) {
          this.updateRoleArr.unshift({ 'label': '管理员', 'lable_en': 'Administrator', value: 50015 })
        }
      }
    },
    cancel() {
      this.loading = false
      this.$emit('update:show', false)
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$emit('submit', this.newUserObj)
        } else {
          this.loading = false
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 获取时区信息列表
    getTimezoneListFunc() {
      getTimezoneList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.timezoneList = res.data
            this.timezoneList.forEach((item) => {
              /* let utc = myTimeFormat(Math.abs(item.utc * 60 * 1000), ':')
              utc = item.utc < 0 ? `-${utc}` : `+${utc}`
              item.label = this.$t(`timezone.${item.countryCode}`, { utc: utc }) */
              item.label = this.$i18n.locale === 'zh' ? item.tzNameCn : item.tzNameEn;
              item.value = item.tzCode
            })
          }
        }
      })
    },
    // 获取区域列表
    async getAreaList(){
      const loop = (arr) => {
        arr.forEach((item) => {
          item.label =  this.$i18n.locale === 'zh' ? item.nameCn : item.name;
          if(item.children){
            loop(item.children)
          }
        })
        return arr
      }
      const { code, data } = await getAreaList();
      if(code == 200){
        this.areaList = loop(data);
        console.log(this.areaList)
      }
    },
    // 选择时区
    changeTimezone(value) {
      this.newUserObj.tzCode = value
    },
    // 选择区域
    changeArea(value){
      this.newUserObj.areaCode = value
    },
    // 选择部门
    changeDept(value, nodes) {
      this.newUserObj.selectedDept = value
      this.updateRoleList(nodes[0].data)
    },
    expandChange(value) {
    },
    // 选择角色
    changeRole(value) {
      this.newUserObj.selectedRole = value;
    },
    // 选择区号
    changeAreaCode(value) {
      // select获取到的值是value，但是显示的是label，所以将显示的值变成数字类型，就能只获取到区号，然后在区号前面加上"+"号
      this.newUserObj.selectedAreaCode = '+' + Number(value)
    }
  }
}
</script>

<style lang="scss" scoped>
.add-user-box {
  .input-box {
    width: 100%;
  }
  .area-code {
    width: 96px;
    margin-right: 12px;
  }
}
</style>
