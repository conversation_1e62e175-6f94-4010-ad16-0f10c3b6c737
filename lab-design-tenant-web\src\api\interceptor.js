import axios from 'axios';
import httpCode, { loginErrors, authErrors, networkStatusError, accessStatusError, billStatusError } from '@/public/constants/httpCode';
import { redirectLogin } from '@/public/utils/token';
import { getStore, setStore } from '@/public/utils/storage';
import { IS_LOCAL_MODEL } from '@/public/constants/setting';
import { message } from '@/public/lib/element/resetMessage';
import QS from 'qs';
import i18n from '@/public/i18n/index';

const CancelToken = axios.CancelToken;
const source = CancelToken.source();
const $message = message;
const handleErrorMessage = (message, reLogin) => {
  $message({
    message: message,
    type: 'error',
    duration: 2 * 1000
  });
  if(reLogin) {
    const timer = setTimeout(() => {
      redirectLogin();
      clearTimeout(timer);
    }, 2000);
  }
};

/**
 * 需要直接对接服务的，跑的命令自己添加--mode local
 * 直接对服务用xh-hg-user: base64，鉴于生成过程十分复杂，这边建议自己直接调接口然后拿到数据压缩 + 转base64 -_-
 *
 * 请求接口 POST https://dev-svc.heygears.com/uc//svc/user/v1/transfer
 * 请求参数 Body {"apiUrl":"123","domain":"http://dev-lab.heygears.com","userCode":100024}  userCode必须是用户端的有效userCode
 * 得到返回，只拿data里面的数据(res.data，和code同级那个)，网上找个JSON格式化工具：压缩 然后base64加密 就得到x-hg-user
 *
 * 每两个小时更新一次 -_- Attention!!!!! 自己替换x-hg-user有效值
 *
 */
const header = IS_LOCAL_MODEL
  ? {
      'x-hg-user':
        'eyJ1c2VyQ29kZSI6MTAwMDI0LCJ0ZW5hbnRDb2RlIjoxMDAwLCJzb2x1dGlvbkNvZGUiOjIwMDAwNSwicm9sZUNvZGVzIjpbNTAwMDNdLCJtZW51Q29kZXMiOltdLCJjb25zdW1lckNvZGUiOm51bGwsIm9yZ0luZm8iOlt7Im9yZ0NvZGUiOjEwMDAxNCwicHJlT3JnQ29kZSI6MTAwMDEwLCJzdWJPcmdDb2RlcyI6W10sImJyb09yZ0NvZGVzIjpbMTAwMDE1LDEwMDAxNl19XSwiZGF0YUNvbnRyb2wiOls5XX0=',
    }
  : {
      authorization: 'Bearer ' + (getStore('AccessToken') || ''),
    }

/**
 * 创建axios实例
 * @param {String} baseUrl
 */
export const getAxios = (baseUrl) => {
  const server = axios.create({
    baseURL: baseUrl,
    timeout: 120000,
    headers: {
      'content-type': 'application/json;charset=utf-8',
      Accept: 'application/json',
      ...header,
    },
  });

  /**
   * 请求拦截
   */
  server.interceptors.request.use((config) => {
    const token = getStore('AccessToken') || '';
    if(!token) {
      config.cancelToken = source.token;
      source.cancel();
      handleErrorMessage(i18n.t('http.error.reloginTip2'), true);
    }

    if (config.method === 'get') {
      config.paramsSerializer = function(params) {
        return QS.stringify(params, { arrayFormat: 'repeat' });
      }
    }

    config.headers.authorization = 'Bearer ' + token;
    return config;
  });

  /**
   * 响应拦截
   */
  server.interceptors.response.use((response) => {
    const { config } = response;
    if (response.status === httpCode.SUCCESS) {  // 貌似现在后端统一返回的response.status都是200
      // const resCode = response.data.code;
      const { code: resCode, data, message, detailMessage } = response.data;
      const newToken = response.headers.authorization;
      if (newToken) {
        compareToken(newToken);
      }

      if (resCode === httpCode.SUCCESS) { // 成功
        const resData = {
          code: resCode,
          data: data,
        }
        return Promise.resolve(resData);

      } else if (resCode === httpCode.remoteLoginStatus) { // 异地登录
        handleErrorMessage(i18n.t('http.error.remoteLogin'), true);
        return Promise.reject({});

      } else if(loginErrors.includes(resCode)) { // 登陆状态错误
        handleErrorMessage(i18n.t('http.error.relogin',[resCode]), true);
        return Promise.reject({});

      } else if(authErrors.includes(resCode)) { // 权限异常
        handleErrorMessage(i18n.t('http.error.noAccess',[resCode]));
        return Promise.reject({});

      } else if(accessStatusError.includes(resCode)) { // 访问异常
        handleErrorMessage(i18n.t('http.error.accessFail',[resCode]));
        return Promise.reject({});

      } else if(networkStatusError.includes(resCode)) { // 4xx 5xx 异常
        handleErrorMessage(i18n.t('http.error.accessFail',[resCode]));
        return Promise.reject({});

      } else if(billStatusError.includes(resCode)){ // 账单管理的异常需要走业务逻辑，抛出
        const resData = {
          code: resCode,
          data,
          message,
          detailMessage
        }
        return Promise.resolve(resData);
      } else { // 业务性质的错误在对应的业务中catch处理吧
        
        if (i18n.te(`http.error.${resCode}`) && !config.isHandlerError) {
          $message({
            message: i18n.t(`http.error.${resCode}`),
            type: 'error',
            duration: 3 * 1000
          });
          return Promise.reject({});
          
        } else {
          const resData = {
            code: resCode,
            data,
            message,
            detailMessage
          }
          return Promise.reject(resData);
        }
        
      }

    } else {
      handleErrorMessage(i18n.t('http.error.accessUnusualStatus',[response.status]));
      return Promise.reject({});
    }

  },(error) => {
    const { response, message } = error;
    if (response) {
      handleErrorMessage(i18n.t('http.error.accessUnusualStatus',[response.status]));
    } else {
      handleErrorMessage(i18n.t('http.error.accessUnusualStatus',[message]));
    }
    return Promise.reject({message});
  });

  return server;
};

// 比较token是否过期了
const compareToken = (newToken) => {
  const oldToken = getStore('AccessToken');
  if (newToken && oldToken !== newToken) {
    // console.log('set token，更新的token：', newToken);
    setStore('AccessToken', newToken);
  }
};
