<template>
  <div class="hg-radio-img-card">
    <el-radio-group 
      v-model="selectValue" 
      :disabled="eventDisable"
      @change="handleParentChange">
      <el-radio v-for="(item, index) in selectList" :key="index" :label="typeof item === 'string' ? item : item.name">
        {{ getI18nName(item, i18nTitle, $getI18nText) }}
      </el-radio>
    </el-radio-group>

    <div class="radio-children-box" v-if="childrenList.length > 0">
      <div 
        v-for="(item, index) in childrenList" 
        :key="item.name+index"
        :class="{'img-checkbox-item': true,'is-active': selectChildValue === item.name}"  
        @click="handleChildChange(item.name)">
        <span>{{ getI18nName(item, i18nTitle, $getI18nText) }}</span>
        <hg-pic :iconPath="item.iconPath"></hg-pic>
      </div>
    </div>
  </div>
</template>

<script>
import HgPic from './HgPic';
import { getI18nName } from '../utils';

export default {
  name: 'HgRadioImgCard',
  components: { HgPic },
  inject: ['getTupdateTimes'],
  model: {
    prop: 'value',
    event: 'update',
  },

  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          child: [],
        }
      }
    },
    i18nTitle: { // i18n需要拼接成i18n国际化文本，数据库就不存太长
      type: String,
      default: '',
    },
    parentItem: { // 关联父级对象
      type: Object,
      default() {
        return null;
      }
    },
    eventDisable: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      selectValue: null,
      selectChildValue: null,

      initChildValue: null, // 记录子集加载时的值

      childrenList: [],
    }
  },

  computed: {
    selectList() {
      const { child } = this.data;
      return child || [];
    },
  },
  watch: {
    value(newValue) {
      this.initOldValue();
    },
    data: {
      deep: true,
      handler(newData) {
        const { value, child } = newData;

        let childItem = child.find(item => item.name === value);
        if(childItem && childItem.value !== this.selectChildValue) {
          this.selectChildValue = childItem.value;
        }
      }
    }
  },
  mounted() {
    this.initOldValue();
  },

  methods: {
    getI18nName,

    initOldValue() {
      this.selectValue = this.value;
      const selectData = this.data.child.find(item => item.name === this.value);
      const { value, child } = selectData;
      this.selectChildValue = value;
      this.initChildValue = value;
      this.childrenList = child || [];
    },

    handleParentChange(value) {
      this.updateChildValue(value, this.value);
      this.$emit('update', value);
    },

    updateChildValue(selectParnetValue, lastSelectValue) {
      
      let lastSelectData = this.data.child.find(item => item.name === lastSelectValue); // 把旧的数据填充回去
      lastSelectData.value = this.initChildValue;

      const selectData = this.data.child.find(item => item.name === selectParnetValue); // 把新的赋值
      if(selectData && selectData.child.length > 0) {
        this.selectChildValue = selectData.value;
        this.initChildValue = selectData.value;
      }
    },

    handleChildChange(value) {
      if(this.eventDisable) { return ;}
      
      this.selectChildValue = value;

      this.data.child.forEach(item => {
        if(item.name === this.selectValue) {
          item.value = value;
        }
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.hg-radio-img-card {

}

.hg-radio-img-card>.radio-children-box {
  display: flex;
  flex-flow: wrap;
  margin-top: 32px;
  padding: 32px;
  border-radius: 2px;
  background: #262629;
  font-size: 12px;

  .img-checkbox-item {
    display: flex;
    flex-direction: column;
    width: 176px;

    span {
      flex: 1;
      display: flex;
      flex-direction: column-reverse;
      line-height: 16px;
    }

    .hg-pic {
      cursor: pointer;
      margin-top: 4px;
    }
  }

  .img-checkbox-item.is-active {
    color: $hg-secondary-primary;

    .hg-pic {
      border: 2px solid $hg-main-blue;
      /deep/.hg-pic-check {
        display: inline-block;
        border-color: $hg-main-blue $hg-main-blue transparent transparent;
  
        .el-icon-check {
          color:$hg-label;
        }
      }
    }
  }

  .img-checkbox-item {
    margin-top: 0 !important;
  }
}
</style>