import { hasOwn } from './other'

/**
 * 解析一个对象多层的键值
 * @param obj: 解析的对象[Object]
 * @param expression: 表达式[String]
 */
export function parsePath(obj, expression) {
  if (hasOwn(obj, expression)) {
    return obj[expression]
  }

  const exps = expression.split('.')
  for (let i = 0, len = exps.length; i < len; i++) {
    if (!obj) {
      return
    }
    obj = obj[exps[i]]
  }
  return obj
}

export function setPathValue(obj, expression, value) {
  const exps = expression.split('.')
  // eslint-disable-next-line
  for (var i = 0, len = exps.length - 1; i < len; i++) {
    if (!obj) {
      return
    }
    obj = obj[exps[i]]
  }
  obj[exps[i]] = value
}

