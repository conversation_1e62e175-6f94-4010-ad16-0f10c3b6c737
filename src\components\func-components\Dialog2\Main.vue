<template>
  <div class="dialog-wrapper" v-if="visible">
    <div class="dialog">
      <div class="dialog-header">
        <span>{{ title }}</span>
      </div>
      <div class="dialog-body">
        {{ text }}
      </div>
      <div class="btn-group">
        <button class="cancel" @click="cancel" v-if="btnCancel">取消</button>
        <button class="confirm" @click="confirm">确定</button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "Main",
  data() {
    return {
      title: "系统提示",
      text: "",
      btnCancel: false,
      onConfirm: null,
      onCancel: null,
      closed: false,
      visible: false,
      type: "show",
    };
  },
  created() {
    if (this.type === "switch") {
      this.btnCancel = true;
    }
  },
  watch: {
    closed(newVal) {
      if (newVal) {
        this.visible = false;
        this.$destroy(true);
        this.$el.parentNode.removeChild(this.$el);
      }
    },
  },
  methods: {
    confirm() {
      this.closed = true;
      if (typeof this.onConfirm === "function") {
        this.onConfirm(this);
      }
    },
    cancel() {
      this.closed = true;
      if (typeof this.onCancel === "function") {
        this.onCancel(this);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog-wrapper {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba($color: $hg-background-color, $alpha: 0.8);
  text-align: center;
  &::after {
    content: "";
    width: 0px;
    height: 100%;
    display: inline-block;
    vertical-align: middle;
  }
  .dialog {
    display: inline-block;
    vertical-align: middle;
    background-color: $hg-main-black;
    width: 560px;
    .dialog-header {
      height: 60px;
      line-height: 60px;
      color: $hg-main-blue;
      font-size: $hg-medium-fontsize;
      font-weight: bold;
      border-bottom: 1px solid $hg-border-color;
      box-sizing: border-box;
      text-align: center;
      position: relative;
      .close-btn {
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        i {
          color: $hg-primary-fontcolor;
          font-size: 18px;
        }
      }
    }
    .dialog-body {
      font-size: $hg-normal-fontsize;
      box-sizing: border-box;
      padding: 24px 24px 28px 24px;
      min-height: 60px;
      line-height: 22px;
      color: $hg-secondary-fontcolor;
      overflow-wrap: break-word;
      text-align: left;
    }
    .btn-group {
      padding: 24px;
      text-align: right;
      button {
        box-sizing: border-box;
        font-size: 14px;
        width: 76px;
        height: 40px;
        border-radius: 4px;
        line-height: 40px;
        text-align: center;
        border: 1px solid $hg-main-blue;
        cursor: pointer;
        transition: all 0.3s ease-in-out;
        box-sizing: border-box;
        outline: none;
      }
      .confirm {
        background: $hg-main-blue;
        color: $hg-primary-fontcolor;
        margin-right: 24px;
      }
      .cancel {
        color: $hg-primary-fontcolor;
        margin-right: 24px;
        border: 1px solid $hg-border-color;
        background-color: $hg-main-black;
      }
    }
  }
}
</style>
