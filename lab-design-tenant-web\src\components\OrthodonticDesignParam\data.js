import { toothInfo } from './data.json';


export const initIPR = () => {
  const { upper, lower } = toothInfo;
  const upperList = upper.map((item, index) => {
    const { sort, number } = item;
    return {
      sort,
      number,
      nextNumber: upper[index+1] ? upper[index + 1].number: '',
      step: '',
      cutValue:'',
    }
  });
  const lowerList = lower.map((item, index) => {
    const { sort, number } = item;
    return {
      sort,
      number,
      nextNumber: lower[index+1] ? lower[index + 1].number: '',
      step: '',
      cutValue: '',
    }
  });
  return {
    upper: upperList,
    lower: lowerList
  }
};


export const initAddData = () => {
  const { upper, lower } = toothInfo;
  const upperList = upper.map((item,index) => {
    const { sort, number } = item;
    return {
      sort,
      number,
      steps: [],
    }
  });
  const lowerList = lower.map((item,index) => {
    const { sort, number } = item;
    return {
      sort,
      number,
      steps: [],
    }
  });
  return {
    upper: upperList,
    lower: lowerList,
  }
};