<template>
  <el-input
    type="number"
    title=""
    class="hg-input-number"
    :disabled="isDisable"
    :placeholder="placeholder"
    v-model="data.value"
    @change="onChange">
    <span slot="suffix">{{ data.unit }}</span>
  </el-input>
</template>

<script>
import { SELECT_GROUP } from '../utils/constant';

export default {
  data(){
    return {
      defaultValue: 0,
    }
  },
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          max: 3,
          min: 0,
        }
      }
    },
    parentItem: {
      type: Object,
      default() {
        return null;
      }
    },
    disabled: <PERSON>olean,
  },
  computed: {
    isDisable() {
      if(this.parentItem && this.parentItem.value === SELECT_GROUP.NO){
        return true;
      }
      return this.disabled;
    },

    placeholder() {
      const data = this.data
      if (data.min && data.max) {
        return `${data.min}~${data.max}`;
      } else {
        return '';
      }
    }
  },
  watch: {
    parentItem: {
      deep: true,
      handler(item) {
        if(item) {
          if(item.value === SELECT_GROUP.NO) {
            this.$emit('update', '');
          }else {
            this.$emit('update', this.defaultValue);
          }
        }
      }
    }
  },
  mounted(){
    this.defaultValue = this.data.value;
  },
  methods: {
    onChange(value){
      const { max: maxValue, min: minValue } = this.data;
      const compareValue = Number(value);
      let updateValue = value;

      if((maxValue || maxValue === 0 ) && compareValue > maxValue) {
        updateValue = String(maxValue);
      }else if ((minValue || minValue === 0) && compareValue < minValue) {
        updateValue = String(minValue);
      }

      if(value === '') {
        this.$emit('update', this.defaultValue);
      }else {
        this.$emit('update', updateValue);
      }

    },
  }
}
</script>

<style lang="scss">
.hg-input-number {
  max-width: 194px;

  .el-input__inner {
    height: 32px;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type='number'] {
    -moz-appearance: textfield;
    box-shadow: none; // 兼容firefox[火狐下会出现红色阴影]
    border: 1px solid $hg-border;
    border-radius: 2px;
    color: $hg-label;
  }
  .el-input__suffix {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 52px;
    color: $hg-disable;
    border-left: 1px solid $hg-border;
  }
}
.hg-input-number.is-disabled {
  color: $hg-disable;
  input[type='number'] {
    background-color: rgba(84, 86, 92, 0.25);
    border: 1px solid $hg-border;
  }
}
</style>