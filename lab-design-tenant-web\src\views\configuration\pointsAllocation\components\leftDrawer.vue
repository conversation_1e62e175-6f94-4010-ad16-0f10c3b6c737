<template>
  <div>
    <el-drawer
      :title="lang('exportPoint')"
      custom-class="designpoints-drawer"
      :visible.sync="designpointsDrawer"
    >
      <div v-show="!isImportExcel" class="point-upload-box" v-loading="uploadLoading">
        <el-upload
          v-if="designpointsDrawer"
          id="repointUpload"
          class="upload-demo"
          drag
          ref="repointUpload"
          action="#"
          :accept="'.xlsx'"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="uploadChange"
          :key="uploadKey"
        >
          <span class="upload-icon">+</span>
          <div class="el-upload__text">
            <p>{{lang('pointImport')}}</p>
            <p style="margin-top: 6px;">({{lang('limitTips')}})</p>
          </div>
        </el-upload>
        <div class="download-btn" @click="downSystemBill">
          <hg-icon icon-name="icon-download-default-lab"></hg-icon
          ><span class="btn-text">{{lang('pointdown')}}</span>
        </div>
      </div>
      <div class="table-list">
        <div class="import-title" v-if="isImportExcel">
          <div v-if="language == 'zh'">共计{{ importTableList.length }}个，导入<span class="success">成功{{ successList.length }}</span>个，<span class="error">失败{{ errorList.length }}</span>个</div>
          <div v-else>{{ importTableList.length }} in total:<span class="success">{{ successList.length }}imported, </span>,<span class="error">{{ errorList.length }} failed.</span></div>
        </div>
        <!-- 列表 -->
        <div class="depart-table" v-if="isImportExcel">
          <hg-table :header-data="headerData" class="user-table" :loading="tableLoading" :data="newMountedList">
            <template #designName="scope">
              <!-- {{scope.row.chargeUnit}} -->
              <span>{{ language == "zh" ? scope.row.designName : scope.row.designEnName }}</span>
            </template>
            <!-- 点数的值 -->
            <template #points="scope">
              <div class="points">
                <span>{{ scope.row.price || "--" }}</span>
              </div>
            </template>

            <template #result="scope">
              <span v-if="scope.row.result === '成功'">{{lang('Imported')}}</span>
              <span class="error-tips" v-else>{{lang('failed')}}</span>
            </template>
          </hg-table>
          <div class="depart-pagination" v-if="isImportExcel">
          <pagination showTotal :total="page.total" :isHavePageSize="false" :isHaveJump="false" :initPageIndex="page.pageNo" :initPageSize="page.pageSize" @onSearch="search"></pagination>
        </div>
        </div>
        <!-- 按钮组 -->
        <div class="btn-list" v-if="isImportExcel">
          <el-button plain @click="reloadUpload">{{lang('exportAgain')}}</el-button>
          <el-button :disabled="importTableList.length <= 0" type="primary" @click="submitPoint">{{lang('submitBtn')}}</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import hgTable from "@/components/HgTable";
import pagination from '@/components/Pagination';
import { mapGetters } from "vuex";
import { exportExcel, importExcel, exportSku } from "@/api/designPoints";
import { getDownloadUrl } from '@/api/file';
import { directDown, createIFrameDownLoad } from "@/public/utils/file.js";
import { server } from "@/config";
import { getLang } from '@/public/utils';
export default {
  name: "leftDrawer",
  components: {
    hgTable,
    pagination
  },
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      uploadFileList: [],
      uploadLoading: false,
      uploadKey: 0,
      isImportExcel: false,
      importTableList: [],
      errorList: [],
      successList: [],
      page: {
        pageSize: 20,
        pageNo: 1,
        total: 0,
      },
      tableLoading: false,
      newMountedList: []
    };
  },
  computed: {
    ...mapGetters(["language"]),
    designpointsDrawer: {
      get() {
        return this.drawer;
      },
      set(val) {
        this.$emit("update:drawer", val);
      },
    },
    headerData() {
      return [
        {
          prop: "rowNumber",
          width: "80px",
          noTip: false,
          getLabel: () => {
            return this.lang('number');
          },
        },
        {
          prop: "designName",
          minWidth: "30%",
          noTip: false,
          getLabel: () => {
            return this.lang('designCode');
          },
        },
        {
          prop: "sku",
          minWidth: "30%",
          noTip: false,
          getLabel: () => {
            return this.lang('skuCode');
          },
        },
        {
          prop: "points",
          minWidth: "20%",
          noTip: false,
          getLabel: () => {
            return this.lang('points');
          },
        },
        {
          prop: "result",
          minWidth: "30%",
          noTip: false,
          getLabel: () => {
            return this.lang('exportResult');
          },
        },
      ];
    },
  },
  watch: {
    designpointsDrawer(newValue, oldValue) {
      console.log(newValue)
      if(!newValue){
        if(this.importTableList.length > 0){
          this.$emit('submitPoint')
        }
        this.newMountedList = [];
        this.isImportExcel = false;
        this.importTableList = [];
        this.successList = [];
        this.errorList = [];
      }
    }
  },
  mounted () {
    // this.newMountedList = this.importTableList.slice(0, 20);
  },
  methods: {
    lang: getLang('designpoints'),
    async uploadChange(file) {
      this.newMountedList = []
      const regRex = /\.(xlsx)$/g;
      if (!regRex.test(file.name.toLowerCase())) {
        this.$message.error(this.lang("uploadError"));
        this.uploadKey++;
        return false;
      }
      const param = new FormData();
      param.append("uploadFile", file.raw);
      this.tableLoading = true;
      const { code, data } = await importExcel(param);
      if (code === 200) {
        // 获取成功和失败的总和
        this.importTableList = data;
        this.page.total = this.importTableList.length;
        this.page.pageNo = 1;
        this.page.pageSize = 20;
        this.newMountedList = this.importTableList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize)
        this.errorList = data.filter((item) => {
          return item.result === "失败";
        });
        this.successList = data.filter((item) => {
          return item.result === "成功";
        });
        this.isImportExcel = true;
        this.tableLoading = false;
        console.log(data, 33333);
      } else {
        this.isImportExcel = false;
        this.tableLoading = false;
        return;
      }
      this.uploadFileList = [];
    },
    // 重新导入
    reloadUpload(){
      this.$refs.repointUpload.$children[0].handleClick()
    },
    // 校验文件
    verifyFile(file, fileList) {
      // if(this.acceptType !== '.xlsx'){
      const regRex = /\.(xlsx)$/g;
      if (!regRex.test(file.name.toLowerCase())) {
        this.$hgOperateFail(this.lang("uploadError"));
        return false;
      }
      // }
      return true;
    },
    // 下载系统账单
    async downSystemBill() {
      // let url = `${server.designSet}/export`;
      // directDown(url, `${this.lang('dental')}.xlsx`);
      this.$message.success(this.lang('downLoadWait'));
      const { code, data } = await exportSku();
      if(code == 200){
        const param = {
          s3FileId: data,
          filename: `${this.lang('dental')}`,
        };
        getDownloadUrl(param).then(res => {
          if(res.code === 200) {
            createIFrameDownLoad(res.data.url);
          }else {
            this.$hgOperateFail(this.$t('http.error.80080003'));
          }
        }).catch(err => {
          console.log('error:',err);
        });
      }
    },
    // 列表跳下一页
    search(type, searchData){
      this.newMountedList = [];
      if(searchData) {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
        this.newMountedList = this.importTableList.slice((pageIndex - 1) * pageSize, pageIndex * pageSize)
      }
    },
    // 确认按钮
    submitPoint(){
      this.$emit('submitPoint')
      this.newMountedList = [];
      this.isImportExcel = false;
      this.importTableList = [];
      this.successList = [];
      this.errorList = [];
    }
  },
};
</script>

<style lang="scss" scoped>
/deep/.designpoints-drawer {
  width: 800px !important;
  background-color: $hg-main-black;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
    color: $hg-label;
  }
  .el-drawer__body {
    padding: 24px;
    overflow: hidden;
  }
  .point-upload-box {
    width: 100%;
    height: 656px;
    background: $hg-hover;
    padding: 20px;
  }
  .el-upload {
    width: 100%;
  }
  .el-upload-dragger {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: $hg-hover;
    width: 100%;
    height: 566px;
    border-color: $hg-main-border;
    .upload-icon {
      font-size: 50px;
    }
  }
  .download-btn {
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    align-items: center;
    color: $hg-main-blue;
    margin-top: 20px;
    cursor: pointer;
  }
  .btn-text {
    vertical-align: top;
    margin-left: 8px;
  }
  .table-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 60px);
    .import-title {
      line-height: 40px;
      .success {
        color: #00b860;
        margin: 0 8px;
      }
      .error {
        color: #e55353;
        margin: 0 8px;
      }
    }
    .depart-table {
      position: relative;
      // flex: 1;
      height: calc(100% - 20px);
      width: 100%;
      .hg-table {
        height: 100%;
        // height: calc(100% - 30px);
        overflow: hidden;
        .el-table {
          // height: calc(100% - 60px)!important;
          // max-height: 100% !important;
        }
      }
      .table-high-light {
        float: left;
        width: auto;
        max-width: calc(100% - 41px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .expedited-time {
        line-height: 40px;
      }
      .error-tips{
        color: #E55353;
      }
    }
    .depart-pagination {
      z-index: 1;
      position: absolute;
      bottom: 0;
      right: 0;
      height: 60px;
      width: 100%;
    }
    .btn-list{
      position: absolute;
      bottom: 24px;
      right: 10px;
      display: flex;
      justify-content: flex-end;
      width: 100%;
      height: 50px;
      padding: 10px 24px 0 0;
      border-top: 1px solid $hg-main-border;
    }
  }
  .el-button.is-plain:focus{
    background: transparent;
  }
  .el-button--primary:focus{
    background: #3760EA;
    border-color: #3760EA;
  }
}
</style>
