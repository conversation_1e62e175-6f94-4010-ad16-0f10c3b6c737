<template>
  <div>
    <el-dialog :title="$t('userList.list.setRang')" :visible.sync="setRangeDialog" width="750px" custom-class="range-dialog">
      <div class="range-content" v-if="setRangeDialog">
        <el-collapse v-model="activeNames" @change="showRange">
          <el-collapse-item :name="item.code" v-for="(item, index) in rangeList" :key="item.code">
            <template slot="title">
              <div>
                {{$t(`apiCommon.${item.code}`)}}
                <!-- {{ language == 'zh' ? item.zhName : item.enName }} -->
                <span :class="['normal-btn', item.isAllCheck ? 'select-btn' : 'hover-btn']" @click.stop="selectAllRange(item,index)">{{$t('userList.list.selectAll')}}</span>
                <!-- <el-checkbox v-model="item.isAllCheck" @change="selectAllRange(item)" style="position: absolute;right: -54px;top: -1px;"></el-checkbox> -->
              </div>
            </template>
            <div class="range-child-list">
              <div
                :class="['range-child', nowSelectArr.indexOf(list.code) != -1 ? 'now-select' : '', (idx + 1) % 4 !== 0 ? 'range-child-four' : '']"
                v-for="(list, idx) in item.thirdTierTypeDtos"
                :key="idx"
                @click="selectRang(item, list, index)"
              >
                <div class="range-img-box">
                  <img class="img" :src="require(`@/assets/images/user/${getImagePath(list.imgUrl, list.code)}`)" />
                  <label
                    :class="['el-upload-list__item-status-label', !(nowSelectArr.indexOf(list.code) != -1) ? 'no-select-status' : '']"
                    :style="nowSelectArr.indexOf(list.code) != -1 ? 'display: block' : ''"
                  >
                    <i class="el-icon-upload-success el-icon-check"></i>
                  </label>
                </div>
                <div class="range-title">{{$t(`apiCommon.${list.code}`)}}</div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="setRangeDialog = false">{{$t('common.cancel')}}</el-button>
        <el-button type="primary" @click="submitRang">{{$t('common.ok')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getRangList } from '@/api/user';
import { mapGetters } from 'vuex';
export default {
  name: 'setRangeDialog',
  props: {},
  data() {
    return {
      setRangeDialog: false,
      activeNames: ['1'],
      nowSelectArr: [],
      rangeList: [],
      nowSelectRang: [], //写入选中的item，index和nowSelectArr的index一样，方便删除
    };
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    setRangeDialog(val) {
      if (val) {
        this.initAllRange();
      }
    },
  },
  mounted() {
    this.getRangList();
  },
  methods: {
    // 获取范围
    getRangList() {
      getRangList().then((res) => {
        if (res.code === 200) {
          this.rangeList = res.data.firstTierTypeDtos ? res.data.firstTierTypeDtos : [];
          this.initAllRange();
        }
      });
    },
    initAllRange() {
      this.activeNames = [];
      this.nowSelectArr = [];
      this.nowSelectRang = [];
      this.rangeList.forEach((item) => {
        item.isAllCheck = false;
        this.activeNames.push(item.code);
      });
    },
    showRange(val) {},
    // 选择范围
    selectRang(item, child, index) {
      if (this.nowSelectArr.indexOf(child.code) != -1) {
        let deleteIndex = this.nowSelectArr.indexOf(child.code);
        this.nowSelectArr.splice(deleteIndex, 1);
        this.nowSelectRang.splice(deleteIndex, 1);
      } else {
        this.nowSelectArr.push(child.code);
        // 需要将一级类和三级拼成一个对象存数组
        let obj = { firstTierDesignCode: item.code, thirdTierDesignCode: child.code };
        this.nowSelectRang.push(obj);
      }
      //判断当前大类是否全选
      if(this.isContained(item.thirdTierTypeDtos)){
        this.$set(this.rangeList[index],'isAllCheck',true);
      } else{
        this.$set(this.rangeList[index],'isAllCheck',false);
      }
    },
    isContained (thirdTierTypeDtos){
      let len = thirdTierTypeDtos.length;
      for (let i = 0; i < len; i++) {
        // 遍历b中的元素，遇到没有包含某个元素的，直接返回false
        if (!this.nowSelectArr.includes(thirdTierTypeDtos[i].code)) return false;
      }
      // 遍历结束，返回true
      return true;
    },
    //全选范围
    selectAllRange(item, index) {
      if (!item.isAllCheck) {
        this.$set(this.rangeList[index],'isAllCheck',true);
        item.thirdTierTypeDtos.forEach((it) => {
          if (
            !this.nowSelectArr.find((now) => {
              return now == it.code;
            })
          ) {
            this.nowSelectArr.push(it.code);
            let obj = { firstTierDesignCode: item.code, thirdTierDesignCode: it.code };
            this.nowSelectRang.push(obj);
          }
        });
      } else {
        this.$set(this.rangeList[index],'isAllCheck',false);
        item.thirdTierTypeDtos.forEach((it) => {
          let deleteIndex = this.nowSelectArr.indexOf(it.code);
          this.nowSelectArr.splice(deleteIndex, 1);
          this.nowSelectRang.splice(deleteIndex, 1);
        });
      }
    },
    // 确定提交负责范围
    submitRang() {
      this.$emit('setRangeList', this.nowSelectRang);
    },
    getImagePath(iconUrl, code) {
      let iconName = 'icon-temp.png';
      if(iconUrl) {
        iconName = `icon-${iconUrl}.svg`;
      }else if(code) { // 用这里是为了不受后端配置限制，目前配置的imgUrl只有本页面用到，用处不大
        switch(code) { 
          case 21404: iconName = 'icon-contactModel.svg'; break;
          default:break;
        }
      }
      return iconName;
    },
  },
};
</script>

<style scoped lang="scss">
.range-dialog {
  .normal-btn{
    position: absolute;
    right: 30px;
    top:6px;
    padding: 2px 10px;
    line-height: 20px;
    border: 1px solid #2D2F33;
    color: #E4E8F7;
    border-radius: 3px;
  }
  .hover-btn{
    &:hover{
      border: 1px solid $hg-main-blue;
      color: $hg-main-blue;
    }
  }
  .select-btn{
    background: $hg-main-blue;
    border: 1px solid $hg-main-blue;
    color: #E4E8F7;
  }
  .range-content {
    width: 725px;
    height: 480px;
    overflow: auto;
    /deep/.el-collapse-item__header {
      color: $hg-main-blue;
      position: relative;
    }
    .range-child-list {
      display: flex;
      flex-wrap: wrap;
      .range-child {
        width: 160px;
        height: 160px;
        border-radius: 4px;
        border: 1px solid #2d2f33;
        padding: 8px;
        position: relative;
        overflow: hidden;
        .range-img-box {
          width: 144px;
          height: 116px;
          padding: 18px 36px;
          .el-upload-list__item-status-label {
            display: none;
            position: absolute;
            right: -17px;
            top: -7px;
            width: 46px;
            height: 26px;
            background: $hg-main-blue;
            text-align: center;
            transform: rotate(45deg);
            box-shadow: 0 1px 1px #ccc;
            i {
              font-size: 12px;
              margin-top: 12px;
              transform: rotate(-45deg);
            }
            .el-icon-check {
              color: #fff;
            }
          }
        }
        .img {
          display: inline-block;
          width: 80px;
          height: 80px;
        }
        .range-title {
          text-align: center;
          line-height: 36px;
          color: #fff;
        }
      }
      .now-select {
        border: 1px solid $hg-main-blue;
      }
      .range-child-four {
        margin-right: 16px;
        margin-bottom: 16px;
      }
      .range-child:hover {
        .range-img-box {
          background: #2d2f33;
        }
        .no-select-status {
          display: block;
          background: #2d2f33;
        }
      }
    }
  }
}
</style>
