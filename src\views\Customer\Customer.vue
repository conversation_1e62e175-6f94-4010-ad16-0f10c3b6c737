<template>
  <div class="customer-box">
    <div class="customer-main-content">
      <plane>
        <div class="customer-data-display">
          <div class="customer-header">
            <div class="customer-search-box">
              <Search
                :placeholder="$t('customer.searchPlaceholder')"
                :search-icons="{
                  add: false,
                  refresh: false,
                }"
                size="medium"
                :input-content="searchObj.searchKey"
                @enterInput="searchCustomer"
              />
            </div>
            <div class="select-box">
              <Select
                :select-options="statusArr"
                :value="searchObj.selectedStatus"
                @change="changeStatus"
              />
            </div>
            <div class="select-box-crm">
              <span>{{ $t('customer.syncByCRM') }}</span>
              <Select
                :select-options="crmStatus"
                :value="searchObj.crmStatus"
                @change="changeCrmStatus"
              />
            </div>
            <div class="select-box-crm">
              <span>{{$t('customer.serveice')}}</span>
              <Select
                :select-options="usenDesign"
                :value="searchObj.usenDesignFilter"
                @change="changeUsenDesign"
              />
            </div>
            <!-- <div class="select-box-crm">
              <span>{{$t('customer.customType')}}</span>
              <Select
                :select-options="customTypeList"
                :value="searchObj.customType"
                @change="changeCustomType"
              />
            </div> -->
            <div class="icon-box">
              <Tooltip :content="$t('customer.refresh')">
                <i v-btn-control class="iconfont icon-refresh" @click="getCustomerListFunc('fresh')" />
              </Tooltip>
              <span class="line" />
              <!-- <VueButton v-permission="['addCustomer', 'disabled']" type="primary" sizes="big" :width="110" :disabled="false" @click.native="openAddBox">{{ $t('customer.addCustomerBtn') }}</VueButton> -->
              <el-button v-permission="['addCustomer', 'disabled']" type="primary"  @click.native="openAddBox">{{ $t('customer.addCustomerBtn') }}</el-button>
              <el-button type="primary" v-permission="['exportCustomers', 'delete']" @click="exportCustomer">{{$t('customer.exportClient')}}</el-button>
            </div>
          </div>
          <new-table class="customer-table" :data="tableData.data" :loading="tableLoading" :header-data="headerData" :needSelect="true" @update-selected-rows="selectTable" v-bind="$attrs">
            <template #orgSn="{ row }">
              <span :title="row.orgSn">{{ row.orgSn }}</span>
              <span v-if="row.isNew" class="new-flag">NEW</span>
            </template>

            <template #orgName="{ row }">
              <span :title="row.orgName">{{ row.orgName }}</span>
            </template>
            <template #customLevel="{ row }">
              <span
                :title="row.customLevel === 0 ? $t('customer.normal') :
                  (row.customLevel === 1 ? 'VIP' : (row.customLevel === 2 ? 'VVIP' : '--'))"
              >{{ row.customLevel === 0 ? $t('customer.normal') : (row.customLevel === 1 ? 'VIP' : (row.customLevel === 2 ? 'VVIP' : '--')) }}</span>
            </template>
            <template #customType="{ row }">
              <span v-if="row.customType === 0" :title="$t('customer.undirectSales')">{{$t('customer.undirectSales')}}</span>
              <span v-else-if="row.customType === 1" :title="$t('customer.directSales')">{{$t('customer.directSales')}}</span>
              <span v-else-if="row.customType === 2" :title="$t('customer.testUser')">{{$t('customer.testUser')}}</span>
              <span v-else-if="row.customType === 3" :title="$t('customer.disaster')">{{$t('customer.disaster')}}</span>
              <span v-else-if="row.customType === 4" :title="$t('customer.agencyuser')">{{$t('customer.agencyuser')}}</span>
            </template>
            <template #createdTime="{ row }">
              <span :title="row.createdTime || '--'">{{ row.createdTime || '--' }}</span>
            </template>
            <template #status="{ row }">
              <span :class="[row.status === 0 ? 'wait-examine' : (row.status === 1 ? 'opened' : (row.status === 2 ? 'forbidden' : (row.status === -1 ? 'unpass' : '')))]" :title="row.status === 0 ? $t('customer.approvalPending') : (row.status === 1 ? $t('customer.opened') : (row.status === 2 ? $t('customer.disable') : (row.status === -1 ? $t('customer.statusUnpass') : '--')))">{{ row.status === 0 ? $t('customer.approvalPending') : (row.status === 1 ? $t('customer.opened') : (row.status === 2 ? $t('customer.disable') : (row.status === -1 ? $t('customer.statusUnpass') : '--'))) }}</span>
            </template>
            <template #usedDesign="{ row }">
              <span v-if="row.usedDesign == 1">{{$t('customer.consume')}}</span>
              <span v-else>{{$t('customer.noConsume')}}</span>
            </template>
            <template #crmStatus="{ row }">
              <span :class="[row.crmStatus === 1 ? 'opened': 'forbidden']">
                {{ row.crmStatus === 1 ? $t('customer.syncSuccess') : $t('customer.unSync') }}
              </span>
            </template>
            <template #memorySn="{ row }">
              <span>
                {{ row.memorySn ? row.memorySn : '--' }}
              </span>
            </template>
            <template #edit="{ row }">
              <i class="iconfont icon-edit iconfont-24" :title="''" @click="openEditBox(row)" />
            </template>
            <template #setting="{ row }">
              <i class="iconfont icon_configuration iconfont-24" :title="''" @click="openSettingBox(row)" />
            </template>
            <template #org="{ row }">
              <i class="iconfont icon_organization iconfont-24" :title="''" @click="openOrgBox(row)" />
            </template>
          </new-table>
        </div>
      </plane>
      <Pagination :total-pages="totalPages" :total="totalUsers" :page-size="pageSize" :pageSizes="[10, 20, 50, 100]" :page-no="pageNo" @changePageSize="changePageSize" @changePageNo="changePageNo" />
    </div>
    <AddCustomer ref="child" :show.sync="showAddCustomer" :popup-title="popupTitle" :area-code-arr="areaCodeArr" @submit="addCustomerFunc" />
    <EditCustomer ref="editCustomer" :show.sync="showEditCustomer" :saveBtn="isHaveSaveBtn" :edit-customer="editCustomerObj" :area-code-arr="areaCodeArr" @submit="editCustomerFunc" />
    <router-view />
  </div>
</template>

<script>
import Plane from '@/components/func-components/Plane.vue'
import Search from '@/components/func-components/Search.vue'
import Table from '@/components/func-components/TableCommon.vue'
import newTable from '@/components/func-components/newTable.vue'
import Select from '@/components/func-components/Select'
import Pagination from '@/components/func-components/Pagination'
import Tooltip from '@/components/func-components/Tooltip'
import EditCustomer from './EditCustomer'
import AddCustomer from './AddCustomer'
import { getCustomerList, addCustomer, getCustomerInfo, editCustomer, exportCustomer } from '@/api/customer'
import { formatDate } from '@/assets/script/formatDate.js'
import { getAreaCodeList } from '@/api/organization'
import md5 from 'js-md5'
import { mapActions, mapMutations, mapState } from 'vuex'
import { getStore } from '@/assets/script/storage.js'
import { directDown, directDown2 } from './file.js'

export default {
  name: 'Customer',
  components: {
    Plane,
    Search,
    Table,
    newTable,
    Select,
    Pagination,
    Tooltip,
    AddCustomer,
    EditCustomer
  },
  data() {
    return {
      // 人员列表
      tableKeys: ['orgSn','orgName', 'customLevel', 'customType', 'createdTime', 'status', 'crmStatus', 'edit', 'setting', 'org'],
      tableLoading: false,
      headerData: [
        {
          prop: 'orgSn',
          minWidth: '20%',
          getLabel: () => {
            return this.$t('customer.orgSn');
          },
        },
        {
          prop: 'orgName',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.orgName');
          },
        },
        {
          prop: 'customLevel',
          minWidth: '25%',
          getLabel: () => {
            return this.$t('customer.customLevel');
          },
        },
        {
          prop: 'customType',
          minWidth: '25%',
          getLabel: () => {
            return this.$t('customer.customType');
          },
        },
        {
          prop: 'memorySn',
          minWidth: '25%',
          getLabel: () => {
            return this.$t('customer.memorySn');
          },
        },
        {
          prop: 'createdTime',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.createdTime');
          },
        },
        {
          prop: 'status',
          minWidth: '25%',
          getLabel: () => {
            return this.$t('customer.status');
          },
        },
        {
          prop: 'usedDesign',
          minWidth: '25%',
          getLabel: () => {
            return this.$t('customer.serveice');
          },
        },
        {
          prop: 'crmStatus',
          minWidth: '25%',
          getLabel: () => {
            return this.$t('customer.syncByCRM');
          },
        },
        {
          prop: 'edit',
          minWidth: '20%',
          getLabel: () => {
            return this.$t('customer.edit');
          },
        },
        {
          prop: 'setting',
          minWidth: '20%',
          getLabel: () => {
            return this.$t('customer.processSetting');
          },
        },
        {
          prop: 'org',
          minWidth: '20%',
          getLabel: () => {
            return this.$t('customer.customerOrg');
          },
        }
      ],
      tableData: {
        title: [
          this.$t('customer.orgSn'),
          this.$t('customer.orgName'),
          this.$t('customer.customLevel'),
          this.$t('customer.customType'),
          this.$t('customer.createdTime'),
          this.$t('customer.status'),
          this.$t('customer.syncByCRM'),
          this.$t('customer.edit'),
          this.$t('customer.processSetting'),
          this.$t('customer.customerOrg')
        ],
        data: []
      },
      totalUsers: 10, // 某组织下的所有人员数
      totalPages: 1, // 总页数
      pageSize: 10, // 默认每页10条
      pageNo: 1, // 默认第一页
      roleArr: [], // 所有角色列表（角色需要区分租户端角色还是用户端角色）

      // 搜索
      searchObj: {
        searchKey: '', // 搜索的关键字
        selectedStatus: null, // 选中的状态id
        crmStatus: null,
        usenDesignFilter: null,
        customType: null
      },
      statusArr: [
        {
          value: null,
          label: this.$t('customer.allStatus')
        },
        {
          value: -1,
          label: this.$t('customer.statusUnpass')
        },
        {
          value: 0,
          label: this.$t('customer.approvalPending')
        },
        {
          value: 1,
          label: this.$t('customer.opened')
        },
        {
          value: 2,
          label: this.$t('customer.disable')
        }
      ],
      // 客户类型列表
      customTypeList: [
        {
          value: null,
          label: this.$t('customer.allCRMStatus')
        },
        {
          value: 0,
          label: this.$t('customer.undirectSales')
        },
        {
          value: 1,
          label: this.$t('customer.directSales')
        },
        {
          value: 2,
          label: this.$t('customer.testUser')
        },
        {
          value: 3,
          label: this.$t('customer.disaster')
        },
        {
          value: 4,
          label: this.$t('customer.agencyuser')
        }
      ],

      // 添加客户
      showAddCustomer: false, // 打开添加客户弹窗
      popupTitle: '',
      addCustomerObj: {}, // 添加的客户信息
      areaCodeArr: [], // 区号列表

      // 编辑客户
      showEditCustomer: false,
      isHaveSaveBtn: true,
      editCustomerObj: {},

      // 功能配置
      showSetting: false,
      // customerObj: {},

      // 客户组织
      showCustomOrg: false,
      curCustomerObj: {},
      customerPartPathList: ['/setProcess', '/customOrg'], //客户管理模块的子路由

      crmStatus: [
        {
          value: null,
          label: this.$t('customer.allCRMStatus')
        },
        {
          value: 1,
          label: this.$t('customer.syncSuccess')
        },
        {
          value: 0,
          label: this.$t('customer.unSync')
        }
      ],
      usenDesign: [
        {
          value: null,
          label: this.$t('customer.allCRMStatus')
        },
        {
          value: 0,
          label: this.$t('customer.noConsume')
        },
        {
          value: 1,
          label: this.$t('customer.consume')
        }
      ],
      selectSelection: []
    }
  },
  computed: {
    curTimezoneName() {
      return getStore('userInfo').timezone.tzName
    },
    ...mapState({
      // customerObj: state => state.customer.customerObj,
      searchData: state => state.customer.searchData,
      fromPath: state => state.customer.lastPath,
    })
  },
  watch: {
    showEditCustomer(val) {
      if (!val) {
        this.getCustomerListFunc()
      }
    },
  },

  beforeRouteUpdate(to, from, next) {
    this.updateLastPath(from.path);
    if(this.customerPartPathList.includes(to.path)) { // 去子节点要在这里记录
      const searchData = Object.assign({}, this.searchObj, { pageSize: this.pageSize, pageNo: this.pageNo });
      this.updateSearchData(searchData);
    }
    next();
  },
  // 离开时-只有去非/customer模块的才会调用这里
  beforeRouteLeave(to, from, next) {
    if(!['/customer',...this.customerPartPathList].includes(to.path)) {
      this.updateSearchData(null);
      this.updateLastPath('');
    }
    next();
  },
  mounted() {
    if(this.customerPartPathList.includes(this.fromPath) && this.searchData) { // 来自子节点且有搜索记录
      // console.log('mounted from 子节点');
      const { searchKey, selectedStatus, pageSize, pageNo, crmStatus } = this.searchData;
      this.searchObj.searchKey = searchKey;
      this.searchObj.selectedStatus = selectedStatus;
      this.searchObj.crmStatus = crmStatus;
      this.pageSize = pageSize;
      this.pageNo = pageNo;
    }

    if(this.$route.path === '/customer') { // 去子节点时不要加载父节点的数据
      if(this.$route.query.orgCode) {
        this.searchObj.searchKey = this.$route.query.orgCode
      }
      Promise.allSettled([this.getCustomerListFunc(), this.getAreaCodeFunc()])
    }
  },

  methods: {
    ...mapMutations({
      updateCustomerObj: 'customer/updateCustomerObj'
    }),
    ...mapActions({
      updateSearchData: 'customer/updateSearchData',
      updateLastPath: 'customer/updateLastPath',
    }),
    changePageSize(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getCustomerListFunc()
    },
    changePageNo(val) {
      this.pageNo = val
      this.getCustomerListFunc()
    },
    // 获取区号列表
    getAreaCodeFunc() {
      return new Promise((resolve) => {
        getAreaCodeList({
          'orderBy': this.$i18n.locale === 'zh' ? 0 : 1
        }).then((res) => {
          if (res.code === 200) {
            this.areaCodeArr = res.data
            resolve()
          }
        })
      })
    },
    // 选中
    selectTable(length, selection){
      this.selectSelection = selection;
    },
    // 导出客户
    async exportCustomer(){
      // 当列表没有选中时需要拒绝
      if (this.selectSelection && this.selectSelection.length == 0) {
        this.$message({
          type: 'error',
          message: this.$t('customer.pleaseSelect'),
        });
        return;
      }
      let arr = [];
      this.$message({
          type: 'success',
          message: this.$t('customer.exporting'),
      });
      this.selectSelection.forEach((item) => {
        arr.push(item.orgCode);
      })
      try {
        const {code, data} = await exportCustomer({orgCodes: arr});
        if(code == 200){
          directDown2(data, `${this.$t('customer.tableName')}.xlsx`)
        }
      } catch (error) {
        this.$message.error(this.$t('customer.exportError'))
      }
    },
    // 获取组织人员列表
    getCustomerListFunc(fresh) {
      this.tableLoading = true;
      getCustomerList({
        'key': this.searchObj.searchKey,
        'status': this.searchObj.selectedStatus,
        'crmOrgFilter': this.searchObj.crmStatus,
        'usedDesignFilter': this.searchObj.usenDesignFilter,
        // 'customType': this.searchObj.customType,
        'pageNo': this.pageNo,
        'pageSize': this.pageSize
      }).then((res) => {
        if (res.code === 200) {
          this.totalUsers = res.data.totalSize
          this.totalPages = res.data.totalPages
          if (res.data.data != null && res.data.data.length) {
            this.tableData.data = res.data.data
            this.tableData.data.forEach((item) => {
              item.createdTime = formatDate(item.createdTime, 'YYYY.MM.DD HH:mm:ss', this.curTimezoneName)
              item.crmStatus = item.isBackupCrmOrg;
            })
          } else {
            this.tableData.data = []
          }
          if (fresh) {
            this.$MessageAlert({
              text: this.$t('customer.refreshSuccessTip'),
              type: 'success'
            })
          }
          this.tableLoading = false;
        }
      })
    },
    changeStatus(value) {
      this.searchObj.selectedStatus = value
      this.pageNo = 1
      this.getCustomerListFunc()
    },
    changeCrmStatus(value) {
      this.searchObj.crmStatus = value;
      this.pageNo = 1;
      this.getCustomerListFunc()
    },
    changeUsenDesign(value){
      this.searchObj.usenDesignFilter = value;
      this.pageNo = 1;
      this.getCustomerListFunc()
    },
    changeCustomType(value){
      this.searchObj.customType = value;
      this.pageNo = 1;
      this.getCustomerListFunc()
    },
    // 搜索客户
    searchCustomer(value) {
      this.searchObj.searchKey = value
      this.pageNo = 1
      this.getCustomerListFunc()
    },
    // 打开添加客户窗口
    openAddBox(){
      this.popupTitle = this.$t('customer.addCustomerBtn')
      this.showAddCustomer = true
    },
    addCustomerFunc(obj) {
      addCustomer(obj).then((res) => {
        if (res.code === 200) {
          // this.$emit('isLoading', false)
          this.$refs.child.loading = false
          this.getCustomerListFunc()
          this.showAddCustomer = false
          this.$MessageAlert({
            text: this.$t('customer.addCustomSuccessTip'),
            type: 'success'
          })
        }
      }).catch(() => {
        this.$MessageAlert({
          text: this.$t('customer.addCustomErrorTip'),
          type: 'error'
        })
        this.$refs.child.loading = false
      })
    },
    // 打开编辑客户窗口
    openEditBox(row) {
      this.getCustomerInfoFunc(row.orgCode, row.isBackupCrmOrg).then(() => {
        // isHaveSaveBtn
        this.showEditCustomer = true
      })
    },
    // 获取客户信息
    getCustomerInfoFunc(code, isSynchronized) {
      return new Promise((resolve) => {
        getCustomerInfo({
          'orgCode': code
        }).then((res) => {
          if (res.code === 200) {
            this.editCustomerObj = res.data
            this.editCustomerObj.isSynchronized = Boolean(isSynchronized);
            if(res.data.customType == 3){
              this.isHaveSaveBtn = false;
            } else {
              this.isHaveSaveBtn = true;
            }
            resolve()
          }
        })
      })
    },
    // 保存客户基础信息
    editCustomerFunc(obj, type, result) {
      editCustomer({
        'customLevel': obj.customLevel,
        'customType': obj.customType,
        'orgCode': obj.orgCode,
        'orgSn': obj.orgSn,
        'status': obj.status,
        'email': obj.email,
        'mobile': obj.contactNumber,
        'mobilePrefix': obj.mobilePrefix,
        'orgAddress': obj.orgAddress,
        'orgLeader': obj.orgLeader,
        'orgName': obj.orgName,
        'tzCode': obj.tzCode,
        // 'bucketCode': obj.bucketCode,
        'settlementType': obj.settlementType,
        'discountRate': obj.discountRate,
        'businessUserCodes': obj.businessUserCodes,
        'settlementCurrency': obj.settlementCurrency,
        'creditScore': obj.creditScore > ********* ? ******** : obj.creditScore,
        'isBackupCrmOrg': obj.isBackupCrmOrg,
        'accountCode': obj.accountCode,
        'headerCode': obj.headerCode,
        'areaCode': obj.areaCode.join(','),
        'memorySn': obj.memorySn
      }).then((res) => {
        if (res.code === 200 && type === 'save') { // 通过点击保存按钮的方式保存成功才需要提示成功
          this.$refs.editCustomer.editLoading = false
          this.$MessageAlert({
            text: this.$t('customer.editCustomSuccessTip'),
            type: 'success'
          })
        }
        if(res.code === 200 && result === 1) {
          this.$refs.editCustomer.ensureExamine(result)
        }
      }).catch(() => {
        this.$refs.editCustomer.editLoading = false
        this.$MessageAlert({
          text: this.$t('customer.editCustomErrorTip'),
          type: 'error'
        })
      })
    },
    // 打开功能配置窗口
    openSettingBox(row) {
      console.log('????');
      // let query = ''
      // this.$route.query.redirection ? query = this.$route.query.redirection : query = window.location.pathname + window.location.hash
      // this.$router.push({path: '/setProcess', query: { 'code': row.orgCode, 'redirection': query }})
      this.$router.push({ path: '/setProcess', query: { 'orgCode': row.orgCode, 'isBackupCrmOrg': row.isBackupCrmOrg }})
      row.isNew = 0
    },
    // 打开客户组织窗口
    openOrgBox(row) {
      // let query = ''
      // this.$route.query.redirection ? query = this.$route.query.redirection : query = window.location.pathname + window.location.hash
      // this.$router.push({ path: '/customOrg', query: { 'orgCode': row.orgCode, 'redirection': query }})
      this.$router.push({ path: '/customOrg', query: { 'orgCode': row.orgCode }})
      row.isNew = 0
    },
  }
}
</script>

<style lang="scss" scoped>
.customer-box {
  background-color: $hg-main-black;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  .customer-main-content {
    position: relative;
    flex: 1;
    height: 100%;
    overflow: hidden;
    padding-bottom: 58px;
    box-sizing: border-box;
    .customer-data-display {
      height: 100%;
      background-color: $hg-main-black;
      // overflow: hidden;
      .customer-header {
        display: flex;
        align-items: center;
        .customer-search-box {
          width: 246px;
          margin-right: 24px;
        }
        .select-box {
          width: 186px;
        }
        .select-box-crm {
          display: flex;
          align-items: center;
          padding-left: 24px;
          &>span {
            padding-right: 8px;
            color: $hg-primary-fontcolor;
          }
        }
        .icon-box {
          margin-left: auto;
          display: flex;
          align-items: center;
          span {
            height: $hg-height-40;
            line-height: $hg-height-40;
          }
          .line {
            width: 1px;
            background: $hg-disable-fontcolor;
            margin: 0 20px 0 28px;
            vertical-align: middle;
          }
          .add-btn {
            width: 110px;
            border-radius: 4px;
            background: $hg-main-blue;
            color: $hg-primary-fontcolor;
            text-align: center;
          }
        }
      }
      .customer-table {
        margin-top: 8px;
        max-height: calc(100vh - 120px);
        // overflow: auto;
        .wait-examine {
          color: $hg-warning-color;
        }
        .unpass {
          color: $hg-error-color;
        }
        .opened {
          color: $hg-main-blue;
        }
        .forbidden {
          color: $hg-secondary-fontcolor;
        }
        .new-flag {
          display: inline-block;
          width: 32px;
          height: 16px;
          text-align: center;
          border-radius: 2px;
          background: $hg-error-color;
          color: $hg-primary-fontcolor;
          font-size: 12px;
          margin-left: 12px;
        }
      }
      ::v-deep .el-table__body-wrapper{
        height: calc(100vh - 300px)!important;
      }
    }
  }
}
</style>
