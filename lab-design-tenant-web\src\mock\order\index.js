import Mock from 'mockjs';

export default {
  // 获取基本信息
  getOrderlist: () => {
    return {
      data: Mock.mock({
        count: 2352,
        list: [
          {
            order_code: '230178322117185536',
            order_no: '20030318014984681',
            org_name: '组织组织3',
            design_type_codes: '20055',
            files: {
              original_files: [
                {
                  file_path: 'test/Attachment/20220214/sqpmacmcl5f1672789222.zip',
                  file_name: '222.stl',
                  file_time: 0,
                  file_size: 0,
                  file_type: 0,
                  design_path: '',
                  scheme_code: 0,
                },
              ],
              design_files: [
                {
                  file_path: 'test/DesignFileNew/20220214/230178322117185536_1644802218936/222_1644802218936.zip',
                  file_name: '222.zip',
                  file_time: 0,
                  file_size: 0,
                  file_type: 0,
                  design_path: '',
                  scheme_code: 0,
                },
              ],
              screenshots: null,
              design_models: null,
              design_videos: null,
              design_proposals: null,
              design_others: null,
              design_zip: null,
              facial_photos: null,
              intraoral_photos: null,
              ct_photos: null,
              original_file_photos: null,
            },
            tooth_design:
              '[{"pid_code":20008,"pid_cost_time":1,"code":20055,"cost_time":0,"tooth":[11,41],"name":{"en":"Clear Aligner","zh":"简单正畸"},"pid_name":{"en":"Ortho Model","zh":"正畸模型"}}]\n',
            status: 5,
            status_name: {
              en: 'Completed',
              zh: '已完成',
            },
            is_stop: 0,
            delivery_time: 1644888388,
            created_time: 1644801988,
            time_cost: '0:03:29',
            expected_time: 1644802147,
            design_status: 6,
            design_user: '',
            iqc: 100956,
            oqc: 100957,
            iqc_user: '',
            oqc_user: '',
            designer: 100958,
            file_name: '222.stl',
            review_time: 1644802240,
          },
          {
            order_code: '230178322117185538',
            order_no: '20030318014984681',
            org_name: '组织组织3',
            design_type_codes: '20055',
            files: {
              original_files: [
                {
                  file_path: 'test/Attachment/20220214/sqpmacmcl5f1672789222.zip',
                  file_name: '222.stl',
                  file_time: 0,
                  file_size: 0,
                  file_type: 0,
                  design_path: '',
                  scheme_code: 0,
                },
              ],
              design_files: [
                {
                  file_path: 'test/DesignFileNew/20220214/230178322117185536_1644802218936/222_1644802218936.zip',
                  file_name: '222.zip',
                  file_time: 0,
                  file_size: 0,
                  file_type: 0,
                  design_path: '',
                  scheme_code: 0,
                },
              ],
              screenshots: null,
              design_models: null,
              design_videos: null,
              design_proposals: null,
              design_others: null,
              design_zip: null,
              facial_photos: null,
              intraoral_photos: null,
              ct_photos: null,
              original_file_photos: null,
            },
            tooth_design:
              '[{"pid_code":20008,"pid_cost_time":1,"code":20055,"cost_time":0,"tooth":[11,41],"name":{"en":"Clear Aligner","zh":"简单正畸"},"pid_name":{"en":"Ortho Model","zh":"正畸模型"}}]\n',
            status: 5,
            status_name: {
              en: 'Completed',
              zh: '已完成',
            },
            is_stop: 0,
            delivery_time: 1644888388,
            created_time: 1644801988,
            time_cost: '0:03:29',
            expected_time: 1644802147,
            design_status: 6,
            design_user: '',
            iqc: 100956,
            oqc: 100957,
            iqc_user: '',
            oqc_user: '',
            designer: 100958,
            file_name: '222.stl',
            review_time: 1644802240,
          },
        ],
      }),
      errcode: 200,
      msg: '操作成功',
    };
  },
  getStatus: () => {
    return {
      data: Mock.mock([
        {
          Code: 9,
          Name: {
            en: 'Returned',
            zh: '已退回',
          },
        },
        {
          Code: 1,
          Name: {
            en: 'To be translated',
            zh: '待译单',
          },
        },
        {
          Code: 2,
          Name: {
            en: 'To be assigned',
            zh: '待指派',
          },
        },
        {
          Code: 3,
          Name: {
            en: 'To be designed',
            zh: '待设计',
          },
        },
        {
          Code: 4,
          Name: {
            en: 'Design',
            zh: '设计中',
          },
        },
        {
          Code: 6,
          Name: {
            en: 'To be audited',
            zh: '待审核',
          },
        },
        {
          Code: 7,
          Name: {
            en: 'To be confirmed',
            zh: '待确认',
          },
        },
        {
          Code: 8,
          Name: {
            en: 'Completed',
            zh: '已完成',
          },
        },
      ]),
      errcode: 200,
      msg: 'ok',
    };
  },
  getManagerDataKiosks: () => {
    return {
      data: Mock.mock({
        activeCustomers: 0,
        designCategoryKiosk: [
          {
            completed: 0,
            designCategoryCode: 0,
            designCategoryEnName: '',
            designCategoryZhName: '',
            incomplete: 0,
            review: 0,
          },
        ],
        designerToBeAssigned: 0,
        internalReturnRate: 0,
        internalReturnRingRate: 0,
        iqcToBeAssigned: 0,
        lastOrder: 0,
        newOrder: 0,
        oqcToBeAssigned: 0,
        outboundReturnRate: 0,
        outboundReturnRingRate: 0,
        timeOut: 0,
        warning: 0,
      }),
      errcode: 200,
      msg: 'ok',
    };
  },
};
