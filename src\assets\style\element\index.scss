// font-size
$hg-normal-fontsize: 14px;
$hg-medium-fontsize: 16px;
$hg-large-fontsize: 24px;
$hg-small-fontsize: 12px;

// color
$hg-main-black: #1d1d1f;
$hg-main-blue: #3054cc;

$hg-primary-fontcolor: #e4e8f7;
$hg-secondary-fontcolor: #83868f;
$hg-disable-fontcolor: #54565c;
$fontColorTagging: #2e313d;

$hg-border-color: #38393d;

$hg-border-radius2: 2px;
$hg-border-radius4: 4px;

// 图标
// 图标禁用颜色(复选框的“√”)
$iconDisabledColor: #38393d;

$hg-background-color: #121213;
$disabledhg-background-color: rgba(84, 86, 92, 0.25);

$linkColor: #5169b8;
$hg-success-color: #6cc740;
$hg-warning-color: #c78840;
$hg-error-color: #c74040;
$normalStatusColor: #535b7a;
$hg-hover-bg-color: #262629;
$hg-active-fontcolor: #ffffff;
$hg-button-hover-fontcolor: #3760eb;
$hg-button-active-fontcolor: #2b4bb8;

$dangerColor: $hg-error-color;
$infoColor: $hg-warning-color;
$primaryColor: #3054cc;
$defaultColor: #3054cc;

// 弹出层下划线
$popUpBottomLine: 1px solid #38393d;

// 边框颜色
$hg-border-color: #38393d;
// 有焦点时边框色
$borderFocusColor: #e4e8f7;

$hg-main-margin: 24px;
$hg-height-60: 60px;

.el-button {
    &.el-button--default {
        color: #fff;
        background-color: $defaultColor;
        border-color: $defaultColor;
        &:hover {
            color: #e4e8f7;
            background: #3760eb;
        }
        &:focus {
            color: #c5c9d6;
            background: #2b4bb8;
        }
        // &.is-plain{
        //     // 朴素按钮样式
        // }
        &.is-disabled,
        &.is-disabled:hover {
            opacity: 0.4;
            color: #e4e8f7;
            background: #54565c;
        }
    }
}

// MessageBox
.el-message-box__wrapper {
    .el-message-box {
        width: 560px;
        border: 0;
        border-radius: 4px;
        padding-bottom: 0;
        background: $hg-main-black;
        .el-message-box__header {
            height: 59px;
            padding: 0 24px;
            border-bottom: $popUpBottomLine;
            .el-message-box__title {
                color: $hg-primary-fontcolor;
                font-size: 16px;
                line-height: 60px;
            }
            .el-message-box__headerbtn {
                top: 0;
                right: 24px;
                .el-message-box__close {
                    color: #c4c4c4;
                    font-size: 22px;
                    line-height: 60px;
                }
            }
        }
        .el-message-box__content {
            padding: 24px;
            .el-message-box__container {
                .el-message-box__message {
                    color: $hg-primary-fontcolor;
                    font-size: 14px;
                }
            }
        }
        .el-message-box__btns {
            padding: 24px;
            padding-top: 0;
        }
    }
}

// Dialog对话框
.el-dialog__wrapper {
    .el-dialog {
        background-color: $hg-main-black;
        .el-dialog__header {
            height: 59px;
            padding: 0;
            border-bottom: $popUpBottomLine;
            .el-dialog__title {
                color: $hg-primary-fontcolor;
                font-size: 16px;
                line-height: 60px;
                padding: 0 24px;
            }
            .el-dialog__headerbtn {
                top: 0;
                right: 24px;
                font-size: 22px;
                line-height: 60px;
            }
        }
    }
}

// page-header页头组件
.el-page-header {
    background: $hg-main-black;
    .el-page-header__left {
        width: 80px;
        height: 32px;
        border: 1px solid $hg-border-color;
        border-radius: 2px;
        box-sizing: border-box;
        padding: 0 16px;
        color: $hg-secondary-fontcolor;
        line-height: 32px;
        .el-page-header__title {
            font-size: 12px;
        }
        .el-icon-back {
            font-size: 12px;
        }
    }
    .el-page-header__content {
        color: $hg-primary-fontcolor;
        font-size: 14px;
        line-height: 32px;
    }
}

// 抽屉
.el-drawer__wrapper{
    .el-drawer{
        background: $hg-main-black;
        .el-drawer__header{
            height: 66px;
            border-bottom: 1px solid $hg-border-color;
            margin: 0;
            padding: 0 24px;
            color: $hg-primary-fontcolor;
            font-size: 16px;
            text-align: center
        }
    }
}
/*elementui的下拉选择框样式全局覆盖*/
.el-select-dropdown, .el-cascader__dropdown {
  border-radius: 4px !important;
  background-color: #1D1D1F !important;
  border: none !important;
  @include bg-box-shadow();
  .popper__arrow, .popper__arrow::after {
    border-top-color: #1D1D1F !important;
    border-bottom-color: #1D1D1F !important;
  }
  .el-select-dropdown__item {
    padding: 0 24px !important;
    background-color: #1D1D1F !important;
    &.selected {
      color: #E4E8F7;
      font-weight: normal;
      background-color: transparent !important;
    }
    &:hover {
      background-color: #262629 !important;
    }
  }
  .el-cascader-menu {
    border-right-color: $hg-border-color;
  }
}




@import './form.scss';
@import './pagination.scss';
@import './table.scss';
@import './upload.scss';

