<template>
  <div v-if="show" class="popup-wrapper">
    <div class="popup">
      <div class="popup-header">
        <span class="popup-header-text">{{ popupTitle }}</span>
        <i v-if="!isUserClose" class="iconfont icon-close-mix iconfont-24" @click="cancel" />
      </div>
      <div class="popup-body">
        <slot name="popupContent" />
      </div>
      <div class="btn-group">
        <el-button v-if="isUseEle" class="confirm ele-btn" type="primary" :loading="loading" @click.native="submit">{{ $t('common.submit') }}</el-button>
        <VueButton
          v-if="!isUseEle"
          v-btn-control
          class="confirm"
          width="104"
          type="primary"
          sizes="big"
          @click.native="submit"
        >
          {{ $t('common.submit') }}
        </VueButton>
        <VueButton
          v-if="!isUserClose"
          v-btn-control
          width="104"
          sizes="big"
          @click.native="cancel"
        >
          {{ $t('common.cancel') }}
        </VueButton>
        <!-- <button class="cancel" @click="cancel">{{ this.$t('common.cancel') }}</button>
        <button class="confirm" @click="submit">{{ this.$t('common.submit') }}</button> -->
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Popup',
  props: {
    popupTitle: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    isUseEle: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    isUserClose: {
      type: [Boolean, String],
      default: false
    }
  },
  data() {
    return {
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    cancel() {
      this.$emit('cancel')
    },
    submit() {
      this.$emit('submit')
    }
  }
}
</script>
<style lang="scss" scoped>
.popup-wrapper {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  text-align: center;
  @include shade-background;
  &::after {
    content: "";
    width: 0px;
    height: 100%;
    display: inline-block;
    vertical-align: middle;
  }
  .popup {
    display: inline-block;
    vertical-align: middle;
    background-color: $hg-main-black;
    width: 520px;
    .popup-header {
      position: relative;
      height: $hg-height-60;
      line-height: $hg-height-60;
      border-bottom: 1px solid $hg-border-color;
      box-sizing: border-box;
      padding: 0 24px;
      text-align: left;
      display: flex;
      align-items: center;
      .popup-header-text {
        font-weight: bold;
        color: $hg-primary-fontcolor;
        font-size: $hg-medium-fontsize;
      }
      .iconfont {
        margin-left: auto;
      }
    }
    .popup-body {
      font-size: $hg-normal-fontsize;
      box-sizing: border-box;
      padding: 24px;
      min-height: $hg-height-60;
      max-height: 486px;
      line-height: 22px;
      color: $hg-secondary-fontcolor;
      overflow-wrap: break-word;
      text-align: left;
      overflow: hidden;
      overflow-y: auto;
    }
    .btn-group {
      display: flex;
      flex-direction: row-reverse;
      padding-bottom: 24px;
      // button {
      //   box-sizing: border-box;
      //   font-size: 14px;
      //   width: 104px;
      //   height: 40px;
      //   border-radius: 4px;
      //   line-height: 40px;
      //   text-align: center;
      //   border: 1px solid $hg-main-blue;
      //   cursor: pointer;
      //   transition: all 0.3s ease-in-out;
      //   box-sizing: border-box;
      //   outline: none;
      // }
      .confirm {
        margin: 0 24px;
      }
      .ele-btn{
        width:104px;background-color: #3054cc;
      }
    }
  }
}
</style>
