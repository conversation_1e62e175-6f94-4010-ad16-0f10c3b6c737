/**
 * 获取国际化文本，参数传递遵循{ en: '', zh: '', tw: '' } 或 { en: [], zh: [], tw: [] }
 */

import store from '@/store';

export default {
  install(Vue) {
    Vue.prototype.$getI18nText = function (item = {}) {
      const language = store.getters.language;
      let { zh, tw, en } = item;
      zh =  (zh instanceof Array) ? zh.join('，') : zh;
      tw =  (tw instanceof Array) ? tw.join('，') : tw;
      en =  (en instanceof Array) ? en.join('，') : en;
      if(language === 'zh'){
        return zh;
      }else if ( language === 'zh-TW'){
        if(tw){
          return tw;
        }
        return zh;
      }else if (language === 'en'){
        return en;
      }else {
        return zh;
      }
    }
  }
}