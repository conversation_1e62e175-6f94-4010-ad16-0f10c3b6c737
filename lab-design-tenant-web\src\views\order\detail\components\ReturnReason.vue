<template>
  <el-dialog
    custom-class="order-detail-page return-reason" 
    append-to-body 
    width="520px" 
    :visible="isShow" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :before-close="handleClose">
    <p slot="title">{{ $t(dialogInfo.title) }}</p>
    <div>
      <el-input 
        show-word-limit 
        :maxlength="dialogInfo.maxlength" 
        :placeholder="$t(dialogInfo.placeholder)" 
        type="textarea" 
        v-model="returnContent"
        :resize="'none'"
        @input="handleInput"></el-input>
        <p class="error-tips">{{errorTips}}</p>
    </div>

    <div class="upload-picture" v-show="needImage">
      <div class="upload-title">
        {{ $t('file.tips.uploadImageTip') }}
      </div>
      
      <el-upload
        class="re-order-upload"
        action="#"
        accept="image/*"
        list-type="picture-card"
        multiple
        :file-list="uploadPictureList"
        :limit="4"
        :auto-upload="false"
        :disabled="uploadPictureList.length === 4"
        :on-exceed="handleExceed"
        :on-change="uploadChange">
          <i slot="default" class="el-icon-plus"></i>
          <div class="tips">{{ $t('file.title.image') }}</div>
          <div  
            class="img-item"
            slot="file" 
            slot-scope="{file}" 
            :class="{'is-uploading': file.isUploading}" 
            @dblclick="handlePictureCardPreview(file, uploadPictureList)">

            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
            <span class="el-upload-list__item-actions">
              <span
                v-if="!file.isUploading"
                class="el-upload-list__item-close"
                @click="handleRemove(file)">
                <i class="el-icon-close" ></i>
              </span>

              <div class="upload-again" v-if="!file.isUploading && !file.isReload" @click="uploadAgain(file, uploadPictureList)">
                {{ $t('file.btn.reUpload') }}
              </div>

              <el-progress
                class="upload-progress"
                :percentage="file.percentage" 
                :color="customColor" 
                v-if="file.isUploading"></el-progress>
            </span>
          </div>
      </el-upload>
    </div>

    <div slot="footer" class="footer">
      <hg-button type="secondary" @click="handleClose">{{ $t('common.btn.cancel') }}</hg-button>
      <hg-button :loading="isLoading" @click="handleConfirm">{{ $t('common.btn.confirm') }}</hg-button>
    </div>

  </el-dialog>
</template>

<script>
import variables from '@/assets/styles/export.scss';
import UploadNormalFile from '@/public/utils/uploadNormalFile';
import { rebackOrderToClient, continueToDesign, rebackOrderToIQC, repulseDesign, rebackToClientForUnauth, disapproveForFree } from '@/api/order/operate';
import { mapGetters } from 'vuex';
import { getDownloadUrl } from '@/api/file';

export default {
  props: {
    orderCode: {
      type: [Number, String],
      required: true
    },
    dialogInfo: {
      type: Object,
      default(){
        return {
          title: 'order.detail.title.returnReason',
          placeholder: 'order.detail.tips.pleaseInputReturnReason',
          maxlength: 100,
        }
      }
    },
    // 回填内容对象
    fillContent: {
      type: Object,
      default() {
        return {
          returnReason: '',
          returnImageList: [],
        }
      }
    }
  },
  data() {
    return {
      isShow: false,
      returnContent: '',
      errorTips:'',
      needImage: false,
      eventType: '', // 事件类型：returnToClient 返单，rejectDesign 设计不通过， confirmReturn 确认退回 rejectDesigner 拒绝返单 disapproveForFree 拒绝免单
      isLoading: false,

      customColor: variables.hgMainBlue,
      uploadPictureList: [],
    }
  },
  computed: {
    ...mapGetters(['orgCode']),
  },
  watch: {
    eventType(type) {
      if(type === 'confirmReback' && this.fillContent) {
        this.uploadPictureList = [];
        const { returnReason, returnImageList } = this.fillContent;
        this.returnContent = returnReason;
        returnImageList.forEach(imagePath => {
          const item = {
            isUploading: false,
            percentage: 100,
            s3FileId: imagePath,
            url: '',
            isReload: true,
            name: imagePath,
            pictureUrl: '',
          };
          this.uploadPictureList.push(item);
          this.getImageUrl(imagePath);
        });
      }
    }
  },
  methods: {
    handleConfirm() {
      if(!this.returnContent && !['confirmReback','rebackOrderToClient', 'disapproveForFree'].includes(this.eventType)) { // 确认返单、IQC返单给客户，不强制要求填写内容
        this.errorTips = this.$t('component.tip.pleaseInput');
        return;
      }

      if(this.eventType === 'confirmReback') {
        this.$emit('getConfrmRebackReason', this.returnContent);
        this.handleClose();

      }else {

        let param = {
          orderCode: this.orderCode,
          returnReason: this.returnContent,
        };
        let proxyPromise = new Promise(() => {});

        switch(this.eventType) {
          case 'rebackOrderToClient':{ // 返单给客户
            param.returnImage = this.uploadPictureList.map(item => item.s3FileId);
            proxyPromise = rebackOrderToClient;
          }
          break;
          case 'continueToDesign': { // 继续设计
            proxyPromise = continueToDesign;
          }
          break;
          case 'rebackOrderToIQC': { // 返单给IQC
            param.returnImage = this.uploadPictureList.map(item => item.s3FileId);
            proxyPromise = rebackOrderToIQC;
          }
          break;
          case 'repulseDesign':{ // 审核不通过
            proxyPromise = repulseDesign;
          }
          break;
          case 'rebackToClientForUnauth' : {
            param.returnImage = this.uploadPictureList.map(item => item.s3FileId);
            proxyPromise = rebackToClientForUnauth;
          }
          break;
          case 'disapproveForFree' :{
            param.imgS3FileIds = this.uploadPictureList.map(item => item.s3FileId);
            param.isPass = false;
            param.reason = this.returnContent;
            proxyPromise = disapproveForFree;
          }
          default:break;
        }

        this.isLoading = true;
        proxyPromise(param).then(() => {
          this.$hgOperateSuccess();
          this.$emit('initOrder');
          this.handleClose();
        }).catch(err => {
          const { message } = err;
          this.$hgOperateFail(message);
        }).finally(() => {
          this.isLoading = false;
        });
      }  
      
    },

    /**
     * 超过限制数的回调函数
     */
    handleExceed(files, fileList) {
      this.$hgOperateWarning(this.$t('file.tips.limitFourImage', [files.length,fileList.length]));
    },
    /**
     * 文件校验 这里的文件是文件对象
     * @param { Array } file 文件
     */
    verifyFile(file, fileList) {
      if (file.size === 0) {
        this.$hgOperateFail(this.$t('file.tips.emptyFile'));
        this.handleRemove(file);
        return false;
      } 
      if (file.size / 1024 / 1024 > 50) {
        this.$hgOperateFail(this.$t('file.tips.imageToBig'));
        this.handleRemove(file);
        return false;
      }
      const isImage = file.raw.type.includes('image/');
      if (!isImage) {
        this.$hgOperateFail(this.$t('file.tips.notImage'));
        this.handleRemove(file);
        return false;
      }

      if (this.uploadPictureList.some(item => item.name === file.name)) {
        this.$hgOperateFail(this.$t('file.tips.imageRepeat'));
        this.uploadPictureList = fileList.filter(item => item.s3FileId);
        return false;
      }
      return true;
    },
    /**
     * 上传
     */
    async uploadChange(file, fileList, isReUpload) {
      try {
        if (!isReUpload && !this.verifyFile(file, fileList) ) { return false; }
        this.$set(file, 'isUploading', true);

        const uploadNormalFile = new UploadNormalFile({file: file.raw});

        uploadNormalFile.onUploadProgress((progressEvent) => {
          let complete = ((progressEvent.loaded / progressEvent.total) * 100) | 0;
          file.percentage = complete;
          if (complete === 100) {
            setTimeout(() => {
              file.isUploading = false;
            }, 1000);
          }
        });

        uploadNormalFile.onEnd((data) => {
          file.s3FileId = data.s3FileId;
          file.pictureUrl = data.url;
          this.uploadPictureList = fileList.filter(item => item.s3FileId);
        });

        uploadNormalFile.onError((data) => {
          console.error('data: ', data);
        });

        uploadNormalFile.onStart();
      } catch (error) {
        console.log('error: ', error);
        file.isUploading = false;
        this.uploadPictureList = fileList;
        this.handleRemove(file);
      }
    },
    /**
     * 重新上传
     * @param file 文件对象
     */
    uploadAgain(file, fileList) {
      this.uploadChange(file, fileList, true);
    },
    /**
     * 预览图片
     * @param file 当前文件
     * @param fileList 上传的文件
     */
    handlePictureCardPreview(file, fileList) {
      const fileIndex = fileList.findIndex(item => item.pictureUrl === file.pictureUrl);
      this.$hgViewer.open({
        imgList: fileList.map(item => item.pictureUrl),
        initialViewIndex: fileIndex
      });
    },

    getImageUrl(s3FileId) {
      const param = {
        s3FileId,
        orgCode: this.orgCode,
        filename: '',
      };
      getDownloadUrl(param, true).then(res => {
        if(res.code === 200){
          const url = res.data.url;
          const imageItem = this.uploadPictureList.find(item => item.s3FileId === s3FileId);
          if(imageItem) {
            this.$set(imageItem, 'url', url);
            this.$set(imageItem, 'pictureUrl', url);
          }
        }
      }).catch(err => {
        console.log('返单的图片', err);
      });
    },

    /**
     * 移除图片
     * @param file 文件对象
     */
    handleRemove(file) {
      this.uploadPictureList = this.uploadPictureList.filter(item => item.name !== file.name);
    },
    /**
     * 监听输入框
     */
    handleInput(){
      if(this.errorTips){
        this.errorTips = '';
      }
    },
    handleClose() {
      if(this.eventType !== 'confirmReback') {
        this.uploadPictureList = [];
      }
      this.isShow = false;
      this.needImage = false;
      this.eventType = '';
      this.errorTips = '';
      this.returnContent = '';
      
    }
  }
}
</script>

<style lang="scss" scoped>
.order-detail-page.return-reason {  
  .error-tips{
    padding-top: 4px;
    color: $hg-error;
  }

  .el-dialog__header {
    .el-dialog__title {
      color: $hg-label;
      font-size: 16px;
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    .hg-button {
      width: 104px;
      height: 40px;

      /* &:first-of-type {
        margin-right: 24px;
      } */
    }
  }
}
</style>

<style lang="scss">
.order-detail-page.return-reason {
  height: auto;

  .el-dialog__body {
    padding: 24px 32px;
    .el-textarea {
      .el-textarea__inner {
        height: 157px;
        padding: 12px 24px;
      }
      .el-input__count {
        right: 24px;
        bottom: 12px;
      }
    }
  }

  .upload-picture {
    margin-top: 20px;
    .upload-title {
      margin-bottom: 10px;
      color: $hg-label;
    }
  }
  .re-order-upload {
    display: flex;
    justify-content: flex-end;
    flex-direction: row-reverse;
    .el-upload--picture-card {
      background-color: $hg-hover;
      border-radius: 2px;
      width: 80px;
      height: 80px;
      display: flex;
      line-height: unset;
      border: 1px dashed $hg-disable;
      color: $hg-secondary-text;
      align-content: center;
      flex-wrap: wrap;
      .el-icon-plus {
        font-size: 14px;
        width: 100%;
        margin-bottom: 5px;
      }
      .tips {
        flex: 1;
      }
    }
    .el-upload-list--picture-card {
      .el-upload-list__item {
        width: 80px;
        height: 80px;
        padding: 5px;
        border-radius: 2px;
        background-color: $hg-hover;
        margin: 0 0 0 18px;
        border: 0;
        .img-item {
          width: 70px;
          height: 70px;
          img {
            display: block;
            border-radius: 2px;
          }
        }
        .is-uploading {
          .el-upload-list__item-actions {
            opacity: 1;
            .upload-progress {
              width: 56px;
              .el-progress__text {
                top: 100%;
                left: 0;
                font-size: 14px !important;
                color: $hg-label;
                margin-left: 0;
              }
            }
          }
        }
        .el-upload-list__item-actions {
          transition: none;
          background-color: rgba(0, 0, 0, 0.85);
          cursor: pointer;
          .el-icon-close {
            display: block;
            color: $hg-label;
          }
          .upload-again {
            font-size: 14px;
            text-decoration: underline;
            color: $hg-secondary-text;
            margin-top: 10px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

</style>