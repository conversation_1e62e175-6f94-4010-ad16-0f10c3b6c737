<template>
  <div class="admin-board" v-loading="loadingAdmin">
    <div class="title">{{ $t('databoard.adminboard.statistics') }}</div>
    <div class="type-content">
      <div :class="['type-box']" :style="'background:' + designCategoryBg[index].background" v-for="(cate, index) in designCategoryKiosk" :key="index">
        <p class="type-title" v-if="cate.designCategoryCode !== 999999">{{ $t(`apiCommon.${cate.designCategoryCode}`) }}</p>
        <p class="type-title" v-else>{{ $t(`databoard.adminboard.${cate.designCategoryCode}`) }}</p>
        <p class="type-num" @click="jumpOrderList(cate, 'incomplete')">{{ $t('databoard.adminboard.noFinish') }}：{{ cate.incomplete }}</p>
        <p class="type-num" @click="jumpOrderList(cate, 'review')">{{ $t('databoard.adminboard.unconfirmed') }}：{{ cate.review }}</p>
        <p class="type-totle">{{ $t('databoard.adminboard.historyTotal') }}：{{ cate.totalHistory | computeNumber }}</p>
        <!-- <p class="type-totle">千万数据测试：{{ testNumber | computeNumber('isover') }}</p> -->
        <img class="type-img" :src="designCategoryBg[index].img()" alt="" />
      </div>
    </div>
    <!-- <div><el-input v-model="testNumber"></el-input></div> -->
    <div class="title">{{ $t('databoard.adminboard.pending') }}</div>
    <div class="type-content">
      <div class="handel-box-left">
        <div class="handel-box" @click="jumpOrderList(null, 'IQC')">
          <span class="box1">{{ reviewOrder.iqcToBeAssigned }}</span>
          <span class="box2">{{ $t('databoard.adminboard.IQC') }}</span>
        </div>
        <div class="handel-box" @click="jumpOrderList(null, 'DESIGNER')">
          <span class="box1">{{ reviewOrder.designerToBeAssigned }}</span>
          <span class="box2">{{ $t('databoard.adminboard.designer') }}</span>
        </div>
        <div class="handel-box" @click="jumpOrderList(null, 'OQC')">
          <span class="box1">{{ reviewOrder.oqcToBeAssigned }}</span>
          <span class="box2">{{ $t('databoard.adminboard.OQC') }}</span>
        </div>
      </div>
      <div class="handel-box-right">
        <!-- <span class="warming-order" @click="jumpOrderList(null, 'warning')">{{ $t('databoard.adminboard.warmingOrder') }}：{{ reviewOrder.warning }}</span>
        <span class="over-order" @click="jumpOrderList(null, 'timeout')">{{ $t('databoard.adminboard.overOrder') }}：{{ reviewOrder.timeOut }}</span> -->
        <div class="handel-box" @click="jumpOrderList(null, 'warning')">
          <span class="box1 warming-order">{{ reviewOrder.warning }}</span>
          <span class="box2">{{ $t('databoard.adminboard.warmingOrder') }}</span>
        </div>
        <div class="handel-box" @click="jumpOrderList(null, 'timeout')">
          <span class="box1 over-order">{{ reviewOrder.timeOut }}</span>
          <span class="box2">{{ $t('databoard.adminboard.overOrder') }}</span>
        </div>
        <div class="handel-box" @click="jumpOrderList(null, 'freeCharge')">
          <span class="box1 over-order">{{ reviewOrder.apply_waiver }}</span>
          <span class="box2">{{ $t('databoard.adminboard.Rejected') }}</span>
        </div>
      </div>
    </div>
    <div class="title">{{ $t('databoard.adminboard.otherData') }}</div>
    <div class="type-content">
      <div class="other-box">
        <div class="normal-box" v-for="(other, index) in otherDataList" :key="index">
          <span class="box1">{{ $t(other.name) }}
            <span class="box4 special-box" v-if="other.type == 2">
              {{ $t('databoard.adminboard.rate') }}<span><i class="el-icon-caret-top top-icon"></i>{{ reviewOrder[other.numberRate] }}%</span>
            </span>
          </span>
          <span class="box2" v-if="other.type == 1">{{ reviewOrder[other.number] | computeNumber('isOver') }}</span>
          <span class="box3" v-if="other.type == 2">{{ reviewOrder[other.number] }}%</span>
          <span class="box4 jump" v-if="other.type == 2" @click="jumpOrderList(null, other.returnOrderCount)"
            >{{ $t(other.returnOrderName) }}：<span><i class="top-icon"></i>{{ reviewOrder[other.returnOrderCount] }}</span></span
          >
          <!-- <img class="arrow-img" src="@/assets/images/databoard/icon-arrow-lab.svg" alt="" /> -->
        </div>
      </div>
    </div>
    <div class="type-content">
      <div class="other-box other-box-second">
        <div :class="['normal-box',other.type ? '' : 'is-visible']" v-for="(other, index) in otherDataListTwo" :key="index">
          <span class="box1">{{ $t(other.name) }}</span>
          <span class="box2" v-if="other.type == 1">{{ reviewOrder[other.number] | computeNumber('isOver') }}</span>
          <!-- <img class="arrow-img" src="@/assets/images/databoard/icon-arrow-lab.svg" alt="" /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { managerDataKiosks } from '@/api/databoard';
import { ROUTE_NAME, UNION_TYPE_CODE } from '@/public/constants';
import { mapGetters } from 'vuex';
import { computeNumber } from '@/filters';
export default {
  name: 'adminBoard',
  computed: {
    ...mapGetters(['language', 'designTypeTree', 'statisJumpSearch']),
  },
  filters: { computeNumber },
  data() {
    return {
      loadingAdmin: true,
      designCategoryBg: [
        {
          background: 'linear-gradient(270deg, #3054CC 0%, #5477EB 100%);',
          img: () => require('@/assets/images/databoard/icon-gudingxiufu-lab.svg'),
        },
        {
          background: 'linear-gradient(269deg, #007252 0.46%, #00AA6D 99.34%);',
          img: () => require('@/assets/images/databoard/icon-huodongxiufu-lab.svg'),
        },
        {
          background: 'linear-gradient(272deg, #5D7092 -0.42%, #6F8BB7 98.8%);',
          img: () => require('@/assets/images/databoard/icon-zhongzhi-lab.svg'),
        },
        {
          background: 'linear-gradient(270deg, #D68E3C 0%, #E3A937 100%);',
          img: () => require('@/assets/images/databoard/icon-zhengji-lab.svg'),
        },
        {
          background: 'linear-gradient(-90deg, #5614AB 0%, #832BF5 100%);',
          img: () => require('@/assets/images/databoard/icon-lianhexiufu-lab.svg'),
        },
      ],
      designCategoryKiosk: [
        {
          incomplete: 0, //未完成
          review: 0, //待确认
          totalHistory: 0, //历史总计
          designCategoryEnName: 'Fixed Restorations',
          designCategoryZhName: '固定修复',
          designCategoryCode: 21000,
        },
        {
          incomplete: 0,
          review: 0,
          totalHistory: 0,
          designCategoryEnName: 'Removable Restorations',
          designCategoryZhName: '活动修复',
          designCategoryCode: 22000,
        },
        {
          incomplete: 0,
          review: 0,
          totalHistory: 0,
          designCategoryEnName: 'Implant Restorations',
          designCategoryZhName: '种植修复',
          designCategoryCode: 23000,
        },
        {
          incomplete: 0,
          review: 0,
          totalHistory: 0,
          designCategoryEnName: 'Orthodontics&Other',
          designCategoryZhName: '正畸&其他',
          designCategoryCode: 24000,
        },
        {
          incomplete: 0,
          review: 0,
          totalHistory: 0,
          designCategoryEnName: 'Combined Restorations',
          designCategoryZhName: '联合修复',
          designCategoryCode: UNION_TYPE_CODE,
        },
      ],
      reviewOrder: {
        iqcToBeAssigned: 0, //待指派IQC，
        designerToBeAssigned: 0, //待指派设计师
        oqcToBeAssigned: 0, //待指派OQC
        warning: 0, //预警订单
        timeOut: 0, //超时订单
        apply_waiver:0,//申请免单
        newOrder: 0, //本月新订单
        lastOrder: 0, //上月订单
        activeCustomers: 0, //活跃客户数
        outboundReturnRate: 0, //外返率
        outboundReturnRingRate: 0, //外返率环比
        internalReturnRate: 0, //内返率
        internalReturnRingRate: 0, //内返率环比
        outboundReturnOrderCount: 0, //外返订单数
        internalReturnOrderCount: 0, //内返订单数
        unauthorizedOrg: 0,//未认证客户数
      },
      otherDataList: [
        {
          type: 1,
          name: 'databoard.adminboard.newOrder',
          number: 'newOrder',
        },
        {
          type: 1,
          name: 'databoard.adminboard.lastOrder',
          number: 'lastOrder',
        },
        {
          type: 1,
          name: 'databoard.adminboard.activeCustomer',
          number: 'activeCustomers',
        },
        {
          type: 2,
          name: 'databoard.adminboard.outRate',
          number: 'outboundReturnRate',
          numberRate: 'outboundReturnRingRate',
          returnOrderName: 'databoard.adminboard.external',
          returnOrderCount: 'outboundReturnOrderCount',
        },
        {
          type: 2,
          name: 'databoard.adminboard.inRate',
          number: 'internalReturnRate',
          numberRate: 'internalReturnRingRate',
          returnOrderName: 'databoard.adminboard.inernal',
          returnOrderCount: 'internalReturnOrderCount',
        },
      ],
      otherDataListTwo: [
        {
          type: 1,
          name: 'databoard.adminboard.unverified',
          number: 'unauthorizedOrg',
        },{},{},{},{}
      ],
      testNumber: 0,
    };
  },
  mounted() {
    this.managerDataKiosks();
  },
  methods: {
    managerDataKiosks() {
      this.loadingAdmin = true;
      managerDataKiosks().then((res) => {
        let data = res.data;
        let designCategoryKioskList = data.designCategoryKiosk || this.designCategoryKiosk;
        designCategoryKioskList = designCategoryKioskList.filter(item => ![12000, 11000].includes(item.designCategoryCode));
        this.designCategoryKiosk = designCategoryKioskList.map(item => { 
          if(item.designCategoryCode === 25000) {
            item.designCategoryCode = UNION_TYPE_CODE;
          }
          return item;
        });

        Object.keys(this.reviewOrder).forEach((item) => {
          this.reviewOrder[item] = data[item] || 0;
        });
      }).finally(() => {
        this.loadingAdmin = false;
      });
    },
    getTypeCodeAll(codes) {
      //在二级添加未知
      var obj = {
        children: '',
        cnName: '未知',
        designCode: 25001,
        enName: 'Unknown',
        hasParas: 0,
        iconUrl: '',
        level: 3,
        parentCode: 99999,
      };
      // 循环遍历
      let tree = this.designTypeTree.filter((item) => {
        return item.designCode == codes;
      });
      tree.forEach((item) => {
        if (
          !item.children.find((it) => {
            return it.designCode == 25001;
          })
        ) {
          item.children.push(obj);
        }
      });
      let list = [];
      const deep2 = function(tree, arr) {
        for (let i = 0; i < tree.length; i++) {
          let treeList = [].concat(arr);
          treeList.push(tree[i].designCode);
          if (tree[i].children && tree[i].children.length > 0) {
            deep2(tree[i].children, treeList);
          } else {
            list.push(treeList);
          }
        }
      };
      deep2(tree, []);
      if(codes === UNION_TYPE_CODE) {
        list.push([UNION_TYPE_CODE]);
      }
      return list;
    },
    getTypeCode(codes) {
      //在二级添加未知
      var obj = {
        children: '',
        cnName: '未知',
        designCode: 25001,
        enName: 'Unknown',
        hasParas: 0,
        iconUrl: '',
        level: 3,
        parentCode: 99999,
      };
      // 循环遍历
      let tree = this.designTypeTree.filter((item) => {
        return item.designCode == codes;
      });
      tree.forEach((item) => {
        if (
          !item.children.find((it) => {
            return it.designCode == 25001;
          })
        ) {
          item.children.push(obj);
        }
      });
      let codeArr = [];
      const loop = function(list) {
        list.forEach((item) => {
          codeArr.push(item.designCode);
          if (item.children.length > 0) {
            loop(item.children);
          }
        });
      };
      loop(tree);
      return codeArr;
    },
    // 点击跳转
    jumpOrderList(cate, type) {
      // cate获取当前大类全部信息
      let designCodesAll = cate ? this.getTypeCodeAll(cate.designCategoryCode) : [];
      let designTypeCodes = cate ? this.getTypeCode(cate.designCategoryCode) : [];
      let statisJumpSearch = {
        keywords: '',
        status: '',
        designTypeCodes: designTypeCodes, //类型
        startTime: 0, //创建时间
        endTime: 0,
        completeStartTime: 0, //完成时间
        completeEndTime: 0,
        designCodesAll: designCodesAll,
        inComplete: null, //跳转带的参数，点击的是未完成
        inStatistics: 1, //是否是数据看板跳转
        currentPersonIsEmpty: null, //处理人为空
        timeOut: null, //超时
        warning: null, //预警
        designCategoryCode: cate && cate.designCategoryCode ? String(cate.designCategoryCode) : null, // 和列表的保持一致，列表用string
        returnOrderType: '',
      };
      switch (type) {
        //未完成
        case 'incomplete':
          statisJumpSearch.inComplete = 1;
          break;
        case 'review':
          statisJumpSearch.status = '7';
          break;
        case 'IQC':
          statisJumpSearch.currentPersonIsEmpty = 1;
          statisJumpSearch.status = '1';
          break;
        case 'DESIGNER':
          statisJumpSearch.currentPersonIsEmpty = 1;
          statisJumpSearch.status = '2';
          break;
        case 'OQC':
          statisJumpSearch.currentPersonIsEmpty = 1;
          statisJumpSearch.status = '6';
          break;
        case 'warning':
          statisJumpSearch.warning = 1;
          break;
        case 'timeout':
          statisJumpSearch.timeOut = 1;
          break;
        case 'freeCharge':
          statisJumpSearch.inStatistics = 0;// 申请免单跳转需要重置为0
          statisJumpSearch.status = '10';
          break;
        case 'outboundReturnOrderCount':
          statisJumpSearch.returnOrderType = '1';
          break;
        case 'internalReturnOrderCount':
          statisJumpSearch.returnOrderType = '2';
          break;
        default:
          break;
      }
      this.$store.dispatch('setJumpSearch', statisJumpSearch);
      this.$router.push({ name: ROUTE_NAME.ORDER_LIST, query: { isFrom: 'databoard' } });
    },
  },
};
</script>

<style lang="scss" scoped>
.admin-board {
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: calc(100vh - 60px - 24px - 24px);

  .title {
    color: $hg-secondary-primary;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .type-content {
    margin-bottom: 22px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .type-box {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 24px;
      border-radius: 4px;
      margin-right: 24px;
      margin-top: 12px;
      min-width: 220px;
      height: 180px;
      width: calc(20% - 20px);
      &:last-child {
        margin-right: 0;
      }
      .type-title {
        padding-bottom: 12px;
        color: #F3F5F7;
        font-weight: bold;
        font-size: 24px;
        text-align: left;
      }
      .type-num {
        padding: 4px 0;
        color: #F3F5F7;
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        cursor: pointer;
      }
      .type-totle {
        padding: 4px 0;
        color: #F3F5F7;
        font-size: 14px;
        line-height: 20px;
        text-align: left;
      }
      .type-img {
        position: absolute;
        width: 127px;
        height: 136px;
        bottom: 0;
        right: 0;
      }
    }
    .handel-box-left {
      flex: 1;
      border-radius: 4px;
      background: #1d1d1f;
      padding: 28px 0;
      width: 100%;
      margin-right: 24px;
      margin-top: 12px;
      display: flex;
      .handel-box {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-right: 1px solid #2d2f33;
        cursor: pointer;
        &:last-child {
          border-right: 0;
        }
        .box1 {
          color: #f7f8fa;
          font-weight: bold;
        }
        .box2 {
          color: #9EA2A8;
          font-size: 14px;
          margin-top: 16px;
        }
      }
    }
    .handel-box-right {
      display: flex;
      margin-top: 12px;
      border-radius: 4px;
      background: #1d1d1f;
      width: 420px;
      height: 136px;
      padding: 24px;
      // flex-direction: column;
      .warming-order {
        color: #ffb22c;
        font-weight: bold;
        font-size: 16px;
        line-height: 24px;
        cursor: pointer;
      }
      .over-order {
        color: #ff5a5a;
        font-weight: bold;
        font-size: 16px;
        line-height: 24px;
        // margin-top: 24px;
        cursor: pointer;
      }
      .handel-box {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-right: 1px solid #2d2f33;
        cursor: pointer;
        &:last-child {
          border-right: 0;
        }
        .box1 {
          // color: #f7f8fa;
          font-size: 14px;
          font-weight: bold;
        }
        .box2 {
          color: #9EA2A8;
          font-size: 14px;
          margin-top: 16px;
        }
      }
    }
    .other-box {
      margin-top: 12px;
      width: 100%;
      display: flex;
      .normal-box {
        flex: 1;
        min-width: 100px;
        min-height: 128px;
        display: flex;
        flex-direction: column;
        padding: 24px;
        background: #1d1d1f;
        border-radius: 4px;
        position: relative;
        margin-right: 24px;
        background-image: url('../../../assets/images/databoard/icon-arrow-lab.svg');
        background-repeat: no-repeat;
        background-position: bottom right;

        &:last-child {
          margin-right: 0;
        }
        .box1 {
          color: #9EA2A8;
          font-weight: bold;
        }
        .box2 {
          margin-top: 16px;
          color: #fff;
          font-size: 24px;
          font-weight: bold;
        }
        .box3 {
          color: #ff5a5a;
          font-weight: bold;
          font-size: 24px;
          margin-top: 16px;
        }
        .box4 {
          display: inline-block;
          z-index: 1;
          margin-top: 16px;
          color: #9EA2A8;
          span {
            color: #ff5a5a;
          }
          .top-icon {
            color: #ff5a5a;
            margin: 0 4px;
          }
        }
        .special-box{
          margin-top: 0;
          float: right;
        }
        .jump {
          cursor: pointer;
          span{
            color: #9EA2A8;
          }
          
        }
        .arrow-img {
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
      .is-visible{
        visibility: hidden;
      }
    }
    .other-box-second{
      margin-top: 0;
    }
  }
}
</style>
