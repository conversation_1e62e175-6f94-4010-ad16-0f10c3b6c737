export default {
  designpoints: {
    skillLevel: 'Skill Level',
    designers: 'Please select designer(s).',
    designError: 'Please make sure selected accounts are all [Designers]. Only designer’s skill level can be defined.',
    configSuccess: 'Defined successfully!',
    configDedine: 'Define Skill Level',
    tips: 'Please select.',
    uploadError: 'Please upload files in XLSL format.',
    
    basic: 'Basic Config.',
    level: 'lv',
    weight: 'Weighting of other factors',
    danwei: 'pts p.d.',
    designCodeCofig: 'Design Application Config.',  
    enterSku: 'Search with application or SKU.',
    btnEdit: 'Edit',
    number: 'No.',
    designCode: 'Design application',
    skuCode: 'SKU',
    time: 'Turnaround',
    points: 'Points',
    hours: 'Hours',
    noEmpty: 'Cannot be blank',
    value: 'Must be a positive integer ≤ 100.',
    discard: 'Configurations unsaved. Exiting will discard them.',
    cancel: 'Cancel',
    save: 'Save',
    exit: 'Exit',
    exportPoint: 'Import', 
    exportAgain: 'Import again',
    submitBtn: 'Confirm', 
    pointImport: 'Drag or click to import the table of points for dental application',
    pointdown: 'Download a template ',
    Imported: 'Imported',
    failed: 'Failed',

    date: 'Month',
    staticsBtn: 'Calculate',
    nodata: 'No point stats for this month yet. Please hit ',
    nodataBtn: 'Calculate.',
    pointsSta: 'Point Stats',
    monthDate: 'Days this month',
    middleDay: 'Day shift',
    nightDay: 'Night shift',
    day: 'days',
    dragExcel: 'Drag or click to import the shift plan for this month.',
    downloadMonth: 'Download the template',
    account: 'Account',
    desinger: 'Designer',
    middle: 'Day-shift days',
    night: 'Night-shift days',
    result: 'Result',
    creat: 'Calculate',
    monthStard: 'Standard pts',
    monthAll: 'Object pts | Finish pts',
    overPoints: 'Exceeding pts',
    complete: 'Finish rate',
    overTime: 'Delayed',
    back: 'Returned',
    free: 'Rejected',
    operate: 'Operations',
    systemTips: 'System hint',
    designGroup: 'Design group',
    all: 'All',
    designPlaceholder: 'Please enter account or name.',
    allocation: 'Allocate',
    againCount: 'Re-Calculate',
    fabu: 'Release',
    backAll: 'Retract all',
    backtips: 'Designers will not be able to check their stats once retracting all reports. Sure to retract?',
    export: 'Export ',
    isFabu: 'The stats report cannot be edited once released. Sure to release it?',
    orderNo: 'Order',
    orderNoHolder: 'Please enter order ID.',
    search: 'Search',
    orgsn: 'Client ID',
    groupqc: 'Group QC',
    point: 'points',
    yes: 'Yes',
    no: 'No',
    delete: 'Delete ',
    noSearch: 'Cannot find the order. You may add a record below.',
    addbtn: 'Add',
    addtips: 'This new Point Allocation record will be generated once added. Sure to add it?',
    addError: 'Failed to allocate.',
    addSuccess: 'Point allocation has been updated!',
    scanPoint: 'Point Details',
    board: 'Point Details',
    moreOrder: 'Multiple orders found. Please select the client.',
    overSix: 'The report cannot be retracted, time limit exceeded.',
    istuichu: 'Closing will not save the information. Sure to close?',
    pleaseInput: 'Please enter',
    noOrgNo: 'Cannot find the client ID.',

    pointDetail: 'Point Details',
    mypoints: 'My points',
    statics: 'Performance',
    monthPoints: 'Object points',
    monthComplete: 'Finish points',
    groupPaiming: 'Group ratings',
    backOrder: 'Returned',
    freeOrder: 'Rejected',
    overOrder: 'Overtime',
    monthStatics: 'Points',
    completeChange: 'Finish rates',
    designfenbu: 'Order composition by design appliance',
    designNum: 'Design appliance counts',
    scanDetail: 'Details',
    commonissu: 'Common issues',

    released: 'Released successfully!',
    restacted: 'Retracted successfully!',
    downLoadWait: 'Downloading. Please be patient ...',
    stats: 'Designer Point Stats',
    dental:'Points for Dental Application_Template',
    blank: 'Required fields cannot be blank.',
    allDesign: 'All applications',
    assignAll: 'Please assign all orders first.',
    intNum: 'Must be a positive integer.',
    allpoint: 'Please enter all the points.',
    template: 'Monthly Shift Plan_Template',
    zhengshu: 'Must be a positive number.',
    pointsExcel: 'Point Details',
    limitTips: 'Only .xlsx format files can be imported.',
    deleteTips: 'Delete successfully!',
    creatSuccess: 'Calculation finished!',

    pieOther: 'Other'
  }
}