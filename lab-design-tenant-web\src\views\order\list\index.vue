<template>
  <div class="order-list">
    <!-- 头部搜索栏 -->
    <div class="header-search">
      <order-filter @searchList="getSearch" :statusList="statusList" :searchList="searchData" :isExpand="isExpand"></order-filter>
    </div>
    <!-- 按钮组 -->
    <div class="btn-group" v-permission="permissionList">
      <hg-button
        type="primary"
        :disabled="selectSelection.length == 0 || button.isDisable"
        v-permission="button.permissionName"
        popper-class="order-list-btn"
        v-for="(button, index) in buttonType"
        :key="index"
        :loading="button.loading"
        @click="selectFileHandle(button)"
        :icon="`iconfont-lab ${button.iconName}`"
      >
        <!-- <hg-icon :icon-name="button.iconName"></hg-icon> -->
        <p class="btn">{{ $t(button.name) }}</p>
      </hg-button>
      <hg-button type="primary" popper-class="order-list-btn" v-permission="['exportOrderAdditionStatus']" @click="exportOrderAdditionStatus">{{$t('orderList.btnList.newOrderStatus')}}</hg-button>
      <!-- <span class="expangIcon" @click="expangSearch"><hg-icon style="font-size: 24px;" :icon-name="!isExpand ? 'icon-icon_double_arrow_up' : 'icon-icon_double_arrow_down'"></hg-icon></span> -->
    </div>
    <!-- 列表 -->
    <div class="depart-table">
      <hg-table
        :header-data="headerData"
        :height="'auto'"
        :needSelect="true"
        :data="tableData"
        :defaultSort="defaultSort"
        :loading="tableLoading"
        @sortChange="sortTable"
        @update-selected-rows="selectTable"
        @enter-detail="openOrderDetails"
      >
        <!-- 订单编号 -->
        <template #orderNo="scope">
          <el-tooltip popper-class="order-no-popper" :disabled="scope.row.orderNo.length <= 17" :content="scope.row.orderNo" placement="top">
            <span class="order-no">
              <img v-show="scope.row.isExpedited && ![8, 11].includes(scope.row.status)" class="jiaji" src="@/assets/images/order/badge_ASAP.png">
              <span style="white-space: pre;">{{handleOrderNumber(scope.row.orderNo)}}</span>
            </span>
          </el-tooltip>
        </template>
        <!-- 用户编码 -->
        <template #orgName="scope">
          <span v-if="scope.row.dept && scope.row.dept != '--'">{{ scope.row.orgName }}（{{scope.row.dept}}）</span>
          <span v-else>{{ scope.row.orgName }}</span>
        </template>
        <!-- 创建时间 -->
        <template #createdTime="scope">
          <span>{{ scope.row.createdTime | dateFormatInHtml }}</span>
        </template>
        <!-- 完成时间 -->
        <template #completeTime="scope">
          <span v-if="scope.row.status == 8 || scope.row.status == 11">{{ scope.row.completeTime | dateFormatInHtml }}</span>
          <span v-else>--</span>
        </template>
        <!-- 倒计时 -->
        <template #timeCost="scope">
          <CountDown :completeTime="scope.row.completeTime" :createdTime="scope.row.createdTime" :stamp="scope.row.deliveryTime" :orderState="scope.row.status"> </CountDown>
        </template>
        <!-- 订单状态 -->
        <template #status="scope">
          <span :class="['table-content-status-name', scope.row.statusClass]">
            {{ $t(`design_tenant_order_status.${scope.row.statusName.en}`) }}
          </span>
        </template>
        <!-- 当前处理人 -->
        <template #designBy="scope">
          <span>{{ scope.row.designBy || $t('orderList.order.unknown') }}</span>
        </template>
        <!-- 设计类型 -->
        <template #designTypeCodes="scope">
          <!-- <span>{{ scope.row.designeName ? $getI18nText(scope.row.designeName) : '' }}</span> -->
          <span>{{scope.row.toothInfoStr || $t('orderList.order.unknownorder')}}</span>
        </template>
        <!-- 设计软件 -->
        <template #designSoftware="scope">
          <span class="order-no">
            <img v-if="scope.row.designSoftwareCode == 1001" src="@/assets/images/order/icon_3Shape.png">
            <img v-else-if="scope.row.designSoftwareCode == 1002" src="@/assets/images/order/icon_exocad.png">
            {{scope.row.designSoft || $t('orderList.order.unknownorder')}}
          </span>
        </template>
        <!-- 订单类型 -->
        <template #designCategory="scope">
          <div :class="{'table-order-type': true, 'is-union': scope.row.isUnion}">
            <span> {{ scope.row.designCategory }} </span>
          </div>
        </template>
      </hg-table>
    </div>
    <div class="depart-pagination">
      <pagination showTotal :total="page.total" :disabled="tableLoading" :initPageIndex="page.pageNo" :initPageSize="page.pageSize" @onSearch="search"></pagination>
    </div>
    <!-- 批量操作的弹窗 -->
    <batch-dialog ref="batchDialog" :assignType="assignType" :selectSelection="selectSelection" @handelbatchList="handelbatchList"></batch-dialog>
    <!-- 由我审核和由我译单弹窗 -->
    <el-dialog :title="$t('orderList.btnList.examine')" :visible.sync="flag.examine" width="500px">
      <span>{{ $t('orderList.btnList.submitExamine') }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="flag.examine = false">{{ $t('common.btn.cancel')}}</el-button>
        <el-button type="primary" @click="batchOperateAll('examine')">{{ $t('common.btn.confirm')}}</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="$t('orderList.btnList.translate')" :visible.sync="flag.translate" width="500px">
      <span>{{ $t('orderList.btnList.submitTranslate') }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="flag.translate = false">{{ $t('common.btn.cancel')}}</el-button>
        <el-button type="primary" @click="batchOperateAll('translate')">{{ $t('common.btn.confirm')}}</el-button>
      </span>
    </el-dialog>

    <!-- 联合指派侧边栏 -->
    <batchLeftDrawer :drawer.sync="drawer" :selectSelection="selectSelection" @batchSuccessUnion="batchSuccessUnion"></batchLeftDrawer>

    <!-- 导出订单新增情况时间选择弹窗 -->
    <el-dialog :title="$t('orderList.btnList.newOrderStatus')" :visible.sync="flag.exportOrderStatus" custom-class="export-order-status" width="500px" :before-close="closeExportStatusDialog">
      <div class="status-content">
        <p class="title">{{$t('orderList.btnList.orderTime')}}</p>
        <date-range-picker valueFormat="timestamp" :pickerOptions="pickerOptions" v-model="erportTime"></date-range-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closedialog">{{ $t('common.btn.cancel')}}</el-button>
        <el-button type="primary" :disabled="disabledExport" @click="exportStatusExcel()" :loading="notAllowClose">{{ $t('common.btn.confirm')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import orderFilter from './components/orderFilter';
import DateRangePicker from '@/components/DateRangePicker';
import hgTable from '@/components/HgTable';
import CountDown from '@/components/CountDown';
import batchDialog from './components/batchDialog';
import pagination from '@/components/Pagination';
import batchLeftDrawer from './components/batchLeftDrawer'
import { ORDER_TYPES, ROUTE_NAME, ROLE_CODE, UNION_TYPE_CODE } from '@/public/constants';
import { getOrderList, batchOperate, batchExamineByMe, getBatchDownloadUrl, exportNewOrder } from '@/api/order';
import { startToDesign } from '@/api/order/operate';
import { getOrderStatus } from '@/api/common';
import { getDownloadUrl } from '@/api/file';
import { directDown, createIFrameDownLoad, createIFrameDownLoad2 } from '@/public/utils/file';
import { getTypeName } from '@/public/utils';
import { mapGetters } from 'vuex';
import { getToothInfo } from '@/public/utils/order';

export default {
  name: 'orderList',
  components: { DateRangePicker, orderFilter, hgTable, CountDown, batchDialog, pagination, batchLeftDrawer },
  data() {
    return {
      permissionList: ['batchDownload', 'batchAssignDesigner', 'batchAssignIQC', 'batchAssignOQC', 'batchExamineByMe', 'batchTranslateByMe'],
      buttonType: [
        { name: 'orderList.btnList.downFiles', loading: false, type: 'download', iconName: 'icon-download-lab', permissionName: ['batchDownload'] },
        {
          name: 'orderList.btnList.batchAll',
          isDisable: false,
          type: 'assign',
          iconName: 'icon-btn-batch-assign',
          permissionName: ['batchAssignDesigner', 'batchAssignIQC', 'batchAssignOQC'],
        },
        { name: 'orderList.btnList.translate', type: 'batchTranslate', iconName: 'icon-btn-batch-translate', permissionName: ['batchTranslate'] },
        { name: 'orderList.btnList.examine', type: 'examine', iconName: 'icon-btn-batch-examine', permissionName: ['batchExamineByMe'] },
        { name: 'orderList.btnList.translateByMe', type: 'translate', iconName: 'icon-btn-batch-translateByMe', permissionName: ['batchTranslateByMe'] },
      ],
      assignType: null, //批量操作的类型 IQC,DESIGNER,OQC
      //搜索条件
      searchData: {
        selectInput: '',
        keywords: '',
        deptCodes: [],
        currentProcessor: 0,
        status: '',
        designTypeCodes: [], //类型
        startTime: 0, //创建时间
        endTime: 0,
        completeStartTime: 0, //完成时间
        completeEndTime: 0,
        designCodesAll: [],
        designCategoryCode: '', //一级类
        returnOrderType: '',//内外返订单，1是外返，2是内返
        designSoftware: '', // 设计软件
      },
      setStatisJumpSearch: {},
      tableData: [],
      selectSelection: [], //选择的row
      // 订单状态 1: 待译单; 2: 待指派; 3: 待设计; 4: 设计中; 5: 待退回; 6: 待审核; 7: 待确认; 8: 已完成; 9: 已退回; 10：审核中；11：已免单（2023、6、19新增10和11）
      statusList: [],
      flag: {
        examine: false, //由我检查
        translate: false, //由我译单
        exportOrderStatus: false, // 导出新增订单情况
      },
      tableLoading: false, //页面loading
      // 排序
      sortObj: {
        asc: true,
        sortField: '',
      },
      defaultSort: {},
      page: {
        pageSize: 10,
        pageNo: 1,
        total: 0,
      },
      batchList: [], //批量处理选中的列表
      isExpand: false, //搜索列表展开收起
      drawer: false, // 联合指派弹窗
      erportTime: [0, 0],
      notAllowClose: false,
      pickerOptions: {
        disabledDate(time) {
          const now = new Date();
          const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59); // 今天的23:59:59
          const oneMonthAgo = new Date(todayEnd);
          oneMonthAgo.setMonth(todayEnd.getMonth() - 1);

          // 禁用大于今天的23:59:59和小于今天一个月的日期
          return time.getTime() > todayEnd.getTime() || time.getTime() < oneMonthAgo.getTime();
        }
      }

    };
  },
  beforeRouteLeave(to, from, next) {
    let searchList = this.searchData;
    if (to.path !== '/order/detail') {
      searchList = {
        selectInput: '',
        keywords: '',
        deptCodes: [],
        currentProcessor: 0,
        status: '',
        designTypeCodes: [], //类型
        startTime: 0, //创建时间
        endTime: 0,
        completeStartTime: 0, //完成时间
        completeEndTime: 0,
        designCodesAll: [],
        designCategoryCode: '', //一级类
        returnOrderType: '', //内外返订单，1是外返，2是内返
        designSoftwar: ''
      };
    }
    const { pageNo, pageSize } = this.page;
    const { asc, sortField } = this.sortObj;
    this.$store.dispatch(
      'changeSearchListObj',
      Object.assign({}, searchList, {
        pageNo,
        pageSize,
        asc,
        sortField,
      })
    );
    next();
  },
  // 路由前置守卫
  beforeRouteEnter(to, from, next) {
    // next是在mounted之后执行，因此不在create 或者mounted 初始化列表数据
    next((vm) => {
      //刷新时清楚看板
      if (from.path != '/databoard' && vm.$route.query.isFrom) {
        vm.$router.push({ path: '/order' });
        vm.$store.dispatch('setJumpSearch', {});
      }
      let pageData;
      if (from.path == '/order/detail') {
        const { keywords, deptCodes, currentProcessor, status, designTypeCodes, startTime, endTime, completeStartTime, completeEndTime, designCodesAll, 
          pageNo, pageSize, asc, sortField, designCategoryCode, returnOrderType, designSoftware, selectInput } = vm.searchListObj;
        vm.searchData = {
          selectInput,
          keywords,
          deptCodes,
          currentProcessor,
          status,
          designTypeCodes, //类型
          startTime, //创建时间
          endTime,
          completeStartTime,
          completeEndTime,
          designCodesAll,
          designCategoryCode, //一级类
          returnOrderType,
          designSoftware
        };
        vm.page.pageSize = pageSize;
        vm.page.pageNo = pageNo;
        vm.sortObj.asc = asc;
        vm.sortObj.sortField = sortField;
        pageData = {};
        pageData.pageSize = pageSize;
        pageData.pageIndex = pageNo;
        let sortType = '';
        if (sortField) {
          sortType = asc == true ? 'ascending' : 'descending';
        }
        vm.defaultSort.prop = sortField;
        vm.defaultSort.order = sortType;
      }
      if (from.path == '/databoard' && vm.$route.query.isFrom) {
        const {
          selectInput,
          keywords,
          deptCodes,
          currentProcessor,
          status,
          designTypeCodes,
          startTime,
          endTime,
          completeStartTime,
          completeEndTime,
          designCodesAll,
          inComplete,
          inStatistics,
          currentPersonIsEmpty,
          timeOut,
          warning,
          designCategoryCode,
          returnOrderType,
          designSoftware
        } = vm.statisJumpSearch;
        //当数据看板跳转过来保存该数据以作搜索
        vm.setStatisJumpSearch = {
          selectInput,
          keywords,
          deptCodes,
          currentProcessor,
          status,
          designTypeCodes, //类型
          startTime, //创建时间
          endTime,
          completeStartTime,
          completeEndTime,
          designCodesAll,
          inComplete,
          inStatistics,
          currentPersonIsEmpty,
          timeOut,
          warning,
          designCategoryCode,
          returnOrderType,
          designSoftware
        };
        //这两个值是用作回显
        vm.searchData.designTypeCodes = designTypeCodes;
        vm.searchData.designCodesAll = designCodesAll;
        vm.searchData.status = status;
        vm.searchData.returnOrderType = returnOrderType;
      }
      const keywords = vm.$route.query.orderNo; // 顶部搜索跳转
      if (keywords) {
        vm.searchData = {
          selectInput: '',
          keywords: keywords,
          deptCodes: [],
          currentProcessor: 0,
          status: '',
          designTypeCodes: [], //类型
          startTime: 0, //创建时间
          endTime: 0,
          completeStartTime: 0, //完成时间
          completeEndTime: 0,
          designCodesAll: [],
          designCategoryCode: '',
          returnOrderType: '', //内外返订单，1是外返，2是内返
          designSoftware: ''
        };
      }
      vm.getOrderStatus('page', pageData);
    });
  },
  watch: {
    '$route.query.orderNo'(value) {
      const keywords = value; // 顶部搜索跳转
      if (keywords) {
        this.searchData = {
          selectInput: '',
          keywords: keywords,
          deptCodes: [],
          currentProcessor: 0,
          status: '',
          designTypeCodes: [], //类型
          startTime: 0, //创建时间
          endTime: 0,
          completeStartTime: 0, //完成时间
          completeEndTime: 0,
          designCodesAll: [],
          designCategoryCode: '',
          returnOrderType: '', //内外返订单，1是外返，2是内返
          designSoftware: ''
        };
        this.page.pageSize = 10;
        this.page.pageNo = 1;
      }
      this.getOrderStatus();
    },
    '$route.query.isFrom'(value) {
      if (!value) {
        this.setStatisJumpSearch = {
          selectInput: '',
          keywords: '',
          deptCodes: [],
          currentProcessor: 0,
          status: '',
          designTypeCodes: [], //类型
          startTime: 0, //创建时间
          endTime: 0,
          completeStartTime: 0, //完成时间
          completeEndTime: 0,
          designCodesAll: [],
          inComplete: null,
          inStatistics: null,
          currentPersonIsEmpty: null,
          timeOut: null,
          warning: null,
          designCategoryCode: '',
          returnOrderType: '',//内外返订单，1是外返，2是内返
          designSoftware: ''
        };
        this.page.pageSize = 10;
        this.page.pageNo = 1;
      }
      this.getOrderStatus();
    },
  },
  mounted() {
    // this.getOrderStatus();
  },
  computed: {
    ...mapGetters(['language', 'designTypeTree', 'roles', 'oneDesignList', 'searchListObj', 'userCode', 'statisJumpSearch']),
    headerData() {
      return [
        {
          prop: 'orderNo',
          width: '180px',
          noTip: true,
          getLabel: () => {
            return this.$t('orderList.order.orderNo');
          },
        },
        {
          prop: 'fileName',
          minWidth: '60px',
          noTip: false,
          getLabel: () => {
            return this.$t('orderList.order.orderfiles');
          },
        },
        {
          prop: 'orgName',
          minWidth: '70px',
          sortable: 'custom',
          getLabel: () => {
            return this.$t('orderList.order.orgName');
          },
        },{
          prop: 'designCategory',
          minWidth: '75px',
          getLabel: () => {
            return this.$t('orderList.order.orderType');
          },
        },
        {
          prop: 'designTypeCodes',
          minWidth: '80px',
          getLabel: () => {
            return this.$t('orderList.order.designTypeCodes');
          },
        },
        {
          prop: 'designSoftware',
          minWidth: '60px',
          noTip: false,
          getLabel: () => {
            return this.$t('order.detail.info.software');
          },
        },
        {
          prop: 'designBy',
          minWidth: '50px',
          sortable: 'custom',
          getLabel: () => {
            return this.$t('orderList.order.designBy');
          },
        },
        {
          prop: 'createdTime',
          minWidth: '70px',
          sortable: 'custom',
          getLabel: () => {
            return this.$t('orderList.searchList.creatTime');
          },
        },
        {
          prop: 'timeCost',
          minWidth: '60px',
          getLabel: () => {
            return this.$t('orderList.order.timeCost');
          },
        },
        // {
        //   prop: 'completeTime',
        //   minWidth: '60px',
        //   sortable: 'custom',
        //   align: 'center',
        //   getLabel: () => {
        //     return this.$t('orderList.searchList.finishTime');
        //   },
        // },
        {
          prop: 'status',
          minWidth: '60px',
          noTip: true,
          sortable: 'custom',
          getLabel: () => {
            return this.$t('orderList.searchList.statusType');
          },
        },
      ];
    },
    roleCodeList() {
      return this.roles.map((role) => role.roleCode);
    },
    disabledExport(){
      return !this.erportTime[0] && !this.erportTime[1]
    }
  },
  methods: {
    // 获取订单状态
    async getOrderStatus(type,page) {
      try {
        const { data } = await getOrderStatus();
        this.statusList = data;
        await this.search(type,page);
      } catch (error) {
        console.log('订单状态出错：', error);
        await this.search(type,page);
      }
    },
    selectFileHandle(button) {
      if (button.type === 'download') {
        // this.getDownloadUrl(button);
        this.getBatchDownFile(button)
      } else if (button.type === 'assign') {
        // 批量指派时需要判断选择的列状态
        if (this.judegeStatus('batch')) {
          if(this.assignType == 'DESIGNER'){
            console.log('this.selectSelection: ', this.selectSelection);
            let haveUnionList = this.selectSelection.filter(item => item.isUnion === 1);
            if(haveUnionList.length === this.selectSelection.length){
              this.drawer = true;
            } else if (haveUnionList.length == 0) {
              this.$refs.batchDialog.batchPeopleDialog = true;
            } else {
              this.showSelectTypeDialog(12);
            }
          } else {
            this.$refs.batchDialog.batchPeopleDialog = true;
          }
        }
      } else if (button.type === 'examine') {
        if (this.judegeStatus('examine')) {
          this.showSelectTypeDialog(5);
        }
      } else if (button.type === 'translate') {
        if (this.judegeStatus('translate')) {
          this.showSelectTypeDialog(6);
        }
      } else if(button.type === 'batchTranslate') {
        if (this.judegeStatus('batchTranslate')) {
          console.log('条件符合');
          this.handleBatchTranslate();
        }
      }
    },
    // 判断选择的行是否一致状态 batch 批量指派 examine 由我检查 translate由我译单
    judegeStatus(type) {
      // 没选中列表不允许通过
      if (this.selectSelection && this.selectSelection.length == 0) {
        this.showSelectTypeDialog(7);
        return false;
      }
      // 判断状态
      const everyHandelStatus = (status) => {
        return this.selectSelection.every((item) => {
          return item.status === status;
        });
      };
      // 判断处理人是否为空
      const everyHandelEmpty = (name) => {
        return this.selectSelection.some((item) => {
          return !!item[name];
        });
      };
      const everyHandlerIsMe = () => {
        return this.selectSelection.every((item) => {
          return item.iqc === this.userCode;
        });
      };
      // 批量指派弹窗判断
      if (type == 'batch') {
        // 当选择的row的status全部为1时，打开IQC status==2,3,4,待指派，待设计，设计中打开设计弹窗 status == 6 待审核打开Oqc弹窗
        // status 为1时，还需要判断当前角色，除了管理员/设计运营打开的是指派IQC；仅有IQC，待译单是不能指派的
        if (everyHandelStatus(1)) {
          if (this.roleCodeList.includes(ROLE_CODE.ADMIN) || this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) || this.roleCodeList.includes(ROLE_CODE.SYSTEM_OPER)) {
            this.assignType = 'IQC';
            return true;
          } else {
            this.showSelectTypeDialog(11);
            return false;
          }
        } else if (everyHandelStatus(3) || everyHandelStatus(2) || everyHandelStatus(4)) {
          // 待指派 待设计 设计中 iqcUser是本人才能指派设计师
          if (this.roleCodeList.includes(ROLE_CODE.IQC) && everyHandlerIsMe()) {
            this.assignType = 'DESIGNER';
            return true;
          }
          if (this.roleCodeList.includes(ROLE_CODE.DESIGN_LEADER)) {
            //设计师组长能查询出来的订单都是可以操作的
            this.assignType = 'DESIGNER';
            return true;
          }

          // 管理员 设计运营
          if(this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) || this.roleCodeList.includes(ROLE_CODE.ADMIN) || this.roleCodeList.includes(ROLE_CODE.SYSTEM_OPER)) {
            this.assignType = 'DESIGNER';
            return true;
          }

          this.showSelectTypeDialog(10);
          return false;
        } else if (everyHandelStatus(6)) {
          this.assignType = 'OQC';
          return true;
        } else {
          this.showSelectTypeDialog(4);
          return false;
        }
      }
      // 由我审核弹窗判断 所选订单为待审核的单且负责人为空
      if (type == 'examine') {
        if (!everyHandelStatus(6) || everyHandelEmpty('designBy')) {
          this.showSelectTypeDialog(9);
          return false;
        }
      }

      //由我译单判断 所选订单为待译单的单且负责人为空
      if (type == 'translate') {
        if (!everyHandelStatus(1) || everyHandelEmpty('designBy')) {
          this.showSelectTypeDialog(8);
          return false;
        }
      }
      return true;
    },
    // 选择按钮报错提示处理
    showSelectTypeDialog(type) {
      const errorTips = (tpe, mes) => {
        return this.$message({
          type: tpe,
          message: mes,
        });
      };
      //1：有未知，不能批量译单 2：必须全部为待译单 3：必须全部为待审核 4：批量操作必须状态一致 5：由我审核弹窗 6：由我译单弹窗 7未选择指派订单 8由我译单 9由我审核
      switch (type) {
        case 1:
          errorTips('warning', this.$t('orderList.btnList.noDesign'));
          break;
        case 2:
          errorTips('warning', this.$t('orderList.btnList.allTranslate'));
          break;
        case 3:
          errorTips('warning', this.$t('orderList.btnList.allExamine'));
          break;
        case 4:
          errorTips('warning', this.$t('orderList.btnList.allOrder'));
          break;
        case 5:
          this.flag.examine = true;
          break;
        case 6:
          this.flag.translate = true;
          break;
        case 7:
          errorTips('warning', this.$t('orderList.btnList.selectOrder'));
          break;
        case 8:
          errorTips('warning', this.$t('orderList.btnList.translateUser'));
          break;
        case 9:
          errorTips('warning', this.$t('orderList.btnList.examineUser'));
          break;
        case 10:
          errorTips('warning', this.$t('orderList.btnList.IQCowne'));
          break;
        case 11:
          errorTips('warning', this.$t('orderList.btnList.noCaozuo'));
          break;
        case 12:
          errorTips('warning', '该操作仅支持所选订单类型同为联合修复，或者不包含联合修复。');
          break;
        default:
          break;
      }
    },
    //搜索栏
    getSearch(value) {
      this.searchData = Object.assign({}, value);
      this.search();
    },
    // 列表排序
    sortTable(row) {
      this.sortObj.asc = row.order === 'descending' ? false : true;
      this.sortObj.sortField = row.prop ? row.prop : '';
      this.search();
    },
    // 其他需要回到第一页的，不需要重置页码
    async search(type, searchData) {
      if(searchData) {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
      }else {
        this.page.pageNo = 1;
        // this.page.pageSize = 10;
      }
      /* if (type == 'page') {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
      } else if (type != 'down' && type != 'page') {
        this.page.pageNo = 1;
        this.page.pageSize = 10;
      } */
      let searchArr = {};
      if (this.$route.query.isFrom) {
        searchArr = this.setStatisJumpSearch;
      } else {
        searchArr = this.searchData;
      }
      const {
        selectInput,
        keywords,
        deptCodes,
        currentProcessor,
        status,
        designTypeCodes,
        startTime,
        endTime,
        completeStartTime,
        completeEndTime,
        inComplete,
        inStatistics,
        currentPersonIsEmpty,
        timeOut,
        warning,
        designCategoryCode,
        returnOrderType,
        designSoftware
      } = searchArr;
      const { pageSize, pageNo } = this.page;

      const searchCateList = designCategoryCode?.split(',') || [];
      let searchDesignCategory = designCategoryCode;
      let designTypeList = designTypeCodes; 
      let isUnion = 0;
      const hasUnion = searchCateList.includes(String(UNION_TYPE_CODE));
      if(hasUnion) {
        isUnion = 1;
        const exceptUnion = searchCateList.filter(code => Number(code) !== UNION_TYPE_CODE);
        searchDesignCategory = exceptUnion.join();
        designTypeList = designTypeCodes.filter(code => Number(code) !== UNION_TYPE_CODE);
      }

      // 类型需要用逗号隔开
      let designType = '';
      designTypeList.forEach((item) => {
        designType += item + ',';
      });
      designType = designType.slice(0, designType.length - 1);
      let data = {
        completeEndTime: completeEndTime,
        completeStartTime: completeStartTime,
        designTypeCodes: inStatistics == 1 ? '' : designType, //数据看板跳转这个直接传空
        endTime: endTime,
        keywords: keywords,
        deptCodes: deptCodes,
        currentProcessor: currentProcessor ? currentProcessor : 0,
        pageNo: pageNo,
        pageSize: pageSize,
        asc: this.sortObj.asc,
        sortField: this.sortObj.sortField,
        startTime: startTime,
        status: status ? status : 0,
        inComplete: inComplete || inComplete == 0 ? inComplete : null,
        inStatistics: inStatistics || inStatistics == 0 ? inStatistics : null,
        currentPersonIsEmpty: currentPersonIsEmpty || currentPersonIsEmpty == 0 ? currentPersonIsEmpty : null,
        timeOut: timeOut || timeOut == 0 ? timeOut : null,
        warning: warning || warning == 0 ? warning : null,
        designCategoryCode: searchDesignCategory || '',
        externalReturn: returnOrderType == 1 ? 1 : -1,//外返订单
        internalReturn: returnOrderType == 2 ? 1 : -1, //内返订单
        isUnion,
        designSoftware
      };
      this.orderList(data);
    },
    // 获取列表数据
    orderList(data) {
      this.tableLoading = true;
      this.selectSelection = [];
      getOrderList(data).then((res) => {
        let tableData = res.data.data ? res.data.data : [];
        this.page.total = res.data.totalSize ? res.data.totalSize : 0;
        this.page.pageNo = res.data.pageNo ? res.data.pageNo : 1;
        this.page.pageSize = res.data.pageSize ? res.data.pageSize : 1;
        this.tableData = this.handelStatus(tableData);
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    // 处理列表的状态显示
    handelStatus(list) {
      list.forEach((item) => {
        // 当item.statusName为null时
        if (item.statusName) {
          // 根据返回的statusName重新设置status，用来做后面的判断
          let newStatus = this.statusList.find((it) => {
            return item.statusName && (item.statusName.cn == it.cnName || item.statusName.en == it.enName);
          });
          if (newStatus) {
            item.status = Number(newStatus.statusCode);
          }
        }
        // 订单文件拿orderLists fileType=1第一项
        if (item.orderLists) {
          let file = item.orderLists.filter((item) => item.fileType === 1)[0];
          item.fileName = file && file.fileName ? file.fileName : item.fileName;
        }
        // 配置状态颜色
        if (ORDER_TYPES.DESIGNING === item.status) {
          item.statusClass = 'is-designing';
        } else if (ORDER_TYPES.PENDING_REVIEW === item.status || ORDER_TYPES.REQUEST_FREE === item.status) {
          item.statusClass = 'is-blue';
        } else if (ORDER_TYPES.PENDING_RETURN === item.status) {
          item.statusClass = 'is-unreback';
        } else if (ORDER_TYPES.COMPLETED === item.status || ORDER_TYPES.APPLY_FREE === item.status) {
          item.statusClass = 'is-finish';
        } else if (ORDER_TYPES.RETURNED === item.status) {
          item.statusClass = 'is-cancel';
        }
        // 处理当前的设计软件
        if(item.designSoftwareCode && item.softwareVersion){
          item.designSoft = this.$t(`design_software.${item.designSoftwareCode}`) + '(' + this.$t(`design_software.${item.softwareVersion}`) + ')'
        } else if(item.designSoftwareCode && !item.softwareVersion){
          item.designSoft = this.$t(`design_software.${item.designSoftwareCode}`)
        }
        // 处理当前的设计类型
        item.toothInfoStr = this.getToothDesign(item.toothDesign);
        //处理当前处理人
        item.designBy = this.getHandler(item.status, item);
        item.designeName = getTypeName(item.designTypeCodes, this.oneDesignList);

        const categoryList = item.designCategory.split(',');
        if(categoryList.length < 2) {
          const data = this.designTypeTree.find(dItem => dItem.designCode === Number(item.designCategory));
          if(data) {
            item.designCategory = this.$t(`apiCommon.${data.designCode}`);
          }
        }else {
          item.isUnion = 1;
          item.designCategory = this.$getI18nText({ zh: '联合修复', en: 'Combined Restorations' });
        }
      });
      return list;
    },
    // 获取当前的设计类型
    getToothDesign(toothDesign){
      let toothInfo = toothDesign && JSON.parse(toothDesign);
      toothInfo = getToothInfo(toothInfo) // 这一步是根据选择的通用符转换牙位号
      let toothInfoStr = '';
      toothInfo.forEach((it) => {
        if(it.children && it.children.length > 0){
          it.children.forEach((child) => {
            let name = this.$t(`apiCommon.${child.code}`);
            if(child.toothValue && child.code != 25001){
              toothInfoStr += name + '(' + child.toothValue + '),'
            } else {
              // 未知类型只要有一个就可以了
              if(this.language == 'zh' && toothInfoStr.indexOf('未知') == -1){
                toothInfoStr += name + ','
              } else if(this.language == 'en' && toothInfoStr.indexOf('Unknown') == -1){
                toothInfoStr += name + ','
              }
            }
          })
        }
        if([25001].includes(it.code)){
          let name = this.$t(`apiCommon.${it.code}`);
          // 未知类型只要有一个就可以了
          if(this.language == 'zh' && toothInfoStr.indexOf('未知') == -1){
            toothInfoStr += name + ','
          } else if(this.language == 'en' && toothInfoStr.indexOf('Unknown') == -1){
            toothInfoStr += name + ','
          }
        } else if([25002].includes(it.code)){
          let name = this.$t(`apiCommon.${it.code}`);
          toothInfoStr += name + ','
        }else {
          let name = this.$t(`apiCommon.${it.code}`);
          if(it.toothValue && it.toothValue.length > 0){
            toothInfoStr += name + '(' + it.toothValue + '),'
          } else {
            toothInfoStr += name + ','
          }
        }
      })
      toothInfoStr = toothInfoStr.slice(0, toothInfoStr.length - 1);
      return toothInfoStr
    },
    // 获取当前处理人
    getHandler(orderState, data) {
      // 待翻译、待退回
      if (orderState === ORDER_TYPES.PENDING_TRANSLATE || orderState === ORDER_TYPES.PENDING_RETURN) {
        return data.iqcUser;
      }
      // 待设计、设计中
      if (orderState === ORDER_TYPES.PENDING_DESIGN || orderState === ORDER_TYPES.DESIGNING) {
        return data.designUser;
      }
      // 待审核
      if (orderState === ORDER_TYPES.PENDING_REVIEW) {
        return data.oqcUser;
      }
      // 待确认、 已完成、已退回
      const clientType = [ORDER_TYPES.PENDING_CONFIRM, ORDER_TYPES.COMPLETED, ORDER_TYPES.RETURNED];
      if (clientType.indexOf(orderState) > -1) {
        return this.$t('orderList.order.dealWithClient');
      }

      // 审核中（2023/6/19新增申请免单流程）
      if (orderState === ORDER_TYPES.REQUEST_FREE) {
        return this.$t('orderList.order.DesignOperations');
      }
      // 已免单（2023/6/19新增申请免单流程）
      if (orderState === ORDER_TYPES.APPLY_FREE) {
        return this.$t('orderList.order.dealWithClient');
      }
    },
    // 选择的table row
    selectTable(length, selection) {
      this.selectSelection = selection;
      // 当角色是IQC 50033但不是系统管理员50018和设计运营50020，而且选择的列表有待审核订单但是没有管理员权限时需要置灰批量指派按钮
      this.$set(this.buttonType[1], 'isDisable', false);
      this.$set(this.buttonType[0], 'isDisable', false);
      const isHavePerssion = function(arr, codeName, code) {
        return arr.some((item) => {
          return item[codeName] == code;
        });
      };
      if (isHavePerssion(this.roles, 'roleCode', ROLE_CODE.IQC) 
        && !isHavePerssion(this.roles, 'roleCode', ROLE_CODE.ADMIN) 
        && !isHavePerssion(this.roles, 'roleCode', ROLE_CODE.SYSTEM_OPER)
        && !isHavePerssion(this.roles, 'roleCode', ROLE_CODE.DESIGN_OPERATE)) {
        if (isHavePerssion(this.selectSelection, 'status', 6)) {
          this.$set(this.buttonType[1], 'isDisable', true);
        }
      }
      //当角色不包含管理员和设计运营IQC，而列表选择的订单有待退回5和已退回9状态的订单，按钮置灰-- 这里明显有问题 roles是一个对象数组
      /* if (!this.roles.includes(ROLE_CODE.ADMIN) && !this.roles.includes(ROLE_CODE.DESIGN_OPERATE) && !this.roles.includes(ROLE_CODE.SYSTEM_OPER) && !this.roles.includes(ROLE_CODE.IQC)) {
        if (isHavePerssion(this.selectSelection, 'status', 5) || isHavePerssion(this.selectSelection, 'status', 9)) {
          this.$set(this.buttonType[0], 'isDisable', true);
        }
      } */
      // 更新逻辑说明：不包含[管理员][设计运营][系统运营][IQC]，且选中[已退回]或[待退回]订单，不能下载
      if(!this.roleCodeList.some(item => [ROLE_CODE.ADMIN, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.SYSTEM_OPER, ROLE_CODE.IQC].includes(item))) {
        if(isHavePerssion(this.selectSelection, 'status', 5) || isHavePerssion(this.selectSelection, 'status', 9)) {
          this.$set(this.buttonType[0], 'isDisable', true);
        }
      }
      // 不为IQC 或 IQC但选中的处理人有非本人 且不包含[其他]类型-4.3.19的需求；4.3.28-[未知]允许译单通过
      const hasUnOperate = (item) => {
        return item.status === 1 && item.iqc !== this.userCode || item.designTypeCodes.includes('25002');
      };
      const hasOperateRole = this.roleCodeList.some(roleCode => [ROLE_CODE.IQC, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.ADMIN].includes(roleCode));
      if(!hasOperateRole || ( hasOperateRole && (selection.some(item => item.status !== 1) || selection.some(item => hasUnOperate(item)))) ) {
        this.$set(this.buttonType[2], 'isDisable', true);
      }else {
        this.$set(this.buttonType[2], 'isDisable', false);
      }
    },
    // 批量处理返回的值
    handelbatchList(list) {
      this.batchList = list;
      this.search();
    },
    // 由我审核examine，由我译单translate
    batchOperateAll(type) {
      let orderCodes = [];
      orderCodes = this.selectSelection.map((item) => {
        return item.orderCode;
      });
      if (type === 'examine') { // 改成只拿code
        batchExamineByMe(orderCodes).then((res) => {
          if (res.code === 200) {
            this.flag.examine = false;
            this.search();
          }
        });
      }

      if (type === 'translate') {
        let data = {
          operateType: 1,
          orderCodes: orderCodes,
        };
        batchOperate(data).then((res) => {
          if (res.code === 200) {
            this.flag.examine = false;
            this.search();
          }
        });
        this.flag.translate = false;
      }
    },
    async getBatchDownFile(button){
      button.loading = true;
      const isHavePerssion = (code) => {
        return this.roles.some((item) => {
          return item.roleCode == code;
        });
      };

      // 循环选中的列表获取S3授权链接
      let requestList = [];
      this.selectSelection.forEach((item, index) => {
        if (item.orderLists && item.orderLists[0].s3FileId && item.orderLists[0].s3FileId != 'null') {
          let data = {
            filename: item.fileName ? item.fileName.substring(0, item.fileName.lastIndexOf('.')) : '未返回name',
            orgCode: item.orgCode,
            s3FileId: item.orderLists[0].s3FileId,
          };
          requestList.push(data);
          // 这里需要判断权限，如果不是管理员，系统运营、设计运营，IQC，设计师，设计师组长，点击下载也不能改变待设计订单的状态，只做下载
          if (item.status == 3 && item.designBy) {
              //当前处理人要有设计师
              if (isHavePerssion(ROLE_CODE.ADMIN) || isHavePerssion(ROLE_CODE.DESIGN_OPERATE) || isHavePerssion(ROLE_CODE.SYSTEM_OPER) || isHavePerssion(ROLE_CODE.DESIGNER) ||
                isHavePerssion(ROLE_CODE.DESIGN_LEADER) || isHavePerssion(ROLE_CODE.IQC)) {
                startToDesign(item.orderCode).then((res) => {
                  this.search('down');
                });
              }
            }
        }
      })
      getBatchDownloadUrl(requestList).then((res) => {
        if(res.code === 200){
          res.data.forEach((item, index) => {
            setTimeout(() => {
              createIFrameDownLoad2(item.url, (index + 1) * 4000, res.data.length, index);
            }, index * 100)
          })
          setTimeout(() => {
            button.loading = false;
          }, 3000 * res.data.length - 1)
        }
      })
    },
    // 下载文件
    getDownloadUrl(button) {
      button.loading = true;
      const isHavePerssion = (code) => {
        return this.roles.some((item) => {
          return item.roleCode == code;
        });
      };
      // 循环选中的列表获取S3授权链接
      let requestList = [];
      this.selectSelection.forEach((item, index) => {
        if (item.orderLists && item.orderLists[0].s3FileId && item.orderLists[0].s3FileId != 'null') {
          let data = {
            filename: item.fileName ? item.fileName.substring(0, item.fileName.lastIndexOf('.')) : '未返回name',
            orgCode: item.orgCode,
            s3FileId: item.orderLists[0].s3FileId,
          };
          requestList.push(
            new Promise((resolve, reject) => {
              // 30秒内响应
              getDownloadUrl(data, true, 30000).then((res) => {
                if (res.code === 200) {
                  res.data.filename = data.filename;
                  res.data.status = item.status; //当前这条下载数据的状态
                  res.data.orderCode = item.orderCode; //订单号
                  res.data.designBy = item.designBy; //当前处理人
                  resolve(res.data);
                } 
              }).catch(err => {
                const { message } = err;
                if(message && message.includes('timeout')) {
                  resolve({orderNo: item.orderNo, timeout: true});
                }else {
                  resolve(item.orderNo);
                }
              });
            })
          );
        } else {
          this.$message.error(item.orderCode + this.$t('orderList.order.noS3FileId'));
        }
      });
      //拿到全部返回的全部url数组result
      Promise.all(requestList).then((result) => {
        const failList = result.filter(item => !item.url && !item.timeout);
        if(failList.length > 0) {
          this.$message.error(this.$t('file.tips.fileExpireByList',[failList.join('、')]));
        }
        // 开始下载
        result.forEach((item, index) => {
          if (item.url) {
            // 这里需要判断权限，如果不是管理员，系统运营、设计运营，IQC，设计师，设计师组长，点击下载也不能改变待设计订单的状态，只做下载
            if (item.status == 3 && item.designBy) {
              //当前处理人要有设计师
              if (
                isHavePerssion(ROLE_CODE.ADMIN) ||
                isHavePerssion(ROLE_CODE.DESIGN_OPERATE) ||
                isHavePerssion(ROLE_CODE.SYSTEM_OPER) ||
                isHavePerssion(ROLE_CODE.DESIGNER) ||
                isHavePerssion(ROLE_CODE.DESIGN_LEADER) ||
                isHavePerssion(ROLE_CODE.IQC)
              ) {
                startToDesign(item.orderCode).then((res) => {
                  this.search('down');
                });
              }
            }
            createIFrameDownLoad(item.url, index * 500);
            // directDown(item.url, item.filename + '.zip');
          }
        });

        const timeoutList = result.filter(item => item.timeout).map(item => item.orderNo);
        if(timeoutList.length > 0) {
          setTimeout(() => {
            this.$hgOperateFail(this.$t('orderList.error.download',[timeoutList.join('、')]));
          }, 100);
        }
      }).finally(() => {
        button.loading = false;
      });
    },
    //长度超过 17 订单号中间显示星号
    handleOrderNumber(orderNumber) {
      const orderLength = orderNumber.length;
      if (orderLength <= 17) {
        return orderNumber;
      } else {
        const start = orderNumber.substr(0, 4);
        const end = orderNumber.substr(orderLength - 4);
        let newNumber = start + '*********' + end;
        return newNumber;
      }
    },
    // 点击行跳转详情
    openOrderDetails(row) {
      let orderCode = row.orderCode;
      this.$router.push({ name: ROUTE_NAME.ORDER_DETAIL, query: { auth:'Y', id: orderCode } });
    },
    // 批量译单
    handleBatchTranslate() {
      const unTranslateList = this.selectSelection.filter(item => item.status === 1).map(item => item.orderCode); // 保证一下数据正确
      this.$confirm(this.$t('orderList.btnList.submitBatchTranslate'), this.$t('orderList.btnList.translate'), {
        confirmButtonText: this.$t('common.btn.confirm'),
        cancelButtonText: this.$t('common.btn.cancel'),
      }).then(() => {
        const param = {
          operateType: 3,
          orderCodes: unTranslateList
        };
        batchOperate(param).then(res => {
          this.search();
        }).catch(err => {

        });
      }).catch(() => {});
    },
    // 联合订单批量指派
    batchSuccessUnion(){
      this.drawer = false;
      this.search();
    },
    exportOrderAdditionStatus(){
      this.flag.exportOrderStatus = true;
      this.notAllowClose = false;
      this.erportTime = [0, 0];
    },
    // 导出
    async exportStatusExcel(){
      this.notAllowClose = true;
      let parame = {
        startTime: this.erportTime[0],
        endTime: this.erportTime[1]
      }
      try {
        const { code, data } = await exportNewOrder(parame);
        if(code == 200){
          this.notAllowClose = false;
          this.flag.exportOrderStatus = false;
          getDownloadUrl({filename: data.fileName,  s3FileId: data.s3FileId}).then((res) => {
            createIFrameDownLoad(res.data.url, 500);
          })
        }
      } catch (error) {
        this.notAllowClose = false;
        this.flag.exportOrderStatus = false;
      }
    },
    // 关闭前做判断是否允许关闭
    closeExportStatusDialog(done){
      if(!this.notAllowClose){
        done()
      }
    },
    closedialog(){
      if(!this.notAllowClose){
        this.flag.exportOrderStatus = false;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.order-list {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  .btn-group {
    display: flex;
    flex-wrap: wrap;
    position: relative;

    .hg-button {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 18px;
      height: 44px;
      width: 192px;
      
      /deep/.iconfont-lab {
        font-size: 20px;
      }
    }
  }
  .order-list-btn {
    margin-right: 20px;
  }
  .btn {
    display: inline-block;
    margin-left: 8px;
  }
  .depart-table {
    flex: 1;
    .hg-table {
      height: 100%;
      /deep/.el-table {
        // max-height: 100% !important;
      }
      .order-no{
        display: flex;
        align-items: center;
        img{
          width: 20px;
          height: 20px;
          margin-right: 6px;
        }
      }
    }
    .table-high-light {
      float: left;
      width: auto;
      max-width: calc(100% - 41px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .table-content-status-name {
      display: inline-block;
      min-width: 82px;
      height: 30px;
      line-height: 30px;
      border-radius: 20px;
      background: rgba(74, 207, 111, 0.161);
      color: #39bf66;
      font-size: 12px;
      text-align: center;
      padding: 0 9px;
    }

    .is-designing {
      background: #5b87f7;
      color: #ffffff;
    }

    .is-blue {
      background: rgba(89, 134, 247, 0.122);
      color: #3760ea;
    }

    .is-finish {
      background: rgba(204, 204, 204, 0.212);
      color: #999999;
    }
    .is-unreback {
      background: #eca04a29;
      color: #ffa01e;
    }

    .is-cancel {
      background: rgba(111, 114, 117, 0.42);
      color: #ffffff;
    }

    .table-order-type {
      &>span {
        display: inline-block;
        padding: 4px 8px;
      }
    }

    .table-order-type.is-union {
      &>span {
        border-radius: 24px;
        border: 1px solid #9EA2A8;
        background: rgba(61, 64, 71, 0.48);
      }
    }
  }
  .depart-pagination {
    z-index: 1;
    position: absolute;
    bottom: 0;
    height: 58px;
    width: 100%;
  }
}
/deep/.el-form {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .el-form-item {
    margin-right: 24px;
  }
}
</style>
<style lang="scss">
  .order-no-popper{
    white-space: pre;
  }
  .export-order-status{
    .status-content{
      .title{
        padding-bottom: 16px;
        color: #fff;
      }
      .date-range-picker .el-date-editor{
        position: relative;
        width: 100%;
      }
      .date-suffix-icon{
        right: 20px;
      }
    }
  }
</style>
