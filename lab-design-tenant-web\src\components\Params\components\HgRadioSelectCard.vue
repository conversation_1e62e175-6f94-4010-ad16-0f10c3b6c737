<template>
  <div class="hg-radio-select-card">
    <div class="radio-select-box">
      <el-radio-group 
        v-model="data.value" 
        :disabled="eventDisable"
        @change="handleChange">
        <el-radio v-for="(item, index) in selectList" :key="index" :label="item.name">
          {{ getI18nName(item, i18nTitle, $getI18nText) }}
        </el-radio>
      </el-radio-group>
    </div>

    <div class="child-select-card">
      <el-radio-group :disabled="eventDisable" v-model="selectChildValue">
        <div v-for="(item, index) in childrenList" :key="index" class="select-item">
          <el-radio 
            :label="item.name"
            @click.native.prevent="handleChangeChild(item.name)">
            {{ getI18nName(item, i18nTitle, $getI18nText) }}
          </el-radio>
        </div>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
import { getI18nName } from '../utils';

export default {
  name: 'HgRadioSelectCard',
  inject: ['getTupdateTimes'],
  model: {
    prop: 'value',
    event: 'update',
  },
  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          max: 3,
          min: 0,
          ranges: []
        }
      }
    },
    i18nTitle: { // i18n需要拼接成i18n国际化文本，数据库就不存太长
      type: String,
      default: '',
    },
    eventDisable: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      // selectValue: '',
      lastSelectValue: '',
      selectChildValue: '',
      oldChildValueList: [], // 数据初始时每个子集的value
    }
  },
  computed: {
    selectList() {
      const { child } = this.data;
      if(child && child.length > 0) {
        return child;
      }else {
        return [];
      }
    },

    childrenList() {
      const { child, value } = this.data;
      if(!child || child.length === 0) {
        return [];
      }
      
      const selectChildIndex = child.findIndex(item => item.name === value);
      if(selectChildIndex > -1) {
        return child[selectChildIndex].child;
      }

      return [];
    },

    updateTimes() { // 保存更新的次数
      return this.getTupdateTimes ? this.getTupdateTimes() : 0;
    }

  },
  watch: {

    updateTimes(value) {
      if(value> 0) {
        this.initData(this.data);
      }
    },
    
    data: {
      deep: true,
      handler(newData) {
        const { value, child } = newData;

        let childItem = child.find(item => item.name === value);
        if(childItem && childItem.value !== this.selectChildValue) {
          this.selectChildValue = childItem.value;
        }
      }
    }
  },

  mounted() {
    if(this.data) {
      this.initData(this.data);
    }
  },

  methods: {
    getI18nName,

    initData(data) {
      const { value, child } = data;

      let childItem = child.find(item => item.name === value);
      if(childItem) {
        this.selectChildValue = childItem.value;
      }
    },

    handleChange(value) {
      if(this.eventDisable) return;

      if(this.lastSelectValue !== value) {
        this.updateChildItemValue(this.lastSelectValue, '');
        this.lastSelectValue = value;
        this.updateChildItemValue(value, '');
      }
    },

    // 子集value变化事件
    handleChangeChild(value) {
      if(this.eventDisable) return;

      if(this.selectChildValue !== value) {
        this.selectChildValue = value;
        this.updateChildItemValue(this.data.value, value);
      }else {
        this.selectChildValue = '';
        this.updateChildItemValue(this.data.value, '');
      }
    },

    // 更新child的value
    updateChildItemValue(parentValue, childValue){
      let childItem = this.data.child.find(item => item.name === parentValue);
      if(childItem) {
        this.$set(childItem, 'value', childValue);
      }
    },


  }
}
</script>

<style lang="scss" scoped>
.hg-radio-select-card {

}

.hg-radio-select-card>.radio-select-box {
  margin-top: 24px;
}

.hg-radio-select-card>.child-select-card {
  margin-top: 24px;
  padding: 32px 32px 12px;
  background: #262629;
  border-radius: 2px;
  
  .el-radio-group {
    display: flex;
    flex-wrap: wrap;
  }

  .select-item {
    margin: 0;
    padding-bottom: 24px;
    width: 35%;
    line-height: 32px;
  }

}
</style>