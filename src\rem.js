function setRem() {
  // 设计稿宽度为1440 默认大小14px; 总体宽度为1440/14rem;每个元素px基础上/14
  //获取现在浏览器宽度
  const htmlWidth =
    document.documentElement.clientWidth || document.body.clientWidth;
  //得到html的Dom元素
  const htmlDom = document.getElementsByTagName("html")[0];
  if (htmlWidth > 1440) {
    htmlDom.style.fontSize = 1440 / 103 + "px";
  } else if (htmlWidth < 1000) {
    htmlDom.style.fontSize = 1000 / 103 + "px";
  } else {
  //设置根元素字体大小，103为设计稿宽度/根元素字体大小
  htmlDom.style.fontSize = htmlWidth / 103 + "px";
  }
}
// 页面初始化时运行设置根元素字体大小
setRem();
// 改变窗口大小时重新设置 rem
// let timer = null;
window.addEventListener("resize", () => {
  // clearTimeout(timer);
  // timer = setTimeout(() => {
  setRem();
  // }, 500);
});
