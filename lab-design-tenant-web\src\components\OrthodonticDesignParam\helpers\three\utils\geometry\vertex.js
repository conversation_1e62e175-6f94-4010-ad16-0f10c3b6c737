import * as THREE from 'three'
import { _vector3 } from '../vector3/index'

import {
  getAttributeFaceIndexsByVertexIndexs,
  getAttributeVertexIndexsByFaceIndexs,
} from '../attribute'

import { mergeVertices } from 'three/examples/jsm/utils/BufferGeometryUtils'

// 根据面索引得到这个面的法向量
export function getGeometryFaceNormalByFaceIndex(geometry, faceIndex) {
  const vertexIndex = faceIndex * 3

  const { attributes, index } = geometry
  const { array } = index
  const { position } = attributes

  const vectors = [
    new THREE.Vector3().fromBufferAttribute(position, array[vertexIndex]),
    new THREE.Vector3().fromBufferAttribute(position, array[vertexIndex + 1]),
    new THREE.Vector3().fromBufferAttribute(position, array[vertexIndex + 2]),
  ]

  const direction1 = new THREE.Vector3().subVectors(vectors[1], vectors[0])
  const direction2 = new THREE.Vector3().subVectors(vectors[2], vectors[1])

  return direction1.cross(direction2).normalize()
}

// 从几何体的顶点索引中得到对应的面片索引
export function getGeometryFaceIndexsByVertexIndexs(geometry, vertexIndexs) {
  const { index } = geometry
  return getAttributeFaceIndexsByVertexIndexs(index, vertexIndexs)
}

// 从几何体的面片索引中得到对应的顶点索引
export function getGeometryVertexIndexsByFaceIndexs(geometry, faceIndexs) {
  const { index } = geometry
  return getAttributeVertexIndexsByFaceIndexs(index, faceIndexs)
}

// 循环顶点索引
export function forGeometryVertexIndexs(geometry, cb) {
  const { index } = geometry
  const { array } = index
  const { length } = array

  for (let i = 0; i < length; i++) {
    cb(array[i])
  }
}

// 把无序的去重的vertexIndexs转成有序的完整的数组
export function getGeometryOrderVertexIndexs(geometry, vertexIndexs) {
  const orderVertexIndexs = []
  const { index } = geometry
  const { array } = index
  const { length } = array

  for (let i = 0; i < length; i += 3) {
    if (
      vertexIndexs.includes(array[i]) &&
      vertexIndexs.includes(array[i + 1]) &&
      vertexIndexs.includes(array[i + 2])
    ) {
      orderVertexIndexs.push(array[i], array[i + 1], array[i + 2])
    }
  }

  return orderVertexIndexs
}

// 从一个几何体中抽离出一些顶点形成一个新的几何体
export function rebuildGeometryByVertexIndexs(geometry, vertexIndexs, options = {}) {
  let { vertexUniqueIndexs, needNormal = true, needColor = true } = options

  if (!vertexUniqueIndexs) {
    vertexUniqueIndexs = Array.from(new Set(vertexIndexs))
  }

  const { attributes } = geometry
  const { position, normal, color } = attributes

  const positionArray = []
  const point = _vector3

  geometry = new THREE.BufferGeometry()

  if (normal && needNormal) {
    const array = []
    for (const vertexIndex of vertexUniqueIndexs) {
      point.fromBufferAttribute(normal, vertexIndex)
      const { x, y, z } = point
      array.push(x, y, z)
    }
    geometry.setAttribute('normal', new THREE.Float32BufferAttribute(array, 3))
  }

  if (color && needColor) {
    const array = []
    for (const vertexIndex of vertexUniqueIndexs) {
      point.fromBufferAttribute(color, vertexIndex)
      const { x, y, z } = point
      array.push(x, y, z)
    }
    geometry.setAttribute('color', new THREE.Float32BufferAttribute(array, 3))
  }

  for (const vertexIndex of vertexUniqueIndexs) {
    point.fromBufferAttribute(position, vertexIndex)
    const { x, y, z } = point
    positionArray.push(x, y, z)
  }

  geometry.setAttribute('position', new THREE.Float32BufferAttribute(positionArray, 3))

  const vertexIndexsNew = []
  for (const vertexIndex of vertexIndexs) {
    const i = vertexUniqueIndexs.indexOf(vertexIndex)
    vertexIndexsNew.push(i)
  }
  geometry.setIndex(vertexIndexsNew)

  return geometry
}

// 从一个几何体中抽离出三角面形成一个新的几何体
export function rebuildGeometryByFaceIndexs(geometry, faceIndexs, options = {}) {
  const vertexIndexs = getGeometryVertexIndexsByFaceIndexs(geometry, faceIndexs)

  if (!options.vertexUniqueIndexs) {
    const vertexUniqueIndexs = Array.from(new Set(vertexIndexs))
    options.vertexUniqueIndexs = vertexUniqueIndexs
  }

  return rebuildGeometryByVertexIndexs(geometry, vertexIndexs, options)
}

// 在几何体中通过面片索引删除对应的顶点索引并重建索引关系
export function removeGeometryVertexIndexsByFaceIndexs(geometry, faceIndexs) {
  const vertexIndex = []
  const { index } = geometry
  const { array } = index
  const { length } = array

  for (let i = 0; i < length; i += 3) {
    const faceIndex = i / 3
    if (!faceIndexs.includes(faceIndex)) {
      vertexIndex.push(array[i], array[i + 1], array[i + 2])
    }
  }
  geometry.setIndex(vertexIndex)
}

// 得到几何体的所有点
export function getGeometryVertexVectors(geometry) {
  const vertexVectors = []
  const { attributes } = geometry
  const { position } = attributes
  const { count } = position

  for (let i = 0; i < count; i++) {
    const point = new THREE.Vector3()
    point.fromBufferAttribute(position, i)
    vertexVectors.push(point)
  }

  return vertexVectors
}

// 得到几何体的顶点索引对应的点
export function getGeometryVertexVectorsByVertexIndexs(geometry, vertexIndexs) {
  const vertexVectors = []
  const { attributes } = geometry
  const { position } = attributes

  for (const vertexIndex of vertexIndexs) {
    const point = new THREE.Vector3()
    point.fromBufferAttribute(position, vertexIndex)
    vertexVectors.push(point)
  }

  return vertexVectors
}

// 得到几何体的面片索引对应的点
export function getGeometryVertexVectorsByFaceIndexs(geometry, faceIndexs) {
  const vertexIndexs = getGeometryVertexIndexsByFaceIndexs(geometry, faceIndexs)
  return getGeometryVertexVectorsByVertexIndexs(geometry, vertexIndexs)
}

export function getOriginGeometry(geometry, clone) {
  geometry = clone ? geometry.clone() : geometry
  geometry.deleteAttribute('normal')
  geometry.deleteAttribute('uv')
  geometry = mergeVertices(geometry)
  return geometry
}
