<template>
  <div>
    <el-drawer custom-class="my-pointsDetails-drawer" :visible.sync="pointsDetails">
      <div class="draw-title" slot="title">{{this.rowData.date}} {{lang('pointDetail')}}</div>
      <div class="my-point-details">
        <div class="points-header">
          <div class="my-search">
            <span class="label">{{lang('orderNo')}}</span>
            <el-input v-model="orderNo" :placeholder="lang('pleaseInput')" @change="search" suffix-icon="el-icon-search" clearable></el-input>
          </div>
          <div class="points-btnlist">
            <el-button v-if="isHaveAllo" type="primary" @click="allocatePoints">{{lang('allocation')}}</el-button>
            <el-button type="primary" @click="exportPoints">{{lang('export')}}</el-button>
          </div>
        </div>
        <!-- 内容 -->
        <div class="order-content" v-loading="loadingDetails">
          <!-- 列表 -->
          <div class="depart-table">
            <hg-table :header-data="headerData" class="user-table" :loading="tableLoading" :data="pointDetailsTable">
              <template v-slot:no="{ row, index }">
                <span>{{ index + 1 }}</span>
              </template>
              <template #orderNo="scope">
                <el-tooltip v-if="scope.row.isCommonQuestions" class="item" effect="dark" :content="lang('commonissu')" placement="bottom">
                  <hg-icon class="label" icon-name="icon-Qbill"></hg-icon>
                </el-tooltip>
              <span>{{ scope.row.orderNo }}</span>
                <!-- <span class="label" v-if="scope.row.isCommonQuestions">常规</span><span>{{ scope.row.orderNo }}</span> -->
              </template>
              <!-- 设计品类 -->
              <template #designTypes="scope">
                <span>{{language == 'zh' ? scope.row.designTypeZhName : scope.row.designTypeEnName}}</span>
              </template>
              <!-- 返单 -->
              <template #isReturn="scope">
                <span v-if="scope.row.isReturn">{{lang('yes')}}</span>
                <span v-else>{{lang('no')}}</span>
              </template>
              <!-- 免单 -->
              <template #isFree="scope">
                <span v-if="scope.row.isFree">{{lang('yes')}}</span>
                <span v-else>{{lang('no')}}</span>
              </template>
              <!-- 超时 -->
              <template #isTimeout="scope">
                <span v-if="scope.row.isTimeout">{{lang('yes')}}</span>
                <span v-else>{{lang('no')}}</span>
              </template>
              <!-- 我的点数 -->
              <template #myPoints="scope">
                <span v-if="scope.row.myPoints">{{getNum(scope.row.myPoints)}}</span>
              </template>
            </hg-table>
            <div class="depart-pagination">
              <pagination showTotal :total="page.total" :pageSizes="[10, 20, 50, 100]" :initPageIndex="page.pageNo" :initPageSize="page.pageSize" @onSearch="search"></pagination>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import hgTable from "@/components/HgTable";
import pagination from '@/components/Pagination';
import { getDetailList, exportDetails } from '@/api/designPoints';
import { getDownloadUrl } from '@/api/file';
import { directDown, createIFrameDownLoad } from "@/public/utils/file.js";
import { server } from "@/config";
import { mapGetters } from "vuex";
import { getLang } from '@/public/utils';
export default {
  name: "pointsDetails",
  components: {hgTable, pagination},
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    rowData: Object,
    isHaveAllo: {
      tyoe: Boolean,
      default: true
    }
  },
  data() {
    return {
      orderNo: '',
      tableLoading: false,
      loadingDetails: false,
      page: {
        pageSize: 20,
        pageNo: 1,
        total: 0,
      },
      pointDetailsTable: []
    };
  },
  computed: {
    ...mapGetters(["language", "oneDesignSkuList"]),
    pointsDetails: {
      get() {
        return this.drawer;
      },
      set(val) {
        this.$emit("update:drawer", val);
      },
    },
    headerData() {
      return [
        {
          prop: "no",
          width: "60px",
          noTip: false,
          getLabel: () => {
            return this.lang('number');
          },
        },
        {
          prop: "orderNo",
          minWidth: "55%",
          noTip: false,
          getLabel: () => {
            return this.lang('orderNo');
          },
        },
        {
          prop: "orgSn",
          minWidth: "20%",
          noTip: false,
          getLabel: () => {
            return this.lang('orgsn');
          },
        },
        {
          prop: "designTypes",
          minWidth: "30%",
          noTip: false,
          getLabel: () => {
            return this.lang('designCode');
          },
        },
        {
          prop: "designUserName",
          minWidth: "20%",
          noTip: false,
          getLabel: () => {
            return this.lang('desinger');
          },
        },
        {
          prop: "groupQcName",
          minWidth: "20%",
          noTip: false,
          getLabel: () => {
            return this.lang('groupqc');
          },
        },
        {
          prop: "isReturn",
          minWidth: "15%",
          noTip: false,
          getLabel: () => {
            return this.lang('back');
          },
        },
        {
          prop: "isFree",
          minWidth: "15%",
          noTip: false,
          getLabel: () => {
            return this.lang('free');
          },
        },
        {
          prop: "isTimeout",
          minWidth: "15%",
          noTip: false,
          getLabel: () => {
            return this.lang('overTime');
          },
        },
        {
          prop: "myPoints",
          minWidth: "30%",
          noTip: false,
          getLabel: () => {
            return this.lang('mypoints');
          },
        }
      ];
    },
  },
  watch: {
    pointsDetails(newValue, oldValue) {
      if(newValue){
        this.getDetailList();
      }
    }
  },
  mounted () {

  },
  methods: {
    lang: getLang('designpoints'),
    getNum(value){
      return value.toFixed(4)
    },
    search(type, searchData){
      if(searchData) {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
      } else {
        this.page.pageNo = 1;
        this.page.pageSize = 20;
      }
      this.getDetailList();
    },
    // 获取列表
    async getDetailList(){
      this.loadingDetails = true;
      let param = {
        month: this.rowData.date,
        orderNo: this.orderNo,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
        userCode: this.rowData.userCode
      }
      const { code, data } = await getDetailList(param);
      if(code == 200){
        this.page.pageNo = data.pageNo;
        this.page.pageSize = data.pageSize;
        this.page.total = data.totalSize;
        this.pointDetailsTable = this.handelDetails(data.data)
        this.loadingDetails = false;
      }
    },
    handelDetails(data){
      if(!data || (data && data.length == 0)){
        return []
      }
      data.forEach((item) => {
        item.designTypeZhName = '';
        item.designTypeEnName = '';
        if(item.designTypes){
          let designTypes = JSON.parse(item.designTypes);
          designTypes.forEach((design) => {
            let node = this.oneDesignSkuList.find((it) => {return it.skuCode == design})
            if(node){
              item.designTypeZhName += node.zhName + '、';
              item.designTypeEnName += node.enName + '、'
            }
          })
          item.designTypeZhName = item.designTypeZhName.slice(0, item.designTypeZhName.length - 1)
          item.designTypeEnName = item.designTypeEnName.slice(0, item.designTypeEnName.length - 1)
        }
      })
      return data
    },
    // 点数分配
    allocatePoints(){
      this.$emit('allocatePoints');
    },
    // 导出excel
    async exportPoints(){
      // this.$message.success(this.lang('downLoadWait'));
      // let url = `${server.designerPoints}/downloadDetailExcel?month=${this.date}&userCode=${this.rowData.userCode}`;
      // directDown(url, `${this.date}设计师点数详情.xlsx`);
      this.$message.success(this.lang('downLoadWait'));
      const { code, data } = await exportDetails(this.rowData.date, this.rowData.userCode, this.rowData.userName);
      if(code == 200){
        const param = {
          s3FileId: data.s3FileId,
          filename: `${data.month} ${data.userName} ${this.lang('pointsExcel')}`,
        };
        getDownloadUrl(param).then(res => {
          if(res.code === 200) {
            createIFrameDownLoad(res.data.url);
          }else {
            this.$hgOperateFail(this.$t('http.error.80080003'));
          }
        }).catch(err => {
          console.log('error:',err);
        });
      }
    }
  },
};
</script>

<style lang="scss">
.my-pointsDetails-drawer {
  width: 1200px !important;
  background-color: $hg-main-black;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
    color: $hg-label;
  }
  .el-drawer__body {
    padding: 24px;
    overflow: hidden;
  }
  .el-button.is-plain:focus{
    background: transparent;
  }
  .el-button--primary:focus{
    background: #3760EA;
    border-color: #3760EA;
  }
  .my-point-details{
    display: flex;
    flex-direction: column;
    height: 100%;
    .points-header{
      position: relative;
      .my-search{
        position: relative;
        width: 560px;
        display: flex;
        align-items: center;
        .el-input__prefix{
          display: none;
        }
        .label{
          margin-right: 20px;
        }
        .date-icon{
          position: absolute;
          right: 40px;
          font-size: 16px;
        }
      }
      .points-btnlist{
        position: absolute;
        right: 0px;
        top: 0;
      }
      .el-input{
        width: 400px;
        background: #141519;
        .el-input--prefix .el-input__inner{
          padding-left: 20px;
        }
      }
    }
    .order-content{
      flex: 1;
      // background: #27292E;
      margin-top: 24px;
      overflow: auto;
      border-radius: 8px;
    }
    .depart-table {
      position: relative;
      // flex: 1;
      height: calc(100% - 20px);
      width: 100%;
      .hg-table {
        height: 100%;
        background: #27292E;
        // height: calc(100% - 30px);
        overflow: hidden;
        .el-table {
          background: #27292E;
          // height: calc(100% - 60px)!important;
          // max-height: 100% !important;
        }
        .label{
          display: inline-block;
          // background: rgba(246, 76, 76, 0.24);
          // padding: 2px 4px;
          // border-radius: 3px;
          color: #EB6F70;
          font-size: 16px;
          margin-right: 6px;
        }
      }
      .table-high-light {
        float: left;
        width: auto;
        max-width: calc(100% - 41px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .expedited-time {
        line-height: 40px;
      }
      .error-tips{
        color: #E55353;
      }
    }
    .depart-pagination {
      z-index: 1;
      position: absolute;
      bottom: 0;
      right: 0;
      height: 60px;
      width: 100%;
    }
  }
}
</style>
