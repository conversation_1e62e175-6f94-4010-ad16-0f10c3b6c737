const state = {
  searchListObj: {
    keywords: '',
    status: 0,
    designTypeCodes: [], //类型
    designCategoryCode: '',
    startTime: 0, //创建时间
    endTime: 0,
    completeStartTime: 0, //完成时间
    completeEndTime: 0,
  },
  statisJumpSearch: {},//数据看板跳转存储

  // 未认证列表检索条件
  unauthSearchData : {
    searchData: null,
    pageData: null,
  }, 

  designCategory: 0,
  designTypeTree: [],
};

// getters
const getters = {
  searchListObj: (state) => state.searchListObj,
  statisJumpSearch: (state) => state.statisJumpSearch
};

const mutations = {
  CHANGE_SEARCH_LIST_OBJ: (state, searchListObj) => {
    state.searchListObj = searchListObj;
  },
  SET_JUMP_SEARCH: (state, statisJumpSearch) => {
    state.statisJumpSearch = statisJumpSearch
  },

  // 未认证订单列表条件更新
  UPDATE_UNAUTH_SEARCH_DATA: (state, {searchData, pageData}) => {
    state.unauthSearchData = {
      searchData,
      pageData,
    };
  },
};

// actions
const actions = {
  // 初始化菜单
  changeSearchListObj: ({ commit }, searchListObj) => {
    commit('CHANGE_SEARCH_LIST_OBJ', searchListObj);
  },
  setJumpSearch: ({commit}, statisJumpSearch) => {
    commit('SET_JUMP_SEARCH', statisJumpSearch);
  }
};

export default {
  state,
  getters,
  mutations,
  actions,
};
