import * as THREE from 'three'
import { createMatrix4 } from '../matrix4'
import { _quaternion } from '../quaternion'

export function getTwoUnitVectors3Matrix4(from, to) {
  const quaternion = _quaternion
  quaternion.setFromUnitVectors(from, to)
  return createMatrix4(null, quaternion)
}

export function getTwoUnitVectors3Quaternion(from, to) {
  const quaternion = new THREE.Quaternion()
  quaternion.setFromUnitVectors(from, to)
  return quaternion
}
