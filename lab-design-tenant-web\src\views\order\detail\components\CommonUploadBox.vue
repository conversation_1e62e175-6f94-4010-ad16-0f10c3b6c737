<template>
  <hg-card class="common-upload-box">
    <order-title langName="resultFile">
      <span v-show="showNewTip" class="new-file-icon">NEW</span>
      <span class="complete-btn" v-if="canCompleteFile">
        <el-button v-show="!editFile" type="primary" @click="editCompleteFile">{{$t('common.btn.edit')}}</el-button>
        <el-button v-show="editFile" plain type="primary" @click="cacelEditCompleteFile">{{$t('common.btn.cancel')}}</el-button>
        <el-button v-show="editFile" type="primary" @click="submitNewCompleteile">{{$t('heypoint.customer.operate.submit')}}</el-button>
      </span>
    </order-title>
    <!-- v-if="!hiddenFileBox" -->
    <!-- 4.3.46改成都可以看到，不管是不是当前设计师或者iqc -->
    <!-- <div v-if="!hiddenFileBox" class="upload-ul"> -->
    <div class="upload-ul">
      <slot name="designRemark"></slot>

      <div class="upload-li" v-for="(item, index) in compList" :key="index">
        <upload-image
          ref="uploadCard"
          v-if="item.compType === UPLOAD_COMP_TYPE.IMAGE_BOX"
          :name="$t(item.name)"
          :uploadList="item.uploadList"
          :fileType="item.fileType"
          :acceptType="item.acceptType"
          :necessary.sync="item.necessary"
          :clientOrgCode="clientOrgCode"
          :needUpload="editImageUpload"
          @necessaryFileUploadSuccess="necessaryFileUploadSuccess"
        ></upload-image>

        <upload-card
          v-else
          ref="uploadCard"
          :name="$t(item.name)"
          :uploadList="item.uploadList"
          :fileType="item.fileType"
          :uploadTip="item.uploadTip"
          :acceptType="item.acceptType"
          :necessary.sync="item.necessary"
          :standarName="item.standarName"
          :clientOrgCode="clientOrgCode"
          :needUpload="editUpload"
          @necessaryFileUploadSuccess="necessaryFileUploadSuccess"
        ></upload-card>
      </div>
    </div>

    <!-- <div class="common-upload-no-data" v-else>
      {{ $t('order.detail.noData') }}
    </div> -->
  </hg-card>
</template>

<script>
import OrderTitle from './OrderTitle';
import UploadCard from './Upload/UploadCard';
import UploadImage from './Upload/UploadImage';
import { UPLOAD_COMP_TYPE, FILE_TYPES } from '@/public/constants';
import { mapGetters } from 'vuex';

export default {
  name: 'CommonUploadBox',
  components: { OrderTitle, UploadCard, UploadImage },
  props: {
    needUpload: Boolean,
    needUploadImage: Boolean,
    canCompleteFile: Boolean,
    clientOrgCode: {
      type: Number,
      require: true,
    },
    fileList: {
      type: Array,
      default() {
        return [];
      }
    },
    standarName: String,
    hiddenFileBox: Boolean,
    showNewTip:Boolean,
    hasMulDesigner: Boolean,
    hasImplantType: Boolean,
    orderInfo: Object
  },
  data() {
    return {
      UPLOAD_COMP_TYPE,
      editUpload: this.needUpload,
      editImageUpload: this.needUploadImage,
      defaultCompList:[
        { name: 'file.title.design', fileType: FILE_TYPES.DESIGN_FILE, compType: 1, uploadList: [], necessary:true, standarName: '' },
        {
          name: 'file.title.model',
          compType: 1,
          uploadList: [],
          necessary: true,
          fileType: FILE_TYPES.DESIGN_MODEL,
          uploadTip: 'file.tips.model',
          acceptType: '.stl,.zip,.dcm,.obj,.ply'
        },
        { name: 'file.title.screenshot', fileType: FILE_TYPES.SCREENSHOT, compType: 2, uploadList: [], necessary:true, },
        { name: 'file.title.video', fileType: FILE_TYPES.DESIGN_VIDEO, compType: 1, uploadList: [], uploadTip: 'file.tips.video', },
        {
          name: 'file.title.prospectus',
          compType: 1,
          uploadList: [],
          fileType: FILE_TYPES.PROSPECTUS,
          uploadTip: 'file.tips.prospectus',
          acceptType: '.pdf'
        },
        { name: 'file.title.other', fileType: FILE_TYPES.OTHER_FILE, compType: 1, uploadList: [], uploadTip: 'file.tips.other', },
      ],
      editFile: false
    }
  },
  watch: {
    needUpload(newValue) {
      this.editUpload = newValue;
    },
    needUploadImage(newValue) {
      this.editImageUpload = newValue;
    },

    hasImplantType: {
      handler(newValue) {
        console.log('hasImplantType-newValue', newValue)
        if (newValue) {
          if (!this.defaultCompList.find(item => item.fileType === FILE_TYPES.DRILL_FILE)) {
            const length = this.defaultCompList.length;
            const obj = {
              name: 'file.title.drill',
              compType: 1,
              uploadList: [],
              fileType: FILE_TYPES.DRILL_FILE,
              uploadTip: 'file.tips.drill',
              acceptType: '.pdf',
              necessary: true,
            };
            this.defaultCompList.splice(length - 2, 0, obj)
          }
        } else {
          this.defaultCompList = this.defaultCompList.filter(item => item.fileType !== FILE_TYPES.DRILL_FILE)
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters(['userCode']),
    compList() {
      const necessaryItems = [FILE_TYPES.DESIGN_FILE, FILE_TYPES.DESIGN_MODEL, FILE_TYPES.SCREENSHOT];
      const errArr = ['', null, undefined];
      let isMyImplantType = false

      if (this.hasImplantType) {
        necessaryItems.push(FILE_TYPES.DRILL_FILE)
        const { isUnion, designerTypes } = this.orderInfo
        if (isUnion && designerTypes) {
          const myDesignItem = designerTypes.find(item => item.designUser === this.userCode)
          if (myDesignItem) {
            const myDesignItemTypes = JSON.parse(myDesignItem.designTypes)
            const myDesignItemTypeCodes = myDesignItemTypes.map(item => item.code)
            if (myDesignItemTypeCodes.some(item => item === 23601)) {
              isMyImplantType = true
            }
          }
        }
      }

      this.defaultCompList.map(comp => {
        const fileList = this.fileList.filter(file => file.fileType === comp.fileType);
        if(comp.fileType === FILE_TYPES.DESIGN_FILE) {
          comp.standarName = this.standarName;
        }
        comp.uploadList = fileList;
        if (this.hasMulDesigner && comp.necessary) {
          comp.necessary = false
        }

        if (isMyImplantType && comp.fileType === FILE_TYPES.DRILL_FILE) {
          comp.necessary = true
        }
        return comp;
      });

      

      // 如果有上传文件，则设计文件，设计STL，设计截图为必传文件
      const isRequired = this.defaultCompList.some(item => necessaryItems.includes(item.fileType) && (item.uploadList.length || item.uploadFlag));
      if (isRequired) {
        this.defaultCompList.forEach(item => {
          if (!errArr.includes(item.necessary)) {
            item.necessary = true
          }
        })
      }
      console.log('this.defaultCompList', this.defaultCompList)
      return this.defaultCompList;
    }
  },
  created() {
    console.log('this.hasImplantType', this.hasImplantType)
    
  },
  methods: {
    /**
     * 父组件调用的方法：获取当前组件的文件列表
     */
    getCompList() {
      let compList = [];
      const domList = this.$refs.uploadCard;
      if(domList.length > 0) {
        domList.forEach(dom => {
          const { necessary, fileType, uploadFileList } = dom;
          const data = {
            necessary,
            fileType,
            fileList: uploadFileList,
          }

          compList.push(data);
        });
      }
      return compList;
    },

    /**
     * 如果有上传文件，则设计文件，设计STL，设计截图为必传文件
     */
    necessaryFileUploadSuccess() {
      this.$nextTick(() => {
        const domList = this.$refs.uploadCard;
        const flag = domList && domList.length && domList.some(dom => dom.uploadFileList.length)
        const errArr = ['', null, undefined];
        this.defaultCompList.forEach(item => {
          if (!errArr.includes(item.necessary)) {
            item.necessary = true
            if (flag) {
              item.uploadFlag = flag
            }
          }
        })
  
        
      })
    },
    // 编辑文件
    editCompleteFile(){
      this.editFile = true;
      this.editUpload = true;
      this.editImageUpload = true;
    },
    cacelEditCompleteFile(){
      this.$confirm(this.$t('order.detail.tips.nosaveResult'), this.$t('bill.leftDrawer.reminding'), {
        confirmButtonText: this.$t('common.btn.confirm'),
        cancelButtonText: this.$t('common.btn.cancel'),
        type: 'warning',
      }).then(() => {
        this.editFile = false;
        this.editUpload = false;
        this.editImageUpload = false;
        this.$emit('submit', 'cancel'); // 重新请求数据
      }).catch(() => {

      });
    },
    // 提交新的文件
    submitNewCompleteile(){
      this.$confirm(this.$t('order.detail.tips.resultChange'), this.$t('bill.leftDrawer.reminding'), {
        confirmButtonText: this.$t('common.btn.confirm'),
        cancelButtonText: this.$t('common.btn.cancel'),
        type: 'warning',
      }).then(() => {
        this.$emit('submit', 'success')
      }).catch(() => {

      });
    },
    resetValue(){
      this.editFile = false;
      this.editUpload = false;
      this.editImageUpload = false;
    }
  },
}
</script>

<style lang="scss" scoped>
.common-upload-box {
  .upload-ul {
    margin-top: 24px;
  }
  .upload-ul > .upload-li {
    margin-bottom: 24px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  .common-upload-no-data {
    padding-top: 24px;
  }
}

.common-upload-box > .order-title {
  display: flex;
  align-items: center;
  position: relative;

  .new-file-icon {
    color: $hg-white;
    background: $hg-new-tag;
    border-radius: 7px;
    padding: 0 5px;
    margin-left: 10px;
    font-size: 12px;
    height: 14px;
    line-height: 14px;
  }
  .complete-btn{
    position: absolute;
    right: 0;
    /deep/.el-button--primary.is-plain{
      background: transparent;
      color: #fff;
    }
  }
}
</style>
