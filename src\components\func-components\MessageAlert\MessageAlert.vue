<template>
  <div
    class="MessageAlert"
    :style="{ top: top + 'px' }"
    :class="isShow ? 'FadeInDown' : 'FadeOutUp'"
  >
    <div class="MA_icon pos-abs">
      <i :class="['iconfont', `icon-${type}`]"></i>
    </div>
    {{ text }}
  </div>
</template>

<style lang="scss" scoped>
.MessageAlert {
  position: fixed;
  top: 86px;
  left: 50%;
  z-index: 1002;
  font-size: $hg-normal-fontsize;
  line-height: 16px;
  padding: 11px 24px 11px 49px;
  background: $hg-hover-bg-color;
  color: $hg-secondary-fontcolor;
  @include bg-box-shadow();
  border-radius: $hg-border-radius4;
  .MA_icon {
    width: 16px;
    height: 16px;
    top: 50%;
    left: 24px;
    transform: translateY(-50%);
  }
  .icon-warning {
    color: $hg-warning-color;
  }
  .icon-error {
    color: $hg-error-color;
  }
  .icon-success {
    color: $hg-success-color;
  }
}
.MessageAlert.FadeInDown {
  -webkit-animation-duration: 0.8s;
  animation-duration: 0.8s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: FadeInDown;
  animation-name: FadeInDown;
}
.MessageAlert.FadeOutUp {
  -webkit-animation-duration: 0.8s;
  animation-duration: 0.8s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: FadeOutUp;
  animation-name: FadeOutUp;
}
</style>
