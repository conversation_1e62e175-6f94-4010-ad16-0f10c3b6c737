<template>
  <div>
    <el-drawer
      :title="$t('orderList.order.multiOrder')"
      custom-class="batch-drawer"
      :visible.sync="batchDrawer"
      :before-close="closeDrawer"
    >
      <div class="batch-content">
        <div class="all-content">
          <div class="left-select">
            <!-- 头部全选 -->
            <div class="top-select-all">
              <el-checkbox :indeterminate="indeterminate" v-model="checkAll" @change="handleCheckAllChange" :disabled="isDisabled">{{$t('orderList.order.multiOrder')}}</el-checkbox>
              <el-button type="primary" @click="resetAllSelect">{{$t('orderList.order.reset')}}</el-button>
            </div>
            <div class="all-select-list">
              <!-- 循环生成多个子全选组 -->
              <div class="select-list" v-for="(group, index) in ordersList" :key="index">
                <el-checkbox :indeterminate="group.indeterminate" v-model="group.isSelected" @change="selectGroup(group)" :disabled="computedDis(group)">{{ group.orderNo }}</el-checkbox>
                <div class="checkbox-list">
                  <div class="one-check-box" v-for="(item, itemIndex) in group.items" :key="itemIndex">
                    <el-checkbox v-model="item.isSelected" :disabled="!!item.userCode" @change="updateParent(group)">
                      {{ language == 'zh' ? item.zhName : item.enName }}
                    </el-checkbox>
                    <span v-if="item.userCode" class="designer-box">{{item.designer}} <span class="clearbox el-icon-close" @click="clearDesigner(index, itemIndex, item)"></span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right-select">
            <!-- 头部 -->
            <div class="top-select-all">
              <span>{{$t('orderList.order.assignDesigner')}}</span>
              <el-button type="primary" @click="batchDesignInLeftList">{{$t('designpoints.submitBtn')}}</el-button>
            </div>
            <!-- 指派列表 -->
            <div class="right-batch-content">
              <!-- 头部搜索 -->
              <div class="batch-header">
                <!-- <p class="batch-title">{{$t('orderList.order.select')}} {{ assignType === 'IQC' ? 'IQC' : assignType === 'DESIGNER' ? (language == 'zh' ? '设计师' : 'designer') : assignType === 'OQC' ? 'OQC' : '' }}</p> -->
                <el-form :inline="true" class="demo-form-inline">
                  <el-form-item>
                    <hg-input v-model="batchSearch.nameOrGroupKeyword" :placeholder="$t('orderList.order.selectHolder')" @change="onSearch" clearable></hg-input>
                  </el-form-item>
                  <el-form-item>
                    <design-type-select class="searchData-order-design-type" v-model="batchSearch.designTypeCodes" @changeSearch="onSearch"></design-type-select>
                  </el-form-item>
                </el-form>
              </div>
              <!-- 内容列表 IQC和设计师OQC区分开 -->
              <!-- 设计师和OQC -->
              <div class="all-batch-list" v-loading="loadBatchList" v-if="batchList.length > 0">
                <div class="batch-list" v-for="(bacth, index) in batchList" :key="index">
                  <div v-if="bacth.isRecommended" class="batch-list-title-tuijian">{{ bacth.groupName }}({{$t('orderList.order.rangFirst')}})</div>
                  <div v-else class="batch-list-title">{{ bacth.groupName }}</div>
                  <div class="batch-list-content">
                    <div :class="['batch', nowSelectBatch.indexOf(bacthItm.userCode) != -1 ? 'now-select' : '']" v-for="(bacthItm, idx) in bacth.staffDtos" :key="idx" @click="selectBatch(bacthItm)">
                      <div class="img-box">
                        <img class="img" v-if="bacthItm.headImgurl" :src="bacthItm.headImgurl" alt="" />
                        <span class="img img-text" v-else>{{ bacthItm.name[0] }}</span>
                        <i class="on-line" :style="bacthItm.isOnline ? 'background:#72D143;' : 'background:#737680;'"></i>
                      </div>
                      <label
                        :class="['el-upload-list__item-status-label', !(nowSelectBatch.indexOf(bacthItm.userCode) != -1) ? 'no-select-status' : '']"
                        :style="nowSelectBatch.indexOf(bacthItm.userCode) != -1 ? 'display: block' : ''"
                      >
                        <i class="el-icon-upload-success el-icon-check"></i>
                      </label>
                      <div class="content-box">
                        <p class="name">{{ bacthItm.name }}</p>
                        <el-tooltip class="item" effect="dark" :disabled="bacthItm.zhRangeNames.length == 0 || bacthItm.enRangeNames.length == 0" 
                      :content="language == 'zh' ? setRangName(bacthItm.zhRangeNames) : setRangName(bacthItm.enRangeNames)" placement="bottom-start">
                        <p class="range">{{$t('orderList.order.rang')}}：{{ language == 'zh' ? setRangName(bacthItm.zhRangeNames) : setRangName(bacthItm.enRangeNames) }}</p>
                      </el-tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div v-loading="loadBatchList" v-else>
                <img class="no-data-img" src="@/assets/images/designPoints/pic-notablet.png">
                <span>暂无数据</span>
              </div> -->
              <div class="table-empty" v-loading="loadBatchList" v-else>
                <img class="no-data-img" src="@/assets/images/inbox-blank.svg" />
                <span style="margin-top: 10px;">{{$t('common.noData')}}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="btn-list">
          <span class="info"><i class="el-icon-info"></i> {{$t('orderList.order.errorAssign')}}</span>
          <el-button type="primary" :disabled="btnDisabled" @click="submitBatch">{{$t('heypoint.customer.operate.submit')}}</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import DesignTypeSelect from './design-type-select';
import { getBatchList, batchSetDesigner } from '@/api/order';
import { getToothInfo } from '@/public/utils/order';
export default {
  name: "batchDrawer",
  components: {
    DesignTypeSelect,
  },
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    // 选中的列表，批量处理
    selectSelection: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      assignType: 'DESIGNER',
      checkAll: false,
      indeterminate: false,
      ordersList: [
        {
          orderNo: "联合订单号1",
          isSelected: false,
          indeterminate: false,
          isDisabled: false,
          items: [
            { label: "设计品类1", isSelected: false, userCode: '', designer: '' },
            { label: "设计品类2", isSelected: false, userCode: '', designer: '' },
            { label: "设计品类3", isSelected: false, userCode: '', designer: '' }
          ]
        },
      ],
      loadBatchList: true,
      batchSearch: {
        //搜索
        nameOrGroupKeyword: null,
        designTypeCodes: [],
      },
      batchList: [],
      nowSelectBatch: [], //当前选中的id
      nowSelectBatchItem: [], //当前选中的item,传回父组件进行批量处理
    };
  },
  computed: {
    ...mapGetters(["language"]),
    batchDrawer: {
      get() {
        return this.drawer;
      },
      set(val) {
        this.$emit("update:drawer", val);
      },
    },
    isDisabled({ordersList}){
      return ordersList.some(item => item.isDisabled)
    },
    btnDisabled({ordersList}){
      let isHaveNoBatch = null
      ordersList.forEach((item) => {
        isHaveNoBatch = item.items.find((item => !item.userCode))
      })
      if(isHaveNoBatch){
        return true
      }
      return false;
    },
  },
  watch: {
    batchDrawer(newVal){
      if(newVal){
        this.init();
        this.getBatchList();
      } else {
        this.checkAll = false,
        this.indeterminate = false,
        this.ordersList = [];
        this.batchSearch = {
          nameOrGroupKeyword: null,
          designTypeCodes: [],
        };
      }
    }
  },
  mounted () {
    // this.getBatchList();
    // this.init();
  },
  methods: {
    closeDrawer(){
      this.$confirm(this.$t('orderList.order.istuichu'), this.$t('orderList.order.systemTips'), {
          confirmButtonText: this.$t('common.ok'),
          cancelButtonText: this.$t('common.cancel')
        }).then(() => {
          this.$emit('batchSuccessUnion');
        })
        .catch(action => {
          
        });
    },
    // 初始化设计品类列表
    init(){
      let ordersList = [];
      this.selectSelection.forEach((item) => {
        let newToothObj = {};
        newToothObj.orderNo = item.orderNo;
        newToothObj.orderCode = item.orderCode;
        newToothObj.isSelected = false;
        newToothObj.indeterminate = false;
        newToothObj.isDisabled = false;
        let newToothInfo = []
        let toothInfo = item.toothDesign && JSON.parse(item.toothDesign);
        toothInfo = getToothInfo(toothInfo) // 这一步是根据选择的通用符转换牙位号
        const processToothInfoRecursive = (toothInfo, newToothInfo) => {
          toothInfo.forEach((it) => {
            if (it.children && it.children.length) {
              processToothInfoRecursive(it.children, newToothInfo);
            } else {
              it.isSelected = false;
              it.userCode = '';
              it.designer = '';
              if(!newToothInfo.find((item) => {return item.code === it.code})){
                newToothInfo.push(it);
              }
            }
          });
        }
        processToothInfoRecursive(toothInfo, newToothInfo)
        newToothObj.items = newToothInfo;
        ordersList.push(newToothObj)
      })
      console.log(ordersList, 5555)
      this.ordersList = ordersList;
    },
    handleCheckAllChange(){
      this.ordersList.forEach(group => {
        group.isSelected = this.checkAll;
        group.indeterminate = false
        group.items.forEach(item => {
          item.isSelected = this.checkAll;
        });
      });
      if(this.checkAll){
        this.indeterminate = false;
      }
    },
    selectGroup(group) {
      group.items.forEach(item => {
        item.isSelected = group.isSelected;
      });
      this.indeterminate = this.ordersList.some(group => group.isSelected);
      this.checkAll = this.ordersList.every(group => group.isSelected);
      if(this.checkAll){
        this.indeterminate = false;
      }
    },
    updateParent(group) {
      group.indeterminate = group.items.some(item => item.isSelected);
      group.isSelected = group.items.every(item => item.isSelected);
      if(group.isSelected){
        group.indeterminate = false;
      }
      this.indeterminate = this.ordersList.some(group => group.indeterminate);
      this.checkAll = this.ordersList.every(group => group.isSelected);
      if(this.checkAll){
        this.indeterminate = false;
      }
    },
    // 清除单个已指派
    clearDesigner(index, itemIndex, item){
      this.$set(this.ordersList[index].items[itemIndex], 'userCode', '');
      this.$set(this.ordersList[index].items[itemIndex], 'designer', '');
      this.ordersList.forEach((group, index) => {
        group.isDisabled = group.items.some(it => !!it.userCode)
      })
    },
    // 搜索
    onSearch(designType, valueList) {
      if (designType == 'designType') {
        this.batchSearch.designTypeCodes = valueList;
      }
      this.getBatchList();
    },
    selectBatch(bacthItm) {
      if (this.nowSelectBatch.length > 0) {
        this.nowSelectBatch = [];
        this.nowSelectBatchItem = [];
      }
      this.nowSelectBatchItem.push(bacthItm);
      this.nowSelectBatch.push(bacthItm.userCode);
    },
    // 重置所有选中
    resetAllSelect(){
      this.checkAll = false;
      this.indeterminate = false;
      this.ordersList.forEach((group, index) => {
        group.isDisabled = false;
        group.isSelected = false;
        group.indeterminate = false;
        group.isDisabled = false;
        group.items.forEach((item) => {
          item.userCode = '';
          item.designer = '';
          item.isSelected = false;
        })
      })
    },
    // 点击确认按钮进行指派
    batchDesignInLeftList(){
      const bacthItm = this.nowSelectBatchItem[0];
      this.ordersList.forEach((group, index) => {
        group.items.forEach((item) => {
          if(item.isSelected && !item.userCode){
            item.userCode = bacthItm.userCode;
            item.designer = bacthItm.name;
          }
        })
        group.isDisabled = group.items.some(it => !!it.userCode)
      })
    },
    computedDis(group){
      return group.items.some(it => !!it.userCode)
    },
    // 获取指派角色列表
    getBatchList() {
      this.loadBatchList = true;
      // 重置选中的列表
      this.nowSelectBatch = [];
      this.nowSelectBatchItem = [];
      let orderCustomerOrgCode = null;
      //这里是订单列表的处理逻辑
      if (this.selectSelection && this.selectSelection.length > 0) {
        let codeArr = [];
        this.selectSelection.forEach((item) => {
          codeArr.push(item.orgCode);
        });
        codeArr = Array.from(new Set(codeArr));
        if (codeArr.length == 1) {
          orderCustomerOrgCode = codeArr[0];
        }
      }
      let designCodes = this.batchSearch.designTypeCodes;
      let nameOrGroupKeyword = this.batchSearch.nameOrGroupKeyword;
      let staffTypeCode = 2;
      getBatchList(designCodes, nameOrGroupKeyword, staffTypeCode, orderCustomerOrgCode).then((res) => {
        if (res.code === 200) {
          let batchList = res.data;
          // 当指派的是IQC时需要特殊处理数组
          if (this.assignType == 'IQC') {
            let IQCArr = [];
            batchList.forEach((item) => {
              IQCArr = IQCArr.concat(item.staffDtos);
            });
            this.batchList = IQCArr;
            this.loadBatchList = false;
          } else {
            this.batchList = batchList;
            this.loadBatchList = false;
          }
        } else {
          this.loadBatchList = false;
        }
      });
    },
    // 处理范围
    setRangName(arr) {
      let str = '';
      arr.forEach((it) => {
        str += it + '、';
      });
      if (str) str = str.slice(0, str.length - 1);
      return str;
    },
    // 提交批量指派
    submitBatch(){
      console.log(this.ordersList)
      // 如果有没指派的，不允许提交
      if(this.ordersList.some((item => !item.isDisabled))){
        this.$message.error(this.lang('assignAll'))
      }
      const findExistingOrder = (outputArray, userCode, orderCode) => {
          return outputArray.find((item) => item.designUser === userCode && item.orderCode === orderCode);
      }
      let outputArray = [];

      this.ordersList.forEach((order) => {
        order.items.forEach((item) => {
          let designType = {
            code: item.code
          };

          let existingOrder = findExistingOrder(outputArray, item.userCode, order.orderCode);

          if (existingOrder) {
            existingOrder.designTypes.push(designType);
          } else {
            outputArray.push({
              designTypes: [designType],
              designUser: item.userCode,
              orderCode: order.orderCode
            });
          }
        });
      });
      try {
        batchSetDesigner(outputArray).then((res) => {
          if(res.code == 200){
            this.$message.success(this.$t('orderList.order.assignSuccess'));
            // this.batchDrawer = false;
            this.$emit('batchSuccessUnion')
          }
        })
      } catch (error) {
        this.$message.error(this.$t('orderList.order.assignerror'));
      }
      console.log(outputArray, 4444);
    }
  },
};
</script>

<style lang="scss" scoped>
/deep/.batch-drawer {
  width: 900px !important;
  background-color: $hg-main-black;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
    color: $hg-label;
  }
  .el-drawer__body {
    padding: 24px;
    overflow: hidden;
  }
  .el-button.is-plain:focus{
    background: transparent;
  }
  .el-button--primary:focus{
    background: #3760EA;
    border-color: #3760EA;
  }
  .batch-content{
    height: 100%;
    width: 100%;
    .all-content{
      display: flex;
      width: 100%;
      height: calc(100% - 84px);
      .left-select{
        display: flex;
        flex-direction: column;
        width: 312px;
        height: 100%;
        overflow: hidden;
        background: $hg-hover;
        margin-right: 16px;
        .el-checkbox__label{
          color: #F3F5F7;
        }
        .el-checkbox__inner{
          border-color: #F3F5F7;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner {
          border-color: #3760EA;
        }
        .el-checkbox__input.is-indeterminate .el-checkbox__inner{
          border-color: #3760EA;
        }
        .el-checkbox__input.is-disabled .el-checkbox__inner{
          background: rgba(255, 255, 255, 0.24);
          border-color: rgba(255, 255, 255, 0.24);
        }
        .el-checkbox__input.is-disabled + span.el-checkbox__label{
          color: rgba(255, 255, 255, 0.24);
        }
        .top-select-all{
          position: relative;
          display: flex;
          align-items: center;
          height: 56px;
          padding: 0 24px;
          border-bottom: 1px solid $hg-main-border;
          .el-checkbox__label{
            color: #fff;
          }
          .el-button--primary{
            position: absolute;
            right: 24px;
            height: 32px;
            width: 56px;
            padding: 8px 16px;
            font-size: 12px;
          }
        }
        .all-select-list{
          flex: 1;
          padding: 24px;
          height: 100%;
          overflow: auto;
          .select-list{
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
            .checkbox-list{
              display: flex;
              flex-direction: column;
              .el-checkbox{
                margin-left: 28px;
                margin-top: 20px;
              }
              .designer-box{
                display: inline-block;
                padding: 4px;
                background: $hg-main-blue;
                font-size: 12px;
                color: #fff;
                border-radius: 4px;
                margin-left: 6px;
              }
              .clearbox{
                display: inline-block;
                // font-size: 14px;
                color: #fff;
                cursor: pointer;
              }
            }
          }
        }
      }
      .right-select{
        flex: 1;
        height: 100%;
        overflow: hidden;
        background: $hg-hover;
        .top-select-all{
          position: relative;
          display: flex;
          align-items: center;
          height: 56px;
          padding: 0 24px;
          border-bottom: 1px solid $hg-main-border;
          .el-button--primary{
            position: absolute;
            right: 24px;
            height: 32px;
            width: 56px;
            padding: 8px 16px;
            font-size: 12px;
          }
        }
      }
      .right-batch-content {
        height: 100%;
        padding: 20px 0 20px 20px;
        .batch-header .batch-title {
          color: #fff;
          height: 28px;
        }
        /deep/.el-form-item{
          margin-right: 16px;
          &:last-child{
            margin-right: 0;
          }
        }
        /deep/.el-form--inline {
          .el-form-item__content {
            width: 226px;
          }
          .el-select {
            width: 240px;
          }
        }
        .all-batch-list {
          height: calc(100% - 100px);
          width: 100%;
          overflow: auto;
        }
        .iqc-batchlist {
          height: 360px;
          width: 496px;
          overflow: auto;
        }
        .batch-list {
          .batch-list-title {
            color: #fff;
            height: 28px;
            margin-top: 6px;
          }
          .batch-list-title-tuijian {
            color: #3760ea;
            height: 28px;
          }
          .batch-list-content {
            display: flex;
            flex-wrap: wrap;
            .batch {
              display: flex;
              padding: 20px;
              width: 226px;
              height: 112px;
              border: 1px solid #2d2f33;
              margin-right: 10px;
              margin-bottom: 10px;
              cursor: pointer;
              position: relative;
              overflow: hidden;
              .img-box {
                width: 48px;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 10px;
                position: relative;
                .on-line {
                  display: inline-block;
                  position: absolute;
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  right: 0;
                  top: 50px;
                }
              }
              .el-upload-list__item-status-label {
                display: none;
                position: absolute;
                right: -17px;
                top: -7px;
                width: 46px;
                height: 26px;
                background: $hg-main-blue;
                text-align: center;
                transform: rotate(45deg);
                box-shadow: 0 1px 1px #ccc;
                i {
                  font-size: 12px;
                  margin-top: 12px;
                  transform: rotate(-45deg);
                }
                .el-icon-check {
                  color: #fff;
                }
              }
              .img-box .img {
                width: 48px;
                height: 48px;
                display: inline-block;
                border-radius: 50%;
              }
              .img-text {
                background-color: #3054cc;
                width: 48px;
                height: 48px;
                line-height: 48px;
                display: block;
                text-align: center;
                color: #e4e8f7;
                font-weight: bold;
                font-style: normal;
              }
              .content-box {
                width: 128px;
                height: 100%;
                // display: flex;
                // flex-direction: column;
                // justify-content: center;
                // margin-right: 10px;
              }
              .name {
                color: #fff;
                margin-bottom: 8px;
                font-weight: 700;
                line-height: 24px;
              }
              .range {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                color: #fff;
                line-height: 20px;
                font-size: 14px;
                font-weight: 400;
              }
            }
            .now-select {
              border: 1px solid $hg-main-blue;
            }
            .batch:hover {
              background: $hg-hover;
              .no-select-status {
                display: block;
                background: #2d2f33;
              }
            }
          }
        }
      }
    }
    .btn-list{
      position: absolute;
      bottom: 24px;
      right: 0px;
      display: flex;
      justify-content: flex-end;
      width: 100%;
      height: 60px;
      padding: 20px 24px 0 0;
      border-top: 1px solid $hg-main-border;
      .info{
        position: absolute;
        bottom: 10px;
        left: 24px;
        color: #FBAA0E;
      }
    }
  }
  .table-empty{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 400px;
  }
}
</style>
