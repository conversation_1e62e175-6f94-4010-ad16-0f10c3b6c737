<!-- <template>
  <div>
    <el-dialog :title="$t('customer.uploadPrice')" :visible.sync="uploadDialog" width="654px" custom-class="pirce-upload-box" :close-on-click-modal="!uploadLoading" :modal-append-to-body="false">
      <div v-loading="uploadLoading">
        <el-upload
          class="price-upload-demo"
          action="#"
          accept=".xlsx"
          :auto-upload="false"
          :show-file-list="false"
          :limit="1"
          :on-change="uploadChange"
          :file-list="uploadFileList"
          :key="uploadKey"
        >
          <span class="upload-icon">+</span>
          <div class="el-upload__text">{{$t('customer.drag')}}</div>
        </el-upload>
        <div class="download-btn" @click="downSystemBill"><span class="el-icon-download down-icon"></span><span class="btn-text">{{$t('customer.downloadPrice')}}</span></div>
      </div>
    </el-dialog>
  </div>
</template> -->
<template>
  <div>
    <el-drawer
    :title="$t('customer.uploadPrice')"
      custom-class="uploadprice-drawer"
      :visible.sync="uploadDialog"
    >
      <div v-show="!isImportExcel" class="point-upload-box" v-loading="uploadLoading">
        <el-upload
          v-if="uploadDialog"
          id="repointUpload"
          class="upload-demo"
          drag
          ref="repointUpload"
          action="#"
          :accept="'.xlsx'"
          :auto-upload="false"
          :limit="1"
          :show-file-list="false"
          :on-change="uploadChange"
          :key="uploadKey"
        >
          <span class="upload-icon">+</span>
          <div class="el-upload__text">
            <p>{{$t('customer.drag')}}</p>
            <!-- <p style="margin-top: 6px;">({{lang('limitTips')}})</p> -->
          </div>
        </el-upload>
        <div class="download-btn" @click="downSystemBill">
          <span class="el-icon-download down-icon"></span>
          <span class="btn-text">{{$t('customer.downloadPrice')}}</span>
        </div>
      </div>
      <div class="table-list">
        <div class="import-title" v-if="isImportExcel">
          <div v-if="language == 'zh'">共计{{ importTableList.length }}个，导入<span class="success">成功{{ successList.length }}</span>个，<span class="error">失败{{ errorList.length }}</span>个</div>
          <div v-else>{{ importTableList.length }} in total:<span class="success">{{ successList.length }}imported, </span>,<span class="error">{{ errorList.length }} failed.</span></div>
        </div>
        <!-- 列表 -->
        <div class="depart-table" v-if="isImportExcel">
          <new-table class="user-table" :hasIndex="true" :loading="tableLoading" :data="newMountedList" :header-data="headerData">
            <template #designName="{ row }">
              <span>{{language == 'zh' ? row.designName : row.designEnName}}</span>
            </template>
            <template #sku="{ row }">
              <span>{{row.sku}}</span>
            </template>
            <template #price="{ row }">
              <span>{{row.price}}</span>
            </template>
            <template #result="scope">
              <span v-if="scope.row.result === '成功'">{{$t('customer.success')}}</span>
              <span class="error-tips" v-else>{{$t('customer.fail')}}</span>
            </template>
          </new-table>
          <div class="depart-pagination">
            <Pagination :total="page.total" :page-size="page.pageSize"
            :pageSizes="[10, 20, 50, 100]" :page-no="page.pageNo" @changePageSize="changePageSize" @changePageNo="changePageNo" />
          </div>
        </div>
        <!-- 按钮组 -->
        <div class="btn-list" v-if="isImportExcel">
          <el-button style="background: transparent;" plain @click="submitPoint">{{$t('customer.cancle')}}</el-button>
          <el-button :disabled="importTableList.length <= 0" type="primary" @click="submitPoint">{{$t('common.confirm')}}</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
// import { getLang } from '@/public/utils';
// import SliceUpload from '@/public/utils/SliceUpload';
import { getDesignPriceImport } from '@/api/customer'
import newTable from '@/components/func-components/newTable.vue'
import Pagination from '@/components/func-components/Pagination'
import { getStore } from '@/assets/script/utils.js'
export default {
  name: "uploadbox",
  components: {
    newTable,
    Pagination
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: true,
    },
    orgCode: [Number, String],
    acceptType: String,
  },
  computed: {
    uploadDialog: {
      get() {
        return this.dialogVisible;
      },
      set(val) {
        this.$emit("update:dialogVisible", val);
      },
    },
    language() {
      return getStore('lang')
    },
    headerData() {
      return [
        {
          prop: 'designName',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.designApplication');
          },
        },
        {
          prop: 'sku',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.artNo');
          },
        },
        {
          prop: 'price',
          minWidth: '20%',
          getLabel: () => {
            return this.$t('customer.unitPrice');
          },
        },
        {
          prop: 'result',
          minWidth: '20%',
          getLabel: () => {
            return this.$t('customer.importedStatus');
          },
        },
      ]
    },
  },
  data() {
    return {
        uploadFileList: [],
        uploadLoading: false,
        uploadKey: 1,
        isImportExcel: false,
        importTableList: [],
        errorList: [],
        successList: [],
        page: {
          pageSize: 20,
          pageNo: 1,
          total: 0,
        },
        tableLoading: false,
        newMountedList: [],
    };
  },
  watch: {
    uploadDialog(newValue, oldValue) {
      if(!newValue){
        if(this.importTableList.length > 0){
          this.$emit('uploadSuccess')
        }
        this.newMountedList = [];
        this.isImportExcel = false;
        this.importTableList = [];
        this.successList = [];
        this.errorList = [];
      }
    }
  },
  methods: {
    // lang: getLang('bill'),
    async uploadChange(file) {
      this.isImportExcel = true;
      this.tableLoading = true;
      const regRex = /\.(xlsx)$/g;
      if(!regRex.test(file.name.toLowerCase())){
        this.$message.error(this.$t('customer.uploadError'))
        this.uploadKey ++;
        return false;
      }
      const param = new FormData()
      param.append('uploadFile', file.raw)
      param.append('orgCode', this.orgCode)
      try {
        const { code, data } = await getDesignPriceImport(param)
        if (code === 200) {
          // 获取成功和失败的总和
          this.importTableList = data;
          this.page.total = this.importTableList.length;
          this.page.pageNo = 1;
          this.page.pageSize = 20;
          this.newMountedList = this.importTableList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize)
          this.errorList = data.filter((item) => {
            return item.result === "失败";
          });
          this.successList = data.filter((item) => {
            return item.result === "成功";
          });
          this.tableLoading = false;
          // console.log(data, 33333);
        } else {
          this.isImportExcel = false;
          this.tableLoading = false;
          return;
        }
      } catch (error) {
        if(error.code == 60010030){
          this.$message.error(this.$t('60027002'))
          this.isImportExcel = false;
          this.tableLoading = false;
          this.uploadKey ++;
        }
      }
      this.uploadFileList = [];
    },
    // 校验文件
    verifyFile(file, fileList) {
      // if(this.acceptType !== '.xlsx'){
        const regRex = /\.(xlsx)$/g;
        if(!regRex.test(file.name.toLowerCase())){
          this.$hgOperateFail(this.lang('leftDrawer.uploadError'));
          return false;
        }
      // }
      return true;
    },
    // 下载系统账单
    downSystemBill(){
      this.$emit('downSystemBill')
    },
    submitPoint(){
      this.uploadDialog = false;
      // this.$emit('uploadSuccess')
    },
    changePageSize(val) {
      this.page.pageSize = val
      this.page.pageNo = 1
      this.newMountedList = this.asyncCrmList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize)
    },
    changePageNo(val) {
      this.page.pageNo = val;
      this.newMountedList = this.importTableList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize)
    },
    // 币种
    getCurrency(row){
      if(!row || !row.currency) return ''
      let obj = {
        0: '$',
        1: '¥',
        2: '€',
        3: '¥'
      }
      return obj[row.currency]
    }
  },
};
</script>

<style lang="scss">
.uploadprice-drawer {
  width: 800px !important;
  background-color: #1B1D22;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
    color: #F3F5F7;
  }
  .el-drawer__body {
    padding: 24px;
    overflow: hidden;
  }
  .point-upload-box {
    width: 100%;
    height: 656px;
    background: #27292E;
    padding: 20px;
  }
  .el-upload {
    width: 100%;
  }
  .el-upload-dragger {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #27292E;
    width: 100%;
    height: 566px;
    border-color: #3D4047;
    .upload-icon {
      font-size: 50px;
      color: #9EA2A8;
    }
  }
  .download-btn {
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    align-items: center;
    color: #3760EA;
    margin-top: 20px;
    cursor: pointer;
  }
  .btn-text {
    vertical-align: top;
    margin-left: 8px;
  }
  .table-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 60px);
    .import-title {
      color: #fff;
      line-height: 40px;
      .success {
        color: #00b860;
        margin: 0 8px;
      }
      .error {
        color: #e55353;
        margin: 0 8px;
      }
    }
    .depart-table {
      position: relative;
      // flex: 1;
      height: calc(100% - 20px);
      width: 100%;
      .hg-table {
        height: 100%;
        // height: calc(100% - 30px);
        overflow: hidden;
        .el-table {
          // height: calc(100% - 60px)!important;
          // max-height: 100% !important;
        }
      }
      .table-high-light {
        float: left;
        width: auto;
        max-width: calc(100% - 41px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .expedited-time {
        line-height: 40px;
      }
      .error-tips{
        color: #E55353;
      }
    }
    .depart-pagination {
      z-index: 1;
      position: absolute;
      bottom: 0;
      right: 0;
      height: 60px;
      width: 100%;
    }
    .btn-list{
      position: absolute;
      bottom: 24px;
      right: 10px;
      display: flex;
      justify-content: flex-end;
      width: 100%;
      height: 50px;
      padding: 10px 24px 0 0;
      border-top: 1px solid #3D4047;;
    }
  }
  .el-button.is-plain:focus{
    background: transparent;
  }
  .el-button--primary:focus{
    background: #3760EA;
    border-color: #3760EA;
  }
}
</style>
</style>
