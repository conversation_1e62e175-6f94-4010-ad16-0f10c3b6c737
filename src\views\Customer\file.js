/**
 * 订单批量下载后端直接返回可下载的链接
 * @param {String} fileUrl 可以直接下载的链接
 * @param {String} fileName 文件名
 */
export const directDown = (fileUrl, fileName) => {
  return new Promise(resovle => {
    ajaxGetBlob(fileUrl)
      .then((blob) => {
        handleSaveByBlod(blob, fileName)
        resovle(true)
      })
  })
}

// 不带token的
export const directDown2 = (fileUrl, fileName) => {
  return new Promise(resovle => {
    ajaxGetBlob2(fileUrl)
      .then((blob) => {
        handleSaveByBlod(blob, fileName)
        resovle(true)
      })
  })
}

/**
 * 请求文件地址获取blob类型数据
 * @param {*} url 文件地址
 * @param {*} progress 进度回调函数
 */
const ajaxGetBlob = (url, progress) => {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest()
    const token = window.localStorage.getItem('AccessToken')
    xhr.open('GET', url, true)
    xhr.setRequestHeader('authorization', `Bearer ${token}`)
    xhr.responseType = 'blob'
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response)
      }
    }
    xhr.onprogress = (e) => {
      progress && progress(((e.loaded / e.total) * 100) | 0)
    }

    xhr.send()
  })
}

// 不带token的
const ajaxGetBlob2 = (url, progress) => {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response)
      }
    }
    xhr.onprogress = (e) => {
      progress && progress(((e.loaded / e.total) * 100) | 0)
    }

    xhr.send()
  })
}

/**
 * 通过bold对象保存文件 （可重命名）
 * @param {*} blob blob数据
 * @param {*} filename 文件名称
 */

const handleSaveByBlod = (blob, filename) => {
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename)
  } else {
    const link = document.createElement('a')
    const body = document.querySelector('body')
    link.href = window.URL.createObjectURL(blob)
    link.download = filename
    // fix Firefox
    link.style.display = 'none'
    body.appendChild(link)
    link.click()
    body.removeChild(link)
    window.URL.revokeObjectURL(link.href)
  }
};
