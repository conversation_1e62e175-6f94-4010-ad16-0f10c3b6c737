/**
 * <PERSON>'s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
 * http://cssreset.com
 */
 @font-face {
  font-family: "webfont";
  font-display: swap;
  src: url("//at.alicdn.com/t/webfont_wkillkd0i1.eot"); /* IE9*/
  src: url("//at.alicdn.com/t/webfont_wkillkd0i1.eot?#iefix") format("embedded-opentype"),
    /* IE6-IE8 */ url("//at.alicdn.com/t/webfont_wkillkd0i1.woff2") format("woff2"),
    url("//at.alicdn.com/t/webfont_wkillkd0i1.woff") format("woff"), /* chrome、firefox */
      url("//at.alicdn.com/t/webfont_wkillkd0i1.ttf") format("truetype"),
    /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
      url("//at.alicdn.com/t/webfont_wkillkd0i1.svg#NotoSansHans-DemiLight")
      format("svg"); /* iOS 4.1- */
}

 html, body, div, span, applet, object, iframe,
 h1, h2, h3, h4, h5, h6, p, blockquote, pre,
 a, abbr, acronym, address, big, cite, code,
 del, dfn, em, img, ins, kbd, q, s, samp,
 small, strike, strong, sub, sup, tt, var,
 b, u, i, center,
 dl, dt, dd, ol, ul, li,
 fieldset, form, label, legend,
 table, caption, tbody, tfoot, thead, tr, th, td,
 article, aside, canvas, details, embed, 
 figure, figcaption, footer, header, hgroup, 
 menu, nav, output, ruby, section, summary,
 time, mark, audio, video{
   margin: 0;
   padding: 0;
   border: 0;
   font-weight: normal;
   vertical-align: baseline;
   box-sizing: border-box;
   -webkit-box-sizing: border-box; 
   -moz-box-sizing: border-box;

   &::after,
   &::before {
    margin: 0;
    padding: 0;
    border: 0;
    font-weight: normal;
    vertical-align: baseline;
    box-sizing: border-box;
    -webkit-box-sizing: border-box; 
    -moz-box-sizing: border-box;
   }

   :focus {
     outline: none;
   }
 }
 /* HTML5 display-role reset for older browsers */
 article, aside, details, figcaption, figure, 
 footer, header, hgroup, menu, nav, section{
   display: block;
 }
 ol, ul, li{
   list-style: none;
 }
 blockquote, q{
   quotes: none;
 }
 blockquote:before, blockquote:after,
 q:before, q:after{
   content: '';
   content: none;
 }
 table{
   border-collapse: collapse;
   border-spacing: 0;
 }
  
 /* custom */
 a{
   color: #7e8c8d;
   text-decoration: none;
   -webkit-backface-visibility: hidden;
 }
 ::-webkit-scrollbar{
   width: 6px;
   height: 6px;
 }
 ::-webkit-scrollbar-track-piece{
   background-color: rgba(0, 0, 0, 0.2);
   -webkit-border-radius: 6px;
 }
 ::-webkit-scrollbar-thumb:vertical{
   height: 6px;
   background-color:rgb(56, 57, 61);
   -webkit-border-radius: 6px;
 }
 ::-webkit-scrollbar-thumb:horizontal{
   width: 6px;
   background-color:rgb(56, 57, 61);
   -webkit-border-radius: 6px;
 }
 html, body{
   width: 100%;
   font-family:"webfont" !important;
 }
 body{
   overflow: hidden;
   line-height: 1;
   font-size: 14px;
   -webkit-text-size-adjust: none;
   -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
 }
  
 /*清除浮动*/
 .clearfix:before,
 .clearfix:after{
   content: " ";
   display: inline-block;
   height: 0;
   clear: both;
   visibility: hidden;
 }
 .clearfix{
   *zoom: 1;
 }
 pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: keep-all;
  font-family:"webfont";
 }
 
 // 引入阿里图标库
 @import url('//at.alicdn.com/t/c/font_3443328_1gjmjzgyzi9.css'); // lab
 @import url('//at.alicdn.com/t/c/font_3979264_d5ray3ooxi.css');// 项目[HG-公共组件] 要用这里又要用hg-icon组件，icon-name必须带上hg-common-iconfont
 @import './transition.scss'