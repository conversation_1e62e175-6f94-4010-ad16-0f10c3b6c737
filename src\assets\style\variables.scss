// 项目中的主题色全部定义在这里
// 开发过程中，颜色配置尽量使用这里
// 命名规则：样式 $hg-xxx   class .hg-xxx


$hg-main-black: #1B1D22;
$hg-main-blue: #3760EA;

$hg-primary-text: #DADFED;
$hg-secondary-text: #C4C8CD;

$hg-disable: #54565C;
$hg-border: #38393D;
$hg-hover: #27292E;
$hg-background: #141519;
$hg-tagging: #2E313D;

$hg-border-second: #2D2F33;
$hg-white: #FFFFFF;
$hg-new-tag: #DC5050;
$hg-update:  #FFA01E;

$hg-link: #5169B8;
$hg-success: #53AE26;
$hg-warning: #C78840;
$hg-error: #C74040;
$hg-remind: #D68E3C;

$hg-unfinish: #343438;

$hg-label: #F3F5F7;
$hg-grey: #737680;
$hg-red: #FF5A5A;

// 默认按钮颜色配置
$hg-btn-primary-hover: #3760EB;
$hg-btn-primary-hover-text: #F3F5F7;
$hg-btn-primary-active: #2B4BB8;
$hg-btn-primary-active-text: #C5C9D6;

// 禁用按钮颜色配置
$hg-btn-disabled: $hg-disable;
$hg-btn-disabled-text: #F3F5F7;

// 次要按钮颜色配置
$hg-btn-second: transparent;
$hg-btn-second-border: #38393D;
$hg-btn-second-text: #F3F5F7;
$hg-btn-second-hover-border: $hg-btn-disabled;
$hg-btn-second-active-border: #27292E;
$hg-btn-second-active-text: #BCBFCC;


// 文字按钮颜色配置
$hg-btn-text-hover: #3760EB;
$hg-btn-text-active: #2B4BB8;
$hg-btn-text-disabled-text: $hg-btn-disabled;

// 危险按钮颜色配置
$hg-btn-danger: $hg-error;
$hg-btn-danger-text: #F3F5F7;
$hg-btn-danger-hover:#DB4747;
$hg-btn-danger-active: #B23939;
$hg-btn-danger-active-text: #C5C9D6;


$mainColorBlue: #3054cc;
$backgroundColor: #121213;
$fontColorPrimary: #F3F5F7;
$mainColorDark: #1B1D22;
$hoverBgcColor: #27292E;
$fontColorSecondary: #C4C8CD;
$borderColor: #38393D;
$errorColor: #C74040;
$fontSizeNormal: 14px;
$marginBottom: 24px;
$headerHeight: 60px;
$fontSizeSmall: 12px;
$fontSizeBig: 16px;

$hg-default-text-color: #F7F8FA;
// 盒子阴影
/* .hg-box-shadow {
  border-radius: 4px;
  background: #1B1D22;
  box-shadow: 0px 12px 32px 0px $hg-background,0px 8px 24px 0px $hg-background,0px 0px 16px 0px $hg-background;
} */

// 自动以样式
/* @mixin box-shadow {
  box-shadow: 0px 4px 12px rgba(18, 19, 20, 0.35), 0px 0px 12px rgba(18, 19, 20, 0.08);
} */
// 4.3.36 UI颜色上有调整，慢慢调整迁移
$hg-main-primary: #3760EA; // 主题色-按钮
$hg-main-danger: #F64C4C; // 按钮
$hg-main-success: #00AA6D; // 按钮
$hg-main-warn: #FBAA0E; // 按钮
$hg-main-border: #3D4047; // 分割线/边框

// 文字-描边-纯图标 - start
$hg-secondary-primary: #5F8AFF; 
$hg-secondary-danger: #EB6F70; 
$hg-secondary-success: #72D143;
$hg-secondary-warn: #FFC24B;
$hg-secondary-border: #3D4047;
// 文字-描边-纯图标 - end

$hg-main-mask:rgba(20, 21, 25, 0.75); // 蒙版色 全局蒙版