// https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/postcss-load-config

module.exports = {
  plugins: {
    //
    // autoprefixer: {
    //   //该插件给css添加前缀，与本内容无关，该变量可忽略
    //   browsers: ["Android >= 4.0", "iOS >= 7"]
    // },
    // "postcss-pxtorem": {
    //   rootValue: 14, //这里填设计稿根元素字体大小，结果为：设计稿元素尺寸/14，比如设计稿某个元素宽140px,最终页面元素宽度会换算成(140/14=10)rem
    //   propList: ["*"] //适用的元素，这里所有元素都需要转化为rem单位，使用*表示通配
    // }
  }
}
