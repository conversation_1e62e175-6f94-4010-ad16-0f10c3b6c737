export default {
  bill: {
    customerName: 'Client Name',
    customerSn: 'Client ID',
    day: 'Daily',
    week: 'Weekly',
    month: 'Monthly',
    date: 'Date:',
    settleState: 'Statement Status:',
    // billStatus: 'Statement Status',
    nameTips: 'Please enter a customer name',
    startTime: 'Choose a start time',
    endTime: 'Choose a end time',
    number: 'Statement No.',
    billDate: 'Date',
    billState: 'Statement Status',
    heypointCost: 'Service Charge',
    detail: 'Statement Details',
    selectStartTime: 'Please select a start time',
    selectEndTime: 'Please select the end time',
    selectTimeTips: 'Please select the date!',
    selectTimeTip: 'Please select the date',
    timeLimitTip: 'The start time cannot be later than the end time',
    dayBillDateTips: 'Daily billing inquiries should be within one month!',
    monthBillDateTips: 'Monthly billing inquiries should be within one year!',
    settled: 'Finalized Fee',
    unsettled: 'Pending',

    billdate: 'Date',
    currency: 'Currency',
    total: 'Total',
    deduction: 'Deduction (Complimentary)',
    freeorder: 'Free (Rejected Order)',
    payable: 'Payable | Payable with VAT',
    billStatus: 'Status',
    billdetails: 'Bill Details',
    hide: 'Hide no-order bills',
    multiDownload: 'Multi-download',
    currencyChange: 'Currency-changed only',
    exportBill: 'Export Bill',
    pushSaleOrder: 'Push Sales Orders',

    leftDrawer: {
      crmNoContactError:'These bills have been sent to CRM, but their submission failed due to recipient information missing. Their Change Orders will not be generated if the bills fail to be approved. Please log on CRM and submit the bill again.',
      bill: 'Statement',
      detail: 'Order Details',
      download: 'Download',
      manual: 'Manual',
      submit: 'Submit',
      reback: 'Withdraw',
      gtm: 'Return to GTM',
      crm: 'Send Sales Orders',
      noOrder: 'No orders.',
      reminding: 'Reminding',
      uploadTips: 'Please upload the manually edited Statement and Order Details by dragging them here. ',
      uploadbtn: 'Download the initial Statement and Order Details created by the system.',
      reason: 'Challenge Reason',
      submitTips: "Please review the Statement and Order Details first. Once you submit, they'll be sent to clients. Sure to submit?",
      GTMTips: 'Sure to return this bill?',
      backTips: 'Sure to withdraw this bill?',
      pushCRMtips: 'Sure to send this bill to CRM?',
      draft: 'Draft',
      all: 'All',
      unconfirmed: 'Unconfirmed',
      confirm: 'Confirmed',
      chanllenged: 'Challenged',
      cny: 'CNY',
      usd: 'USD',
      eur: 'EUR',
      jpy: 'JPY',
      aud: 'AUD',
      uploadError: 'Please upload files in XLSL format.',
      successful: 'Successful!',
      backsuccess: 'Successful!',
      pushSuccess: 'Successful!',
      pushError: 'Failed to create this order on CRM.',
      pushUpdataError:'Failed to create this update order on CRM',
      nosent: 'Not sent',
      sent: 'Sent',
      faile: 'Failed',
      errordialog: 'Failed to send to CRM',
      know: 'Got it',
      sendcrm: 'Send Sales Orders',
      pushUpdateCRM:'Send Update Orders',
      deleteError: 'Deletion failed',
      saleOrderStatus:'order',
      updateOrderStatus:'Change order'
    },

    info: {
      billManager: 'Bill management',
      billDate: 'Date',
      billDetail: 'Statement Details',
      exportBill: 'Download',
      customerName: 'Customer Name',
      settlementType: 'Payment Way',
      settlementCurrency: 'Currency',
      salesManager: 'Sales Manager',
      heypointCost: 'Total Cost',
      giveHeypoint: 'Used (Free Credit)',
      rechargeCost: 'Used (Paid Credit)',
      useOverdraft: 'Available Overdraft',
      USD: 'USD',
      EUR: 'EUR',
      CNY: 'CNY',
      JPY: 'JPY',
      AUD: 'AUD',
      discountRate: 'Discount Rate',
      amountSpendOnThisBill: 'Amount spend on this bill',
      debt: 'Debt',
      number: 'No.',
      designType: 'Design Type',
      materialNo: 'Material ID',
      standardPrice: 'Price',
      totalCost: 'Total Cost',
      exchangeRate: 'Exchange Rate',
      amountPayable: 'Payable',
      quantity: 'Amount'
    }
  }
}