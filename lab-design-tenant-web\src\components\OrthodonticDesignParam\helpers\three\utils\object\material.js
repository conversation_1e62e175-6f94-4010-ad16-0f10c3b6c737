import {
  traverseTreeParent,
  traverseTreeChildren,
  hasOwn,
} from '@/components/OrthodonticDesignParam/helpers/utils/index'

export function checkObjectMaterialVisible(object) {
  const { material, hiddenMaterial } = object

  if (hasOwn('hiddenMaterial')) {
    return !hiddenMaterial
  }

  if (!material) {
    return !hiddenMaterial
  }

  if (material instanceof Array) {
    return material.some((item) => {
      return item.visible
    })
  } else {
    return material.visible
  }
}

export function visibleObjectMaterialOnly(object, visible) {
  object.hiddenMaterial = !visible

  const { material } = object

  if (!material) {
    return
  }

  if (material instanceof Array) {
    for (const materialItem of material) {
      materialItem.visible = visible
    }
  } else {
    material.visible = visible
  }
}

export function visibleObjectMaterial(object, visible, { deepParent, deepChildren, deepFilter } = {}) {
  if (visible && deepParent) {
    const allParentVisible = traverseTreeParent(object, (parent) => {
      return checkObjectMaterialVisible(parent)
    })

    if (!allParentVisible) {
      visibleObjectMaterial(object, false, { deepChildren })
      return
    }
  }

  visibleObjectMaterialOnly(object, visible)

  if (deepChildren) {
    traverseTreeChildren([object], (child) => {
      if (object === child) {
        return true
      }

      if (deepFilter) {
        const effect = deepFilter(child)
        effect && visibleObjectMaterialOnly(child, visible)
      } else {
        visibleObjectMaterialOnly(child, visible)
      }

      return true
    })
  }
}

function _setObjectMaterialPropertyOnly(object, property, value) {
  const { material } = object

  if (!material) {
    return
  }

  if (material instanceof Array) {
    for (const materialItem of material) {
      materialItem[property] = value
    }
  } else {
    material[property] = value
  }
}

function _setObjectMaterialProperty(object, property, value, { deepParent, deepChildren, deepFilter } = {}) {
  _setObjectMaterialPropertyOnly(object, property, value)

  if (deepChildren) {
    traverseTreeChildren([object], (child) => {
      if (object === child) {
        return true
      }

      if (deepFilter) {
        const effect = deepFilter(child)
        effect && _setObjectMaterialProperty(child, property, value)
      } else {
        _setObjectMaterialProperty(child, property, value)
      }

      return true
    })
  }
}

export function setObjectMaterialProperty() {
  const args = [...arguments]
  if (args[1] === 'visible') {
    args.splice(1, 1)
    visibleObjectMaterial(...args)
  } else {
    _setObjectMaterialProperty(...args)
  }
}
