NODE_ENV = 'dev'
VUE_APP_ENVIRONMENT = 'dev'

// With this configuration, vue-cli uses babel-plugin-dynamic-import-node
// It only does one thing by converting all import() to require()
// So that all asynchronous components can be import synchronously using this plugin
// This configuration can significantly increase the speed of hot updates when you have a large number of pages
// https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js
VUE_CLI_BABEL_TRANSPILE_MODULES = true

# 接口地址
VUE_APP_BASE_URL = 'https://dev-gw.heygears.com/gw'

# 正畸预览地址
VUE_APP_ORTHO_URL = 'https://dev-tenant-lab.heygears.com/lab_ortho_web/#/orthodonticPreview?side=tenant'

# 静态资源地址
VUE_APP_STATIC_URL = 'https://cloudcdn.heygears.cloud/dev/design'