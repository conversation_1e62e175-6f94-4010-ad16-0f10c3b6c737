<template>
  <Popup :show="show" :popup-title="$t('personal.changePasswordTitle')" :is-use-ele="true" :is-user-close="passwordExpired" :loading="loading" @cancel="cancel" @submit="submitForm('pwRuleForm')">
    <div slot="popupContent" class="change-password custom-form">
      <el-form ref="pwRuleForm" :model="changePasswordObj" :rules="rules">
        <el-form-item prop="oldPassword" class="password-label">
          <template slot="label">
            <span :title="$t('personal.oldPassword')">{{ $t('personal.oldPassword') }}</span>
          </template>
          <el-input v-model="changePasswordObj.oldPassword" :type="oldPasswordType" :placeholder="$t('personal.changePwPh')" :title="changePasswordObj.oldPassword ? '' : $t('personal.changePwPh')">
            <i slot="suffix" :class="['iconfont', showOldPassword ? 'icon-preview-on' : 'icon-preview-off']" @click="showOldPasswordFunc" />
          </el-input>
        </el-form-item>
        <el-form-item prop="newPassword" class="password-label">
          <template slot="label">
            <span :title="$t('personal.newPassword')">{{ $t('personal.newPassword') }}</span>
          </template>
          <el-input v-model="changePasswordObj.newPassword" :type="newPasswordType" :placeholder="$t('personal.changeNewPwPh')" :title="changePasswordObj.newPassword ? '' : $t('personal.changeNewPwPh')">
            <i slot="suffix" :class="['iconfont', showNewPassword ? 'icon-preview-on' : 'icon-preview-off']" @click="showNewPasswordFunc" />
          </el-input>
        </el-form-item>
        <el-form-item prop="ensurePassword" class="password-label">
          <template slot="label">
            <span :title="$t('personal.confrimPassword')">{{ $t('personal.confrimPassword') }}</span>
          </template>
          <el-input v-model="changePasswordObj.ensurePassword" :type="ensurePasswordType" :placeholder="$t('personal.ensurePwPh')" :title="changePasswordObj.ensurePassword ? '' : $t('personal.ensurePwPh')">
            <i slot="suffix" :class="['iconfont', showEnsurePassword ? 'icon-preview-on' : 'icon-preview-off']" @click="showEnsurePasswordFunc" />
          </el-input>
        </el-form-item>
      </el-form>
    </div>
  </Popup>
</template>

<script>
import Popup from '@/components/func-components/Popup'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { refreshLabel } from '@/assets/script/refreshLabel.js'

export default {
  name: 'ChangePassword',
  components: {
    Popup
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    passwordExpired: {
      type: [Boolean, String],
      default: false
    }
  },
  data() {
    var checkNewPassword = (rule, value, callback) => {
      if (value !== '') {
        if (!COMMON_CONSTANTS.PASSWORD_RULE.test(value)) {
          return callback(new Error(this.$t('personal.passwordErro')))
        } else if (value === this.changePasswordObj.oldPassword) {
          return callback(new Error(this.$t('65000014')))
        } else if (this.changePasswordObj.ensurePassword !== '') {
          this.$refs.pwRuleForm.validateField('ensurePassword')
        }
      }
      callback()
    }
    var checkEnsurePassword = (rule, value, callback) => {
      if (value !== '' && value !== this.changePasswordObj.newPassword) {
        return callback(new Error(this.$t('personal.ensurePasswordErro')))
      } else {
        callback()
      }
    }
    return {
      changePasswordObj: {
        oldPassword: '',
        newPassword: '',
        ensurePassword: ''
      },
      rules: {
        oldPassword: [
          { required: true, message: this.$t('personal.changePwPh'), trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: this.$t('personal.newPasswordErro'), trigger: 'blur' },
          { validator: checkNewPassword, trigger: 'blur' }
        ],
        ensurePassword: [
          { required: true, message: this.$t('personal.ensurePwPh'), trigger: 'blur' },
          { validator: checkEnsurePassword, trigger: 'blur' }
        ]
      },
      showOldPassword: false, // 是否显示原密码
      showNewPassword: false, // 是否显示新密码
      showEnsurePassword: false, // 是否显示确认密码
      oldPasswordType: 'password',
      newPasswordType: 'password',
      ensurePasswordType: 'password',
      loading: false
    }
  },
  computed: {
  },
  watch: {
    show(val) {
      if (val) {
        refreshLabel('password-label')
      } else {
        this.resetForm('pwRuleForm')
        this.showOldPassword = false
        this.showNewPassword = false
        this.showEnsurePassword = false
        this.oldPasswordType = 'password'
        this.newPasswordType = 'password'
        this.ensurePasswordType = 'password'
      }
    },
    changePasswordObj: {
      handler: function(val, oldVal) {
        this.changePasswordObj.oldPassword = val.oldPassword
        this.changePasswordObj.newPassword = val.newPassword
        this.changePasswordObj.ensurePassword = val.ensurePassword
      },
      deep: true
    }
  },
  methods: {
    // 是否显示原密码
    showOldPasswordFunc() {
      this.showOldPassword = !this.showOldPassword
      if (this.showOldPassword) {
        this.oldPasswordType = 'text'
      } else {
        this.oldPasswordType = 'password'
      }
    },
    // 是否显示新密码
    showNewPasswordFunc() {
      this.showNewPassword = !this.showNewPassword
      if (this.showNewPassword) {
        this.newPasswordType = 'text'
      } else {
        this.newPasswordType = 'password'
      }
    },
    // 是否显示确认密码
    showEnsurePasswordFunc() {
      this.showEnsurePassword = !this.showEnsurePassword
      if (this.showEnsurePassword) {
        this.ensurePasswordType = 'text'
      } else {
        this.ensurePasswordType = 'password'
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$emit('submit', this.changePasswordObj)
        } else {
          this.loading = false
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    cancel() {
      this.loading = false
      this.$emit('update:show', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-form {
  ::v-deep .el-form .el-input__inner {
    padding: 0 48px 0 24px !important;
  }
  .icon-preview-off {
    color: #38393D;
  }
}
</style>
