<!--
  * 组件名称 Tree
  * @desc 公用树组件
  * <AUTHOR>
  * 组件说明: 树组件的高度、树节点信息、树节点默认属性由父组件自定义控制，父组件可以通过插槽的方式自定义节点的操作，例如树节点的新增/编辑/删除操作
-->
<template>
  <div class="component-tree">
    <div class="tree-search">
      <Search
        @enterInput="searchOrgByKeyword"
        :searchIcons="{
          add: false,
          refresh: false,
        }"
      />
    </div>
    <overlay-scrollbars
      class="tree-scroll-content"
      :style="{'height': height}"
      :options="{
        paddingAbsolute: true,
      }"
    >
      <el-tree
        class="filter-tree"
        icon-class="iconfont icon-arrow-right"
        :data="treeData"
        :props="defaultProps"
        :default-expand-all="isExpandAll"
        :expand-on-click-node="isClickExpand"
        :filter-node-method="filterNode"
        :indent="16"
        @node-expand="expandNode"
        ref="tree"
      >
        <div :class="['custom-tree-node', node.data.depth < 3 ? 'fwb' : '']" :id="node.data.orgCode" slot-scope="{ node }"  @mouseenter="hoverLabel($event,node)" @mouseleave="leaveLabel($event,node)" @click="clickLabel($event,node)">
          <slot class="node-label finger" name="treeLabel" :node="node"></slot>
          <slot class="operate" name="operate" :node="node"></slot>
        </div>
      </el-tree>
    </overlay-scrollbars>
  </div>
</template>

<script>
import Search from "@/components/func-components/Search.vue";

export default {
  name: "Tree",
  components: {
    Search,
  },
  props: {
    treeData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          id: 'id',
          children: "children",
          label: "label",
        };
      },
    },
    height: {
      type: String,
      default: '100%'
    },
    isExpandAll: {
      type: Boolean,
      default: false
    },
    isClickExpand: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      filterText: '',
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  mounted() {
  },
  destroyed() {},
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.orgName.indexOf(value) !== -1;
    },
    searchOrgByKeyword(key) {
      this.filterText = key;
    },
    hoverLabel(e,node) {
      this.$emit('hover', e, node);
    },
    leaveLabel(e,node) {
      this.$emit('leave', e, node);
    },
    clickLabel(e,node) {
      this.$nextTick(() => {
        let activeNodes = document.getElementsByClassName('active');
        activeNodes = Array.from(activeNodes);
        if (activeNodes.length) {
          activeNodes.forEach((item) => {
            item.classList.remove('active');
          });
        }
        document.getElementById(node.data.orgCode).parentElement.classList.add('active');
        this.$emit('clickLabel', e, node);
      });
    },
    // 展开节点（共三个参数，nodeObj:传递给 data 属性的数组中该节点所对应的对象、nodeData:节点对应的 Node、node:节点组件本身）
    expandNode(nodeObj, nodeData, node) {
      this.$emit('expandNode', nodeObj, nodeData, node);
    },
  },
};
</script>

<style lang="scss" scoped>
.component-tree {
  width: 100%;
  height: 100%;
  background-color: $hg-main-black;
  position: relative;
  // padding: 0 16px;
  .tree-search {
    width: 100%;
    padding: 12px 16px 16px;
    box-sizing: border-box;
  }
  .tree-scroll-content {
    .filter-tree {
      background-color: $hg-main-black;
      .icon-arrow-right {
          width: 14px;
          height: 14px;
        }
      ::v-deep .el-tree-node__content {
        height: 44px;
        &:first-child { // 第一层级缩进16px,其余层级根据插件的缩进属性缩进16px
          .icon-arrow-right {
            margin-left: 16px ;
          }
        }
        .el-tree-node__expand-icon {
          padding: 6px 0;
          margin-right: 6px;
          box-sizing: border-box;
        }
        .expanded:before, .icon-arrow-right:before {
          width: 14px;
          height: 14px;
          color: #83868f;
        }
        .is-leaf:before {
          display: none;
          // color: transparent;
        }
        .custom-tree-node {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          overflow: hidden;
          .node-label {
            color: $hg-primary-fontcolor;
            // font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            background-color: transparent;
            border: none;
            height: 100%;
          }
        }
        &.active{
          background-color: $hg-hover-bg-color;
        }
      }
      ::v-deep .el-tree-node__content:hover {
        background-color: $hg-hover-bg-color;
      }

    }

  }
  ::v-deep .os-scrollbar-vertical {
    right: -10px;
  }
}
</style>
