import * as THREE from 'three'

import {
  toFixed,
  traverseArrayByCombination,
} from '@/components/OrthodonticDesignParam/helpers/utils/index'

import { _box3 } from '../box3'

export const _vector3 = new THREE.Vector3()

// 得到点结合的中心点,(这个方法不严谨),最好使用三角面带索引计算
export function getVectorsCenter(vectors) {
  const center = new THREE.Vector3()
  const box = _box3
  box.setFromPoints(vectors)
  const { max, min } = box
  center.addVectors(max, min)
  center.multiplyScalar(0.5)
  return center
}

// 精度要求
export function toFixedVector3(vector, precision) {
  vector.x = toFixed(vector.x, precision)
  vector.y = toFixed(vector.y, precision)
  vector.z = toFixed(vector.z, precision)
}

// 要保证共面才能使用
export function getVectorReferRadian(referNormal, startVector, endVector, precision = 6) {
  const normal = _vector3.copy(startVector).cross(endVector).normalize()
  toFixedVector3(normal, precision)

  const { x, y, z } = normal

  if (!x && !y && !z) {
    return 0
  }

  const ne = referNormal.equals(normal)
  const de = referNormal.equals(normal.negate())

  // 不共面
  if (!ne && !de) {
    console.error('不共面')
    return
  }

  let radian = startVector.angleTo(endVector)

  if (de) {
    radian = -radian
  }

  return radian
}
// 判断vector3是否共面
export function isCoplanarityVector3() {
  if (arguments.length < 3) {
    return true
  }

  const args = [...arguments]

  let referNormal

  const isCoplanarity = traverseArrayByCombination(args, (left, right) => {
    const normal = left.clone().cross(right).normalize()

    const { x, y, z } = normal

    if (x && y && z) {
      if (!referNormal) {
        referNormal = normal
      }
    } else {
      return true
    }

    if (referNormal.equals(normal) || referNormal.equals(normal.negate())) {
      return true
    }
    return false
  })

  return isCoplanarity
}

