<template>
  <svg :class="{'svg-icon': true }">
    <use :xlink:href="`#icon-${iconClass}`"></use>
  </svg>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    // svg icon的名字
    iconClass: {
      type: String,
      require: true
    }
  }
};
</script>

<style lang="scss" scoped>
.svg-icon {
  stroke: currentColor;
  fill: currentColor;
  // vertical-align: middle;
  width: 100%;
  height: 100%;
}
.svg-icon .icon-spin {
  animation: icon-spin 2s infinite linear;
}
@keyframes icon-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
</style>
