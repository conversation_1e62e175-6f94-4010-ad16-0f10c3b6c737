<template>
  <div class="agent-box">
    <div class="agent-main-content">
      <Plane>
        <div class="agent-data-display">
          <div class="agent-header">
            <div class="agent-search-box">
              <Search
                placeholder="请输入编码/代理商名称搜索"
                :input-content="searchKey"
                :search-icons="{
                  add: false,
                  refresh: false,
                }"
                @enterInput="handleSearch"
              />
            </div>
            <div class="icon-box">
              <Tooltip content="刷新">
                <i v-btn-control class="iconfont icon-refresh" @click="handleRefresh" />
              </Tooltip>
              <span class="line" />
              <el-button type="primary" @click="handleAddAgent">添加代理商</el-button>
              <el-button type="primary" @click="handleExportTransaction">导出交易明细</el-button>
            </div>
          </div>
          
          <!-- 表格 -->
          <new-table 
            class="agent-table" 
            :data="tableData.data" 
            :loading="tableLoading" 
            :header-data="headerData" 
            :needSelect="true" 
            @update-selected-rows="selectTable"
            v-bind="$attrs"
          >
            <template #orgSn="{ row }">
              <span :title="row.orgSn">{{ row.orgSn }}</span>
              <span v-if="row.isNew" class="new-flag">NEW</span>
            </template>

            <template #orgName="{ row }">
              <span :title="row.orgName">{{ row.orgName }}</span>
            </template>
            
            <template #agentCode="{ row }">
              <span :title="row.agentCode">{{ row.agentCode }}</span>
              <span v-if="row.isNew" class="new-flag">NEW</span>
            </template>
            
            <template #agentName="{ row }">
              <span :title="row.agentName">{{ row.agentName }}</span>
            </template>
            
            <template #agentScope="{ row }">
              <span :title="row.agentScope">{{ row.agentScope }}</span>
            </template>
            
            <template #deviceCount="{ row }">
              <span class="device-count clickable" @click="handleViewCustomers(row)">{{ row.deviceCount }}</span>
            </template>
            
            <template #edit="{ row }">
              <i class="iconfont icon-edit iconfont-24" :title="''" @click="handleEdit(row)" />
            </template>
            
            <template #user="{ row }">
              <i 
                class="iconfont icon-manage_accounts iconfont-24" 
                :title="'管理账户'" 
                @click="handleManageUsers(row)" 
              />
            </template>
            
            <template #detail="{ row }">
              <i class="iconfont icon-price_change iconfont-24" :title="''" @click="handleViewDetails(row)" />
            </template>
            
            <!-- 空状态模板 -->
            <template #empty>
              <div class="table-empty">
                <i class="iconfont icon-inbox-blank no-data-icon"></i>
                <span class="no-data-text">暂无数据</span>
              </div>
            </template>
          </new-table>
        </div>
      </Plane>
      
      <Pagination
        :page-no="pageNo"
        :page-size="pageSize"
        :total="total"
        :total-pages="totalPages"
        :page-sizes="[10, 20, 50, 100]"
        :show-total="true"
        @changePageNo="changePageNo"
        @changePageSize="changePageSize"
      />
    </div>
    
    <AddAgent 
      ref="addAgent" 
      :show.sync="showAddAgent" 
      :popup-title="popupTitle"
      :area-code-arr="areaCodeArr"
      :role-arr="roleArr"
      :dept-arr="allDeptList"
      @submit="addAgentFunc" 
    />
    
    <EditAgent
      ref="editAgent"
      :show.sync="showEditAgent"
      :save-btn="true"
      :edit-agent-data="editAgentObj"
      :area-code-arr="areaCodeArr"
      @submit="editAgentFunc"
    />
    
    <AgentCustomerList 
      ref="agentCustomerList" 
      :show.sync="showCustomerList" 
      :agent-info="selectedAgentInfo"
    />
  </div>
</template>

<script>
import Plane from '@/components/func-components/Plane.vue'
import Search from '@/components/func-components/Search.vue'
import newTable from '@/components/func-components/newTable.vue'
import Pagination from '@/components/func-components/Pagination'
import Tooltip from '@/components/func-components/Tooltip'
import AddAgent from './AddAgent.vue'
import EditAgent from './EditAgent.vue'
import AgentCustomerList from './AgentCustomerList.vue'
import { getAreaCodeList } from '@/api/organization'
import { getRolesList } from '@/api/role'
import { getAgentList, addAgent, getAgentInfo, editAgent } from '@/api/agent'

export default {
  name: 'AgentManagement',
  components: {
    Plane,
    Search,
    newTable,
    Pagination,
    Tooltip,
    AddAgent,
    EditAgent,
    AgentCustomerList
  },
  data() {
    return {
      // 搜索相关
      searchKey: '',
      
      // 弹窗相关
      showAddAgent: false,
      showEditAgent: false,
      showCustomerList: false,
      popupTitle: '',
      
      // 编辑相关
      editAgentObj: {},
      
      // 客户列表相关
      selectedAgentInfo: {},
      
      // 数据数组
      areaCodeArr: [],
      roleArr: [],
      allDeptList: [],
      
      // 表格相关
      tableKeys: ['agentCode', 'agentName', 'agentScope', 'deviceCount', 'edit', 'user', 'detail'],
      tableLoading: false,
      headerData: [
        {
          prop: 'agentCode',
          minWidth: '20%',
          getLabel: () => {
            return '编码';
          },
        },
        {
          prop: 'agentName',
          minWidth: '25%',
          getLabel: () => {
            return '名称';
          },
        },
        {
          prop: 'agentScope',
          minWidth: '25%',
          getLabel: () => {
            return '代理范围';
          },
        },
        {
          prop: 'deviceCount',
          minWidth: '15%',
          getLabel: () => {
            return '终端数量';
          },
        },
        {
          prop: 'edit',
          minWidth: '10%',
          getLabel: () => {
            return '编辑';
          },
        },
        {
          prop: 'user',
          minWidth: '15%',
          getLabel: () => {
            return '管理账户';
          },
        },
        {
          prop: 'detail',
          minWidth: '15%',
          getLabel: () => {
            return '设置价格';
          },
        }
      ],
      tableData: {
        data: []
      },
      
      // 分页相关
      pageNo: 1,
      pageSize: 20,
      total: 0,
      totalPages: 0,
      
      // 多选相关
      selectSelection: []
    }
  },
  mounted() {
    this.getAreaCodeFunc()
    this.getRolesFunc()
    this.getAgentListFunc()
  },
  methods: {
    // 获取区号列表
    getAreaCodeFunc() {
      return new Promise((resolve) => {
        getAreaCodeList({
          'orderBy': this.$i18n.locale === 'zh' ? 0 : 1
        }).then((res) => {
          if (res.code === 200) {
            this.areaCodeArr = res.data
            resolve()
          }
        })
      })
    },
    
    // 获取角色列表
    getRolesFunc() {
      getRolesList({ system: 0 }).then((res) => {
        if (res.code === 200) {
          this.roleArr = res.data
        }
      })
    },
    
    // 刷新处理
    handleRefresh() {
      this.getAgentListFunc()
      this.$MessageAlert({
        text: '刷新成功',
        type: 'success'
      })
    },
    
    // 选中表格行
    selectTable(length, selection) {
      this.selectSelection = selection;
    },
    
    // 搜索处理
    handleSearch(searchKey) {
      this.searchKey = searchKey
      this.pageNo = 1
      this.getAgentListFunc()
    },
    
    // 添加代理商
    handleAddAgent() {
      this.popupTitle = '添加代理商'
      this.showAddAgent = true
    },
    
    // 添加代理商提交处理
    addAgentFunc(obj) {
      console.log('提交代理商数据:', obj)
      
      // 数据格式转换
      const params = {
        businessUserCodes: obj.businessUser && obj.businessUser.length > 0 ? obj.businessUser.map(code => parseInt(code)) : [],
        email: obj.email,
        mobile: obj.mobile || '',
        mobilePrefix: obj.mobilePrefix || '',
        orgLeader: obj.leader || '',
        orgSn: obj.agentCode,
        rangeType: obj.rangeType || [],
        supportUserCodes: obj.techSupport && obj.techSupport.length > 0 ? obj.techSupport.map(code => parseInt(code)) : [],
        tzCode: parseInt(obj.tzCode)
      }
      
      addAgent(params).then((res) => {
        if (res.code === 200) {
          // 成功处理
          if (this.$refs.addAgent) {
            this.$refs.addAgent.loading = false
          }
          this.showAddAgent = false
          if (this.$MessageAlert) {
            this.$MessageAlert({
              text: '添加代理商成功',
              type: 'success'
            })
          }
          // 刷新列表数据
          this.getAgentListFunc()
        }
      }).catch((error) => {
        console.error('添加代理商失败:', error)
        if (this.$refs.addAgent) {
          this.$refs.addAgent.loading = false
        }
        if (this.$MessageAlert) {
          this.$MessageAlert({
            text: error.msg || '添加代理商失败',
            type: 'error'
          })
        }
      })
    },
    
    // 导出交易明细
    handleExportTransaction() {
      console.log('导出交易明细')
      // TODO: 导出功能
    },
    
    // 查看客户列表
    handleViewCustomers(row) {
      console.log('查看客户列表:', row)
      this.selectedAgentInfo = {
        orgCode: row.orgCode,
        agentName: row.agentName,
        agentCode: row.agentCode
      }
      this.showCustomerList = true
    },
    
    // 编辑代理商
    handleEdit(row) {
      console.log('编辑代理商:', row)
      // 获取代理商详细信息
      this.getAgentInfoFunc(row.orgCode)
    },
    
    // 获取代理商详细信息
    getAgentInfoFunc(orgCode) {
      this.tableLoading = true
      getAgentInfo({ orgCode: Number(orgCode) }).then(res => {
        if (res.code === 200 && res.data) {
          const data = res.data
          // 填充表单数据
          this.editAgentObj = {
            agentName: data.orgName,
            agentCode: data.orgSn,
            customerCode: data.memorySn || '',
            tzCode: data.tzCode,
            address: data.orgAddress || '',
            leader: data.orgLeader || '',
            email: data.email,
            mobile: data.mobile || '',
            mobilePrefix: data.mobilePrefix || '+86',
            currency: data.settlementCurrency,
            creditLimit: 0,
            status: data.status,
            rangeType: data.rangeType || [],
            orgCode: data.orgCode,
            userCode: data.userCode,
            // 新增银行信息
            accountCode: data.bankInfo ? data.bankInfo.accountCode : '',
            // 新增账单抬头信息
            headerCode: data.headerInfo ? data.headerInfo.headerCode : '',
            // 业务人员信息
            businessUserCodes: data.businessUserInfos && data.businessUserInfos.length > 0 
              ? data.businessUserInfos.map(user => user.userCode) 
              : []
          }
          
          // 显示编辑弹窗
          this.showEditAgent = true
        } else {
          if (this.$MessageAlert) {
            this.$MessageAlert({
              text: res.message || '获取代理商信息失败',
              type: 'error'
            })
          }
        }
      }).catch(error => {
        console.error('获取代理商信息失败:', error)
        if (this.$MessageAlert) {
          this.$MessageAlert({
            text: error.message || '获取代理商信息失败',
            type: 'error'
          })
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    
    // 编辑代理商提交处理
    editAgentFunc(obj, type) {
      console.log('AgentManagement - 接收到编辑事件:', { obj, type });
      console.log('提交编辑代理商数据:', obj)
      
      // 构建API请求参数，确保与接口文档匹配
      const params = {
        orgCode: parseInt(obj.orgCode), // 代理商组织编号（必填）
        orgName: obj.agentName || '', // 代理名称
        orgSn: obj.agentCode || '', // 客户编码（CRM）
        orgAddress: obj.address || '', // 地址
        orgLeader: obj.leader || '', // 负责人
        email: obj.email || '', // 邮箱
        mobile: obj.mobile || '', // 手机号
        mobilePrefix: obj.mobilePrefix || '', // 手机号前缀
        tzCode: obj.tzCode ? parseInt(obj.tzCode) : null, // 时区编码
        rangeType: obj.rangeType || [], // 代理范围
        settlementCurrency: obj.currency ? parseInt(obj.currency) : null, // 结算币种
        accountCode: obj.accountCode ? parseInt(obj.accountCode) : null, // 银行账户编号
        headerCode: obj.headerCode ? parseInt(obj.headerCode) : null, // 账单抬头编号
        businessUserCodes: obj.businessUserCodes && obj.businessUserCodes.length > 0 
          ? obj.businessUserCodes.map(code => parseInt(code)) 
          : [] // 业务人员编号数组
      }
      
      editAgent(params).then((res) => {
        if (res.code === 200) {
          // 成功处理
          if (this.$refs.editAgent) {
            this.$refs.editAgent.editLoading = false
          }
          
          if (type === 'save') {
            if (this.$MessageAlert) {
              this.$MessageAlert({
                text: '编辑代理商成功',
                type: 'success'
              })
            }
          }
          
          // 刷新列表数据
          this.getAgentListFunc()
        }
      }).catch((error) => {
        console.error('编辑代理商失败:', error)
        if (this.$refs.editAgent) {
          this.$refs.editAgent.editLoading = false
        }
        if (this.$MessageAlert) {
          this.$MessageAlert({
            text: error.msg || '编辑代理商失败',
            type: 'error'
          })
        }
      })
    },
    
    // 管理用户
    handleManageUsers(row) {
      console.log('管理用户:', row)
      // TODO: 跳转到用户管理页面
    },
    
    // 查看详情
    handleViewDetails(row) {
      console.log('查看详情:', row)
      // TODO: 打开详情弹窗或跳转详情页
    },
    
    // 分页处理
    changePageNo(page) {
      this.pageNo = page
      this.getAgentListFunc()
    },
    
    // 每页数量变化  
    changePageSize(size) {
      this.pageSize = size
      this.pageNo = 1
      this.getAgentListFunc()
    },
    
    // 转换代理范围格式
    convertAgentScopeToRangeType(agentScope) {
      const scopeMap = {
        '0': [0],    // 设备
        '1': [1],    // 设计服务
        '2': [2]     // AI软件
      }
      return scopeMap[agentScope] || []
    },
    
    // 获取代理商列表
    getAgentListFunc() {
      this.tableLoading = true
      const params = {
        key: this.searchKey,
        pageNo: this.pageNo - 1, // API使用0基索引
        pageSize: this.pageSize
      }
      
      getAgentList(params).then((res) => {
        if (res.code === 200 && res.data) {
          this.total = res.data.totalSize
          this.totalPages = res.data.totalPages
          this.tableData.data = res.data.data.map(item => ({
            orgCode: item.orgCode, // 保留orgCode用于详情功能，但不在表头显示
            agentCode: item.orgSn,
            agentName: item.orgName,
            agentScope: this.convertRangeTypeToText(item.rangeType), // 修复字段名
            deviceCount: item.bindingNums,
            isNew: this.checkIsNew(item.orgCode)
          }))
        } else {
          this.tableData.data = []
          this.total = 0
          this.totalPages = 0
        }
      }).catch((error) => {
        console.error('获取代理商列表失败:', error)
        this.tableData.data = []
        this.total = 0
        this.totalPages = 0
        if (this.$MessageAlert) {
          this.$MessageAlert({
            text: error.msg || '获取代理商列表失败',
            type: 'error'
          })
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    
    // 将rangeType数组转换为显示文本
    convertRangeTypeToText(rangeType) {
      if (!rangeType || rangeType.length === 0) return '未设置'
      
      const typeMap = {
        0: '设备',
        1: '设计服务',
        2: 'AI软件'
      }
      
      return rangeType.map(type => typeMap[type] || '未知').join('、')
    },
    
    // 判断是否为新代理商
    checkIsNew(orgCode) {
      // 可以根据业务逻辑判断，这里简单示例
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.agent-box {
  width: 100%;
  max-width: 100%;
  background-color: $hg-main-black;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  overflow: hidden;
  
  .agent-main-content {
    position: relative;
    flex: 1;
    width: 0; // 强制flex子项不超出父容器
    height: 100%;
    overflow: hidden;
    padding-bottom: 58px;
    box-sizing: border-box;
    
    .agent-data-display {
      width: 100%;
      height: 100%;
      background-color: $hg-main-black;
      overflow: hidden;
      
      .agent-header {
        display: flex;
        align-items: center;
        width: 100%;
        flex-wrap: nowrap;
        
        .agent-search-box {
          width: 576px;
          min-width: 240px;
          margin-right: 24px;
          flex-shrink: 0;
        }
        
        .icon-box {
          margin-left: auto;
          display: flex;
          align-items: center;
          flex-shrink: 0;
          
          span {
            height: $hg-height-40;
            line-height: $hg-height-40;
          }
          
          .line {
            width: 1px;
            background: $hg-disable-fontcolor;
            margin: 0 20px 0 28px;
            vertical-align: middle;
          }
        }
      }
      
      .agent-table {
        margin-top: 8px;
        width: 100%;
        max-width: 100%;
        max-height: calc(100vh - 120px);
        overflow: hidden;
        
        .device-count {
          &.clickable {
            color: $hg-main-blue;
            cursor: pointer;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
        
        .new-flag {
          display: inline-block;
          width: 32px;
          height: 16px;
          text-align: center;
          border-radius: 2px;
          background: $hg-error-color;
          color: $hg-primary-fontcolor;
          font-size: 12px;
          margin-left: 12px;
        }
        
        // 空状态样式
        .table-empty {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 60px 0;
          color: $hg-secondary-fontcolor;
          
          .no-data-icon {
            font-size: 48px;
            color: $hg-disable-fontcolor;
            margin-bottom: 16px;
          }
          
          .no-data-text {
            font-size: 14px;
            color: $hg-secondary-fontcolor;
          }
        }
        
        // 空状态样式
        .table-empty {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 60px 0;
          color: $hg-secondary-fontcolor;
          
          .no-data-icon {
            font-size: 48px;
            color: $hg-disable-fontcolor;
            margin-bottom: 16px;
          }
          
          .no-data-text {
            font-size: 14px;
            color: $hg-secondary-fontcolor;
          }
        }
      }
      
      ::v-deep .el-table__body-wrapper{
        height: calc(100vh - 300px)!important;
      }
      
      // 搜索框样式定制
      ::v-deep .agent-search-box {
        .component-search {
          .left {
            .component-input {
              height: 40px;
              border-radius: 4px;
              border-width: 1px;
              opacity: 1;
              
              .input-box {
                height: 40px;
                padding: 8px 12px;
                gap: 12px;
                border-radius: 4px;
                
                input {
                  height: 24px;
                  line-height: 24px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 确保表格不会超出容器宽度
::v-deep .component-table {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  
  .table-box {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    
    .table-header {
      display: flex;
      width: 100%;
      
      .th {
        flex: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    
    .table-body {
      width: 100%;
      max-width: 100%;
      overflow: auto;
    }
  }
}

// 分页组件样式统一（参考客户管理页面）
::v-deep .pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 58px;
  padding: 0 16px;
  background: $hg-main-black;
  border-top: 1px solid $hg-border-color;
  
  .el-pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    
    .el-pagination__total {
      color: $hg-primary-fontcolor;
      font-size: 14px;
      margin-right: auto;
      order: -1;
      font-weight: normal;
      
      &::before {
        content: '总数: ';
      }
    }
    
    .btn-prev,
    .btn-next {
      width: 32px;
      height: 32px;
      border: 1px solid $hg-border-color;
      border-radius: 4px;
      background: transparent;
      color: $hg-primary-fontcolor;
      margin: 0 4px;
      
      &:disabled {
        background: $hg-border-color;
        color: $hg-disable-fontcolor;
      }
    }
    
    .el-pager {
      margin: 0 8px;
      
      li {
        width: 32px;
        height: 32px;
        border: 1px solid $hg-border-color;
        border-radius: 4px;
        background: transparent;
        color: $hg-primary-fontcolor;
        margin: 0 4px;
        line-height: 30px;
        
        &:hover {
          border-color: $hg-main-blue;
        }
        
        &.active {
          background: $hg-main-blue;
          border-color: $hg-main-blue;
          color: white;
        }
      }
    }
    
    .el-pagination__sizes {
      margin-left: 16px;
      
      .el-input {
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          background: transparent;
          border: 1px solid $hg-border-color;
          border-radius: 4px;
          color: $hg-primary-fontcolor;
          text-align: center;
        }
      }
    }
    
    .edit-input {
      width: 44px;
      margin: 0 8px;
    }
    
    .go-page,
    .page-text {
      color: $hg-primary-fontcolor;
      font-size: 14px;
      margin: 0 4px;
    }
  }
}
</style>
