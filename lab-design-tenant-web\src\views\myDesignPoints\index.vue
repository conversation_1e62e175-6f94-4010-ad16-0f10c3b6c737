<template>
  <div class="my-design-points" v-loading="loadingChart">
    <!-- 头部导航 -->
     <div class="my-header">
      <span class="name">{{userName}}{{lang('statics')}}</span>
      <div class="time-search">
        <span class="label">{{lang('date')}}</span>
        <el-date-picker v-model="date" type="month" :picker-options="pickerOptions" format="yyyy-MM" value-format="yyyy-MM" placeholder="选择月" :clearable="false" clear-icon="el-icon-date" @change="changeMonth"></el-date-picker>
        <span class="date-icon el-icon-date"></span>
      </div>
      <div class="all-desc">
        <div class="desc-list"><span class="level-label">{{lang('skillLevel')}}</span><span class="level">{{allDetails.levelCode}}{{lang('level')}}</span></div>
        <div class="desc-list"><span class="level-label">{{lang('middle')}}</span><span class="level">{{allDetails.midShiftDays}}{{lang('day')}}</span></div>
        <div class="desc-list"><span class="level-label">{{lang('night')}}</span><span class="level">{{allDetails.nightShiftDays}}{{lang('day')}}</span></div>
      </div>
     </div>
     <!-- 统计数据 -->
     <allDetailsBox :allDetails="allDetails"></allDetailsBox>
     <div class="top-chart">
      <!-- 月点数柱形图 -->
      <div class="left-chart">
        <monthPointsChart :monthsArray="monthsArray" :monthlyPoints="monthlyPoints" @click="openDetails"></monthPointsChart>
      </div>
      <!-- 完成率折线图 -->
      <div class="right-chart">
        <completeChart :monthsArray="monthsArray" :completionRate="completionRate"></completeChart>
      </div>
     </div>

     <div class="bottom-chart">
      <!-- 设计品类饼图 -->
      <div class="left-chart">
        <designCodeChart :date="date" :designTypeProportions="designTypeProportions" @click="clickChart"></designCodeChart>
      </div>
      <!-- 完成率折线图 -->
      <div class="right-chart">
        <designCodeNumberChart :monthsArray="monthsArray" :designTypeQuantities="designTypeQuantities"></designCodeNumberChart>
      </div>
     </div>
     <pointsDetails :drawer.sync="pointDetailsDrawer" :rowData="rowData" :isHaveAllo="false"></pointsDetails>
  </div>
</template>

<script>
import allDetailsBox from './components/allDetailsBox';
import monthPointsChart from './components/monthPointsChart'; 
import completeChart from './components/completeChart';
import designCodeChart from './components/designCodeChart';
import designCodeNumberChart from './components/designCodeNumberChart';
import { getPointsStatistics } from '@/api/designPoints';
import pointsDetails from '../designerPoints/components/pointsDetails';
import { mapGetters } from 'vuex';
import { getLang } from '@/public/utils';
export default {
	name: 'myDesignPoints',
  components: {
    allDetailsBox,monthPointsChart,completeChart,designCodeChart,designCodeNumberChart,pointsDetails
  },
  data() {
    return {
      pickerOptions: {
        disabledDate: (time) => {
          let nowYear = new Date().getFullYear();
          let nowMonth = new Date().getMonth() + 1;
          let nowYM = nowYear + '-' + (nowMonth >= 10 ? nowMonth : ('0' + nowMonth));
          let timeM = time.getMonth() + 1;
          let timeYM = time.getFullYear() + '-' + (timeM >= 10 ? timeM : ('0' + timeM));
          return timeYM > nowYM
        }
      },
      date: '',
      userName: '',
      userCode: '',
      monthsArray: [], //月份数组，X轴显示
      monthlyPoints: {}, // 月点数统计
      completionRate: [], //完成率数组
      designTypeQuality: [], //设计品类的原始数组
      designTypeQuantities: {}, //设计品类数量变化
      designTypeProportions: [], // 设计品类分布
      allDetails: {
        monthlyIndicatorPoints: 0, // 月总指标点数
        monthlyCompletionPoints: 0, // 月完成点数
        excessPoints: 0, // 超额点数
        groupRank: 0, // 组内排名
        completionRate: 0, // 完成率
        levelCode: 0, // 技能等级
        midShiftDays: 0, // 中班天数
        nightShiftDays: 0, // 夜班天数
        totalReturnQty: 0, // 返单订单
        totalFreeQty: 0, // 免单订单
        totalTimeoutQty: 0 // 超时订单
      },
      loadingChart: false,
      pointDetailsDrawer: false,
      rowData: {
        date: '',
        userCode: this.userCode,
        userName: this.userName
      }
    }
  },
  computed: {
    ...mapGetters(['language', 'oneDesignList']),
  },
  created () {
    this.setTime();
    this.getValue();
  },
  mounted () {
  },
  methods: {
    lang: getLang('designpoints'),
    // 获取链接传过来的值
    getValue(){
      if(this.$route.query.date){
        this.date = this.$route.query.date;
        this.userName = this.$route.query.userName;
        this.userCode = this.$route.query.userCode;
      } else {
        let userInfo = JSON.parse(localStorage.getItem('userInfo'));
        console.log(userInfo)
        this.userName = userInfo.realName;
        this.userCode = userInfo.userCode;
      }
      this.getMonthList(this.date)
      this.getPointsStatistics();
    },
    // 根据月份获取六个月的数组
    getMonthList(inputDate){
      const date = new Date(inputDate + '-01'); // 将输入日期转换为 JavaScript Date 对象
      const monthsArray = [];

      for (let i = 5; i >= 0; i--) {
          const tempDate = new Date(date.getFullYear(), date.getMonth() - i);
          const year = tempDate.getFullYear();
          const month = tempDate.getMonth() + 1;
          const formattedMonth = `${year}-${month.toString().padStart(2, '0')}`;
          monthsArray.push(formattedMonth);
      }
      this.monthsArray = monthsArray
    },
    /**
     * 设置默认时间
     */
     setTime() {
      // 获取当月第一天和最后一天
      const year = new Date().getFullYear();
      const month = new Date().getMonth();
      const endMonth = new Date(year, month, 0, 23, 59, 59);
      const formattedDate = endMonth.toISOString().slice(0, 7);
      this.date = formattedDate;
    },
    // 改变月份
    changeMonth(){
      this.getMonthList(this.date)
      this.getPointsStatistics();
    },
    // 获取设计师点数统计
    async getPointsStatistics(){
      this.loadingChart = true;
      let parame = {
        month: this.date,
        userCode: this.userCode
      }
      const { code, data } = await getPointsStatistics(parame);
      if(code == 200){
        this.loadingChart = false;
        // 总数据
        this.allDetails = {
          monthlyIndicatorPoints: data && data.monthlyIndicatorPoints ? data.monthlyIndicatorPoints : 0, // 月总指标点数
          monthlyCompletionPoints: data && data.monthlyCompletionPoints ? data.monthlyCompletionPoints.toFixed(4) : 0, // 月完成点数
          excessPoints: data && data.excessPoints ? data.excessPoints.toFixed(4) : 0, // 超额点数
          groupRank: data && data.groupRank ? data.groupRank : 0, // 组内排名
          completionRate: data && data.completionRate ? data.completionRate: 0, // 完成率
          levelCode: data && data.levelCode ? data.levelCode: 0, // 技能等级
          midShiftDays: data && data.midShiftDays ? data.midShiftDays : 0, // 中班天数
          nightShiftDays: data && data.nightShiftDays ? data.nightShiftDays : 0, // 夜班天数
          totalReturnQty: data && data.totalReturnQty ? data.totalReturnQty : 0, // 返单订单
          totalFreeQty: data && data.totalFreeQty ? data.totalFreeQty : 0, // 免单订单
          totalTimeoutQty: data && data.totalTimeoutQty ? data.totalTimeoutQty : 0 // 超时订单
        }
        // 月总指标
        let monthlyPoints = data && data.monthlyPoints ? data.monthlyPoints : [];
        this.monthlyPoints = this.handelMonthLyPoints(monthlyPoints);
        // 完成率
        let monthlyCompletionRates = data && data.monthlyCompletionRates ? data.monthlyCompletionRates : [];
        this.completionRate = this.handelComplete(monthlyCompletionRates);

        // 设计品类分布
        let designTypeProportions = data && data.designTypeProportions ? data.designTypeProportions : [];
        this.designTypeProportions = this.getDesignTypeProportions(designTypeProportions);

        // 设计品类数量变化
        let designTypeQuality = data && data.designTypeQuantities ? data.designTypeQuantities : [];
        this.designTypeQuality = designTypeQuality;
        this.designTypeQuantities = this.getDesignTypeQuantities(designTypeQuality);
      }
    },
    // 月总指标处理
    handelMonthLyPoints(arr){
      let monthlyCompletionPointsArr = [];
      let monthlyIndicatorPointsArr = [];
      this.monthsArray.forEach((item) => {
        let nowMonth = arr.find((it) => {return it.month == item});
        if(nowMonth){
          monthlyCompletionPointsArr.push(nowMonth.monthlyCompletionPoints.toFixed(4));
          monthlyIndicatorPointsArr.push(nowMonth.monthlyIndicatorPoints);
        } else{
          monthlyCompletionPointsArr.push(0);
          monthlyIndicatorPointsArr.push(0);
        }
      })
      return {monthlyCompletionPointsArr, monthlyIndicatorPointsArr};
    },
    // 完成率变化
    handelComplete(arr){
      let completionRateArr = [];
      this.monthsArray.forEach((item) => {
        let nowMonth = arr.find((it) => {return it.month == item});
        if(nowMonth){
          completionRateArr.push(this.getRate(nowMonth.completionRate));
        } else{
          completionRateArr.push(0);
        }
      })
      return completionRateArr;
    },
    getRate(value){
      return (value * 100).toFixed(2);
    },
    // 设计品类分布
    getDesignTypeProportions(arr){
      // 仅筛选出排名前五个，其他的归类为其他，比例相加
      // 排序数组，取前五个proportion最大的对象
      let colorList = ['#5F8AFF', '#00B860', '#FBAA0E', '#E55353', '#8127DB', '#61646D']
      const topFive = arr.sort((a, b) => b.proportion - a.proportion).slice(0, 5);

      // 计算其余proportion之和
      const sumOfRest = arr.slice(5).reduce((acc, current) => acc + current.proportion, 0);
      const sumQty = arr.slice(5).reduce((acc, current) => acc + current.qty, 0);
      // 创建新对象并加入结果数组
      const others = { name: this.$t('designpoints.pieOther'), value: sumOfRest, designCode: 'other', proportion: sumOfRest, qty: sumQty };
      const result = arr.length > 5 ? [...topFive, others] : [...topFive];
      result.forEach((item, index) => {
        if(item.designCode != 'other'){
          item.name = this.$t(`apiCommon.${item.designCode}`);
          item.value = item.proportion;
          item.qty = item.qty;
        }
        item.itemStyle = { color: colorList[index] }
      })
      return result
    },
    // 设计品类数量变化
    getDesignTypeQuantities(arr, name, color){
      let designTypeAllQuanty = [];
      let designTypeName = name ? name : this.lang('allDesign');
      this.monthsArray.forEach((item) => {
        let qty = 0;
        let qualityList = arr.filter((it) => {return it.month == item});
        qualityList.forEach((qua) => {
          qty += qua.qty
        })
        designTypeAllQuanty.push(qty);
      })
      return { designTypeAllQuanty, designTypeName, color: color ? color : '#FFF' }
    },
    // 点击拼图联动设计品类数量变化
    clickPieToDesignQuality(data){
      let designTypeList = this.designTypeQuality.filter((item) => { return item.designCode == data.designCode });
      this.designTypeQuantities = this.getDesignTypeQuantities(designTypeList, data.name, data.itemStyle.color);
    },
    // 饼图点击
    clickChart(data){
      this.clickPieToDesignQuality(data)
    },
    // 柱形图查看详情
    openDetails(data){
      this.rowData = {
        date: data[0].axisValue,
        userCode: this.userCode,
        userName: this.userName
      }
      this.pointDetailsDrawer = true;
    }
  },
};
</script>

<style lang="scss" scoped>
.my-design-points{
  background: #1B1D22;
  padding: 16px;
  height: 100%;
  overflow: auto;
  .my-header{
    position: relative;
    display: flex;
    height: 40px;
    align-items:  center;
    .name{
      margin-right: 36px;
    }
    /deep/.time-search{
      position: relative;
      width: 460px;
      display: flex;
      align-items: center;
      .label{
        margin-right: 10px;
      }
      .date-icon{
        position: absolute;
        right: 40px;
        font-size: 16px;
      }
      .el-input{
        width: 400px;
      }
      .el-input--prefix .el-input__inner{
        padding-left: 20px;
      }
      .el-input.el-input--prefix .el-input__prefix{
        display: none;
      }
    }
    .all-desc{
      display: flex;
      position: absolute;
      right: 0;
      .desc-list{
        display: flex;
        align-items: center;
        width: 180px;
        height: 40px;
        background: #27292E;
        border-radius: 4px;
        margin-left: 16px;
        .level-label{
          display: inline-flex;
          width: 120px;
          justify-content: center;
          align-items: center;
          background: rgba(61, 64, 71, 0.24);
          border-radius: 4px;
        }
        .level{
          display: inline-flex;
          width: 60px;
          height: 36px;
          justify-content: center;
          align-items: center;
          background: $hg-main-primary;
          border-radius: 4px;
        }
      }
    }
  }
  .top-chart{
    display: flex;
    .left-chart{
      flex: 1;
    }
    .right-chart{
      flex: 1;
    }
  }
  .bottom-chart{
    margin-top: 24px;
    display: flex;
    .left-chart{
      flex: 1;
    }
    .right-chart{
      flex: 1;
      background: #0B0C0D;
    }
  }
}
.el-month-table td.disabled .cell {
    background-color: #4b4c4f;
    cursor: not-allowed;
    color: #b1b7c1;
}
</style>
