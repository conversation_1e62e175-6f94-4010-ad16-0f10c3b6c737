const _toString = Object.prototype.toString

export function toRawType(value) {
  return _toString.call(value).slice(8, -1)
}

export function isUndefined(value) {
  return value === undefined
}

export function isObject(value) {
  return value && typeof value === 'object'
}

export function isArray(value) {
  return toRawType(value) === 'Array'
}

export function isPlainObject(value) {
  return toRawType(value) === 'Object'
}

export function isFunction(value) {
  return toRawType(value) === 'Function'
}

export function isString(value) {
  return value === 'String'
}
