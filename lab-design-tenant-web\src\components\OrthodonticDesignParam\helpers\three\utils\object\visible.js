import { traverseTreeParent, traverseTreeChildren } from '@/components/OrthodonticDesignParam/helpers/utils/index'
import { checkObjectMaterialVisible } from './material'

export function checkObjectVisible(object) {
  if (!object.visible) {
    return false
  }

  return checkObjectMaterialVisible(object)
}

export function visibleObjectOnly(object, visible) {
  object.visible = visible
}

export function visibleObject(object, visible, { deepParent, deepChildren, deepFilter } = {}) {
  if (visible && deepParent) {
    const allParentVisible = traverseTreeParent(object, (parent) => {
      return checkObjectVisible(parent)
    })

    if (!allParentVisible) {
      if (deepChildren) {
        visibleObject(object, false, { deepChildren })
      }
      return
    }
  }

  visibleObjectOnly(object, visible)

  if (deepChildren) {
    traverseTreeChildren([object], (child) => {
      if (object === child) {
        return true
      }

      if (deepFilter) {
        const effect = deepFilter(child)
        effect && visibleObjectOnly(child, visible)
      } else {
        visibleObjectOnly(child, visible)
      }

      return true
    })
  }
}
