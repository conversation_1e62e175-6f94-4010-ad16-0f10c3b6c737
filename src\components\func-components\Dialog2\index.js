import Vue from 'vue';
import Main from "./Main.vue";


let DialogConstructor = Vue.extend(Main);

let seed = 1,
    instance,
    instances = {};
const Dialog = function (options = {}) {
    if (typeof options === 'string') {
        options = {
            text: options
        }
    }
    let userCancel = options.onCancel,
        userConfirm = options.onConfirm;
    let id = 'dialog_' + seed++;
    options.onCancel = () => {
        Dialog.close(id, userCancel);
    }
    options.onConfirm = () => {
        Dialog.close(id, userConfirm);
    }
    instance = new DialogConstructor({
        data: options
    })
    instance.id = id;
    instance.$mount();
    document.body.appendChild(instance.$el);
    instance.visible = true;
    instances[id] = instance;
    return instance;
}



Dialog.close = function (id, callBack) {
    const ins = instances[id];
    if (typeof callBack === 'function') {
        callBack(ins);
    }
    delete instances[id];
}

function registryDialog() {
    Vue.prototype.$Dialog = Dialog
}
['show', 'switch'].forEach(type => {
    Dialog[type] = options => {
        if (typeof options === 'string') {
            options = {
                text: options
            };
        }
        options.type = type;
        return Dialog(options);
    };
});
export default registryDialog;