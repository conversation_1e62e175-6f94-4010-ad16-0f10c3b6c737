export default {
  order: {
    // 订单详情
    detail: {
      upperJaw: 'Maxillary',
      lowerJaw: 'Mandibular',
      leftTop: 'Top Left',
      leftBottom: 'Bottom Left',
      rightTop: 'Top Right',
      rightBottom: 'Bottom Right',
      toothPosition: 'Tooth Bit',
      noData: 'None',
      number: 'Order No.：{0}',
      out: 'Overtime ',
      rest: 'RestTime ',
      complete: 'TotalTime ',
      rejectPass: 'disapproved application',
      rejectPassReason: 'Disapproval Reason',
      rejectPassReasonTips: 'Please enter the reason to disapprove of this rejection from client.',
      CBCTFile: 'CBCT File',
      implant: {
        implantScheme: 'Implant Plan',
        tips: '*Implant plan is required as the order includes surgical guides.',
        pendingApproval: 'Pending',
        approved: 'Confirmed',
        notPass: 'Redesigning',
        submitLimit: 'Please upload the implant plan first.',
        submitConfirm: 'Sure to submit the implant plan?',
        submitSuccess: "Submitted successfully! Please wait for the client's confirmation.",
        returnTitle: 'Redesign Implant Plan',
        designFinishLimit: 'Please submit the implant plan first.',
        designFinishImplantTips: 'The client has not confirmed the implant plan. Sure to finish the order?'
      },
      guide: {
        'implantSystem': 'Implant system',
        implantSystemTips: 'Please select the implant system.',
        'implantSeries': 'Implant Series',
        'isAtOnce': 'Immediate Implant',
      },
      step: {
        new: 'New',
        translate: 'Checked',
        design: 'Designing',
        examine: 'QC',
        finish: 'Complete',
        reback: 'Returning',
        rebacked: 'Returned',
        edit: 'Customer edit',
        resubmit: 'Resubmit',
        askForFree: 'Applying for rejection',
        isFree: 'Application approved',
      },

      btn: {
        translate: 'Check',
        edit: 'Edit',
        reback: 'Return',
        assignIQC: 'IQC',
        translateByMe: 'Translate',
        assignDesigner: 'Assign',
        rebackByDesigner: 'Return',
        assignOQC: 'OQC',
        finish: 'Finish',
        revokeByDesigner: 'Recall',
        examineByMe: 'Review',
        pass: 'Pass',
        notPass: 'Reject',
        revokeFromClient: 'Recall',
        confirmReback: 'Confirm',
        continueDesign: 'Continue',
        cancelReback: 'Recall',
        updateOrder: 'Confirm',
        cancelUpdate: 'Cancel',
        approveForFree: 'Approve',
        disapproveForFree: 'Disapprove',
        normalQuestion: 'COMMON ISSUE', 
      },

      tips: {
        leave: 'Current modification has not been saved, confirm to leave and abandon the modifications?',
        loadImageFail: 'Photos remarks loading failed.',
        remarkTips: "Please translate the customer's remarks, clarify or supplement the constraints, and guide the designer to design",
        differentFileName: 'The design file name is different from the order file name, please check',
        designRemark: 'Please enter the comment',
        no: 'None',
        selectRPD: 'Select RPD',
        pleaseSelectRPD: 'Please select RPD.',
        selectRPDNotEmpty: "Can't be null!",
        loadSelectListFail: 'RPD loading failed.',
        cannotClearAll: 'Cannot clear all drop-down boxes.',
        limitFourComp: 'Up to 4 options can be added.',
        clientUploadFile: 'The customer has uploaded the file, ',
        goToDownload: 'go to download',
        pleaseInputReturnReason: 'Please enter the reason',
        pleaseInputReasonToClient: 'Please enter the reason for returning order to customer.',
        pleaseInputReasonToIQC: 'Please enter the reason for returning order to IQC.',
        pleaseInputNotPassReason: 'Please enter the reason',
        pleaseSupplyClientReturen: 'Please explain the reason for the customer',
        pleaseRejectDesigner: 'Please enter the reason for refusing return order',
        fileIsUpload: 'The design file has been uploaded, ',
        pleaseToExamine: 'go to review',
        orderIdIsNull: 'ID cannot be empty.',
        loadInfoFail: 'Details loading failed.',
        pleaseSelectRPDType: 'Please select the type',
        pleaseHandleDesignerReturn: "Please deal with the reason for the designer's return",
        pleaseSelectDesignType: 'Please select design requirement.',
        dontSubmitUnknow: 'Design requirement cannot contain [unknown], please reselect.',
        noUnExamineOrder: `There's no "Pending Review" orders need proceeding now.`,
        pleaseSelectBeforeSubmit: 'Please select a design type.',
        confirmQuestion: "Confirm there're common design issues in this order?",
        selectsoftware: 'Design software cannot be blank.',
        selectversion: 'Software version cannot be blank.',
        otherDesignTips: 'This design application is in progress by other designer.',
        otherDesignLimitTips: 'Cannot clear design type from other designers',
        unionNormalProblem: 'Please select the designer(s) in this order that have common issues.',
        unionIssues: 'Common issues',
        unionRecall: 'Recalled',

        resultChange: 'Designed outputs changed. Are you sure to submit?',
        nosaveResult: 'Changes unsaved. Are you sure to quit editing?'
      },

      title: {
        toothInfo: 'Design Requirement',
        sourceFile: 'Original File',
        parameters: 'Parameters & Solution',
        process: 'Record',
        resultFile: 'Design Outputs',
        designRemark: 'Designer comment',
        selectYourRPD: 'Please select the type',
        rpdTitle: 'RPD Type',
        implantScheme: 'Implant Plan',
        
        selectYourType: 'Please select your designed {0}',
        typeTitle: '{0} Type',
        uploadImage: 'Upload',
        returnReason: 'Return reason',
        supplement: 'Supplementary Instructions',
        rejectReturn: 'Refuse return',
        notPassReason: 'Reason for not passing',
        clientReturn: 'Customer returned order',
        designerReturn : 'Designer return order reason is ',
        clientReturnReason: 'Return reason',
        iqcSupply: 'IQC return order supplement: ',
        iqcReject: 'IQC refuse return order',
        notPass: 'Did not pass',
        mySupply: 'My comments: {0}',
        askFreeReason: 'Reason',
        myDesign: 'Mine',
        otherDesign: 'Other designers'
      },
      
      info: {
        designer: 'Designer',
        noDesigner: 'Pending assign',
        expectTime: 'Expected completion time',
        clientName: 'Client ID',
        createdTime: 'Created Time',
        finishTime: 'Actual completion time',
        remark: 'Comment',
        clientRemark: 'Customer Notes: ',
        patientName: 'Patient Name',
        teamQC: 'Group QC',
        software: 'Design software',
        version: 'Software version',
        placeholder: 'Please select'
      },

      timeLine: {
        designer: 'Designer',
        auditor: 'QC',
        1: '【{0}】created order',
        2: '【{0}】edited order and submitted again',
        3: '【{0}】user updated order comments.',
        4: '【{0}】confirm the order has been completed',
        6: '【{0}】recalled order',
        7: '【{0}】downloaded the designed file',
        8: '【{0}】redesign after returning the order, the reason for the return is {1}', 
        13: '【{0}】translation completed',
        14: '【{0}】returned order，reason：{1}',
        15: '【{0}】translation completed',
        16: '【{0}】cancel the apply for returning order. Reason: 【{1}】',
        17: 'Designer【{0}】is designing',
        18: 'Designer【{0}】design completed, waiting for review',
        19: '【{0}】apply to return the order. Reason:【{1}】',
        20: 'Designer【{0}】recall design',
        21: 'Designer【{0}】has completed the design OQC【{1}】',
        22: 'Did not pass, reason: 【{0}】 OQC【{1}】',
        23: 'OQC【{0}】recall the designed file',
        25: '【{0}】 withdraw return order.',
        26: '{0}【{1}】edited the order information.',
        27: '【{0}】applied for rejection, reason :{1}',
        28: '【{0}】approved application',
        29: '【{0}】disapproved application',
        31: 'OQC 【{0}】 decided there are common design issues.',
        32: 'Designer【{0}】design completed, Group QC【{1}】, waiting for review',
        33: 'Categories that do not need design were automatically excluded: {0}',
        36: 'Designer【{0}】submitted the implant plan.',
        37: '【{0}】confirmed the implant plan.',
        38: '【{0}】asked to redesign the implant plan.',
      }
    },

    // 正畸
    ortho: {
      title: {
        newScheme: 'Latest Treatment Plan',
        historyScheme: 'History{0}',
        patientPic: 'Patient Photos',
        facePic: 'Facial Photos',
        intraPic: 'Intraoral Photos',
        ctPic: 'X-ray Photos',
        correctStep: 'Alignment Trays',
        clientReturnReason: 'Remarks',
        selectImage: 'Selected Pictures',
        uploadPateintImage: 'Please select the type of patient photos.',
        program: '3D Treatment Plan',
        face: 'Facial',
        intra: 'Intraoral',
        ct: 'X-ray',
      },

      upper: 'Upper Trays Amount',
      lower: 'Lower Trays Amount',

      status: {
        pendingReview: 'To be audited',
        returnFromClient: 'Return',
      },

      tips: {
        correctStepNotNull: "Steps amount can't be null!",
        pleaseAddScheme: 'Please add solution and reupload the design.',
        pleaseInputStep: 'Please enter the amount of the steps.',
        noPateintImage: 'Please upload patient photos, or match each type of picture from the order file.',
        previewProgram: '3D Treatment Plan Preview',
        createPrograming: 'Generating......',
        createProgramFail: 'Generation failed!',
        noProgram: '3D Treatment Plan is not generated.',
        selectImageFromOrder: 'Order Image（Please click to choose the type after selecting the image.）',
        submitNoProgram: '3D Treatment Plan has not been generated, please click "Generate 3D Plan"',
        creatingProgram: 'Generating 3D Treatment Plan, please preview and confirm the solution, then click "Finish"',
        programFailTips: 'Failed to generate 3D Treatment Plan preview, please retry.',
        viewBeforeFinish: 'Please check and confirm the 3D Treatment Plan preview, then click "Finish"',

        newPlan: 'Aure you sure to delete this new plan?'
      },
      
      btnCreateProgram: 'Generate 3D Plan',
      
    },

    operate: {
      assign: 'Assign',
      translateByMe: 'Translate',
      askTranslateByMe: 'Confirm to translate the order?',
      examineByMe: 'Review',
      askExamineByMe: 'Confirm to review the order?',
      askConfirmRevoke: 'Confirm to withdraw?',
      askCloseImageDailog: 'Continue to close without classifying the selected images?',
      askClose: 'Prompt',
    }

  }
}