<template>
  <div class="no-tooth-info">{{$t('order.add.no.tooth.info')}}</div>
</template>
<script>
export default {
  name: 'no-tooth-info',
  components: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
.no-tooth-info {
  padding-top: 12px;
  padding-left: 24px;
  width: 100%;
  height: 100%;
  border-radius: 2px;
  border-radius: 2px;
  font-family: 'webfont';
  font-size: 14px;
  // color: #6f7376;
  line-height: 22px;
}
</style>
