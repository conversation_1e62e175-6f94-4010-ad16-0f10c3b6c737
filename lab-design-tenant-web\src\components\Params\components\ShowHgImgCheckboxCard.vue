<template>
  <div class="hg-img-checkbox-card">
    <div class="img-checkbox-box" v-if="data">
      <div 
        v-for="(item, index) in data.child" 
        :key="item.value+index"
        :class="{'img-checkbox-card-item': true,'is-active': selectValue === item.name}"  >
        <span>{{ getI18nName(item, i18nTitle, $getI18nText) }}</span>
        <hg-pic :iconPath="item.iconPath"></hg-pic>
      </div>
    </div>

    <div class="children-card">
      <div
        v-for="(child, cIndex) in childrenList" 
        :key="child.name+cIndex" 
        :class="{'child-item': true, 'is-active': selectChildValue === child.name}" >
        <span>{{ getI18nName(child, i18nTitle, $getI18nText) }}</span>
        <hg-pic :iconPath="child.iconPath"></hg-pic>
      </div>
    </div>

  </div>
</template>
/* HgImgCheckboxCard对应的展示组件，要兼容交互太过麻烦，因此提供一个仅显示的组件 */
<script>
import HgPic from './HgPic';
import { getI18nName } from '../utils';

export default {
  components: { HgPic },
  model: {
    prop: 'value',
    event: 'update',
  },

  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          child: [],
        }
      }
    },
    i18nTitle: { // i18n需要拼接成i18n国际化文本，数据库就不存太长
      type: String,
      default: '',
    },
  },

  data() {
    return {
      selectValue: '', // 当前data的value
      selectChildValue: '', // 当前data的value-item 选中的value
    }
  },

  computed: {
    childrenList(){  // 子集组合

      if(!this.data) { return[]; }

      const { child } = this.data;
      if(!child || child.length === 0) {
        return [];
      }
      
      const selectChildIndex = child.findIndex(item => item.name === this.selectValue);
      if(selectChildIndex > -1) {
        return child[selectChildIndex].child;
      }

      return [];
    },
  },

  watch: {
    data: {
      deep: true,
      handler(newData) {
        this.initOldValue();
      }
    }
  },

  mounted() {
    // 初始化value 和 子集value
    if(this.data && this.data.child && this.data.child.length > 0) {
      this.initOldValue();
    }
  },
  
  methods: {
    getI18nName,
    // 更新selectChildValue的值
    setSelectChildValue(parentValue) {
      const selectChildIndex = this.data.child.findIndex(item => item.name === parentValue);
      if(selectChildIndex > -1) {
        const childItem = this.data.child[selectChildIndex];
        this.selectChildValue = childItem.value;
      }
    },

    // 加载旧值
    initOldValue() {
      const { value, child } = this.data;
      this.selectValue = value;
      this.setSelectChildValue(value);
    },

  }
}
</script>

<style lang="scss" scoped>
.hg-img-checkbox-card {
  position: relative;
  display: flex;
  flex-direction: column;
  font-size: 12px;

  .img-checkbox-box {
    display: flex;
    flex-wrap: wrap;
  }

  .img-checkbox-card-item {
    .hg-pic {
      cursor: pointer;
      margin-top: 4px;
    }
  }

  .is-active {
    color: $hg-secondary-primary;

    .hg-pic {
      border: 2px solid $hg-main-blue;
      /deep/.hg-pic-check {
        display: inline-block;
        border-color: $hg-main-blue $hg-main-blue transparent transparent;
  
        .el-icon-check {
          color:$hg-label;
        }
      }
    }
  }

  .children-card {
    display: flex;
    flex-wrap: wrap;
    margin-top: 24px;
    padding: 0 24px 24px 24px;
    background: $hg-hover;
    border-radius: 2px;

    .child-item {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      margin: 24px 24px 0 0;
      width: 176px;

      span {
        flex: 1;
        display: flex;
        flex-direction: column-reverse;
        line-height: 16px;
      }

      .hg-pic {
        margin-top: 4px;
      }
    }
    
  }
}
</style>