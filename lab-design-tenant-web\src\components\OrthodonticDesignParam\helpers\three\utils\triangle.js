import * as THREE from 'three'
// import { ExtendedTriangle } from 'three-mesh-bvh'

export const _triangle = new THREE.Triangle()

export function setTriangle(triangle, i, index, position) {
  const ta = triangle.a
  const tb = triangle.b
  const tc = triangle.c

  let i0 = i
  let i1 = i + 1
  let i2 = i + 2

  if (index) {
    i0 = index.getX(i)
    i1 = index.getX(i + 1)
    i2 = index.getX(i + 2)
  }

  ta.x = position.getX(i0)
  ta.y = position.getY(i0)
  ta.z = position.getZ(i0)

  tb.x = position.getX(i1)
  tb.y = position.getY(i1)
  tb.z = position.getZ(i1)

  tc.x = position.getX(i2)
  tc.y = position.getY(i2)
  tc.z = position.getZ(i2)
}
