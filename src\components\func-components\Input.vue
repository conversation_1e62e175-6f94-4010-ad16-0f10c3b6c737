<template>
  <div class="component-input-box">
    <div ref="componentInput" :class="['component-input', `component-input-${size}`, disabled ? 'disabled' : '']" :style="{'padding-left': paddingLeft, 'padding-right': paddingRight}">
      <div v-if="prefixIcon" class="icon">
        <i :class="['iconfont', `${prefixIcon}`]" />
      </div>
      <input
        ref="inputTarget"
        v-model="inputValue"
        :class="['input', `input_${size}`]"
        :style="{'text-align': textAlign}"
        :placeholder="placeholder"
        :title="inputValue ? inputValue : placeholder"
        :type="inputType"
        :disabled="disabled"
        textIndet
        @focus="focusInput"
        @blur="blurInput"
        @keyup="input"
        @keyup.enter="enterInput"
        @input="handleInput"
        @compositionupdate="handleComposition"
        @compositionend="handleComposition"
      >
      <div v-if="suffixIcon" class="icon">
        <i :class="['iconfont', `${suffixIcon}`]" />
      </div>
      <div v-if="type==='password' && showPwIcon && inputValue" class="icon" @click="showPasswordFunc">
        <i :class="['iconfont', showPassword ? 'icon-preview-on' : 'icon-preview-off']" />
      </div>
    </div>
  </div>
</template>

<script>
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
export default {
  name: 'Input',
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    type: { // input的类型：text,number,password
      type: String,
      default: 'text'
    },
    disabled: { // 是否禁用
      type: Boolean,
      default: false
    },
    size: { // 输入框大小
      type: String,
      default: 'normal'
    },
    prefixIcon: { // 输入框前面的图标
      type: String,
      default: ''
    },
    suffixIcon: { // 输入框后面的图标
      type: String,
      default: ''
    },
    showPwIcon: { // 当type为password时是否显示切换显示隐藏密码的图标
      type: Boolean,
      default: true
    },
    paddingLeft: { // 输入内容前面的空格间距
      type: String,
      default: '16px'
    },
    paddingRight: { // 输入内容后面的空格间距
      type: String,
      default: '16px'
    },
    textAlign: { // 输入内容的显示方向，left,right,center
      type: String,
      default: 'left'
    },
    inputContent: { // 输入的内容
      type: [String, Number],
      default: ''
    },
    replaceChar: Boolean,
    replaceCharReg: RegExp
  },
  data() {
    return {
      inputValue: this.inputContent,
      showPassword: false,
      inputType: this.type,
      isComposing: false
    }
  },
  watch: {
    inputContent(val) {
      this.inputValue = val
    },
    type(val) {
      this.inputType = val
    }
  },
  methods: {
    input(e) {
      this.$emit('update:inputContent', e.target.value)
      this.$emit('input', e.target.value)
    },
    // 回车调用失去焦点事件
    enterInput() {
      this.$refs.inputTarget.blur()
    },
    blurInput(e) {
      this.$refs.componentInput.classList.remove('active')
      this.$emit('blurInput', e.target.value)
    },
    focusInput(e) {
      this.$refs.componentInput.classList.add('active')
      this.$emit('focusInput', e.target.value)
    },
    showPasswordFunc() {
      this.showPassword = !this.showPassword
      if (this.showPassword) {
        this.inputType = 'text'
      } else {
        this.inputType = 'password'
      }
    },
    handleComposition(event) {
      this.isComposing = event.type !== 'compositionend'
    },
    handleInput(event) {
      // 监听器来处理微软输入法的中文字符输入过程。当输入法开始转换或结束转换时，将更新isComposing属性。在handleInput方法中，只在非拼音转换输入时触发，并避免对已输入的汉字进行意外替换
      if (!this.isComposing) {
        const value = event.target.value
        if (this.replaceChar) {
          let { SPECIAL_CHAR_REGEX: banReg } = COMMON_CONSTANTS
          if (this.replaceCharReg) {
            banReg = this.replaceCharReg
          }
          event.target.value = value.replace(banReg, '')
          this.inputValue = event.target.value
        }
        this.$emit('input', this.inputValue) // 这里回调父级定义的input事件
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.component-input-box {
  width: 100%;
  .component-input {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid $hg-border-color;
    border-radius: $hg-border-radius2;
    box-sizing: border-box;
    .icon {
      color: $hg-primary-fontcolor;
    }
    .input {
      display: block;
      width: 100%;
      height: 32px;
      border: none;
      background-color: transparent;
      color: $hg-primary-fontcolor;
      font-weight: normal;
      &::placeholder {
        color: $hg-secondary-fontcolor;
      }
      &_normal {
        height: 40px;
        line-height: 40px;
      }
      &_medium {
        height: 32px;
        line-height: 32px !important;
      }
      &_small {
        height: 24px;
        line-height: 24px;
      }
    }
    &-normal {
      height: 40px;
      padding: 0 24px;
      .icon {
        width: $hg-large-fontsize;
        height: $hg-large-fontsize;
        line-height: $hg-large-fontsize;
        margin-right: 8px;
        .iconfont {
          font-size: $hg-medium-fontsize;
        }
      }
      .input {
        margin-left: 8px;
      }
    }
    &-small, &-medium {
      .icon {
        width: $hg-medium-fontsize;
        height: $hg-medium-fontsize;
        line-height: $hg-medium-fontsize;
        .iconfont {
          font-size: $hg-normal-fontsize;
        }
      }
    }
    &-medium {
      height: 32px;
      padding: 0 16px;
      .icon {
        margin-right: 8px;
      }
    }
    &-small {
      height: 24px;
      padding: 0 12px;
      .icon {
        margin-right: 4px;
      }
    }
    &:hover {
      border: 1px solid $hg-disable-fontcolor;
    }
    &.active {
      border-color: $hg-primary-fontcolor;
    }
    &.disabled {
      background-color: rgba(84,86,92,0.25);
      border: 1px solid $hg-border-color;
      .input {
        color: $hg-disable-fontcolor;
        &::placeholder {
          color: $hg-disable-fontcolor;
        }
      }
    }
  }
}
</style>
