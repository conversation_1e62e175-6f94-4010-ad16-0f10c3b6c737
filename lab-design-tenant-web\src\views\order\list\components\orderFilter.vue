<template>
  <div class="order-filter" :class="isExpand ? 'hiddenall' : ''">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item>
        <div class="all-input">
          <el-select v-model="selectInput" :class="[selectInput == '1' ? 'input-select' : 'select-input']" placeholder="请选择" @change="changeInputBox">
            <el-option :label="$t('orderList.searchList.select1')" value="1"></el-option>
            <el-option :label="$t('orderList.searchList.select2')" value="2"></el-option>
          </el-select>
          <hg-input v-if="selectInput == '1'" class="input" v-model="searchData.keywords" :placeholder="$t('orderList.searchList.pleaseSearch')" clearable @change="changeSearch" @clear="clearKey" replaceChar
            ><i slot="suffix" class="el-input__icon el-icon-search"></i
          ></hg-input>
          <el-select v-else class="dept-select" multiple collapse-tags filterable v-model="searchData.deptCodes" :placeholder="$t('orderList.searchList.pleaseSearch')" @change="changeSearch">
            <el-option v-for="item in deptList" :key="item.deptCode" :label="item.deptName" :value="item.deptCode"></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item :label="$t('orderList.searchList.designtype')">
        <design-type-select v-model="searchData.designTypeCodes" :designCodesAll="searchData.designCodesAll" needCombined @changeSearch="changeSearch"></design-type-select>
      </el-form-item>
      <el-form-item :label="$t('orderList.searchList.statusType')">
        <el-select v-model="searchData.status" :placeholder="$t('orderList.searchList.statusHolder')" clearable @change="changeSearch('statusSearch')">
          <el-option v-for="(item, index) in statusList" :key="index" :label="$t(`design_tenant_order_status.${item.enName}`)" :value="item.statusCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('orderList.searchList.returnedType')">
        <el-select v-model="searchData.returnOrderType" :placeholder="$t('orderList.searchList.statusHolder')" clearable @change="changeSearch('statusSearch')">
          <el-option v-for="(item, index) in returnOrderList" :key="index" :label="language == 'zh' ? item.cnName : item.enName" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('order.detail.info.software')">
        <el-select v-model="searchData.designSoftware" :placeholder="$t('orderList.searchList.designHolder')" clearable @change="changeSearch('statusSearch')">
          <el-option v-for="(item, index) in designSoftwareList" :key="index" :label="language == 'zh' ? item.cnName : item.enName" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="isHaveSearch" :label="$t('orderList.order.designBy')">
        <el-select v-model="searchData.currentProcessor" :placeholder="$t('designpoints.all')" @change="changeSearch">
          <el-option v-for="item in designerList" :key="item.userCode" :label="item.userName" :value="item.userCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('orderList.searchList.creatTime')">
        <date-range-picker valueFormat="timestamp" v-model="TimeRangeData.creatTime" @change="changeSearch"></date-range-picker>
      </el-form-item>
      <el-form-item :label="$t('orderList.searchList.finishTime')">
        <date-range-picker valueFormat="timestamp" v-model="TimeRangeData.finishTime" @change="changeSearch"></date-range-picker>
      </el-form-item>

      <!-- <el-form-item>
        <el-radio v-model="searchData.externalReturn" @click.native.prevent="cancelSelcet('externalReturn')" :label="1">外返订单</el-radio>
        <el-radio v-model="searchData.internalReturn" @click.native.prevent="cancelSelcet('internalReturn')" :label="1">内返订单</el-radio>
      </el-form-item> -->
    </el-form>
    <span class="expangIcon" @click="expangSearch"><hg-icon style="font-size: 24px;" :icon-name="!isExpand ? 'icon-icon_double_arrow_up' : 'icon-icon_double_arrow_down'"></hg-icon></span>
  </div>
</template>

<script>
import DateRangePicker from '@/components/DateRangePicker';
import DesignTypeSelect from './design-type-select';
import { getUserList, getdeptInfos } from '@/api/order';
import { mapGetters } from 'vuex';
export default {
  name: 'orderFilter',
  components: { DateRangePicker, DesignTypeSelect },
  data() {
    return {
      isExpand: false,
      selectInput: '1',
      searchData: {
        deptCodes: [],
        currentProcessor: 0, // 当前处理人
        keywords: '',
        status: '',
        designTypeCodes: [], //类型
        designCodesAll: [], //设计类型回显
        designCategoryCode: '', //一级类
        returnOrderType: '',//返单类型
        designSoftware: '', // 设计软件
      },
      TimeRangeData: {
        creatTime: [0, 0], //创建时间
        finishTime: [0, 0], //完成时间
      },
      returnOrderList: [{
        cnName: '外返订单',
        enName: 'External',
        value: '1'
      },{
        cnName: '内返订单',
        enName: 'Internal',
        value: '2'
      }],
      designSoftwareList: [{
        cnName: '3Shape',
        enName: '3Shape',
        value: 1001
      },{
        cnName: 'Exocad',
        enName: 'Exocad',
        value: 1002
      }],
      deptList: [], // 二级部门列表
    };
  },
  props: {
    statusList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    searchList: Object,
    // isExpand: Boolean
  },
  computed: {
    ...mapGetters(['language', 'roles']),
    isHaveSearch(){
      let roleList = this.roles.map((item) => {return item.roleCode})
      const exists = roleList.some(value => [50033, 50034, 50031].includes(value));
      return exists
    },
    designerList(){
      // iqc，oqc，设计师组长可以看到这个搜索
      let roleList = this.roles.map((item) => {return item.roleCode})
      let arr = [
        {
          userName: this.$t('orderList.searchList.all'),
          userCode: 0
        },
        {
          userName: this.$t('orderList.searchList.me'),
          userCode: 1
        }
      ]
      if(roleList.includes(50033) || roleList.includes(50034)){
        arr.push({
          userName: this.$t('orderList.searchList.none'),
          userCode: 2
        })
      }
      return arr
    }
  },
  watch: {
    searchList: {
      handler: function(newVal, oldVal) {
        const { keywords, status, designTypeCodes, startTime, endTime, completeStartTime, completeEndTime, designCodesAll, returnOrderType, designSoftware, deptCodes, currentProcessor, selectInput } = newVal;
        this.searchData.keywords = keywords;
        this.searchData.deptCodes = deptCodes;
        // 如果是部门的搜索要转换
        this.selectInput = selectInput ? selectInput : '1';
        this.searchData.currentProcessor = currentProcessor;
        this.searchData.status = status ? status : '';
        this.searchData.designTypeCodes = designTypeCodes;
        this.TimeRangeData.creatTime = [startTime, endTime];
        this.TimeRangeData.finishTime = [completeStartTime, completeEndTime];
        this.searchData.designCodesAll = designCodesAll;
        this.searchData.returnOrderType = returnOrderType;
        this.searchData.designSoftware = designSoftware;
      },
      deep: true,
    },
  },
  mounted () {
    this.getdept()
  },
  methods: {
    // 搜索栏的收起展开
    expangSearch(){
      this.isExpand = !this.isExpand;
    },
    // 清除keyword
    clearKey() {
      if (this.$route.query.orderNo) {
        this.$router.push({ path: '/order' });
      }
    },
    //输入框条件切换时清楚搜索条件
    changeInputBox(){
      this.searchData.deptCodes = [];
      this.searchData.keywords = '';
      this.changeSearch()
    },
    // 搜索
    changeSearch(designType, valueList, designCodesAll, firstCode) {
      //当不是数据看板跳转过来切操作了这两个有回显的数据就要清空他们在赋值
      if(designType != 'designType'){
        if (this.$route.query.isFrom) {
          this.searchData.designTypeCodes = [];
          this.searchData.designCodesAll = [];
          this.searchData.designCategoryCode = '';
        }
      }
      if (designType != 'statusSearch') {
        if (this.$route.query.isFrom) {
          this.searchData.status = '';
          this.searchData.returnOrderType = ''
        }
      }
      if (designType == 'designType') {
        this.searchData.designTypeCodes = valueList;
        this.searchData.designCodesAll = designCodesAll;
        this.searchData.designCategoryCode = firstCode;
      }
      let searchValue = {
        keywords: this.searchData.keywords,
        status: this.searchData.status ? this.searchData.status : '',
        designTypeCodes: this.searchData.designTypeCodes, //类型
        startTime: this.TimeRangeData.creatTime[0], //创建时间
        endTime: this.TimeRangeData.creatTime[1],
        completeStartTime: this.TimeRangeData.finishTime[0], //完成时间
        completeEndTime: this.TimeRangeData.finishTime[1],
        designCodesAll: this.searchData.designCodesAll,
        designCategoryCode: this.searchData.designCategoryCode, //一级类
        designSoftware: this.searchData.designSoftware,
        deptCodes: this.searchData.deptCodes,
        currentProcessor: this.searchData.currentProcessor ? this.searchData.currentProcessor : 0,
        selectInput: this.selectInput,

        returnOrderType: this.searchData.returnOrderType,//内外返订单，1是外返，2是内返
      };
      this.$emit('searchList', searchValue);
      // 当进行搜索的时候清空数据看板跳转的条件
      if (this.$route.query.isFrom) {
        this.$router.push({ path: '/order' });
        this.$store.dispatch('setJumpSearch', {});
      }
    },
    // 获取二级部门
    async getdept(){
      let {code, data} = await getdeptInfos();
      this.deptList = data
    },
  },
};
</script>

<style lang="scss" scope>
.order-filter {
  position: relative;
  .all-input{
    // position: relative;
    .input-select{
      // position: absolute;
      // left: 4px;
      vertical-align: top;
      border-top-right-radius: 0;
      margin-right: 0px;
      width: 240px;
      .el-input .el-input__inner{
        // height: 34px;
        background: #313B49;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
    .select-input{
      // position: absolute;
      // z-index: 2;
      vertical-align: top;
      left: 0px;
      width: 140px;
      .el-input .el-input__inner{
        // height: 34px;
        background: #313B49;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
    .input {
      width: 300px;
      vertical-align: top;
      .el-input__inner {
        // padding-left: 260px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
      .el-input__icon {
        margin-left: 14px !important;
        font-size: 16px;
      }
      .el-input__prefix {
        margin-left: 0;
      }
    }
    .dept-select{
      width: 300px;
      .el-input__inner {
        // padding-left: 160px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
      .el-select__input{
        // padding-left: 140px;
        color: #fff;
      }
      // .el-input__icon:after{
      //   content: "\e778";
      // }
      // .el-select__caret{
      //   display: flex;
      //   transform: rotateZ(360deg);
      // }
      .el-input.is-focus .el-input__inner{
        border-color: #F3F5F7;
      }
    }
  }
  // .design-type-select{
  //   width: 300px;
  // }
  .el-select {
    width: 240px;
    .el-input.is-focus .el-input__inner{
      border-color: #F3F5F7;
    }
  }
  .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner{
    box-shadow: none;
  }
  .expangIcon{
    position: absolute;
    right: 0px;
    top: 10px;
    cursor: pointer;
  }
}
.hiddenall{
  height: 60px;
  overflow: hidden;
}
</style>
