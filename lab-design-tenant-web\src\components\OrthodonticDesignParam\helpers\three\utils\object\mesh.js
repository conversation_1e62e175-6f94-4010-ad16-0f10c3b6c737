import * as THREE from 'three'
import { traverseTreeChildren } from '@/components/OrthodonticDesignParam/helpers/utils/index'
import { _matrix4 } from '../matrix4'
import { getGeometrySetCenterMatrix, getGeometryBox3Vectors } from '../geometry/index'

// 做几何居中,geometry做居中,mesh做修正保持原空间状态
export function centerMeshGeometry(mesh) {
  const { geometry } = mesh
  const geometrySetCenterMatrix = getGeometrySetCenterMatrix(geometry)
  geometry.center()

  traverseTreeChildren(mesh, (child) => {
    if (mesh === child || !child.geometry) {
      return true
    }

    child.geometry.applyMatrix4(geometrySetCenterMatrix)

    return true
  })

  mesh.applyMatrix4(_matrix4.copy(geometrySetCenterMatrix).invert())
}

export function getMeshOriginApplyBox3(mesh) {
  const box3 = new THREE.Box3()
  const { geometry, matrixWorld } = mesh

  geometry.computeBoundingBox()
  box3.copy(geometry.boundingBox).applyMatrix4(matrixWorld)
  return box3
}

export function getMeshsBoundingSphere(meshs, center = new THREE.Vector3()) {
  const vectors = []

  for (const mesh of meshs) {
    mesh.updateMatrix()

    const { maxs, mins } = getGeometryBox3Vectors(mesh.geometry)
    const points = []

    {
      const { x, y, z } = maxs
      points.push(x, y, z)
    }

    {
      const { x, y, z } = mins
      points.push(x, y, z)
    }

    for (const point of points) {
      point.applyMatrix4(mesh.matrixWorld)
    }

    vectors.push(...points)
  }

  const sphere = new THREE.Sphere()
  sphere.setFromPoints(vectors, center)

  return sphere
}
