import store from '@/store';

export default {
  inserted(el, binding) {
    const { value } = binding;
    if (value && value instanceof Array && value.length > 0) {
      const permissionRoles = value;
      const hasPermission = store.getters.authList.some(auth => {
        return permissionRoles.includes(auth);
      });

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error(`使用方式： v-permission="['admin','editor']"`);
    }
  }
};
