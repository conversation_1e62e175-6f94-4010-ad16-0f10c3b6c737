import http from '../request'

/** 获取客户列表（分页）
 * @params {
 * "orgCode": 0,  // 客户编号
 * "pageNo": 0,   // 当前页码
 * "pageSize": 0, // 每页数量
 * "status": 0    // 状态 0 待审核 1 已开通 2 禁用
 * } data
 */
export const getCustomerList = (params) => {
  return http({
    url: '/user-basic/org/v1/customerListByPage',
    method: 'POST',
    data: params,
    showTip: true
  })
}

export const getCRMList = (orgName) => {
  return http({
    url: '/user-basic/org/v1/getOrgByRetrieval',
    method: 'GET',
    params: {
      orgName
    }
  });
};

/** 添加客户
 * @params {
 * "customLevel": 0,   // 客户等级 0 普通；1 VIP ;2 VVIP
 * "customType": 0,    // 客户类型 0 非直营客户； 1直营客户
 * "email": "",        // 邮箱
 * "mobile": "",       // 手机号
 * "mobilePrefix": "", // 区号
 * "orgAddress": "",   // 公司地址
 * "orgLeader": "",    // 负责人
 * "orgName": "",      // 公司名称
 * "password": ""      // 密码（md5）
 * } data
 */
 export const addCustomer = (params) => {
  return http({
    url: '/user-basic/org/v1/addCustomer',
    method: 'POST',
    data: params,
  })
}

/** 获取客户信息
 * @params {
 * "orgCode": 0,   // 客户编号
 * } data
 */
 export const getCustomerInfo = (params) => {
  return http({
    url: '/user-basic/org/v1/customerInfo',
    method: 'POST',
    data: params,
  })
}

/** 编辑客户信息
 * @params {
 * "customLevel": 0,   // 客户等级 0 普通；1 VIP ;2 VVIP
 * "customType": 0,    // 客户类型 0 非直营客户； 1直营客户
 * "orgCode": ""       // 客户编号
 * "status": ""        // 客户状态 1启用 2禁用
 * } data
 */
 export const editCustomer = (params) => {
  return http({
    url: '/user-basic/org/v1/editCustomer',
    method: 'POST',
    data: params,
  })
}

/** 客户流程配置功能——根据组织code获取sku列表
 * @params {
 * "orgCode": ""  // 客户编号
 * "pageNo": 0,   // 当前页码
 * "pageSize": 0, // 每页数量
 * "skuName": ''   // 按sku名称查询
 * } data
 */
 export const getDesignSkuList = (params) => {
  return http({
    url: '/order-config-service/design-sku/v1/designSku/getDesignSkuPage',
    method: 'POST',
    data: params,
  })
}

/** 客户流程配置功能——开通/关闭sku
 * @params {
 * "orgCode": ""  // 客户编号
 * "skuCode": "", // sku 编号
 * "skuStatus": 0 // sku 状态 0关闭，1开通
 * } data
 */
 export const changeSKU = (params) => {
  return http({
    url: '/order-config-service/design-sku/v1/designSku/openAndOffDesignSku',
    method: 'POST',
    data: params,
  })
}

/** 客户流程配置功能——导入sku信息
 * @params {
 * "orgCode": ""  // 客户编号
 * "uploadFile": "", // 报价单文件
 * } data
 */
 export const uploadSKU = (params) => {
  return http({
    url: '/order-config-service/design-sku/v1/uploadSkuExcel',
    method: 'POST',
    data: params,
    headers: {
      'content-type': 'multipart/form-data',
    }
  })
}

/** 去除组织new标识
 * @params {
 * "orgCode": ""  // 客户编号
 * } data
 */
export const clearNewFlag = (params) => {
  return http({
    url: '/user-basic/org/v1/setOld',
    method: 'POST',
    data: params
  })
}

/** 审核客户
 * @params {
 * "isPass": 0,   // 是否通过 0：不通过 1：通过
 * "orgCode": ""       // 客户编号
 * } data
 */
export const examineCustomer = (params) => {
  return http({
    url: '/user-basic/org/v1/audit',
    method: 'POST',
    data: params
  })
}

/** 客户流程配置功能——根据组织code获取价格清单分页列表
 * @params {
 * "orgCode": ""  // 客户编号
 * "pageNo": 0,   // 当前页码
 * "pageSize": 0, // 每页数量
 * } data
 */
export const getDesignPriceList = (params) => {
  return http({
    url: '/design-basic-service/tenant/price/v1/list',
    method: 'POST',
    data: params
  })
}
export const getDesignPriceImport = (params) => {
  return http({
    url: '/design-basic-service/tenant/price/v1/import',
    method: 'post',
    data: params,
    headers: {
      'content-type': 'multipart/form-data'
    }
  })
}

/** 客户流程配置功能——获取当前客户组织是否开启极速下单
 * @query
 * "orgCode": ""  // 客户编号
 */
export const getQuickOrder = (param) => {
  return http({
    url: '/design-basic-service/tenant/org/v1/getOrderQuickly?orgCode=' + param,
    method: 'GET'
  })
}

/** 客户流程配置功能——设置当前客户开启/关闭极速下单
 * @params {
 * "orgCode": ""  // 客户编号
 * "pageNo": 0,   // 当前页码
 * "pageSize": 0, // 每页数量
 * } data
 */
export const setQuickOrder = (params) => {
  return http({
    url: '/design-basic-service/tenant/org/v1/addOrderQuickly',
    method: 'POST',
    data: params
  })
}

/** 客户流程配置功能——获取所有设计师小组
 * 无请求参数
 */
export const getAllDesignGroups = (param) => {
  return http({
    url: '/user-basic/labUser/v1/designerDept',
    method: 'POST'
  })
}

/** 客户流程配置功能——获取当前客户组织的关联设计师小组
 * @query
 * "orgCode": ""  // 客户编号
 */
export const getRelateDesignGroups = (param) => {
  return http({
    url: '/design-basic-service/tenant/org/v1/getOrgRelevanceDesign?orgCode=' + param,
    method: 'GET'
  })
}

/** 客户流程配置功能——给当前客户关联设计师小组
 * @params {
 * "orgCode": ""  // 客户编号
 * "deptCodes": [], // 关联设计组的code
 * }
 */
export const bindDesignGroups = (params) => {
  return http({
    url: '/design-basic-service/tenant/org/v1/addOrgRelevanceDesign',
    method: 'POST',
    data: params
  })
}

/** 客户流程配置功能——获取加急时间列表
 * @query
 * "orgCode": ""  // 客户编号
 */
export const getOrgUrgentList = (param) => {
  return http({
    url: '/design-basic-service/tenant/org/v1/getOrgUrgent?orgCode=' + param,
    method: 'GET'
  })
}

/** 客户流程配置功能——设置当前组织价格清单中的加急时间
 * @query
 * "orgCode": 0,
 * "urgentCodes": [] // 编辑的加急时间集合
 */
export const editOrgUrgentTime = (params) => {
  return http({
    url: '/design-basic-service/tenant/org/v1/editUrgent',
    method: 'POST',
    data: params
  })
}

/** 客户流程配置功能——编辑价格清单
 * @params {
 * "orgCode": ""  // 客户编号
 * "priceInfoEditReqs": [ // 编辑价格清单信息的集合
 *  {
 *    "designCode": 0, // 品类code
 *    "price": 0,
 *    "urgentCode": 0 // 加急时间code
 * 	}
 * ]
 */
export const editPrice = (params) => {
  return http({
    url: '/design-basic-service/tenant/price/v1/edit',
    method: 'POST',
    data: params
  })
}

/** 获取当前所有的币种*/
export const getAllCurrency = (params = '') => {
  return http({
    url: '/design-settlement-service/exchangerate/getAll?name=' + params,
    method: 'GET'
  })
}

// 获取银行账户列表
export const getBankList = () => {
  return http({
    url: '/user-basic/bank/v1/list',
    method: 'POST'
  })
}

// 根据crm和币种获取银行信息
export const refreshBankAccount = (data) => {
  return http({
    url: '/user-basic/bank/v1/refreshBankAccount',
    method: 'POST',
    data
  })
}

// 导出客户
export const exportCustomer = (data) => {
  return http({
    url: '/user-basic/excel/v1/exportCustomer',
    method: 'POST',
    data
  })
}
// 获取账单抬头列表
export const getHeaderList = () => {
  return http({
    url: '/user-basic/header/v1/list',
    method: 'POST'
  })
}
// 同步crm列表
export const getCrmList = (data) => {
  return http({
    url: '/design-basic-service/tenant/price/v1/crmList',
    method: 'POST',
    data
  })
}
// 同步crm
export const asyncCrmInTable = (data) => {
  return http({
    url: '/design-basic-service/tenant/price/v1/asyncCrm',
    method: 'POST',
    data
  })
}
// 获取是否已经修改过税
export const getAccountByOrgCode = (data) => {
  return http({
    url: '/design-settlement-service/account/getAccountByOrgCode',
    method: 'GET',
    params: data
  })
}
// 修改是否含税
export const writeTax = (data) => {
  return http({
    url: '/design-settlement-service/account/update/tax',
    method: 'POST',
    params: data
  })
}

// 获取所有设计品类
export const getDesignAll = (data) => {
  return http({
    url: '/design-basic-service/systemCommon/v1/getDesignAll',
    method: 'GET',
  })
}
// 获取已经设置的品类
export const getDesignFilter = (data) => {
  return http({
    url: '/design-basic-service/tenant/org/v1/getDesignFilter',
    method: 'GET',
    params: data
  })
}
// 设置过滤设置品类
export const setDesignFilter = (data) => {
  return http({
    url: '/design-basic-service/tenant/org/v1/setDesignFilter',
    method: 'POST',
    data
  })
}
