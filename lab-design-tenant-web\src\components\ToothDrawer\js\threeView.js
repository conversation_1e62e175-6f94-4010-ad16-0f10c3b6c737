import * as THREE from 'three';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { upperNumbers, lowerNumbers, toothNumbers } from './constant';
import { actions, resetStyle, getIndexMaximum, disposeChild } from './actions';
import { staticResourcesUrl } from '@/config';

let commonMaterial = new THREE.MeshPhongMaterial({
  color: 0xcecece,
});

export default class ThreeView {
  constructor(dom) {
    this.dom = dom;
    this._selectedTooths = [];
    this.ToothGroup = new THREE.Group(); //牙齿模型mesh组
    this.BrigeGroup = new THREE.Group(); //桥体相关mesh组
    this.raycaster = new THREE.Raycaster(); //这个类用于进行raycasting（光线投射）。 光线投射用于进行鼠标拾取（在三维空间中计算出鼠标移过了什么物体）。
    this.mouse = new THREE.Vector2();
    this.loader = new STLLoader();
    this.init();
    this.loadModel();
    this.animate();

    Object.defineProperty(this, 'selectedTooths', {
      set(value) {
        //console.log(value);
        this._selectedTooths = value;
        this.initSelectStatus();
        this.selectTooth([])
      },
      get() {
        return this._selectedTooths;
      },
      configurable: true,
    });
  }
  loadModel() {
    const path = `${staticResourcesUrl}/tooth_model/`;
    this.loader.setPath(path);
    const GumsArr = ['upper', 'lower'];
    const bracketArr = ['bracket_up_half', 'bracket_low_half'];
    const bracketWideArr = ['bracket_up_half_l', 'bracket_low_half_l'];
    const fourArr = ['bracket_low_four_one_left', 'bracket_low_four_one_right','bracket_up_four_one_left','bracket_up_four_one_right'];
    const bracketPostions = [ new THREE.Vector3(171, 87, 10), new THREE.Vector3(171, 86, 10)];
    const bracketWidePostions = [ new THREE.Vector3(171, 87, 7), new THREE.Vector3(171, 85, 7)];
    const fourPostions = [new THREE.Vector3(172, 98, 10),new THREE.Vector3(172, 98, 10),new THREE.Vector3(170, 86, 6),new THREE.Vector3(170, 86, 6) ]
   
    let curIndex = 0;
    let total = toothNumbers.length + GumsArr.length + bracketArr.length + bracketWideArr.length + fourArr.length;
    const handle = () => {
      curIndex++;
      if (curIndex === total) {
        // this.$emit('loaded',true)
        this.viewerLoaded()
      }
    };

    const renderTooth = () => {
      for (let i = 1; i < toothNumbers.length + 1; i++) {
        this.loader.load(
          'unn' + i + '.stl',
          geometry => {
            // let g1 = new THREE.Geometry().fromBufferGeometry(geometry)
            // g1.mergeVertices() /* ADDED MERGE WHICH GIVES ERROR */
            // g1.computeVertexNormals()
            let mesh = new THREE.Mesh(
              geometry,
              // commonMaterial
              new THREE.MeshPhongMaterial().copy(commonMaterial)
            );
            mesh.name = toothNumbers[i - 1];
            //绑定盒子模型
            mesh.geometry.computeBoundingBox();
            let centerId = new THREE.Vector3();
            // 将该向量设置为a + b。
            centerId.addVectors(mesh.geometry.boundingBox.min, mesh.geometry.boundingBox.max);
            // 将该向量与所传入的标量s进行相乘。
            centerId.multiplyScalar(0.5);
            // 将该向量乘以四阶矩阵m（第四个维度隐式地为1），and divides by perspective.
            // 世界矩阵
            // 一个对象的世界矩阵.matrixWorld是该对象本地矩阵及其所有所有祖宗对象本地矩阵的乘积，或者每一个对象的世界矩阵是对象本地矩阵和父对象的世界矩阵的乘积。
            centerId.applyMatrix4(mesh.matrixWorld);
            mesh.geometry.center(centerId.x, centerId.x, centerId.x);
            mesh.position.set(centerId.x, centerId.y, centerId.z);

            this.ToothGroup.add(mesh);
            handle();
          },
          progress => {},
          err => {
            console.error(err);
          }
        );
      }
    };
    const renderFloor = () => {
      GumsArr.forEach(ele => {
        this.loader.load(`${ele}.stl`, geometry => {
          // let g1 = new THREE.Geometry().fromBufferGeometry(geometry)
          // g1.mergeVertices() /* ADDED MERGE WHICH GIVES ERROR */
          // g1.computeVertexNormals()
          let mesh1 = new THREE.Mesh(geometry, new THREE.MeshPhongMaterial().copy(commonMaterial));
          mesh1.name = ele;
          mesh1.position.z = -5;
          this.scene.add(mesh1);
          handle();
        });
      });
    };
    const renderBracket = () => {
      for (let i = 0; i < bracketArr.length; i++) {
        let name = bracketArr[i];
        this.loader.load(name + '.stl', geometry => {
          let material = new THREE.MeshBasicMaterial({
            color: '#B8C0D9',
            transparent: true,
            opacity: 1,
          });
          let mesh = new THREE.Mesh(geometry, material);
          mesh.name = name;
          mesh.position.copy(bracketPostions[i]);
          mesh.visible = false;
          this.scene.add(mesh);
          handle();
        });
      }

      for (let i = 0; i < fourArr.length; i++) {
        let name = fourArr[i];
        this.loader.load(name + '.stl', geometry => {
          let material = new THREE.MeshBasicMaterial({
            color: '#B8C0D9',
            transparent: true,
            opacity: 1,
          });
          let mesh = new THREE.Mesh(geometry, material);
          mesh.name = name;
          mesh.position.copy(fourPostions[i]);
          mesh.visible = false;
          this.scene.add(mesh);
          handle();
        });
      }

      bracketWideArr.forEach((name, i) => {
        this.loader.load(name + '.stl', geometry => {
          let material = new THREE.MeshBasicMaterial({
            color: '#B8C0D9',
            transparent: true,
            opacity: 1,
          });
          let mesh = new THREE.Mesh(geometry, material);
          mesh.name = name;
          mesh.position.copy(bracketWidePostions[i]);
          mesh.visible = false;
          this.scene.add(mesh);
          handle();
        });
      });
    };
    renderTooth();
    renderFloor();
    renderBracket();
  }
  /**
   * 初始化threeJS
   */
  init() {
    const fov = 10,
      near = 0.1,
      far = 10000,
      domWidth = 400,
      domHeight = 400;
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x262629);
    this.camera = new THREE.PerspectiveCamera(fov, domWidth / domHeight, near, far);

    // let axis = new THREE.AxisHelper(1000)
    //   this.scene.add(axis);
    this.camera.position.set(170, 80, 820);
    this.camera.lookAt(new THREE.Vector3(170, 95, 0));
    this.scene.add(this.camera);

    this.scene.add(new THREE.AmbientLight(0x333333));
    let light = new THREE.SpotLight(0xffffff, 1);
    light.position.set(200, 95, 800);
    this.scene.add(light);
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      preserveDrawingBuffer: true,
    });
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.setSize(domWidth, domHeight);
    this.renderer.gammaInput = true;
    this.renderer.gammaOutput = true;
    this.renderer.shadowMap.enabled = true;
    this.scene.add(this.ToothGroup);
    this.scene.add(this.BrigeGroup);

    this.renderer.render(this.scene, this.camera);
    this.camera.aspect = parseInt(this.renderer.domElement.style.width) / parseInt(this.renderer.domElement.style.height);
    this.camera.updateProjectionMatrix();
    // 渲染容器监听鼠标点击事件
    this.renderer.domElement.addEventListener('mousedown', this.onMouseDown.bind(this), false);
    // 渲染容器监听鼠标移动事件
    this.renderer.domElement.addEventListener('mousemove', this.onMouseMove.bind(this), false);

    // setTimeout(() => {
    // //console.log(this.dom)
    this.dom && this.dom.appendChild(this.renderer.domElement);
    this.camera.aspect = parseInt(this.renderer.domElement.style.width) / parseInt(this.renderer.domElement.style.height);
    this.camera.updateProjectionMatrix();
    // },0);

    // let controls = new THREE.OrbitControls(
    //   this.camera,
    //   this.renderer.domElement
    // );
    // controls.target.set(0, 0, 0);
  }
  /**
   * 获取模型截图
   */
  getImage() {
    this.selectedTooths = [];
    this.clearSelectBorder();
    //  截图
    this.toothImageBase64 = this.renderer.domElement.toDataURL('image/png', 1);
    return this.toothImageBase64;
  }

  /**
   * 清除选中边框样式
   */
  clearSelectBorder() {
    for (let i = this.scene.children.length - 1; i >= 0; i--) {
      if (this.scene.children[i].name === 'outline') {
        const mesh = this.scene.children[i];
        disposeChild(mesh);
        this.scene.remove(mesh);
      }
    }
    this.renderer.render(this.scene, this.camera);
  }
  /**
   * 初始化选择状态 设置选中的牙齿出现边框
   */
  initSelectStatus() {
    this.clearSelectBorder();
    let m = new THREE.MeshBasicMaterial({ color: 0x1d43a9 });
    for (let i = 0, len = this.selectedTooths.length; i < len; i++) {
      let mesh = this.selectedTooths[i];
      var cube = new THREE.Mesh(mesh.geometry, m);
      cube.name = 'outline';
      this.scene.add(cube);
      cube.scale.set(1.1, 1.1, 1.05);
      cube.position.set(mesh.position.x, mesh.position.y, mesh.position.z - 1.5);
    }
  }
  /**
   * 动画效果
   */
  animate() {
    this.timer = requestAnimationFrame(this.animate.bind(this));
    this.renderer.render(this.scene, this.camera);
  }
  /**
   * 选中的牙齿
   * @param toothObj 牙齿对象 Mesh
   */
  handleCurrentSelect(toothObj, isShift) {
    if (!toothObj) {
      this.selectedTooths = [];
      return;
    }
    let lastName = (this.selectedTooths[this.selectedTooths.length - 1] || {}).name;
    let curName = toothObj.name;
    let len = this.selectedTooths.length;

    //多选
    if (lastName && isShift) {
      let numbers = upperNumbers.includes(lastName) && upperNumbers.includes(curName) ? upperNumbers : lowerNumbers;
      let p1 = getIndexMaximum(numbers, [lastName, curName]);
      let result = numbers.slice(p1.min, p1.max + 1).map(ele => this.ToothGroup.getObjectByName(ele));
      //console.log(result);
      result.forEach(ele => {
        if (!this.selectedTooths.some(child => child.name === ele.name)) {
          this.selectedTooths.push(ele);
          this.initSelectStatus();
        }
      });
      return;
    }

    //反选清除
    for (let i = 0; i < len; i++) {
      if (this.selectedTooths[i].name == curName) {
        this.selectedTooths.splice(i, 1);
        this.initSelectStatus();
        return;
      }
    }

    this.selectedTooths.push(toothObj);
    this.initSelectStatus();
  }
  /**
   * 点击模型
   * @param event 事件对象
   */
  onMouseDown(event) {
    if (event.buttons === 2) {
      return false;
    }
    this.mouse.x = ((event.clientX - this.renderer.domElement.getBoundingClientRect().left) / this.renderer.domElement.offsetWidth) * 2 - 1;
    this.mouse.y = -((event.clientY - this.renderer.domElement.getBoundingClientRect().top) / this.renderer.domElement.offsetHeight) * 2 + 1;
    // 这个类用于进行raycasting（光线投射）。 光线投射用于进行鼠标拾取（在三维空间中计算出鼠标移过了什么物体）。
    this.raycaster.setFromCamera(this.mouse, this.camera);
    // See if the ray from the camera into the world hits one of our meshes
    // // 计算物体和射线的焦点
    let intersects = this.raycaster.intersectObject(this.ToothGroup, true);

    // Toggle rotation bool for meshes that we clicked
    if (intersects.length > 0) {
      this.handleCurrentSelect(intersects[0].object, event.shiftKey);
    } else {
      this.handleCurrentSelect();
    }
    this.selectTooth(this.selectedTooths);
  }
  onMouseMove(event) {
    this.mouse.x = ((event.clientX - this.renderer.domElement.getBoundingClientRect().left) / this.renderer.domElement.offsetWidth) * 2 - 1;
    this.mouse.y = -((event.clientY - this.renderer.domElement.getBoundingClientRect().top) / this.renderer.domElement.offsetHeight) * 2 + 1;
    this.raycaster.setFromCamera(this.mouse, this.camera);
    // See if the ray from the camera into the world hits one of our meshes
    let intersects = this.raycaster.intersectObject(this.ToothGroup, true);
    // 获取鼠标经过的支架
    if (intersects.length > 0) {
      // this.hoverToothNum = intersects[0].object.name;
      this.hoverTooth(intersects[0].object.name);
    }
  }
  /**
   *
   * @param {String} type
   * @param {Function} callback
   */
  addEventListener(type, callback) {
    // 'selectTooth'
    this[type] = callback;
  }
  /**
   * 根据toothDesign绘制样式
   * @param {*} toothDesign
   * @returns
   */
  draw(toothDesign) {
    resetStyle(this);
    if (!toothDesign.length) return;

    toothDesign.forEach(toothDesignItem => {
      let action = actions[toothDesignItem.code] || actions['default'];
        action(this, toothDesignItem);
    });
  }
  /**
   * 清除占用内存
   */
  destroyed() {
    this.scene.traverse(item => {
      disposeChild(item);
    });
    THREE.Cache.clear();
    this.scene.clear();
    this.renderer.dispose();
    this.renderer.forceContextLoss();
    cancelAnimationFrame(this.timer);
  }
}
