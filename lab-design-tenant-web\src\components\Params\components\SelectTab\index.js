import { addResizeListener , removeResizeListener} from '../../utils/resize-event';

export default {
  data(){
    return {
      tabBoxNavOffset: 0,
      nextEnd: false,
      tabBoxWidth: 0,   // tab 可视化区域总长度
      tabBoxNavWidth: 0, // tab菜单的总长度
    }
  },
  computed: {
    tabBoxNavStyle() {
      return {
        transform: `translateX(-${this.tabBoxNavOffset}px)`
      };
    },
    prevEnd() {
      return this.tabBoxNavOffset === 0;
    },
  },
  mounted() {
    addResizeListener(this.$el, this.update);
  },
  methods: {
    scrollPrev() {
      const containerSize = this.$refs.tabBox['offsetWidth'];
      const currentOffset = this.tabBoxNavOffset;

      if (!currentOffset) return;

      this.nextEnd = false; // 前移，next肯定是false

      let newOffset = currentOffset > containerSize
        ? currentOffset - containerSize
        : 0;
      
      this.tabBoxNavOffset = newOffset;
    },
    scrollNext() {
      const tabBoxNavSize = this.$refs.tabBoxNav['offsetWidth'];
      const containerSize = this.$refs.tabBox['offsetWidth'];
      const currentOffset = this.tabBoxNavOffset;

      if(currentOffset + (containerSize * 2) >= tabBoxNavSize) { // tabBox的可视范围*2 + 当前移动范围 大于等于 tabBoxNavBar的总长度时，右边到了尽头
        this.nextEnd = true;
      }else {
        this.nextEnd = false;
      }
      //console.log(containerSize, currentOffset, tabBoxNavSize - currentOffset);
      
      if (tabBoxNavSize - currentOffset <= containerSize) return;

      let newOffset = tabBoxNavSize - currentOffset > containerSize * 2
        ? currentOffset + containerSize
        : (tabBoxNavSize - containerSize);

      this.tabBoxNavOffset = newOffset;
    },

    update() {
      this.tabBoxNavOffset = 0; // 边框宽度发生变化，offset重置为零
      const tabBoxWidth = this.$refs.tabBox['offsetWidth'];
      const tabBoxNavWidth = this.$refs.tabBoxNav['offsetWidth'];

      if(tabBoxWidth >= tabBoxNavWidth) {
        this.nextEnd = true;
      }else {
        this.nextEnd = false;
      }
    }

  },
  beforeDestroy() {
    removeResizeListener(this.$el, this.update)
  },
}