<template>
<div class="hg-dialog-box" v-if="show">
    <div class="dialog">
      <div class="dialog-header">
        <span class="dialog-header-text">{{ title }}</span>
        <i class="iconfont icon-close-mix iconfont-24" @click="close"></i>
      </div>
      <div class="dialog-body" id="dialogMsg">
        {{ msg }}
      </div>
      <div class="btn-group">
        <VueButton
          v-if="showCancelButton"
          class="dialog-cancel"
          width="104"
          sizes="big"
          @click.native="cancel"
        >
          {{ cancelBtnText }}
        </VueButton>
        <VueButton
          v-if="showConfirmButton"
          ref="dialogConfirm"
          class="dialog-confirm"
          width="104"
          type="primary"
          sizes="big"
          @click.native="ensure"
        >
          {{ ensureBtnText }}
        </VueButton>
        <!-- <button class="dialog-cancel" @click="cancel" v-if="showCancelButton">{{ cancelBtnText }}</button>
        <button class="dialog-confirm" ref="dialogConfirm" @click="ensure" v-if="showConfirmButton">{{ ensureBtnText }}</button> -->
      </div>
    </div>
  </div>
</template>

<script>

  export default {
    name: "Main",
    data(){
      return {
        show: false,
        title: '',
        msg: '',
        showConfirmButton: true,
        showCancelButton: true,
        confirmAction: '',
        cancelAction: '',
        cancelBtnText: '',
        ensureBtnText: '',
        updateShow: true,
        init: '',
      }
    },
    watch: {
      show(val) {
        if (val) {
          this.init();
        }
      },
    },
    methods:{
      // 确定按钮事件
      ensure() {
         // 关闭对话框
        this.updateShow ? this.show = false : '';
        // 执行点击确认后的回调函数
        this.confirmAction();
      },
      // 取消按钮事件
      cancel() {
        this.show = false;
        // 执行点击取消后的回调函数
        this.cancelAction();

      },
      // 右上角X号关闭弹窗
      close() {
        this.show = false;
        this.cancelAction();
      },
    }
  }
</script>

<style lang="scss" scoped>
.hg-dialog-box {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(18,18,19, 0.8);
  text-align: center;
  &::after {
    content: "";
    width: 0px;
    height: 100%;
    display: inline-block;
    vertical-align: middle;
  }
  .dialog {
    display: inline-block;
    vertical-align: middle;
    background-color: $hg-main-black;
    width: 520px;
    .dialog-header {
      position: relative;
      height: $hg-height-60;
      line-height: $hg-height-60;
      border-bottom: 1px solid $hg-border-color;
      box-sizing: border-box;
      padding: 0 24px;
      text-align: left;
      display: flex;
      align-items: center;
      .dialog-header-text {
        font-weight: bold;
        color: $hg-primary-fontcolor;
        font-size: $hg-medium-fontsize;
      }
      .iconfont {
        margin-left: auto;
      }
    }
    .dialog-body {
      font-size: $hg-normal-fontsize;
      box-sizing: border-box;
      padding: 24px;
      min-height: $hg-height-60;
      line-height: 22px;
      color: $hg-secondary-fontcolor;
      overflow-wrap: break-word;
      text-align: left;
    }
    .btn-group {
      display: flex;
      flex-direction: row-reverse;
      padding-bottom: 24px;
      .dialog-cancel, .dialog-confirm {
        margin-right: 24px;
      }
    }
  }
}
</style>


