import { toRawType } from './type'

// TODO: 增加循环引用的跳出
// 深度比较
export function deepCompare(left, right) {
  function _deepCompare(left, right) {
    if (left === right) {
      return true
    }

    const leftType = toRawType(left)
    const rightType = toRawType(right)

    if (leftType !== rightType) {
      return false
    }

    if (leftType !== 'Object' && leftType !== 'Array') {
      return left === right
    }

    if (Object.keys(left).length !== Object.keys(right).length) {
      return false
    }

    const caches = Object.create(null)

    for (const key in left) {
      const leftItem = left[key]
      const rightItem = right[key]

      if (!deepCompare(leftItem, rightItem)) {
        return false
      } else {
        caches[key] = true
      }
    }

    for (const key in right) {
      if (caches[key]) {
        continue
      }

      const leftItem = left[key]
      const rightItem = right[key]

      if (!deepCompare(leftItem, rightItem)) {
        return false
      }
    }

    return true
  }

  return _deepCompare(left, right)
}

export function findIndexByDeepCompare(list, target) {
  for (let i = 0, length = list.length; i < length; i++) {
    const item = list[i]
    if (deepCompare(item, target)) {
      return i
    }
  }

  return -1
}

// TODO: 深度对比逻辑
