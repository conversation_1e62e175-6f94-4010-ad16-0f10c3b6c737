import { hasOwn } from './other'

// TODO: 增加return机制
export function traverseProperties(object, cb, rootKey) {
  if (typeof object !== 'object') {
    return
  }

  for (const key in object) {
    if (hasOwn(object, key)) {
      const element = object[key]
      const newRootKey = rootKey ? rootKey + '.' + key : key
      cb(newRootKey, element)
      traverseProperties(element, cb, newRootKey)
    }
  }
}
