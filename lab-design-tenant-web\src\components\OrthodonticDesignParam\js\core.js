import * as THREE from 'three'

import {
  extractProps,
} from '@/components/OrthodonticDesignParam/helpers/utils/index'

import {
  disposeObject,
  setGroupCentre,
} from '@/components/OrthodonticDesignParam/helpers/three/utils/index'

import Eventer from '@/components/OrthodonticDesignParam/helpers/class/Eventer/index'

export default class Feature extends Eventer {
  constructor(properties, userData) {
    super()

    this.properties = this.defineProperties()

    this._properties = properties
    extractProps(this.properties, properties, true)

    this.object = this.defineObject()

    if (userData) {
      this.userData = userData
    }

    this.created()
  }

  defineProperties() {
    return Object.create(null)
  }

  defineObject() {
    return new THREE.Object3D()
  }

  created() {
  }

  destroy() {
    if (this.__dispose__) {
      return
    }

    this.properties = null
    this.object && disposeObject(this.object)

    this._properties = null

    this.__dispose__ = true
  }

  setProperties(properties) {
    this._properties = properties
    extractProps(this.properties, properties, true)
    this.refresh()
  }

  refresh() {

  }

  setCentre(position) {
    setGroupCentre(this.object, position)
  }
}
