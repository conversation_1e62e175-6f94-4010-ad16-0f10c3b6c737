import { getI18nDict } from '@/api/common';
import <PERSON><PERSON> from 'dexie';
import i18n from '@/public/i18n';
import { getType } from "@/public/utils";
import store from '@/store';

const setDict = (dictList) => {
  console.log('dictList: ', dictList);
  const otherModules = ['design_client_order_status', 'design_software', 'design_sync', 'design_tenant_order_status'];
  let apiZh = {
    apiCommon: {}
  };
  let apiEn = {
    apiCommon: {}
  };
  otherModules.forEach(other => {
    if (dictList.some(item => item.module === other)) {
      const otherListItem = dictList.filter(item => item.module === other)
      console.log('otherListItem: ', otherListItem);
      otherListItem.forEach(otherItem => {
        if (!apiZh[other]) {
          apiZh[other] = {}
        }
        if (!apiEn[other]) {
          apiEn[other] = {}
        }
        apiZh[other][otherItem.code] = otherItem.lang['zh-CN'];
        apiEn[other][otherItem.code] = otherItem.lang['en-US'];
      })
      dictList = dictList.filter(item => item.module !== other)
    }
  })
  dictList.forEach((item) => {
    if (item.code.includes('.')) {
      const keys = item.code.split('.'); // 根据 '.' 分割字符串
      let currentZh = apiZh.apiCommon
      let currentEn = apiEn.apiCommon
      // 遍历每个键，并创建嵌套结构
      keys.forEach((key, index) => {
        // 如果是最后一个键，赋值为一个空对象或任何需要的值 
        if (index === keys.length - 1) {
          currentZh[key] = item.lang['zh-CN']; // 创建最终的对象
          currentEn[key] = item.lang['en-US']; // 创建最终的对象
        } else {
          if (getType(currentZh[key]) !== 'Object') {
            const zhContent = currentZh[key]
            if (zhContent) {
              currentZh[key] = {
                default: zhContent
              }
            } else {
              currentZh[key] = {}
            }
          }
          if (getType(currentEn[key]) !== 'Object') {
            const zhContent = currentEn[key]
            if (zhContent) {
              currentEn[key] = {
                default: zhContent
              }
            } else {
              currentEn[key] = {}
            }
          }
        }
        currentZh = currentZh[key]; // 移动到下一个嵌套级别
        currentEn = currentEn[key]; // 移动到下一个嵌套级别
      });
    } else {
      if (getType(apiZh['apiCommon'][item.code]) === 'Object') {
        apiZh['apiCommon'][item.code]['default'] = item.lang['zh-CN'];
      } else {
        apiZh['apiCommon'][item.code] = item.lang['zh-CN'];
      }

      if (getType(apiEn['apiCommon'][item.code]) === 'Object') {
        apiEn['apiCommon'][item.code]['default'] = item.lang['en-US'];
      } else {
        apiEn['apiCommon'][item.code] = item.lang['en-US'];
      }
    }
  });
  let newApi = { apiZh, apiEn };
  console.log('newApi: ', newApi);
  let arr = ['zh', 'en'];
  let obj = { 'zh': 'apiZh', 'en': 'apiEn' };
  arr.forEach((item) => {
    i18n.mergeLocaleMessage(item, newApi[obj[item]]);
  });
};

const db = new Dexie('database');
db.version(2).stores({
  i18nDictList: 'id,value',
});

const initializeI18n = () => {
  return new Promise((resolve, reject) => {
    db.on('ready', async () => {
      try {
        const has = await db.i18nDictList.get({ id: 'i18n' });
        const isSetI18n = store.getters.isSetI18n;
        console.log('isSetI18n: ', isSetI18n);
        if (has) {
          if (!isSetI18n) {
            setDict(has['value']);
            store.commit('CHANGE_ISSETI18N', true);
          }
          resolve(); // 数据已设置，成功
        } else {
          const res = await getI18nDict();
          if (res.code === 200) {
            let dictList = res.data;
            setDict(dictList);
            resolve(); // 数据已设置，成功
          } else {
            reject(new Error('处理错误'));
          }
        }
      } catch (error) {
        reject(error); // 捕获并抛出错误
      }
    });
    db.open().catch(reject); // 打开数据库时捕获错误
  });
};

// 导出初始化函数
export { initializeI18n };