<template>
  <div v-show="show===1" class="design-service-box">
    <div class="function-box">
      <div class="title">
        <!-- {{ $t('customer.functionSetting') }} -->
        <span>{{$t('customer.basicconfig')}}</span>
        <div class="btn-list" v-permission="['editDesignBasicConfig', 'delete']">
          <el-button v-show="basicLeft" plain type="primary" @click="cancelEditbasicLeft">{{$t('personal.cancle')}}</el-button>
          <el-button v-show="basicLeft" type="primary" @click="saveData">{{$t('personal.save')}}</el-button>
          <el-button v-show="!basicLeft" type="primary" @click="editBasicLeft">{{$t('customer.edit')}}</el-button>
        </div>
      </div>
      <div class="content">
        <p class="customer-name"><span>{{ $t('customer.customerName') }}</span><span>{{ customer.orgName }}</span></p>
        <div class="form-item">
          <div class="quick-item">
            <span>{{ $t('customer.quickCreate') }}</span>
            <VueSwitch :value="isQuick" :disable="!basicLeft" @change="changeQuickOrder" />
          </div>
          <div class="item item-design">
            <div class="design-group-label">{{ $t('customer.bindDesignGroup') }}</div>
            <!-- 编辑状态下 -->
            <div v-if="basicLeft" class="select">
              <VueCascader
                :value="designGroups"
                :options="allDesignGroups"
                :collapse-tags="true"
                :props-obj="propsObj"
                @change="selectDesignGroups"
                @removeTag="removeBindDesign"
              />
            </div>
            <!-- 非编辑状态下 -->
            <Tooltip v-else :content="designGroupsName.join('、')">
              <p class="name-list">{{ designGroupsName.join('、') }}</p>
            </Tooltip>
          </div>

          <!-- 加急权限 -->
          <div class="check-list">
            <p class="design-group-label">{{$t('customer.rush')}}</p>
            <div class="box-list">
              <div v-for="(time, index) in timeArr" :key="index" class="checkbox-item">
                <el-checkbox :checked="time.isChecked" :disabled="time.urgentCode == 24 || !basicLeft" @change="selectTime(time)">{{ $t('customer.urgentTime', {'num': time.urgentCode}) }}</el-checkbox>
              </div>
            </div>
          </div>

          <!-- 不计数的设计品类 -->
          <div class="no-design-list" v-permission="['materialFilter', 'delete']">
            <p class="design-group-label">{{$t('customer.categories')}}
              <el-tooltip class="item" effect="dark" :content="$t('customer.automatically')" placement="top">
                <span class="el-icon-info tips"></span>
              </el-tooltip>
              <span v-if="basicLeft" @click="openfilterDialog()" class="el-icon-edit edit-icon"></span>
            </p>
            <div class="filter-design-list" v-loading="designFilterLoading">
              <el-tree :data="localDesignData" default-expand-all :props="{children: 'children', label: language == 'zh' ? 'cnName' : 'enName'}"></el-tree>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="price-box">
      <div class="price-header">
        <p class="title" v-permission="['isTaxes', 'delete']">{{$t('customer.priceconfig')}}：</p>
        <div class="check-box" v-permission="['isTaxes', 'delete']">
          <span>{{$t('customer.notTax')}}</span>
          <div class="checkbox-item">
            <el-switch v-model="isTax" active-color="#3760EA" inactive-color="#61646D" @change="changeTax"></el-switch>
          </div>
        </div>
        <div class="search-list">
          <Input
            :placeholder="placeholder ? placeholder : $t('common.searchTip')"
            padding-left="24px"
            padding-right="24px"
            size="normal"
            prefix-icon="icon-search"
            :input-content="inputContent"
            @blurInput="enterInput"
          />
          <div class="btn-list">
            <!-- <el-button v-show="basicRight" plain type="primary" @click="cancelBasicRight">{{$t('personal.cancle')}}</el-button>
            <el-button v-show="basicRight" type="primary" @click="saveTablePrice">{{$t('personal.save')}}</el-button> -->
            <el-button v-permission="['syncCrmPrice', 'delete']" type="primary" @click="pullpriceInCrm">{{$t('customer.crm')}}</el-button>
            <el-button v-permission="['editDesignPriceConfig', 'delete']" type="primary" @click="openUpload">{{$t('customer.uploadPrice')}}</el-button>
            <!-- <el-button v-show="!basicRight" type="primary" @click="editBasicRight">{{$t('customer.edit')}}</el-button> -->
          </div>
        </div>
      </div>
      <div class="price-search">
        <!-- <Input
          :placeholder="placeholder ? placeholder : $t('common.searchTip')"
          padding-left="24px"
          padding-right="24px"
          size="normal"
          prefix-icon="icon-search"
          :input-content="inputContent"
          @blurInput="enterInput"
        /> -->
        <!-- <VueButton v-permission="['editCustomer', 'disabled']" class="down-btn" :width="180" :disabled="false" @click.native="downExport">{{ $t('customer.downloadPrice') }}</VueButton>
        <el-upload
          class="batch-gift"
          action="#"
          accept=".xls, .xlsx"
          :auto-upload="false"
          :show-file-list="false"
          :limit="1"
          :on-change="uploadChange"
          :file-list="fileList"
        >
          <VueButton
            v-permission="['editCustomer', 'disabled']"
            type="primary"
            sizes="big"
            class="import-btn"
            :width="110"
            :disabled="false"
          >
            {{ $t('customer.importPrice') }}</VueButton>
        </el-upload> -->
      </div>

      <plane>
        <div class="data-display">
          <Table
            class="set-price-table"
            :table-style="{ hoverBackground: '#2D2F33' }"
            :keys="tableKeys"
            :table-data="tableData"
          >
            <template slot="number" slot-scope="scope">
              <span>{{ scope.index +1 + pageSize*pageNo - pageSize || '--' }}</span>
            </template>
            <template slot="designName" slot-scope="scope">
              <span :title="$i18n.locale == 'zh' ? scope.data.designName : scope.data.designEnName">{{ $i18n.locale == 'zh' ? scope.data.designName : scope.data.designEnName || '--' }}</span>
            </template>
            <template slot="chargeUnit" slot-scope="scope">
              <span>{{ $i18n.locale == 'zh' ? scope.data.chargeUnit && scope.data.chargeUnit.cn : scope.data.chargeUnit && scope.data.chargeUnit.en || '--' }}</span>
            </template>
            <template slot="expeditedTime" slot-scope="scope">
              <div class="expedited-time">
                <span v-for="(val, index) in scope.data.urgentInfos" :key="index" class="expedited-item">{{ `${val.urgentCode} ${$t('customer.hours')}` || '--' }}</span>
              </div>
            </template>
            <template slot="designCode" slot-scope="scope">
              <div class="expedited-time">
                <span v-for="(val, index) in scope.data.urgentInfos" :key="index" class="expedited-item">{{ val.erpSkuCode || '--' }}</span>
              </div>
            </template>
            <template slot="price" slot-scope="scope">
              <div class="expedited-time">
                <!-- <span v-for="(val, index) in scope.data.urgentInfos" :key="index" class="expedited-item">{{ val.price }}</span> -->
                <!-- @change="changePrice($event, index, scope, val.urgentCode)" -->
                <div class="price-input-box" v-for="(val, index) in scope.data.urgentInfos" :key="index">
                  <el-input
                    :key="index"
                    v-model="tableDataForPrice[scope.index].urgentInfos[index].price"
                    type="number"
                    :disabled="!basicRight"
                    :class="['price-input', !basicRight ? 'dis-price-input' : '' ]"
                    @change="changePrice($event, val, scope.data)"
                  />
                  <!-- <el-input :key="index+'key'" :value="'--'" class="dis-price-input" disabled type="text" /> -->
                </div>
                <!-- <div v-for="(val, index) in timeArr" :key="index">
                  <el-input
                    v-if="val.isChecked"
                    :key="index"
                    v-model="tableDataForPrice[scope.index].urgentInfos[index].price"
                    type="number"
                    :disabled="!editing"
                    :class="['price-input', !editing ? 'dis-price-input' : '' ]"
                    @change="changePrice($event, index, scope, val.urgentCode)"
                  />
                  <el-input v-if="!val.isChecked" :key="index+'key'" :value="'--'" class="dis-price-input" disabled type="text" />
                </div> -->
              </div>
            </template>
            <!-- <template v-for="(val, index) in timeArr" :slot="val.urgentCode + 'urgentTime'" slot-scope="scope"> -->
            <!-- 控制编辑和非编辑状态下的样式 -->
            <!-- <el-input v-if="val.isChecked" :key="index" v-model="tableDataForPrice[scope.index].urgentInfos[index].price" type="number" :disabled="!editing" :class="['price-input', !editing ? 'dis-price-input' : '' ]" @change="changePrice($event, index, scope, val.urgentCode)" />
              <el-input v-else :key="index" :value="'--'" class="dis-price-input" disabled type="text" />
            </template> -->
          </Table>
        </div>
      </plane>
      <Pagination :total-pages="totalPages" :total="totalSize" :page-size="pageSize" :page-no="pageNo" @changePageSize="changePageSize" @changePageNo="changePageNo" />
    </div>

    <uploadbox :dialogVisible.sync="dialogVisible" @uploadSuccess="uploadSuccess" @downSystemBill="downSystemBill" :acceptType="'.xlsx'" :orgCode="this.customer.orgCode"></uploadbox>

    <el-dialog
      custom-class="price-result-dialog"
      append-to-body
      top="8vh"
      width="720px"
      :title="$t('customer.importPrice')"
      :visible.sync="isShow"
      :close-on-click-modal="false"
      @close="onClose"
    >
      <plane>
        <div class="data-display">
          <Table
            class="result-price-table"
            :table-style="{ height: 'calc(100vh - 566px)', hoverBackground: '#2D2F33' }"
            :keys="resultKeys"
            :table-data="resultData"
          >
            <template slot="rowNumber" slot-scope="scope">
              <span> {{ scope.data.rowNumber || '--' }}</span>
            </template>
            <template slot="designName" slot-scope="scope">
              <span>{{ language === 'zh' ? scope.data.designName : scope.data.designEnName || '--' }}</span>
            </template>
            <template slot="sku" slot-scope="scope">
              <span> {{ scope.data.sku || '--' }}</span>
            </template>
            <template slot="price" slot-scope="scope">
              <span> {{ handlePrice(scope.data.price) || '--' }}</span>
            </template>
            <template slot="result" slot-scope="scope">
              <span v-if="language === 'zh'" :class="scope.data.result !== '成功' ? 'failColor':''"> {{ scope.data.result }}</span>
              <span v-else-if="language === 'en'" :class="scope.data.result !== '成功' ? 'failColor':''"> {{ scope.data.result === '成功' ? 'successful' : 'failed' }}</span>
            </template>
          </Table>
        </div>
      </plane>
      <div class="result-bottom">
        <span v-if="language === 'zh'">全部文件{{ resultData.data.length }}个，成功导入<span style="color:#72D143; font-weight: 700;">{{ successNum }}个</span>，失败<span style="color: #DB4848; font-weight: 700;">{{ failNum }}个</span></span>
        <span v-else-if="language === 'en'">Total&ensp;{{ resultData.data.length }}&ensp;files, imported&ensp;<span style="color:#72D143; font-weight: 700;">{{ successNum }}&ensp;files,</span>failed&ensp;<span style="color: #DB4848; font-weight: 700;">{{ failNum }}&ensp;files.</span></span>
        <VueButton
          v-permission="['editDesignPriceConfig']"
          sizes="big"
          class="result-bottom-btn"
          :width="110"
          :disabled="false"
          @click.native="onClose"
        >{{ $t('customer.cancle') }}</VueButton>
        <VueButton
          v-permission="['editDesignPriceConfig']"
          type="primary"
          sizes="big"
          class="result-bottom-btn"
          :width="110"
          :disabled="false"
          @click.native="onClose"
        >{{ $t('customer.confrim') }}</VueButton>
      </div>
    </el-dialog>
    <pullPriceInCRM :drawer.sync="drawer" @submitPrice="submitPrice"></pullPriceInCRM>

    <!-- 设计品类过滤弹窗 -->
    <el-dialog :title="$t('customer.categoryfilter')" width="600px" :visible.sync="designVisible" custom-class="design-dialog">
      <div class="title">{{$t('customer.designstatistics')}}</div>
      <div class="design-tree" v-if="designVisible">
        <el-tree
          :data="designData"
          show-checkbox
          node-key="designCode"
          default-expand-all
          :default-checked-keys="selectTreeCode"
          @check="getCheckedKeysList"
          :props="{children: 'children', label: language == 'zh' ? 'cnName' : 'enName'}">
        </el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <span class="footer-tips" style="float: left;">{{$t('customer.selected', {'num': selectTreeCode.length})}}</span>
        <el-button class="cancel-btn" @click="cancelSubmit()">{{$t('personal.cancle')}}</el-button>
        <el-button type="primary" @click="dialogSubmit">{{$t('personal.confrim')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/func-components/Pagination'
import Plane from '@/components/func-components/Plane.vue'
import Table from '@/components/func-components/TableCommon.vue'
import { getDesignPriceList, getQuickOrder, setQuickOrder, getRelateDesignGroups, getAllDesignGroups, bindDesignGroups, getOrgUrgentList,
  editOrgUrgentTime, editPrice, getDesignPriceImport, writeTax, getAccountByOrgCode, getDesignAll, setDesignFilter, getDesignFilter } from '@/api/customer'
import VueSwitch from '@/components/func-components/VueSwitch.vue'
import VueCascader from '@/components/func-components/VueCascader'
import Tooltip from '@/components/func-components/Tooltip'
import Input from '@/components/func-components/Input.vue'
import { directDown } from './file.js'
import { baseUrl } from '@/api/baseurl.config'
import { getStore } from '@/assets/script/utils.js'
import uploadbox from "./uploadbox.vue"
import pullPriceInCRM from "./pullPriceInCRM"

export default {
  name: 'DesignService',
  components: {
    Pagination,
    Plane,
    Table,
    VueSwitch,
    VueCascader,
    Tooltip,
    Input,
    uploadbox,
    pullPriceInCRM
  },
  props: {
    show: {
      type: Number,
      default: 1
    },
    customer: {
      type: Object,
      default: () => {
        return {}
      }
    },
    editing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 价格清单列表
      tableKeys: ['number', 'designName', 'chargeUnit', 'expeditedTime', 'designCode', 'price'],
      tableData: {
        title: [
          this.$t('customer.No'),
          this.$t('customer.designType'),
          this.$t('customer.unit'),
          this.$t('customer.urgent'),
          this.$t('customer.materialID'),
          this.$t('customer.price')
        ],
        data: []
      },
      tableDataForPrice: [], // 在table里编辑单个单元格会导致输入一个字符就更新一次tableData，因此另外定义一个tableDataForPrice为编辑价格所用
      totalSize: 0, // 某组织下的所有人员数
      totalPages: 1, // 总页数
      pageSize: 10, // 默认每页10条
      pageNo: 1, // 默认第一页
      timeArr: [], // 时间类型
      selectTimeArr: [], // 选中的时间类型
      curInputPrice: 0, // 当前正在编辑输入的价格
      editPriceArr: [], // 编辑过的价格信息
      // 设计服务功能配置
      isQuick: false, // 是否开启极速下单，默认为关
      designGroups: [], // 绑定的设计师小组
      designGroupsName: [], // 绑定的设计师小组名称
      allDesignGroups: [], // 所有设计师小组
      noChildGroups: [], // 所有设计师小组（无上下级结构）
      inputContent: null, // 搜索关键字
      isTax: true, // 是否含税
      placeholder: this.$t('customer.enterTip'),
      fileList: [],
      loading: false,
      resultList: [],
      isShow: false,
      propsObj: {
        value: 'orgCode',
        label: 'orgName',
        children: 'sonList',
        emitPath: false,
        checkStrictly: true,
        multiple: true
      },
      resultHeaderData: [],
      resultData: {
        title: [],
        data: []
      },
      resultTitle: [
        this.$t('customer.No'),
        this.$t('customer.application'),
        this.$t('customer.materialID'),
        this.$t('customer.price'),
        this.$t('customer.importedStatus')
      ],
      resultKeys: [
        'rowNumber',
        'designName',
        'sku',
        'price',
        'result'
      ],
      successNum: 0,
      failNum: 0,
      basicLeft: false, //左边栏编辑
      basicRight: false, // 右边栏编辑
      dialogVisible: false,
      drawer: false,

      designVisible: false,
      designData: [],// 设计品类
      oneDesignList: [], // 设计品类一维数组
      selectTreeCode: [], //选中的code
      localDesignData: [], // 过滤品类回显
      designFilterLoading: true,
      //一二级永远不包含在传过去的数组里面
      filterCode: [21000, 21100, 21200, 21300, 21400, 21500, 22000, 22600, 22700, 22100, 22200, 22300, 22400, 22500, 23000, 23100, 23200, 23300, 23400, 24000, 24100,24300,24400,24500,25001],
    }
  },
  computed: {
    language() {
      return getStore('lang')
    },
  },
  watch: {
    async show(val) {
      if (val === 1) {
        await this.getOrgUrgentListFunc()
        Promise.allSettled([this.getAllDesignGroupsFunc(), this.getQuickOrderFunc(), this.getPriceListFunc(), this.getAccountByOrgCode()])
      } else {
        this.reset()
      }
    },
    '$i18n.locale'(val) {
      this.getPriceListFunc()
    }
  },
  async mounted() {
    await this.getOrgUrgentListFunc()
    Promise.allSettled([this.getDesignAll(), this.getAllDesignGroupsFunc(), this.getQuickOrderFunc(), this.getPriceListFunc(1), this.getAccountByOrgCode()])
  },
  methods: {
    // 左边栏的编辑
    editBasicLeft(){
      this.basicLeft = true;
    },
    cancelEditbasicLeft(){
      this.basicLeft = false;
      // 取消的时候将所有值回退
      // this.isQuick = this.oldBasicLeft.isQuick;
      // this.designGroups = this.oldBasicLeft.designGroups;
      this.getOrgUrgentListFunc();
      this.getQuickOrderFunc();
      this.getRelateDesignGroupsFunc();
      this.getDesignFilter();
    },
    // 右边栏编辑
    editBasicRight(){
      this.basicRight = true;
    },
    cancelBasicRight(){
      this.basicRight = false;
      // this.getPriceListFunc();
    },
    reset() {
      this.isQuick = false
      this.timeArr = []
    },
    changePageSize(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getPriceListFunc()
    },
    changePageNo(val) {
      this.pageNo = val
      this.getPriceListFunc()
    },
    // 获取当前客户是否开启了极速下单
    getQuickOrderFunc() {
      getQuickOrder(this.customer.orgCode).then((res) => {
        if (res.code === 200) {
          this.isQuick = res.data
        }
      })
    },
    // 切换极速下单状态
    changeQuickOrder(result) {
      this.isQuick = result;
    },
    // 设置当前客户开启/关闭极速下单
    async setQuickOrderFunc() {
      await setQuickOrder({ orgCode: this.customer.orgCode, isEnable: this.isQuick })
    },
    // 获取当前客户关联的设计师小组
    getRelateDesignGroupsFunc() {
      getRelateDesignGroups(this.customer.orgCode).then((res) => {
        this.designGroups = res.data != null && res.data.length ? res.data : []
        this.getSelectedDesignGroupName(this.designGroups)
      })
    },
    mergeArr(arr1, arr2) {
      arr2.forEach((item) => {
        arr1 = arr1.concat(item)
        if (item.sonList != null && item.sonList.length) {
          arr1 = this.mergeArr(arr1, item.sonList)
        }
      })
      return arr1
    },
    // 获取选中的设计组名称
    getSelectedDesignGroupName(designGroups) {
      this.designGroupsName = []
      if (designGroups.length) {
        designGroups.forEach((item) => {
          this.noChildGroups.forEach(value => {
            if (value.orgCode === item) {
              this.designGroupsName.push(value.orgName)
            }
          })
        })
      } else {
        this.designGroupsName.push('--')
      }
    },
    // 获取所有可选设计组层级
    getAllDesignGroupsFunc() {
      getAllDesignGroups().then((res) => {
        this.allDesignGroups = res.data != null && res.data.length ? res.data : []
        this.noChildGroups = this.mergeArr([], this.allDesignGroups)
        this.getRelateDesignGroupsFunc()
      })
    },
    // 选择绑定设计组
    selectDesignGroups(value) {
      this.designGroups = value
    },
    // 移除绑定的设计组
    removeBindDesign(value) {
      this.$nextTick(() => {
        this.designGroups = this.designGroups.filter(item => item !== value)
      })
    },
    // 设置绑定设计组
    async bindDesignGroupsFunc() {
      await bindDesignGroups({
        deptCodes: this.designGroups,
        orgCode: this.customer.orgCode
      }).then((res) => {
        if (res.code === 200) {
          this.getSelectedDesignGroupName(this.designGroups)
        }
      })
    },
    // 获取加急时间列表
    async getOrgUrgentListFunc() {
      try {
        this.timeArr = []
        const res = await getOrgUrgentList(this.customer.orgCode)
        this.timeArr = res.data != null && res.data.length ? res.data : []
        this.selectTimeArr = []
        this.timeArr.forEach(item => {
          if (item.isChecked) {
            this.selectTimeArr.push(item.urgentCode)
          }
        })
      } catch (error) {
      }
    },
    // 勾选当前编辑的加急时间
    selectTime(time) {
      if (this.selectTimeArr.includes(time.urgentCode)) {
        time.isChecked = false
        this.selectTimeArr = this.selectTimeArr.filter(item => item !== time.urgentCode)
      } else {
        time.isChecked = true
        this.selectTimeArr.push(time.urgentCode)
      }
      // this.updatePriceCheckStatus(this.tableData.data, this.timeArr)
    },
    // 设置当前已选中的加急时间
    async setOrgUrgentTimeFunc() {
      await editOrgUrgentTime({
        orgCode: this.customer.orgCode,
        urgentCodes: this.selectTimeArr // this.selectTimeArr
      }).then((res) => {
        // 设置成功后的操作
        // this.getOrgUrgentListFunc()
      })
    },

    /**
     * 更新价格清单的加急时间和单价的选中状态
     */
    updatePriceCheckStatus(priceList, selectTimeArr) {
      console.log('selectTimeArr: ', selectTimeArr)
      if (priceList.length) {
        priceList.forEach(item => {
          if (item.urgentInfos && item.urgentInfos.length) {
            item.urgentInfos.forEach(urgent => {
              selectTimeArr.forEach(time => {
                if (urgent.urgentCode === time.urgentCode) {
                  urgent.isChecked = time.isChecked
                }
              })
            })
          }
        })
      }
    },

    // 获取价格清单
    getPriceListFunc(time) {
      getDesignPriceList({
        'orgCode': this.customer.orgCode,
        'pageNo': this.pageNo,
        'pageSize': this.pageSize,
        'keyword': this.inputContent
      }).then((res) => {
        if (res.code === 200) {
          this.totalSize = res.data ? res.data.totalSize : 0
          this.totalPages =  res.data ? res.data.totalPages : 0
          if (res.data.data != null && res.data.data.length) {
            this.tableData.data = res.data.data
            this.tableDataForPrice = JSON.parse(JSON.stringify(this.tableData.data))
            this.tableKeys = ['number', 'designName', 'chargeUnit', 'expeditedTime', 'designCode', 'price']
            this.tableData.title = [
              this.$t('customer.No'),
              this.$t('customer.designType'),
              this.$t('customer.unit'),
              this.$t('customer.urgent'),
              this.$t('customer.materialID'),
              this.$t('customer.price')
            ]
            // this.updatePriceCheckStatus(this.tableData.data, this.timeArr)
          } else {
            this.tableData.data = []
          }
        }
      })
    },
    // 修改价格（旧需求：根据ui要求做成一次编辑只编辑一个品类的一个加急时间的价格；20220829新需求：ui改成点击保存编辑多条价格信息）
    changePrice(price, value, row) {
      if (!price) {
        // if (this.tableDataForPrice[scope.index].urgentInfos[index]) {
        //   this.tableDataForPrice[scope.index].urgentInfos[index].price = 0
        //   value = 0
        // }
        price = 0
      } else if (price < 0) {
        // if (this.tableDataForPrice[scope.index].urgentInfos[index]) {
        //   this.tableDataForPrice[scope.index].urgentInfos[index].price = Math.abs(this.tableDataForPrice[scope.index].urgentInfos[index].price)
        //   value = Math.abs(value)
        // }
        price = Math.abs(price)
      }
      if (price > 99999.99) {
        price = 99999.99
      }
      if (parseFloat(price) !== parseInt(price)) {
        price = this.fomatFloat(price, 2)
        // if (this.tableDataForPrice[scope.index].urgentInfos[index]) {
        //   this.tableDataForPrice[scope.index].urgentInfos[index].price = value
        // }
      }
      if (this.editPriceArr.length) {
        const designCodeList = this.editPriceArr.map(item => item.designCode)
        if (designCodeList.includes(row.designCode)) {
          const editItem = this.editPriceArr.find(item => item.designCode === row.designCode && item.urgentCode === value.urgentCode)
          if (editItem) {
            editItem.price = Number(price)
          } else {
            this.editPriceArr.push({
              designCode: row.designCode,
              price: Number(price),
              urgentCode: value.urgentCode
            })
          }
        } else {
          this.editPriceArr.push({
            designCode: row.designCode,
            price: Number(price),
            urgentCode: value.urgentCode
          })
        }
      } else {
        this.editPriceArr.push({
          designCode: row.designCode,
          price: Number(price),
          urgentCode: value.urgentCode
        })
      }
      console.log('this.editPriceArr: ', this.editPriceArr)
    },
    handlePrice(num) {
      let resNum = 0
      if (parseFloat(num) !== parseInt(num)) {
        num = this.fomatFloat(num, 2)
        resNum = num
      } else {
        resNum = num
      }
      return resNum
    },
    // 编辑价格
    editPriceFunc() {
      editPrice({
        orgCode: this.customer.orgCode,
        priceInfoEditReqs: this.editPriceArr
      }).then((res) => {
        // 编辑成功后的操作
        this.cancelBasicRight();
      })
    },
    // 保存所有编辑过的数据
    async saveData() {
      // 1. 设置当前客户开启/关闭极速下单； 2. 设置绑定设计组； 3. 设置当前已选中的加急时间； 4.更新不计数设计品类
      Promise.allSettled([this.setQuickOrderFunc(), this.bindDesignGroupsFunc(), this.setOrgUrgentTimeFunc(), this.submitFilterDesign()]).then((results) =>{
        this.cancelEditbasicLeft();
      })
    },

    async saveTablePrice(){
      //4. 编辑价格, this.editPriceFunc()
      this.editPriceFunc()
    },

    // 获取是否已经修改过税
    async getAccountByOrgCode(){
      const { code, data } = await getAccountByOrgCode({orgCode: this.customer.orgCode})
      if(code == 200){
        if(data.isTax !== '' && data.isTax !== null  ){
          this.isTax = !data.isTax;
        } else {
          // 设置是否含税
          let arr = ['development', 'sit', 'uat', 'prod', 'cnprod'];
          if(arr.includes(process.env.NODE_ENV)){
            this.isTax = true;
          } else {
            this.isTax = false;
          }
        }
      }
    },

    // 修改是否含税
    changeTax(){
      let message = this.isTax ? this.$t('customer.noIncludesTax') : this.$t('customer.includesTax');
      this.$confirm(message, this.$t('customer.remainding'), {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: 'warning',
        }).then(async() => {
          let obj = {
            isTax: !this.isTax,
            orgCode: this.customer.orgCode
          }
          const { code, data } = await writeTax(obj)
        }).catch(() => {
          this.isTax = !this.isTax
        });
    },

    // 按回车查询列表
    enterInput(arg) {
      if (this.inputContent !== arg) {
        this.inputContent = arg
        this.getPriceListFunc()
      }
    },
    downExport() {
      const URL = `${baseUrl}/design-basic-service/tenant/price/v1/export`
      directDown(URL, this.language === 'zh' ? '导入价格模板.xlsx' : 'Price.xlsx')
    },
    onClose() {
      this.isShow = false;
      this.cancelBasicRight();
    },
    // 打开上传弹窗
    openUpload(){
      this.dialogVisible = true;
    },
    // 同步Crm
    pullpriceInCrm(){
      if(this.customer.isBackupCrmOrg == 0){
        this.$message.error(this.$t('customer.boundsCrm'))
        return
      }
      this.drawer = true;
    },
    submitPrice(){
      this.drawer = false;
      this.getPriceListFunc()
    },
    // 上传后的操作
    async uploadSuccess(res){
        // this.dialogVisible = false;
        // this.resultData.data = res.data
        // this.resultData.title = this.resultTitle
        this.getPriceListFunc()
        // this.getOrgUrgentListFunc()
        // this.numFunc(this.resultData.data)
        // this.isShow = true
    },
    // 下载系统账单
    downSystemBill(){
      this.downExport();
    },
    numFunc(list) {
      this.successNum = 0
      this.failNum = 0
      list.forEach((item) => {
        if (item.result === '成功') {
          this.successNum++
        } else if (item.result === '失败') {
          this.failNum++
        }
      })
    },
    cancleResultDialog() {
      this.isShow = false
    },
    // num为传入的值，n为保留的小数位
    fomatFloat(num, n) {
      var f = parseFloat(num)
      if (isNaN(f)) {
        return false
      }
      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n) // n 幂
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      // while (s.length <= rs + n) {
      //   s += '0'
      // }
      return s
    },
    // 打开dialog
    openfilterDialog(){
      this.designVisible = true;
    },
    getIsNode(treeArr, parentCode){
      let child = ''
      let nodeMap = new Map();
      treeArr.forEach((item) => {
        if(item.children && item.children.length > 0){
          // nodeMap.set(item.designCode, item);
          child = item.children.find((it) => {return it.designCode == parentCode})
        }
      })
      return child
    },
    getNewArray(arr, codeArr){
      let newDesignList = [];
      let TreeArr = new Map(arr.map(item => [item.designCode, item]))
      codeArr.forEach((item) => {

        // let tree = arr.find((it) => {return it.designCode == item})
        let tree = TreeArr.get(Number(item))
        if(tree.parentCode){
          let secondTree = {};
          // 这里找到二级，要判断二级是否已经存在，存在则加进二级children，而不是新建
          secondTree = this.getIsNode(newDesignList, tree.parentCode);
          // 特殊处理，在二级的类没有三级
          if([24200].includes(tree.designCode)){
            let firstTree = {};
            firstTree = newDesignList.find((oldfirst) => {return oldfirst.designCode == 24000})
            if(firstTree){
              firstTree['children'].push(tree)
            } else {
              firstTree['children'] = [tree]
              newDesignList.push(firstTree)
            }
          } else if(secondTree) {
            secondTree.children.push(tree)
          } else {
            secondTree = TreeArr.get(Number(tree.parentCode))
            secondTree['children'] = [tree];
          }

          if(secondTree && secondTree.parentCode){
            // console.log(secondTree.parentCode)
            // 这里找到一级，判断一级是否已经存在，存在则加进一级children，而不是新建
            let firstTree = {};
            firstTree = newDesignList.find((oldfirst) => {return oldfirst.designCode == secondTree.parentCode})
            if(firstTree){
              // 这里需要判断是否已经存在了相同的二级，存在不要push进去
              if(!firstTree.children.find((oldSe) => {return oldSe.designCode == secondTree.designCode})){
                firstTree['children'].push(secondTree)
              }
            } else {
              firstTree = arr.find((first) => {return first.designCode == secondTree.parentCode})
              // firstTree = TreeArr.get(Number(secondTree.parentCode))
              firstTree['children'] = [secondTree]
              newDesignList.push(firstTree)
            }
          }
        }
      })
      return newDesignList
    },
    // 获取当前过滤的品类回显
    async getDesignFilter(){
      this.designFilterLoading = true;
      let parame = {
        orgCode: this.customer.orgCode,
      }
      const { code, data } = await getDesignFilter(parame);
      if(data){
        this.localDesignData = this.getNewArray(this.oneDesignList, data.split(','));
        this.selectTreeCode = data.split(',');
        this.designFilterLoading = false;
      } else {
        this.localDesignData = [];
        this.selectTreeCode = [];
        this.designFilterLoading = false;
      }
    },

    // 获取所有的设计品类
    async getDesignAll(){
      //转一维数组
      const getOneDimensional = (dataList) => {
        let designList = [];

        dataList.forEach(node => {
          const { cnName, enName, iconUrl, designCode, children, level, parentCode, hasParas } = node;
          const item = { cnName, enName, iconUrl, designCode, level, parentCode, hasParas };
          designList.push(item);
          if(children && children.length > 0) {
            designList = designList.concat(getOneDimensional(children));
          }
        });
        return designList;
      };
      const { code, data } = await getDesignAll();
      if(code == 200){
        this.designData = data.slice(0, 4);
        this.oneDesignList = getOneDimensional(data)
        this.getDesignFilter()
      }
    },
    // 选中树获取的code
    getCheckedKeysList(node, nodeKey){
      // 将 a 转换为 Set
      const setA = new Set(this.filterCode);
      // 使用 filter 方法剔除 b 中存在于 a 的值
      const result = nodeKey.checkedKeys.filter(x => !setA.has(x));
      this.selectTreeCode = result
    },
    // 提交
    async submitFilterDesign(){
      let parame = {
        orgCode: this.customer.orgCode,
        designCodes: this.selectTreeCode
      }
      const { code, data } = await setDesignFilter(parame);
      if(code == 200){
        // 更新过滤品类
        this.localDesignData = this.getNewArray(this.oneDesignList, data.split(','));
      }
    },
    // 点击弹窗关闭弹窗更新，然后点击整体保存才更新
    dialogSubmit(){
      this.designVisible = false;
      // 更新过滤品类
      this.localDesignData = this.getNewArray(this.oneDesignList, this.selectTreeCode);
    },
    // 取消选择
    cancelSubmit(){
      this.getDesignFilter();
      this.designVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.design-service-box {
  display: flex;
  height: calc( 100% - 80px );
  border-radius: 4px;
  // background: $hg-main-black;
  @include bg-box-shadow;
  padding: 16px 16px 24px;
  box-sizing: border-box;
  .function-box {
    background: $hg-main-black;
    height: 100%;
    width: 360px;
    // padding: 24px;
    margin-right: 20px;
    margin-bottom: 24px;
    .title {
      display: flex;
      position: relative;
      align-items: center;
      height: 64px;
      color: #F3F5F7;
      font-size: 16px;
      font-weight: bold;
      padding: 12px 24px;
      border-bottom: 1px solid #3D4047;
      .el-button--primary.is-plain{
        background: transparent;
        color: #fff;
      }
      .btn-list{
        position: absolute;
        right: 24px;
        top: 12px;
        display: inline-block;
      }
    }
    .content {
      height: calc( 100% - 100px);
      padding: 24px;
      border-radius: 4px;
      overflow: auto;
      // background: $hg-hover-bg-color;
      padding: 24px;
      box-sizing: border-box;
      color: $hg-primary-fontcolor;
      .customer-name {
        height: 32px;
        border-bottom: 1px dotted #3D4047;
        span:nth-of-type(1) {
          margin-right: 5px;
        }
        span:nth-of-type(2) {
          font-weight: bold;
        }
      }
      .form-item {
        // display: flex;
        margin-top: 16px;
        height: 40px;
        .quick-item {
          height: 64px;
          padding-bottom: 12px;
          display: flex;
          align-items: center;
          span {
            margin-right: 16px;
          }
          .select {
            width: 320px;
          }
        }
        .item-design {
          .design-group-label {
            margin-bottom: 12px;
          }
          .tooltip-box {
            // width: 70%;
            // margin-top: 20px;
          }
          .name-list {
            min-height: 40px;
            width: 100%;
            background: #141519;
            // overflow: hidden;
            // text-overflow: ellipsis;
            // white-space: nowrap;
            padding: 10px;
            border-radius: 6px;
          }
        }
        .no-design-list{
          position: relative;
          margin-top: 20px;
          .tips{
            color: #5F8AFF
          }
          .edit-icon{
            position: absolute;
            right: 0;
            color: #F3F5F7;
            cursor: pointer;
          }
        }
        .check-list{
          margin-top: 20px;
          .box-list{
            background: #141519;
            margin-top: 12px;
            padding: 12px;
            border-radius: 6px;
          }
          .design-group-label {
            white-space: nowrap;
          }
          .checkbox-item {
            // margin-left: 32px;
            margin: 12px 0;
          }
          ::v-deep .el-checkbox {
            .el-checkbox__input {
              width: 16px;
              height: 16px;
              line-height: 14px;
              .el-checkbox__inner {
                width: 16px;
                height: 16px;
                background: transparent;
              }
            }
            .is-checked {
              .el-checkbox__inner {
                border: none;
                background: $hg-main-blue;
                &::after {
                  border-color: $hg-background-color !important;
                  border-width: 2.5px;
                  top: 2px;
                  left: 5px;
                }
              }
            }
            .el-checkbox__input.is-checked+.el-checkbox__label {
              color: $hg-primary-fontcolor;
            }
            .is-disabled {
              .el-checkbox__inner {
                border: 1px solid $hg-border-color;
                background: #AAADB3;
                &::after {
                  border-color: $hg-border-color !important;
                  top: 1px;
                }
              }
              &+span.el-checkbox__label {
                color: $hg-secondary-fontcolor;
              }
            }
          }
          .hour-24 {
            // margin-left: 32px;
            color: $hg-primary-fontcolor;
            p {
              display: inline-block;
              width: 16px;
              height: 16px;
              line-height: 13px;
              border: 1px solid $hg-border-color;
              border-radius: 2px;
              text-align: center;
              margin-right: 8px;
              box-sizing: border-box;
              span {
                display: inline-block;
                width: 8px;
                height: 8px;
                background: $hg-main-blue;
              }
            }
          }
          .hour-24[disabled] {
            span {
              background: #54565C3F;
            }
          }
        }
      }
    }
  }
  @media (max-width: 1400px) {
    .function-box{
      width: 360px;
    }
  }
  @media (min-width: 1400px) {
    .function-box{
      width: 480px;
    }
  }
  .price-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    background: $hg-main-black;
    // height: 100%;
    overflow: hidden;
    padding-bottom: 55px;
    box-sizing: border-box;
    .price-header {
      display: flex;
      position: relative;
      align-items: center;
      padding: 12px 24px;
      padding-bottom: 12px;
      border-bottom: 1px solid #3D4047;
      flex-wrap: wrap;
      .check-box{
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10px;
        span{
          color: #F3F5F7;
          margin-right: 10px;
        }
      }
      .el-button--primary.is-plain{
        background: transparent;
        color: #fff;
      }
      .title {
        color: #F3F5F7;
        font-size: 14px;
        // font-weight: bold;
        // margin-right: 12px;
      }
      .search-list{
        display: flex;
        flex-wrap: wrap;
        .component-input-box{
          width: 260px;
          margin-right: 240px;
        }
        .btn-list{
          position: absolute;
          min-width: 240px;
          right: 24px;
          top: 12px;
        }
      }
    }
    .price-search{
      display: flex;
      margin-top: 10px;
      margin-bottom: 16px;
      .down-btn{
        margin-left: 24px;
        padding: 0 10px;
        // width: 100px;
      }
      .import-btn{
        margin-left: 24px;

      }
    }
    ::v-deep .component-plane {
      // flex: 1;
      height: calc( 100% - 100px);
      overflow: auto;
      margin-bottom: 0;
      ::v-deep .content {
        padding-top: 0;
        height: 100%;
      }
    }
    ::v-deep .component-plane .table-body{
      height: 100%;
    }
    .data-display {
      height: 100%;
      background-color: $hg-main-black;
      overflow: hidden;
      .set-price-table {
        // margin-top: 8px;
        .wait-examine {
          color: $hg-warning-color;
        }
        .opened {
          color: $hg-main-blue;
        }
        .forbidden {
          color: $hg-error-color;
        }
        .expedited-time{
          display: flex;
          flex-direction: column;
          .expedited-item{
            padding-top: 24px;
          }
          .expedited-item:last-child{
            padding-bottom: 24px;
          }
          .price-input-box{
            margin: 5px 0;
          }
        }
        ::v-deep .dis-price-input {
          width: 50%;
          cursor: default;
          .el-input__inner {
            padding-left: 0;
            border: none;
            background: transparent;
            color: $hg-primary-fontcolor;
            cursor: default;
          }
        }
        ::v-deep .price-input {
          width: 50%;
          .el-input__inner {
            // padding-left: -10px !important;
          }
        }
      }
    }
  }
  .price-result-dialog{
    .el-dialog__header{
      background: #1D1D1F;
      border-radius: 4px;
      color: #E4E8F7;
      font-weight: 700;
      font-size: 16px;
    }
  }
}
</style>
<style lang="scss">
.price-result-dialog {
  width: 720px;

  .el-dialog__body{
    background: #1D1D1F;
    padding: 24px;
    .content{
      padding: 0;
    }
  }
  .el-dialog__header{
    border-bottom: 1px solid #38393D;
    background: #1D1D1F;

    font-size: 16px;
    color: #E4E8F7;
    font-weight: 700;
  }
  .failColor{
    color: #FF5A5A;
  }
  .result-bottom{
    display: flex;
    align-items: end;
    justify-content: end;
    margin-top: 24px;
    .result-bottom-btn{
      margin-left: 24px;
    }
  }
}

.design-dialog{
  .el-dialog__header{
    border-bottom: 1px solid #38393D;
    background: #1D1D1F;
    font-size: 14px;
    color: #E4E8F7;
  }
  .el-dialog__title{
    font-size: 14px;
    color: #E4E8F7;
  }
  .title{
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #fff;
    margin-bottom: 10px;
  }
  .el-dialog__body{
    background: #1D1D1F;
    padding: 24px;
    height: 600px;
    overflow: hidden;
    .content{
      padding: 0;
    }
    .design-tree{
      height: 500px;
      overflow: auto;
    }
    .el-tree__empty-block{
      background: #1D1D1F;
    }
    .el-tree__empty-text{
      color: #fff;
    }
    .el-tree{
      padding: 10px;
      background: #000;
    }
    .el-tree-node__content:hover{
      background: rgba(55, 96, 234, 0.24);
    }
    .el-tree-node__expand-icon{
      color: #fff;
    }
    .el-tree-node__expand-icon.is-leaf{
      color: transparent;
    }
    .el-checkbox__inner{
      background-color: #1D1D1F;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner{
      background-color: #3054CC;
    }
  }
  .el-dialog__footer{
    border-top: 1px solid #38393D;
    background: #1D1D1F;
  }
  .footer-tips{
    color: #5F8AFF;
    line-height: 40px;
  }
  .cancel-btn{
    background: transparent;
    &:hover{
      background: #3054CC;
      color: #fff;
      border-color: transparent;
    }
  }
}

.filter-design-list{
  margin-top: 10px;
  .el-tree__empty-block{
    background: #1D1D1F;
  }
  .el-tree__empty-text{
    color: #fff;
  }
  .el-tree{
    padding: 10px;
    background: #141519;
  }
  .el-tree-node__content:hover{
    background: rgba(55, 96, 234, 0.24);
  }
  .el-tree-node__expand-icon{
    color: #fff;
  }
  .el-tree-node__expand-icon.is-leaf{
    color: transparent;
  }
}
</style>
