<template>
  <div>
    <el-dialog title="提示" :visible.sync="uploadDialog" width="654px" custom-class="upload-box" :close-on-click-modal="!uploadLoading">
      <div v-loading="uploadLoading">
        <el-upload
          class="upload-demo"
          drag
          multiple
          ref="reOrderUpload"
          action="#"
          :accept="'.xlsx'"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleChange"
        >
          <span class="upload-icon">+</span>
          <div class="el-upload__text">{{lang('leftDrawer.uploadTips')}}</div>
        </el-upload>
        <div class="download-btn" @click="downSystemBill"><hg-icon icon-name="icon-download-default-lab"></hg-icon><span class="btn-text">{{lang('leftDrawer.uploadbtn')}}</span></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
import SliceUpload from '@/public/utils/SliceUpload';
export default {
  name: "uploadbox",
  props: {
    dialogVisible: {
      type: Boolean,
      default: true,
    },
    clientOrgCode: {
      type: Number,
      require: true,
    },
    acceptType: String,
  },
  computed: {
    uploadDialog: {
      get() {
        return this.dialogVisible;
      },
      set(val) {
        this.$emit("update:dialogVisible", val);
      },
    },
  },
  data() {
    return {
        uploadFileList: [],
        uploadLoading: false
    };
  },
  methods: {
    lang: getLang('bill'),
    handleChange(file, fileList) {
        if (!this.verifyFile(file, fileList) ) { return false; }
        this.uploadLoading = true
        const sliceUpload = new SliceUpload({file: file.raw, orgCode: this.clientOrgCode, fileType: this.fileType});

        this.uploadFileList.push(sliceUpload); //一开始就放到uploadFileList

        sliceUpload.onCheckMd5((data) => {
          const { md5 } = data;
          sliceUpload.onUpload();
        });

        console.log(this.uploadFileList)
        sliceUpload.onSuccess((data) => {
          const { filePath, fileName } = data;
          console.log('onEnd', data, file.raw);
          this.$emit('uploadSuccess', { data, file})
          this.uploadLoading = false;
          this.uploadDialog = false;
        });

        sliceUpload.onError((data) => {
          console.error('data: ', data, file);
          this.uploadLoading = false;
          this.uploadDialog = false;
        });
   
        sliceUpload.onStart();
    },
    // 校验文件
    verifyFile(file, fileList) {
      // if(this.acceptType !== '.xlsx'){
        const regRex = /\.(xlsx)$/g;
        if(!regRex.test(file.name.toLowerCase())){
          this.$hgOperateFail(this.lang('leftDrawer.uploadError'));
          return false;
        }
      // }
      return true;
    },
    // 下载系统账单
    downSystemBill(){
        this.$emit('downSystemBill')
    }
  },
};
</script>

<style lang="scss" scoped>
.upload-box {
    .download-btn{
        display: flex;
        width: 100%;
        height: 40px;
        justify-content: center;
        align-items: center;
        color: $hg-main-blue;
        margin-top: 20px;
        cursor: pointer;
    }
    .btn-text{
        vertical-align: top;
        margin-left: 8px;
    }
}
</style>
<style>
.upload-box {
  .el-dialog__header {
    display: none;
  }
  .el-upload{
    width: 100%;
  }
  .el-upload-dragger{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #141519;
    width: 100%;
    height: 337px;
    .upload-icon{
        font-size: 50px;
    }
  }
}
</style>
