<template>
  <div class="layout">
    <Header 
      :userInfo="userInfo" 
      :currentAppPath="CURRENT_APP_PATH" 
      :currentEnv="currentEnv" 
      :doLogout="false" 
      @logOut="toLogout"
      :doChangeMenu="false"
      @changeMenu="handleChangeMenu"></Header>

    <div class="layout-box">
      <sidebar></sidebar>
      <div class="main-container">
        <transition name="fade-transform" mode="out-in">
          <router-view></router-view>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import Header from 'heygears-cloud-header';
// import Header from 'heygears-cloud-header-beta';
import Sidebar from './component/sidebar';
import { CURRENT_APP_PATH } from '@/public/constants/setting';
import { redirectLogin } from '@/public/utils/token';
import { logout } from '@/api/common';

export default {
  name: 'Layout',
  components: { Header, Sidebar },
  data() {
    return {
      CURRENT_APP_PATH,
      userInfo: {},
      currentTimer: null,
    };
  },
  computed: {
    ...mapGetters(['showTipFromDetail']),
    currentEnv() {
      return process.env.VUE_APP_ENVIRONMENT;
    }
  },
  created() {
    const info = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userInfo = info;
    // this.initI18nDictList();
  },
  mounted() {
    this.currentTimer = setInterval(() => {
      this.$store.commit('CHANGE_CURRENT_TIME', Date.now());
    }, 1000);
    this.initDefaultParamList();
  },
  methods: {
    ...mapActions(['initDefaultParamList', 'updateTipFromDetail', 'initI18nDictList']),
    toLogout() {
      const handleLogout = () => {
        logout().then(res => {
          this.updateTipFromDetail(false);
          redirectLogin();
        });
      };
      this.$askBeforeLeaveDetail(this.showTipFromDetail,null, handleLogout);
      
    },
    handleChangeMenu(changeMenuFn) {
      if(this.showTipFromDetail) {
        const confirmFun = () => {
          this.updateTipFromDetail(false);
          changeMenuFn();
        }
        this.$askBeforeLeaveDetail(this.showTipFromDetail,null, confirmFun);
      }else {
        changeMenuFn();
      }
    }
  },

  beforeDestroy() {
    if (this.currentTimer) {
      clearInterval(this.currentTimer);
    }
  },
};
</script>

<style lang="scss" scoped>
.layout {
  height: 100vh;

  .layout-box {
    position: relative;
    display: flex;
    flex-direction: row;
    padding: 24px;
    height: calc(100vh - 60px);

    .sidebar {
      margin-right: 24px;
    }

    .main-container {
      flex: 1;
      overflow: hidden;
    }
  }

  @media screen and (max-width: 1280px) {
    .layout-box {
      height: calc(100vh - 60px);
      overflow-y: auto;
    }
  }
}
</style>