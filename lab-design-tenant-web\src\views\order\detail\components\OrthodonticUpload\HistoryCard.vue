<template>
  <div class="order-detail history-card">
    <orth-program-title 
      :titleContent="showNewContent? $t('order.ortho.title.newScheme') : $t('order.ortho.title.historyScheme', [number||1])"
      :createdTime="historyItem.createdTime"
      :isHistory="showNewContent">
      <span class="orth-title_tip" slot="showTip" v-if="showTips">{{ getTipContent() }}</span>
    </orth-program-title>
    
    <orth-view-card 
      :isHistory="true"
      :isAudit="Boolean(historyItem.isAudit)"
      :programStatus="historyItem.currentState"
      :isGenerateModel="historyItem.isGenerateModel"
      :programCode="historyItem.programmeCode" 
      :orderCode="historyItem.orderCode"
      :htmlTaskId="historyItem.htmlTaskId"
      :taskId="historyItem.taskId">
    </orth-view-card>

    <div class="design-file-box">
      <div class="file-card-ul" v-for="(item, index) in designAndModelFiles" :key="index">

        <p class="title">{{ $t(item.name) }}</p>
        <file-item  
          class="file-item" 
          :needMask="false"
          v-for="(file, fileIndex) in item.fileList" 
          :key="fileIndex" 
          :item="file" 
          @dragstart.native.prevent>
          <div slot="eventBtn" class="file-event-btn">
            <span v-if="file.canView" @click.stop="toggleModelView(file)">
              <hg-icon icon-name="icon-preview-on-lab"></hg-icon>{{ $t('common.btn.preview') }}
            </span>
            <span v-permission="['download']" @click.stop="downloadFile(file)">
              <hg-icon icon-name="icon-download-lab"></hg-icon>{{ $t('common.btn.download') }}
            </span>
          </div>

          <div slot="loadModel">
            <model-preview 
              bg="#262629"
              class='pre-model-style'
              :errorText="$t('file.tips.loadFileFail')"
              v-if="file.canView && file.visible"
              :ref="`historyModelBox${file.fileName}`"
              :file-list='[file]'
              @error="loadError(file)"
              ></model-preview>
          </div>

        </file-item>

        <div v-if="item.fileList.length===0" class="no-data">
          {{ $t('file.noData') }}
        </div>
      </div>
    </div>

    <div class="patient-image-box">
      <p>{{ $t('order.ortho.title.patientPic') }}</p>
      <div class="file-box" v-if="facePicList.length > 0 || intraoralPicList.length > 0 || ctPicList.length > 0">
        <div class="face-box" v-if="facePicList.length > 0">
          <p>{{ $t('order.ortho.title.facePic') }}</p>
          <div class="image-ul">
            <div v-for="(img, index) in facePicList" :key="index" class="image-li">
              <img @click.stop="openView(img.pictureUrl)" :src="img.pictureUrl" alt="">
            </div>
          </div>
        </div>

        <div class="intraoral-box" v-if="intraoralPicList.length > 0">
          <p>{{ $t('order.ortho.title.intraPic') }}</p>
          <div class="image-ul">
            <div v-for="(img, index) in intraoralPicList" :key="index" class="image-li">
              <img @click.stop="openView(img.pictureUrl)" :src="img.pictureUrl" alt="">
            </div>
          </div>
        </div>

        <div class="ct-box" v-if="ctPicList.length > 0">
          <p>{{ $t('order.ortho.title.ctPic') }}</p>
          <div class="image-ul">
            <div v-for="(img, index) in ctPicList" :key="index" class="image-li">
              <img @click.stop="openView(img.pictureUrl)" :src="img.pictureUrl" alt="">
            </div>
          </div>
        </div>
      </div>

      <div v-else class="no-data">
        <span>{{ $t('file.noData') }}</span>
      </div>
    </div>

    <div class="design-remark">
      <p>{{ $t('order.detail.title.designRemark') }}</p>
      <pre v-html="historyItem.remark|| $t('common.isNull') "></pre>
    </div>

    <div class="design-param">
      <orthodontic-design-param disabled v-model="orthoDesignParam" :schemeId="historyItem.programmeCode"></orthodontic-design-param>
    </div>

    <div class="client-return-reason" v-if="historyItem.returnReasons || clientReturnImages.length > 0">
      <p>{{ $t('order.ortho.title.clientReturnReason') }}</p>
      <div>
        <span class="reason-content">{{ historyItem.returnReasons }}</span>
        <div class="reason-img-box" v-show="clientReturnImages.length > 0">
          <img v-for="url in clientReturnImages" :key="url" :src="url" @click="openView(url)" alt="">
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { FILE_TYPES, ORDER_TYPES } from '@/public/constants';
import FileItem from './FileItem';
import ModelPreview from '@/components/ModelPreview';
import OrthViewCard from './OrthViewCard';
import OrthProgramTitle from './OrthProgramTitle';
import OrthodonticDesignParam from '@/components/OrthodonticDesignParam';
import { isStl, parseJson } from '@/public/utils';
import { getDownloadUrl } from '@/api/file';
import { createIFrameDownLoad, getFileNameNoSuffix } from '@/public/utils/file';

export default {
  components: { OrthViewCard, ModelPreview, FileItem, OrthProgramTitle, OrthodonticDesignParam, },
  name: 'HistoryCard',
  props: {
    number: Number,
    historyItem: {
      type: Object,
      require: true,
    },
    clientOrgCode: Number,
    showNewContent: Boolean,
    orderStatus: Number,
    canCompleteFile: Boolean
  },
  data() {
    return {
      clientReturnImages: [],

      facePicList: [],
      intraoralPicList: [],
      ctPicList: [],
      createProgramLoading: false,
    }
  },
  computed: {
    // 是否是已完成状态并且有待审核的订单
    isHaveCompleteScheme(){
      if([8, 11].includes(this.orderStatus) && this.canCompleteFile && this.historyItem.isAudit == 0){
        return true
      }
      return false
    },
    designAndModelFiles() {
      const designFileItem = {
        name: 'file.title.design',
        fileList: this.historyItem.orderFiles.filter(item => item.fileType === FILE_TYPES.DESIGN_FILE)
      };
      const modelFileItem = {
        name: 'file.title.model',
        fileList: this.historyItem.orderFiles.filter(item => item.fileType === FILE_TYPES.DESIGN_MODEL)
      };
      const correctItem = {
        name: 'file.title.correct',
        fileList: this.historyItem.orderFiles.filter(item => item.fileType === FILE_TYPES.CORRECT_FILE)
      };

      designFileItem.fileList.map(file => {
        const canView = file.filePath && isStl(file.fileName);
        this.$set(file, 'canView', canView);
        this.$set(file, 'visible', false);
        this.$set(file, 'viweUrl', '');
        return file;
      });
      modelFileItem.fileList.map(file => {
        const canView = file.filePath && isStl(file.fileName);
        this.$set(file, 'canView', canView);
        this.$set(file, 'visible', false);
        this.$set(file, 'viweUrl', '');
        return file;
      });

      return [designFileItem, modelFileItem, correctItem];
    },
    showTips() {
      if(this.orderStatus === ORDER_TYPES.DESIGNING && this.historyItem.currentState !== 2) { // 设计中 非OQC审核不通过
        return this.showNewContent;
      }else if(this.orderStatus === ORDER_TYPES.PENDING_REVIEW) {
        return this.showNewContent;
      }
      return false;
    },

    orthoDesignParam() {
      const { iprTiming, attachmentTiming } = this.historyItem;
      if(iprTiming && attachmentTiming) {
        return {
          iprInfo: parseJson(iprTiming) || null,
          additionInfo: parseJson(attachmentTiming) || null,
        };
      }
      return null;
    }
  },
  mounted() {
    if(this.historyItem.files) {
      this.getReturnImageList();
    }
    const patientList = this.historyItem.orderFiles.filter(item => [FILE_TYPES.FACE_PIC, FILE_TYPES.INTRAORAL_PIC, FILE_TYPES.CT_PIC ].includes(item.fileType));
    this.filterPatientPic(patientList);
  },
  methods: {
    filterPatientPic(list) {
      this.facePicList = [];
      this.intraoralPicList = [];
      this.ctPicList = [];
      list.forEach(async item => {
        const { fileName, filePath, fileType, fileSize, fileTime } = item;
        let imageItem = {
          fileName,
          filePath,
          fileType,
          fileSize,
          fileTime,
          pictureUrl: '',
        }
        if(fileType === FILE_TYPES.FACE_PIC) {
          this.facePicList.push(imageItem);
        }else if(fileType === FILE_TYPES.INTRAORAL_PIC) {
          this.intraoralPicList.push(imageItem);
        }else if(fileType === FILE_TYPES.CT_PIC) {
          this.ctPicList.push(imageItem);
        }

        const param = {
          s3FileId: filePath,
          orgCode: this.clientOrgCode,
        };
        try {
          const { data } = await getDownloadUrl(param);
          if(data.url) {
            this.$set(imageItem, 'pictureUrl', data.url);
          }
        } catch (error) {
          console.log('获取患者照片',error);
        }
      });
    },

    // 获取提示文本
    getTipContent() {
      if(this.showNewContent) {
        // 已完成设计师组长新增方案也加上
        if(this.historyItem.isAudit === 0 && [ORDER_TYPES.PENDING_REVIEW, 8, 11].includes(this.orderStatus)) { // 待审核
          return this.$t('order.ortho.status.pendingReview');
        }else if(this.historyItem.currentState === 3 && this.orderStatus === ORDER_TYPES.DESIGNING) { // 客户返单
          return this.$t('order.ortho.status.returnFromClient');
        }
      }
      return '';
    },
    downloadFile(file) {
      const { filePath, fileName } = file;
      if(filePath) {
        const param = {
          s3FileId: filePath,
          filename: getFileNameNoSuffix(fileName),
          orgCode: this.clientOrgCode
        };
        getDownloadUrl(param).then(res => {
          const { data } = res;
          if(data && data.url) {
            createIFrameDownLoad(data.url);
          }else {
            this.$hgOperateFail(this.$t('http.error.80080003'));
          }
        }).catch(err => {
          console.log('error:',err);
          // this.$hgOperateFail(this.$t('file.tips.downloadFail'));
        });
      }
    },
    /**
     * 预览模型
     */
    toggleModelView(item) {
      if(!item.visible) {
        this.$set(item, 'visible', true);
        this.$nextTick(() => {
          console.log(this.$refs);
          this.$refs[`historyModelBox${item.fileName}`][0].resetVisibleStatus();
        });
      }else {
        this.$set(item, 'visible', false);
      }
    },

    getReturnImageList() {
      const imageS3IdList = parseJson(this.historyItem.files) || [];
      imageS3IdList.forEach(async s3Id => {
        const param = {
          s3FileId: s3Id,
          orgCode: this.clientOrgCode,
        };
        const { data } = await getDownloadUrl(param);
        if(data.url) {
          this.clientReturnImages.push(data.url);
        }
      });
    },
    openView(url) {
      this.$hgViewer.open({
        imgList: [url],
        initialViewIndex: 0
      });
    },
    // 文件加载失败，viewUrl重置，再次点击打开会获取文件地址
    loadError(item) {
      item.viewUrl = '';
    },

    // 新增方案
    clickToCreateInComplete(){
      this.$emit('creat');
    },
  }
}
</script>

<style lang="scss" scoped>
.history-card {
  padding: 24px;
  background: #1D1D1F;
  border-radius: 4px;

  &>div {
    margin-bottom: 24px;
    &:last-of-type{
      margin-bottom: 0;
    }
  }

  .patient-image-box,
  .design-remark,
  .client-return-reason {
    >p {
      margin-bottom: 12px;
      font-size: 16px;
      line-height: 24px;
      font-weight: bold;
    }
  }
}

.history-card>.design-file-box {
  .file-card-ul {
    margin-bottom: 24px;
    &:last-of-type {
      margin-bottom: 0;
    }

    .no-data {
      padding: 24px;
      color: #737680;
      font-size: 14px;
      line-height: 20px;
    }
  }
  .file-card-ul>p{
    margin-bottom: 12px;
    font-size: 16px;
    line-height: 24px;
    font-weight: bold;

  }
  .file-card-ul>.file-item {
    margin-bottom: 12px;

    &:last-of-type {
      margin-bottom: 0;
    }

    .file-event-btn>span {
      cursor: pointer;
      margin-left: 32px;

      .hg-icon {
        padding-right: 8px;
      }
    }

    .pre-model-style {
      height: 320px;
      width: 100%;
    }
  }
}

.history-card>.design-remark {
  >pre {
    padding: 24px;
  }
}

.history-card>.patient-image-box {
  
  .file-box {
    padding: 16px 24px;
    border-radius: 4px;
    border: 1px solid #2D2F33;

    &>div {
      margin-bottom: 10px;
      border-bottom: 1px dashed #2D2F33;

      &:last-of-type {
        margin-bottom: 0;
        border-bottom: none;
      }

      &>p {
        padding-bottom: 12px;
        font-size: 14px;
        line-height: 20px;
        color: #ECECEE;
      }
    }

    .image-ul {
      display: flex;
      flex-wrap: wrap;

      .image-li {
        position: relative;
        margin: 0 20px 10px 0;
        padding: 8px;
        width: 140px;
        height: 140px;
        border-radius: 4px;
        border: 1px solid #2D2F33;

        >img {
          width: 100%;
          height: 100%;
        }

        .hg-icon {
          cursor: pointer;
          position: absolute;
          top: 12px;
          right: 12px;
          color: #737680;
          font-size: 24px;
        }
      }
    }
  }
}


.history-card>.client-return-reason {
  .reason-content {
    font-size: 14px;
    line-height: 20px;
    color: #FFB22C;
  }

  .reason-img-box {
    &>img {
      margin: 12px 20px 0 0;
      width: 140px;
      height: 140px;
      border-radius: 4px;
      border: 1px solid #2D2F33;
    }
  }
}

.history-card>.orth-program-title {
  position: relative;
  .orth-title_tip {
    margin-left: 8px;
    padding: 4px 12px;
    color: #EF9B34;
    font-size: 12px;
    border-radius: 12px;
    background: #EF9B3433;
  }
  .tips-btnlist{
    position: absolute;
    right: 0;
    top: -8px;
  }
}
</style>