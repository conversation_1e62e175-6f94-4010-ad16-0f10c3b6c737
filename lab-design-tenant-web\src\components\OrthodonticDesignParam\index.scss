.orth-design-param {
  &>div {
    .param-info>p { /* 标题 */
      margin-bottom: 12px;
      font-size: 16px;
      line-height: 24px;
      font-weight: bold;
    }

    .open-tooth-canvas { /* 牙位图按钮 */
      position: relative;
      margin-bottom: 24px;
      max-width: 1090px;
      text-align: center;
      
      &>p {
        cursor: pointer;
        color: #4477FB;
      }

      .tooth-box {
        position: absolute;
        z-index: -1;
        width: 100%;
        height: 720px;
        border-radius: 4px;
        border: 1px solid #38393D;
        box-shadow: 0px 24px 48px -12px rgba(0, 0, 0, 0.18);

        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 24px;
          color: #fff;
          height: 40px;
          
          span {
            font-weight: bold;
          }

          .hg-icon {
            cursor: pointer;
          }
        }

        .tooth-canvas {
          height: 678px;
          background-color: #fff;
          border-radius: 0 0 4px 4px;
        }
      }
      .tooth-info-canvas{
        width: 144px;
        height: 52px;
        border: 1px solid #000;
        box-sizing: border-box;
        background: #fff;
        padding: 8px;
        p{
          text-align: left;
          color: #000;
          font-weight: 900;
          font-size: 24px;
        }
        .title{
          margin-bottom: 4px;
        }
      }
      .hg-icon.hg-icon-viewer-next {
        margin-left: 4px;
        display: inline-block;
        transform: rotate(90deg);
      }
    }

    .open-tooth-canvas.is-open {
      height: 720px;

      .tooth-box {
        z-index: 1;
      }
    }
  }
}

/* IPR start */
.orth-design-param>.ipr-box>.param-info {
  
  .info {
    max-width: 1090px;
    padding: 0 52px;

    &>div {
      display: contents;
    }
  }
  /* IPR的十字中线 */
  .middle-line {
    display: flex;
    width: 100%;
    
    &>p:first-of-type {
      margin-right: -1px;
      margin-top: 24px;
      padding-bottom: 24px;
      width: 50%;
      border-right: 1px dashed #38393D;
      border-top: 1px dashed #38393D;
    }
    &>p:nth-of-type(2) {
      margin-bottom: 24px;
      padding-top: 24px;
      width: 50%;
      border-left: 1px dashed #38393D;
      border-bottom: 1px dashed #38393D;
    }
  }
}

.orth-design-param>.ipr-box>.param-info>.info>div>.ul-upper,
.orth-design-param>.ipr-box>.param-info>.info>div>.ul-lower {
  display: flex;
  padding: 24px 0;

  .li-box {
    display: flex;
    align-items: flex-end;
    height: 72px;

    &>span {
      display: inline-block;
      margin: 0 4px;
      width: 16px;
    }

    .button-box  {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 40px;

      .line {
        border-left: 1px solid #38393D;
        height: 32px;
      }
    }
  }
}
/* 反转下颌 */
.orth-design-param>.ipr-box>.param-info>.info>div>.ul-lower {
  .li-box {
    align-items: flex-start;

    .button-box {
      flex-direction: column-reverse;
    }
  }
}
/* IPR end */

/* 添加附件 start */
.orth-design-param>.addtional-box> {
  .open-tooth-canvas {
    max-width: 1090px;
  }
}
.orth-design-param>.addtional-box>.param-info {
  .info {
    max-width: 1090px;
    padding: 0 52px;

    &>div {
      display: contents;
    }
  }
}
.orth-design-param>.addtional-box>.param-info>.info>div:first-of-type {
  .addition-ul {
    margin-top: 24px;
    border-bottom: 1px dashed #38393D;
  }
}

.orth-design-param>.addtional-box>.param-info>.info>div {
  .addition-ul {
    display: flex;
  
    // 偏左
    .is-center {
      margin-right: 16px;
      border-right: 1px dashed #38393D;
    }
  }

  .addition-ul.addition-ul_upper {
    margin-top: 24px;
    .li-box {
      padding-bottom: 24px;
    }
  }
  .addition-ul.addition-ul_lower {
    margin-bottom: 24px;
    .li-box {
      padding-top: 24px;
    }
  }
}
/* 添加附件 start */