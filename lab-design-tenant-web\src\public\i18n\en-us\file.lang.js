/**
 * 文件相关
 */
export default {
  file : {
    noFile: 'None',
    noData: 'None',

    title: {
      design: 'Designed file',
      model: 'Designed STL File',
      screenshot: 'Screenshot',
      video: 'Video',
      prospectus: 'Proposal',
      other: 'Others',
      image: 'Upload',
      correct: 'Orthodontic Treatment Plan',
      drill: 'Drilling Protocol',
    },

    tips: {
      model: 'Upload designed STL file (.stl,.zip,.dcm,.obj,.ply)',
      video: 'Upload video file',
      prospectus: 'Upload proposal file (.pdf)',
      drill: 'Upload Drilling Protocol file (.pdf)',
      implant: 'Upload Implant Plan file (.pdf)',
      other: 'Upload attachment',
      upload: 'Upload {0}',
      uploadSameFile: "{0} has been uploaded, please don't repeat the operation.",
      uploadFail: 'Upload failed, please reupload.',
      wrongFileType: 'Incorrect format, please upload the file in {0} format.',
      downloadFail: 'Download failed.',
      loadFileFail: 'STL file loading failed.',
      emptyFile: 'Please do not upload empty files!',
      imageToBig: 'Uploaded images should not exceed 50M!',
      notImage: 'Please do not upload non-image files!',
      imageRepeat: 'Please do not upload repeated image files!',
      uploadImageTip: 'You can add up to 4 pics.',
      limitFourImage: 'Upload limit is 4 pics. Uploaded {1} pics.',
      uploadOriginFileFail: 'Original file download failed.',
      uploading: 'uploading...',
      limitSize: 'File size cannot exceed {0}MB',
      limitFile: 'Only {0} files can be uploaded currently.',
      correct: 'Please upload the treatment plan in html format.',
      filePackTimeout: 'Order【{0}】 files packaging timeout.',
      fileExpireByList: 'This file of the order【{0}】 has expired. You can obtain the file by contacting Xiao Hey.',
      noFileCanDownload: '【{0}】No files can be downloaded.',
      toothImgExpire: 'Tooth map has expired. You can obtain the file by contacting Xiao Hey.',
      uploadToothImgFail: 'Tooth map upload failed',
      isUploading: '( {0} )File uploading',
    },

    btn: {
      reUpload: 'Re-upload',
    }
       
  }
}