import moment from "moment-timezone";
import Vue from "vue";

/**
 * 设置时区
 * @param {*} timezone 时区  选填  当前浏览器时间 | currentTimezone (当前时间)、 Asia/Tokyo(日本时间) 、 America/New_York(纽约时间)...
 * @param {*} format
 */
const setTimezone = function(timezone) {
  if (!timezone) timezone = moment.tz.guess(true);
  moment.tz.setDefault(timezone);
};

/**
 * 时间过滤器
 * @param {*} time  时间戳/日期-时间
 * @param {*} format 格式化日期  YYYY-MM-DD | YYYY-MM-DD | YYYY-MM-DD HH:mm:ss | HH:mm:ss
 * @param {*} timezone 时区 选填 默认时区 | currentTimezone (当前时间)、 Asia/Tokyo(日本时间) 、 America/New_York(纽约时间)...
 */
Vue.filter("dateformat", function(time, format = "YYYY-MM-DD", timezone) {
  // debugger
  if (time) {
    return formatDate(time, format, timezone);
  } else {
    return time || "--";
  }
});

// moment.tz.guess()	 如果你不知道你自己当前所在地的时区，请使用此代码查看.

/**
 * 时间转换工具
 * @param {*} time  时间戳/日期-时间
 * @param {*} format 格式化日期  YYYY-MM-DD | YYYY-MM-DD | YYYY-MM-DD HH:mm:ss | HH:mm:ss
 * @param {*} timezone 时区 选填  默认时区 | currentTimezone (当前时间)、 Asia/Tokyo(日本时间) 、 America/New_York(纽约时间)...
 */

const formatDate = function(time, format = "YYYY-MM-DD", timezone) {
  if (timezone) {
    if (timezone.toLowerCase() === "currenttimezone") {
      timezone = moment.tz.guess(true);
    }
    return moment(time)
      .tz(timezone)
      .format(format);
  }
  return moment(time).format(format);
};

/**
 * 时间戳转换成时分格式
 * @param {*} ms_time  时间戳
 * @param {*} format  时间格式
 */
 const myTimeFormat = function(ms_time , format){
  if(!ms_time) ms_time = 0

  ms_time = (ms_time / 1000 / 60 / 60).toString();
  let hour = ms_time.split('.')[0] * 1 >= 10 ? ms_time.split('.')[0] * 1 : `0${ms_time.split('.')[0] * 1}`;
  let minute = '00';
  if (ms_time.split('.')[1] != undefined) {
    minute = `0.${ms_time.split('.')[1]}`;
    minute = (minute * 60).toString();
    let tm = minute.split('.')[0];
    if (tm != 0) { // 大于1分钟，获取分钟格式
      minute = tm * 1 >= 10 ? tm * 1 : `0${tm * 1}`;
    } else {
      minute = "00"
    }
  }
  return `${hour}${format}${minute}`;
}

export { setTimezone, formatDate, myTimeFormat };
