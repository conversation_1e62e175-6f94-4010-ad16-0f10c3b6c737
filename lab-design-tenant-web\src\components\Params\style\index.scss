
.hg-parameter-box>.hg-parameter-ul {
  display: flex;
  flex-wrap: wrap;
}

.hg-parameter-box {
  .hg-parameter-li {
    display: flex;
    align-items: center;
    width: 33.3%;
    margin-bottom: 24px;
  
    .parameter-label,
    .parameter-component {
      width: 50%;
    }
  
    .parameter-label {
      display: flex;
      padding: 0 12px;
      line-height: 20px;
      color: $hg-label;
      &>span {
        margin: auto 0;
      }
    }
    
    .parameter-component {
      /deep/.el-radio__input.is-checked {
        .el-radio__inner {
          border-color: $hg-main-blue;
          &::after {
            background-color: $hg-main-blue;
          }
        }
      }
    }
  }
  .has-child-one-line {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .one-line {
      width: 100%;
      line-height: 20px;
      margin-bottom: 12px;
      span {
        font-weight: 700;
      }
    }
    .parameter-component {
      width: 100%;
    }
    .parameter-container {
      .parameter-child {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .parameter-title {
          color: #E4E8F7;
          width: 16.65%;
          padding: 0 12px;
        }
      }
    }
  }
}

.hg-parameter-box .hg-parameter-li.on-one-line-li {
  width: 100%;
  .parameter-label {
    width: 16.65%;
  }
}

.hg-parameter-box .hg-parameter-li.is-line-li {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
  width: 100%;

  .parameter-label {
    margin-bottom: 12px;
    width: 100%;
  }
  .parameter-component {
    padding: 0 12px;
    width: 100%;
  }
}

@media only screen and (max-width: 1280px) {
  .hg-parameter-box .hg-parameter-li {
    width: 50%;
  }

  .hg-parameter-box .hg-parameter-li.on-one-line-li {
    .parameter-label {
      width: 25%;
    }
  }
}

@media only screen and (max-width: 900px) {
  .hg-parameter-box .hg-parameter-li {
    width: 100%;
  }

  .hg-parameter-box .hg-parameter-li.on-one-line-li {
    .parameter-label {
      width: 50%;
    }
  }
}

.hg-parameter-box .no-parameter {
  margin-bottom: 20px;
  width: 100%;
  color: $hg-label;
}


.hg-program-box .hg-program-col {
  display: flex;
  flex-direction: column;
  margin: 0;

  .program-label {
    margin-top: 32px;

    &>span {
      font-weight: bold;
      font-size: 14px;
      color: $hg-secondary-text;
    }
  }

  .program-content {
    color: $hg-label;

    /deep/.hg-pic:hover {
      // background: rgba(56, 57, 61, 0.4);
    }
    /deep/.img-checkbox-item.is-active,
    /deep/.img-checkbox-card-item.is-active,
    /deep/.child-item.is-active {
      .hg-pic {
        /* .hg-pic-check {
          display: inline-block;
          border-color: $hg-secondary-primary $hg-secondary-primary transparent transparent;
    
          .el-icon-check {
            color:$hg-label;
          }
        } */

        &:hover {
          background: transparent;
        }
      }
    }

    /deep/.hg-param-select,
    /deep/.hg-radio,
    /deep/.img-checkbox-item,
    /deep/.img-checkbox-card-item,
    /deep/.hg-radio-img-card {
      margin: 24px 8px 0 0;
    }

    /deep/.child-select-card {
      border-radius: 4px;
      border: 1px solid $hg-border;
    }

    /deep/.hg-param-select {
      .el-select {
        width: 400px;
      }
    }
  }
}

.hg-program-box .no-program {
  margin-top: 24px;
  width: 100%;
  color: $hg-label;
}