import { removeArrayItem } from '../array'
import { defineProp } from './other'
import { getProxyOrigin } from './proxy'

// 监听一个纯对象
export function reactive(object, key, fn, { immediate = false } = {}) {
  if (!object[`__reactive__${key}`]) {
    defineProp(object, `__reactive__${key}`, [])

    let val = object[key]
    Object.defineProperty(object, key, {
      enumerable: true,
      configurable: true,
      get() {
        return val
      },
      set(newVal) {
        if (newVal === val) {
          return
        }
        const oldVal = val
        val = newVal

        object[`__reactive__${key}`].forEach((fn) => {
          fn(newVal, oldVal)
        })
      }
    })
  }

  object[`__reactive__${key}`].push(fn)

  if (immediate) {
    fn(object[key])
  }

  return function unReactive() {
    removeArrayItem(object[`__reactive__${key}`], (item) => {
      return item === fn
    })
  }
}

export function reactiveProxy(object, key, fn, options) {
  const proxyOrigin = getProxyOrigin(object, key)

  return reactive(proxyOrigin, key, fn, options)
}
