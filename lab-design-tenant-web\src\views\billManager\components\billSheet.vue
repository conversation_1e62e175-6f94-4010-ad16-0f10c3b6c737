<template>
  <div class="body-table">
    <div id="luckysheet" class="luckysheet"></div>
  </div>
</template>

<script>
import LuckyExcel from 'luckyexcel';
export default {
  name: "billSheet",
  props: {
    files: Array,
  },
  data() {
    return {
      exportJson: null,
    };
  },
  watch: {
    files(newValue, oldValue) {
      if(newValue && newValue.length > 0){
        this.exportJson = null;
        this.uploadExcelBill(newValue)
      }
    }
  },
  mounted () {
    // this.billSheet = new luckysheet()
      if(this.files && this.files.length > 0){
          this.uploadExcelBill(this.files)
      }
  },
  methods: {
    uploadExcelBill(files) {
      if (files == null || files.length == 0) return alert("没有文件等待导入");
      if(this.exportJson){
        const arr = [exportJson.sheets[2]];
        exportJson.sheets[2].config.rowhidden = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}; //隐藏行
        exportJson.sheets[2].config.borderInfo = [{
          rangeType: "range",
          borderType: "border-all",
          style: "3",
          color: "#f5f5f5",
          range: [{
            row: [],
            column: [],
          }],
        }];
        window.luckysheet.destroy();
        window.luckysheet.create({
          data: arr,
          title: exportJson.info.name,
          userInfo: exportJson.info.name.creator,
          container: "luckysheet", // 设定DOM容器的id
          showtoolbar: false, // 是否显示工具栏
          showinfobar: false, // 是否显示顶部信息栏
          showstatisticBar: false, // 是否显示底部计数栏
          sheetBottomConfig: false, // sheet页下方的添加行按钮和回到顶部按钮配置
          allowEdit: false, // 是否允许前台编辑
          enableAddRow: false, // 是否允许增加行
          enableAddCol: false, // 是否允许增加列
          sheetFormulaBar: false, // 是否显示公式栏
          enableAddBackTop: false, // 返回头部按钮
          showsheetbar: true, // 是否显示底部sheet页按钮
          enableAddRow: false, //允许添加行
          row: 1, //行数
          column: 1, //列数
          // 自定义配置底部sheet页按钮
          showsheetbarConfig: {
            add: false,
            menu: false,
          },
        });
        setTimeout(() => {
          this.$emit('loadingSheet', false);
        }, 1000)
        return
      }

      LuckyExcel.transformExcelToLucky(files[0], (exportJson, luckysheetfile) => {
        this.exportJson = exportJson;
        if (exportJson.sheets == null || exportJson.sheets.length == 0) return alert("读取excel文件内容失败, 目前不支持XLS文件!");
        const arr = [exportJson.sheets[2]];
        exportJson.sheets[2].config.rowhidden = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}; //隐藏行
        exportJson.sheets[2].config.borderInfo = [{
          rangeType: "range",
          borderType: "border-all",
          style: "3",
          color: "#f5f5f5",
          range: [{
            row: [],
            column: [],
          }],
        }];
        window.luckysheet.destroy();
        this.billSheet.create({
          data: arr,
          title: exportJson.info.name,
          userInfo: exportJson.info.name.creator,
          container: "luckysheet", // 设定DOM容器的id
          showtoolbar: false, // 是否显示工具栏
          showinfobar: false, // 是否显示顶部信息栏
          showstatisticBar: false, // 是否显示底部计数栏
          sheetBottomConfig: false, // sheet页下方的添加行按钮和回到顶部按钮配置
          allowEdit: false, // 是否允许前台编辑
          enableAddRow: false, // 是否允许增加行
          enableAddCol: false, // 是否允许增加列
          sheetFormulaBar: false, // 是否显示公式栏
          enableAddBackTop: false, // 返回头部按钮
          showsheetbar: true, // 是否显示底部sheet页按钮
          enableAddRow: false, //允许添加行
          row: 1, //行数
          column: 1, //列数
          // 自定义配置底部sheet页按钮
          showsheetbarConfig: {
            add: false,
            menu: false,
          },
        });
        setTimeout(() => {
          this.$emit('loadingSheet', false);
        }, 1000)
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.body-table{
  width: 100%;
  height: 100%
}
.luckysheet {
  width: 970px;
  height: 700px;
}
</style>
