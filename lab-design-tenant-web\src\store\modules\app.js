import { getStore, setStore } from '@/public/utils/storage';
import { DEFAULT_LANGUAGE } from '@/public/constants/setting';
import { getDefaultParam } from '@/api/common';

const currentLang = getStore('lang') || DEFAULT_LANGUAGE; 

const state = {
  language: currentLang, //语言类型
  currentTime: Date.now(),  // 当前时间
  defaultParamMap: null, //
  defaultProgramMap: null,
  isLoadDefaultParam: false,
  showTipFromDetail: false, // 离开详情页是否需要弹出提示
  utcOffset: 0, // 当前用户时区偏移量
  changeRoute: true, // 离开编辑页时是否点击了取消按钮没有跳转
  isSetI18n: false
};

const mutations = {
  // 记录时区
  SET_UTC_OFFSET: (state, utcOffset) => {
    state.utcOffset = utcOffset;
  },
  // 切换语言
  CHANGE_LANGUAGE: (state, language) => {
    state.language = language;
    setStore('lang', language);
  },

  CHANGE_CURRENT_TIME: (state, time) => {
    state.currentTime = time;
  },

  INIT_DEFAULT_PARAM_LIST: (state, {paramMap, programMap}) => {
    state.defaultParamMap = paramMap;
    state.defaultProgramMap = programMap;
    state.isLoadDefaultParam = true;
  },
  UPDATE_TIP_FROM_DETAIL: (state, showTipFromDetail) => {
    state.showTipFromDetail = showTipFromDetail;
  },
  UPDATE_CHANGEROUTE: (state, changeRoute) => {
    state.changeRoute = changeRoute;
  },
  CHANGE_ISSETI18N: (state, isSetI18n) => {
    state.isSetI18n = isSetI18n;
  }
};

const actions = {
  // 切换语言
  changeLanguage: ({ commit }, language) => {
    commit('CHANGE_LANGUAGE', language);
  },

  // 更新当前时间
  changeCurrentTime: ({ commit }, currentTime) => {
    commit('CHANGE_CURRENT_TIME', currentTime)
  },

  initDefaultParamList: ({ commit }) => {
    let paramMap = new Map();
    let programMap = new Map();
    getDefaultParam().then(res => {
      const { code, data } = res;
      if(code === 200) {
        data.forEach(item => {
          const { software, parameter, program, designCode } = item;
          let itemMap = new Map();
          parameter.forEach(param => {
            const { software:itemSoftware, data:paramList } = param;
            itemMap.set(itemSoftware, paramList);
          });

          paramMap.set(designCode, { software, parameter: itemMap });
          programMap.set(designCode, program);
        });

        commit('INIT_DEFAULT_PARAM_LIST', {paramMap, programMap});
      }
    }).catch(err => {});
  },

  updateTipFromDetail: ({commit}, showTipFromDetail) => {
    commit('UPDATE_TIP_FROM_DETAIL', showTipFromDetail);
  },
  updateChageRoute: ({commit}, changeRoute) => {
    commit('UPDATE_CHANGEROUTE', changeRoute);
  },

};

export default {
  state,
  actions,
  mutations
};
