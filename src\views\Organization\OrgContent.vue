<template>
  <div v-if="show" class="organization">
    <div class="nav">
      <p class="nav-title">{{ $t('org.orgStructure') }}</p>
      <Tree :tree-data="orgList" :default-props="treeDefaultProps" :is-click-expand="false" :height="'calc(100vh - 280px)'" @hover="hoverOrgNode" @leave="leaveOrgNode" @clickLabel="clickOrgNode" @expandNode="expandNode">
        <template slot="treeLabel" slot-scope="scope">
          <!-- 只能修改部门的名称不能修改组织的名称；isDept为1时代表部门 -->
          <input v-if="!scope.node.data.isDept" v-model="scope.node.data.orgName" type="text" class="node-label" :readonly="true" :title="scope.node.data.orgName || ''">
          <input v-else v-model="scope.node.data.orgName" type="text" class="node-label finger" :readonly="scope.node.data.orgCode !== editOrgObj.orgCode" :title="scope.node.data.orgName || ''" @dblclick.stop="dbInput(scope.node)" @change="changeInput(scope.node)" @keyup.enter="enterInput($event,scope.node)">
        </template>
        <template slot="operate" slot-scope="scope">
          <!--  -->
          <p v-if="curHoverOrgId === scope.node.id" class="operate-box">
            <!-- 最多5个层级，第五层不能再添加部门 -->
            <i v-if="scope.node.data.depth < 5" v-permission="['editCustomer', 'delete']" class="iconfont icon_add_nor iconfont-24" @click.stop="addOrg(scope.node)" />
            <!-- 只有部门才显示删除按钮 -->
            <i v-if="scope.node.data.isDept === 1" v-permission="['editCustomer', 'delete']" class="iconfont icon_delete_nor iconfont-24" @click.stop="deleteOrgFunc(scope.node)" />
          </p>
        </template>
      </Tree>
    </div>
    <div class="main-content">
      <plane :slot-title="true">
        <span slot="title">{{ $t('org.userList') }}</span>
        <div slot="content" class="data-display">
          <Search
            :placeholder="$t('org.searchTip')"
            :search-icons="{
              add: true,
              refresh: false,
            }"
            size="medium"
            @add="openUserBox"
            @enterInput="searchTaskByKeyword"
          />
          <Table
            ref="orgTable"
            class="org-table"
            :table-style="{ height: tableHeight }"
            :keys="tableKeys"
            :table-data="tableData"
          >
            <template slot="operation" slot-scope="scope">
              <div class="operation-box">
                <Tooltip :content="$t('org.resetPassword')">
                  <i v-permission="['editCustomer', 'disabled']" class="iconfont icon_refresh_nor iconfont-24" @click="resetPasswordFunc(scope)" />
                </Tooltip>
                <Tooltip :content="$t('org.editUser')">
                  <i v-permission="['editCustomer', 'disabled']" class="iconfont icon-edit iconfont-24" @click="openEditUserBox(scope)" />
                </Tooltip>
                <Tooltip :content="$t('org.deleteUser')">
                  <i v-permission="['editCustomer', 'disabled']" class="iconfont icon_delete_nor iconfont-24" @click="deleteStaffFunc(scope)" />
                </Tooltip>
              </div>
            </template>
          </Table>
        </div>
      </plane>
      <Pagination :total-pages="totalPages" :total="totalUsers" :page-size="pageSize" :page-no="pageNo" @changePageSize="changePageSize" @changePageNo="changePageNo" />
    </div>
    <AddOrg ref="addOrg" :show.sync="showAddOrg" :popup-title="popupTitle" :parent-org.sync="addOrgObj" @submit="addOrgFunc" />
    <AddOrgUser ref="addOrgUser" :show.sync="showAddOrgUser" :popup-title="popupTitle" :cur-org-obj.sync="curOrgObj" :dept-arr="allDeptList" :role-arr="roleArr" :area-code-arr="areaCodeArr" :from-customer="fromCustomer" @submit="addUserFunc" />
    <EditOrgUser ref="editOrgUser" :show.sync="showEditOrgUser" :popup-title="popupTitle" :role-arr="roleArr" :area-code-arr="areaCodeArr" :dept-arr="allDeptList" :user-obj="curEditUserObj" :from-customer="fromCustomer" @submit="editUserFunc" />
  </div>
</template>

<script>
import Plane from '@/components/func-components/Plane.vue'
import Search from '@/components/func-components/Search.vue'
import Table from '@/components/func-components/TableCommon.vue'
import Tree from '@/components/func-components/Tree'
import Pagination from '@/components/func-components/Pagination'
import Tooltip from '@/components/func-components/Tooltip'
import AddOrg from './AddOrg'
import AddOrgUser from './AddOrgUser'
import EditOrgUser from './EditOrgUser'
import { getUserInfoByCode } from '@/api/login'
import { getOrgList, getStaffList, addDept, deleteDept, editDept, addUser, editUser, getAreaCodeList, resetPassword, deleteUser, getAllOrgTree } from '@/api/organization'
import { getRolesList } from '@/api/role'
import { mapState } from 'vuex'
import { clipboardFunc, clipDestroy } from '@/assets/script/clipBoardFunc.js'
import { changeByLang } from '@/assets/script/utils.js'

export default {
  name: 'OrgContent',
  components: {
    Tree,
    Plane,
    Search,
    Table,
    Pagination,
    AddOrg,
    AddOrgUser,
    EditOrgUser,
    Tooltip
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    curFatherOrgId: { // 当前最顶级组织编码，null默认是黑格科技
      default: null
    },
    customerOrg: { // 如果是从客户列表进来的则需要传这个值
      type: Object,
      default: () => {
        return {}
      }
    },
    tableHeight: {
      type: String,
      default: 'calc(100vh - 300px)'
    }
  },
  data() {
    return {
      // 组织树
      orgList: [],
      allDeptList: [], // 编辑人员可选的部门
      treeDefaultProps: {
        id: 'orgCode',
        children: 'children',
        label: 'orgName'
      },
      // curFatherOrgId: null, // 当前最顶级组织编码，null默认是黑格科技
      curHoverOrgId: '', // 当前鼠标移上的组织或部门编码
      curOrgObj: {}, // 当前操作的组织或部门
      popupTitle: '', // 弹窗标题
      showAddOrg: false, // 打开新增下级组织/部门弹窗
      addOrgObj: {}, // 新增部门的上级组织
      editOrgObj: {}, // 正在编辑名称的部门
      // 人员列表
      tableKeys: ['userCode', 'realName', 'roleName', 'orgName', 'mobile', 'email', 'operation'],
      tableData: {
        title: [
          this.$t('org.userId'),
          this.$t('org.userName'),
          this.$t('org.role'),
          this.$t('org.department'),
          this.$t('org.phone'),
          this.$t('org.email'),
          this.$t('org.operate')
        ],
        data: []
      },
      totalUsers: 10, // 某组织下的所有人员数
      totalPages: 1, // 总页数
      pageSize: 10, // 默认每页10条
      pageNo: 1, // 默认第一页
      // 新增组织或部门人员
      showAddOrgUser: false, // 打开新增组织/部门人员弹窗
      deptArr: [], // 所有部门列表
      roleArr: [], // 所有角色列表（角色需要区分租户端角色还是用户端角色）
      areaCodeArr: [], // 区号列表
      // 编辑用户信息
      showEditOrgUser: false,
      curEditUserObj: {},
      searchKey: '',
      readOnly: true,
      fromCustomer: false // 是否是客户管理的组织架构
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.init()
      } else {
        this.reset()
      }
    }
  },
  mounted() {
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo
    })
  },
  methods: {
    init() {
      this.getOrgListFunc(this.curFatherOrgId).then((data) => {
        if (Object.keys(this.customerOrg).length) { // 如果是从客户列表进来查看客户的组织架构的，则要显示客户本身
          this.fromCustomer = true
          const firstOrg = JSON.parse(JSON.stringify(this.customerOrg))
          firstOrg.isClient = 1
          firstOrg.isDept = 0
          firstOrg.depth = 1
          firstOrg.isLeaf = data.length ? 0 : 1
          firstOrg.children = data
          this.orgList.push(firstOrg)
          this.curOrgObj = firstOrg
        } else {
          this.fromCustomer = false
          this.orgList = data
          if (data.length) {
            this.curOrgObj = data[0]
          }
        }
        if (Object.keys(this.curOrgObj).length) {
          this.$nextTick(() => { // 默认选中第一条数据
            document.getElementById(this.curOrgObj.orgCode).parentElement.classList.add('active')
          })
          Promise.allSettled([this.getStaffListFunc(), this.getRolesFunc(this.curOrgObj.orgCode), this.getDeptsFunc(), this.getAreaCodeFunc()])
        }
      })
    },
    reset() {
      this.orgList = []
      this.curHoverOrgId = ''
      this.curOrgObj = {}
      this.addOrgObj = {}
      this.resetUserList()
    },
    resetUserList() {
      this.tableData.data = []
      this.totalPages = 1
      this.pageSize = 10
      this.pageNo = 1
      this.searchKey = ''
    },
    changePageSize(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getStaffListFunc()
    },
    changePageNo(val) {
      this.pageNo = val
      this.getStaffListFunc()
    },
    // 鼠标移上树节点，获取树节点信息，保存当前节点id
    hoverOrgNode(e, node) {
      this.curHoverOrgId = node.id
    },
    // 鼠标离开树节点，重置当前节点id
    leaveOrgNode() {
      this.curHoverOrgId = ''
    },
    // 双击树节点标签
    dbInput(node) {
      this.editOrgObj = node.data
      this.readOnly = false
      if (window.getSelection) {
        var selObj = window.getSelection()
        selObj.collapseToEnd() // 取消当前选区，并把光标定位在原选区的最末尾处（注：双击时会选中文本，被选中的文本就是选区）
      }
    },
    // 双击树节点标签编辑后，回车调用失去焦点事件
    enterInput(e, node) {
      e.target.blur(node)
    },
    changeInput(node) {
      this.readOnly = true
      this.editOrgFunc(node.data)
    },
    // 获取组织列表
    getOrgListFunc(orgCode) {
      return new Promise((resolve) => {
        getOrgList({
          'orgCode': orgCode
        }).then((res) => {
          if (res.code === 200) {
            if (res.data != null && res.data.length) {
              res.data.forEach(item => {
                if (!item.isLeaf) {
                  item.children = [{
                    orgCode: '',
                    orgName: '',
                    children: []
                  }]
                }
              })
              resolve(res.data)
            } else {
              resolve([])
            }
          }
        })
      })
    },
    // 加载某个组织的下级列表
    getOneOrgList(node) {
      this.getOrgListFunc(node.data.orgCode).then((data) => {
        node.data.children = data
      })
    },
    // 获取组织人员列表
    getStaffListFunc() {
      return new Promise((resolve) => {
        getStaffList({
          'key': this.searchKey,
          'orgCode': this.curOrgObj.orgCode,
          'pageNo': this.pageNo,
          'pageSize': this.pageSize
        }).then((res) => {
          if (res.code === 200) {
            this.totalUsers = res.data.totalSize
            this.totalPages = res.data.totalPages
            if (res.data.data != null && res.data.data.length) {
              this.tableData.data = res.data.data
              this.tableData.data.forEach((item) => {
                const roleArr = item.roleInfo.map((role) => {
                  return this.$t(role.i18nCode)
                })
                item.roleName = this.$i18n.locale == 'zh' ? roleArr.join('，') : roleArr.join(', ')
                const orgArr = item.orgInfo.map((org) => {
                  return org.orgName
                })
                item.orgName = orgArr.join()
              })
            } else {
              this.tableData.data = []
            }
          }
          resolve()
        })
      })
    },
    // 点击组织获取该组织的人员列表以及下级组织或部门
    clickOrgNode(e, node) {
      this.resetUserList()
      this.curOrgObj = node.data
      this.getStaffListFunc()
    },
    // 点击展开节点的图标
    expandNode(nodeObj, nodeData, node) {
      this.getOneOrgList(nodeData)
    },
    // 添加组织部门
    addOrg(node) {
      this.popupTitle = this.$t('org.addNewDept')
      this.addOrgObj = node
      this.showAddOrg = true
    },
    addOrgFunc(orgObj) {
      const newOrgObj = JSON.parse(JSON.stringify(orgObj))
      addDept({
        'orgName': orgObj.orgName,
        'parentCode': orgObj.parentCode
      }).then((res) => {
        if (res.code === 200) {
          this.$refs.addOrg.loading = false
          newOrgObj.orgCode = res.data
          if (this.addOrgObj.data.children) {
            this.addOrgObj.data.children.push(newOrgObj)
          } else {
            this.$set(this.addOrgObj.data, 'children', [newOrgObj])
          }
          // 若该节点本来是叶子节点，现新增了节点，则该节点应改成非叶子节点
          this.addOrgObj.data.isLeaf = 0
          this.showAddOrg = false
          if (res.data) {
            this.getDeptsFunc()
            this.$MessageAlert({
              text: this.$t('org.addDeptSuccessTip'),
              type: 'success'
            })
          }
        }
      }).catch(() => {
        this.$refs.addOrg.loading = false
      })
    },
    // 编辑部门（名称）
    editOrgFunc(orgObj) {
      editDept({
        'orgName': orgObj.orgName,
        'orgCode': orgObj.orgCode
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.getDeptsFunc()
            this.$MessageAlert({
              text: this.$t('org.editDeptNameSuccessTip'),
              type: 'success'
            })
          }
        }
      })
    },
    // 删除组织部门
    deleteOrgFunc(node) {
      const zhText = this.$t('org.deleteConfirm', { orgText: node.data.isDept ? this.$t('org.department') : this.$t('org.organization'), orgName: node.data.orgName })
      const enText = this.$t('org.deleteConfirm', { orgText: node.data.isDept ? this.$t('org.department') : this.$t('org.organization'), orgName: node.data.orgName })
      this.$Dialog({
        message: changeByLang(zhText, enText),
        ensureBtnText: this.$t('org.deleteBtn'),
        confirmAction: () => {
          this.getDeptsFunc()
          // 点击确认按钮后的操作
          this.ensureDeleteOrg(node.data.orgCode).then(() => {
            this.$MessageAlert({
              text: this.$t('org.deleteDeptSuccessTip', { orgText: node.data.isDept ? this.$t('org.department') : this.$t('org.organization') }),
              type: 'success'
            })
            // 删除树节点
            node.parent.data.children = node.parent.data.children.filter(item => item.orgCode !== node.data.orgCode)
            // 删除部门后，若被删部门的上级下面没有子节点了，则该上级应变为叶子节点
            if (!node.parent.data.children.length) {
              node.parent.data.isLeaf = 1
            }
            // 如果删除的节点是正在查看人员列表的节点，则删除节点后改为正在查看第一个节点的人员列表
            if (node.data.orgCode === this.curOrgObj.orgCode) {
              this.curOrgObj = this.orgList[0]
              document.getElementById(this.curOrgObj.orgCode).parentElement.classList.add('active')
              this.getStaffListFunc()
            }
          })
        }
      })
    },
    ensureDeleteOrg(code) {
      return new Promise((resolve) => {
        deleteDept({
          'orgCode': code
        }).then((res) => {
          if (res.code === 200) {
            if (res.data) {
              resolve()
            }
          }
        })
      })
    },
    // 打开新增用户弹窗
    openUserBox() {
      // this.getDeptsFunc(),
      // Promise.allSettled([this.getRolesFunc(), this.getDeptsFunc(), this.getAreaCodeFunc()]).then(() => {
      //   this.popupTitle = this.$t('org.addUser');
      //   this.showAddOrgUser = true;
      // });
      this.popupTitle = this.$t('org.addUser')
      this.showAddOrgUser = true
    },
    // 获取当前端的所有角色
    getRolesFunc(orgCode) {
      return new Promise((resolve) => {
        getRolesList({
          'system': this.curOrgObj.isClient ? 0 : 1,
          'orgCode': orgCode
        }).then((res) => {
          if (res.code === 200) {
            this.roleArr = []
            res.data.forEach(item => {
              this.roleArr.push({ 'label': this.$t(item.i18nCode), 'lable_en': this.$t(item.i18nCode), 'i18nCode': item.i18nCode, 'value': item.roleCode })
            })
            resolve()
          }
        })
      })
    },
    // 获取区号列表
    getAreaCodeFunc() {
      return new Promise((resolve) => {
        getAreaCodeList({
          'orderBy': this.$i18n.locale == 'zh' ? 0 : 1
        }).then((res) => {
          if (res.code === 200) {
            this.areaCodeArr = res.data
            resolve()
          }
        })
      })
    },
    // 递归判断是否是叶子节点
    setLeaf(arr) {
      arr.forEach(obj => {
        if (obj.sonList && obj.sonList.length) {
          obj.isLeaf = 0
          this.setLeaf(obj.sonList)
        } else {
          obj.isLeaf = 1
        }
      })
    },
    // 获取当前部门的最顶级部门的所有下级部门列表
    getDeptsFunc() {
      return new Promise((resolve) => {
        getAllOrgTree({
          'orgCode': this.curOrgObj.parentCodes ? this.curOrgObj.parentCodes.split(',')[0] : this.curOrgObj.orgCode
        }).then((res) => {
          if (res.code === 200) {
            this.allDeptList = res.data
            this.setLeaf(this.allDeptList) // 递归设置是否是叶子节点
            resolve()
          }
        })
      })
    },
    // 确认新增用户
    addUserFunc(user) {
      addUser({
        'email': user.email,
        'mobile': user.mobile,
        'mobilePrefix': user.selectedAreaCode,
        'orgCode': user.selectedDept,
        'roleCodes': user.selectedRole,
        'userName': user.realName,
        'tzCode': user.tzCode,
      }).then((res) => {
        if (res.code === 200) {
          this.$refs.addOrgUser.loading = false
          if (res.data) {
            this.getStaffListFunc()
            this.showAddOrgUser = false
            this.$Dialog({
              message: this.$t('org.addUserSuccessTip') + res.data,
              ensureBtnText: this.$t('org.copy'),
              cancelBtnText: this.$t('org.close'),
              updateShow: false,
              init: () => {
                clipboardFunc(res.data)
              },
              cancelAction: () => {
                // 点击关闭按钮后的操作
                clipDestroy() // 销毁剪贴板实例
              }
            })
          }
        } else {
          this.$refs.addOrgUser.loading = false
        }
      }).catch(() => {
        this.$refs.addOrgUser.loading = false
      })
    },
    // 获取个人信息（时区）
    getUserinfoFunc(userCode) {
      return new Promise((resolve) => {
        getUserInfoByCode({
          userCode: userCode
        }).then((res) => {
          if (res.code == 200) {
            this.curEditUserObj = res.data
            resolve()
          }
        })
      })
    },

    // 打开编辑弹窗
    openEditUserBox(scope) {
      const selectedRole = scope.data.roleInfo.map((role) => {
        return role.roleCode
      })
      const selectedDept = scope.data.orgInfo.map((org) => {
        const obj = {}
        obj.orgCode = org.orgCode
        obj.parentCodes = org.parentCodes
        return obj
      })
      this.getUserinfoFunc(scope.data.userCode).then(() => {
        this.popupTitle = this.$t('org.editUser')
        this.curEditUserObj.selectedRole = selectedRole;
        this.curEditUserObj.selectedDept = selectedDept[0].orgCode;
        this.curEditUserObj.parentCodes = selectedDept[0].parentCodes;
        this.curEditUserObj.orgInfo = scope.data.orgInfo;
        if(this.curEditUserObj.areaCode){
          this.curEditUserObj.areaCode = this.curEditUserObj.areaCode.split(',');
        }
        this.showEditOrgUser = true
      })
    },
    // 确认编辑用户
    editUserFunc(user) {
      editUser({
        'email': user.email,
        'mobile': user.mobile,
        'mobilePrefix': user.mobilePrefix,
        'roleCode': user.selectedRole,
        'orgCode': [user.selectedDept],
        'userCode': user.userCode,
        'realName': user.realName,
        'tzCode': user.tzCode,
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$refs.editOrgUser.loading = false
            this.getStaffListFunc()
            this.showEditOrgUser = false
            this.$MessageAlert({
              text: this.$t('org.editUserSuccessTip'),
              type: 'success'
            })
          }
        } else {
          this.$refs.editOrgUser.loading = false
        }
      }).catch(() => {
        this.$refs.editOrgUser.loading = false
      })
    },
    searchTaskByKeyword(value) {
      this.searchKey = value
      this.pageNo = 1
      this.getStaffListFunc()
    },
    // 重置用户密码
    resetPasswordFunc(scope) {
      this.$Dialog({
        title: this.$t('org.resetPassword'),
        message: this.$t('org.resetPasswordConfirm', { realName: scope.data.realName }),
        ensureBtnText: this.$t('org.confirm'),
        confirmAction: () => {
          // 点击确认按钮后的操作
          this.ensureResetPassword(scope.data.userCode)
        }
      })
    },
    ensureResetPassword(code) {
      resetPassword({
        'userCode': code
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$Dialog({
              message: this.$t('org.resetSuccessTip', { password: res.data }),
              ensureBtnText: this.$t('org.copy'),
              cancelBtnText: this.$t('org.close'),
              updateShow: false,
              init: () => {
                clipboardFunc(res.data)
              },
              cancelAction: () => {
                // 点击关闭按钮后的操作
                clipDestroy() // 销毁剪贴板实例
              }
            })
          }
        }
      })
    },
    // 删除用户
    deleteStaffFunc(scope) {
      this.$Dialog({
        title: this.$t('org.deleteUser'),
        message: this.$t('org.deleteUserConfirm', { realName: scope.data.realName }),
        ensureBtnText: this.$t('org.confirm'),
        confirmAction: () => {
          // 点击确认按钮后的操作
          this.ensureDeleteStaff(scope.data.userCode).then(() => {
            this.$MessageAlert({
              text: this.$t('org.deleteUserSuccessTip'),
              type: 'success'
            })
            this.getStaffListFunc()
          })
        }
      })
    },
    ensureDeleteStaff(code) {
      return new Promise((resolve) => {
        deleteUser({
          'userCode': code
        }).then((res) => {
          if (res.code === 200) {
            if (res.data) {
              resolve()
            }
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.organization {
  height: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  .nav {
    height: 100%;
    width: 300px;
    margin-right: 24px;
    overflow: hidden;
    .nav-title {
      color: #3054cc;
      font-size: 16px;
      margin-bottom: 12px;
      font-weight: bold;
    }
    .operate-box {
      // flex: 1;
      // margin-left: auto;
      // text-align: right;
      // flex-shrink: 0;
      position: absolute;
      right: 0;
      padding-right: 16px;
      i {
        margin-left: 8px;
        // font-size: 24px;
      }
    }
  }
  .main-content {
    position: relative;
    flex: 1;
    height: 100%;
    overflow: hidden;
    padding-bottom: 58px;
    box-sizing: border-box;
    background-color: $hg-main-black;
    .data-display {
      height: 100%;
      background-color: $hg-main-black;
      overflow: hidden;
      .org-table {
        margin-top: 8px;
        ::v-deep .th:last-child {
          text-align: center !important;
        }
        .operation-box {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          .tooltip-box {
            margin-right: 16px;
            &:nth-last-child(1) {
              margin-right: 0px;
            }
          }
        }
      }
    }
  }
}
</style>
