import { defineProp } from '../object/index'

const arrayStore = new Map()

const arrayProto = Array.prototype

const arrayProxyProto = Object.create(arrayProto)

const arrayCreaseMethods = [
  'push',
  'pop',
  'unshift',
  'shift',
  'splice',
]

arrayCreaseMethods.forEach((method) => {
  const original = arrayProto[method]
  defineProp(arrayProxyProto, method, function () {
    const args = []
    arrayProto.push.apply(args, arguments)
    const result = original.apply(this, args)

    if (arrayStore.has(this)) {
      const creaser = arrayStore.get(this)

      switch (method) {
        case 'push': {
          for (const item of args) {
            creaser._add(item)
          }
          break
        }

        case 'pop': {
          creaser._remove(result)
          break
        }

        case 'unshift': {
          for (const item of args) {
            creaser._add(item)
          }
          break
        }

        case 'shift': {
          creaser._remove(result)
          break
        }

        case 'splice': {
          for (const item of result) {
            creaser._remove(item)
          }
          for (const item of args.slice(2)) {
            creaser._add(item)
          }
          break
        }
      }
    }

    return result
  })
})

export function observeArrayCrease(arr, creaser) {
  // eslint-disable-next-line
  arr.__proto__ = arrayProxyProto
  arrayStore.set(arr, creaser)
}

export function unobserveArrayCrease(arr) {
  arr = arrayProto
  arrayStore.delete(arr)
}
