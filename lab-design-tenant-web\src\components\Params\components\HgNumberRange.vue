<template>
  <div class="hg-number-range">
    <div class="number-range-box">
      <input 
        class="min" 
        type="number" 
        v-model="minValue" 
        @change.stop="handleMinChange" />
      -
      <input 
        class="max" 
        type="number" 
        v-model="maxValue" 
        @change.stop="handleMaxChange" />
      <span class="unit">{{ data.unit }}</span>
    </div>
  </div>
</template>

<script>
import { parseJson } from '../utils/index';

export default {
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          max: 3,
          min: 0,
        }
      }
    },
  },
  data(){
    return {
      minValue: '0.0',
      maxValue: '0.0',
      valueList: ['0.0','0.0']
    }
  },
  mounted() {
    const { value } = this.data;
    if(value) {
      const valueList = parseJson(value) || [];
      if(valueList.length === 2) {
        const minValue = Number(valueList[0]);
        const maxValue = Number(valueList[1]);
        this.minValue = minValue.toFixed(1);
        this.maxValue = maxValue.toFixed(1);
        this.valueList = valueList;
      }
    }
  },
  methods: {

    handleMinChange() {
      const newVal = Number(this.minValue);
      const { min } = this.data;
      if (!isNaN(newVal)) {
        this.setCurrentValue(newVal, 'min');
      }else {
        this.minValue = min.toFixed(1);
      }

      this.valueList[0] = this.minValue;
      this.$emit('update',JSON.stringify(this.valueList));
    },
    handleMaxChange() {
      const newVal = Number(this.maxValue);
      const { max } = this.data;
      if (!isNaN(newVal)) {
        this.setCurrentValue(newVal, 'max');
      }else {
        this.maxValue = max.toFixed(1);
      }

      this.valueList[1] = this.maxValue;
      this.$emit('update',JSON.stringify(this.valueList));
    },

    setCurrentValue(value, type) {
      let newValue = value;
      const { min, max } = this.data;
      if(newValue < min) newValue = min;
      if(newValue > max) newValue = max;

      if(type === 'min'){
        this.minValue = newValue.toFixed(1);
      }else if (type === 'max') {
        this.maxValue = newValue.toFixed(1);
      }

    },

  }
}
</script>

<style lang="scss" scoped>
.hg-number-range {

  .number-range-box {
    position: relative;
    display: flex;
    width: 192px;
    border: 1px solid $hg-border;
    border-radius: 4px;
    line-height: 30px;
    color: $hg-secondary-text;

    .min,.max {
      width: 50px;
      height: 30px;
      border: none;
      color: $hg-label;

      &:hover {
        cursor: text;
      }
    }

    .min {
      margin-left: 20px;
    }

    .max {
      margin-left: 30px;
      margin-right: 20px;
    }

    .unit {
      position: absolute;
      height: 100%;
      font-size: 14px;
      top: 3%;
      right: 10px;
      color: $hg-disable;
    }
  }

}
</style>

<style lang="scss">
.hg-number-range {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type='number'] {
    padding: 0;
    -moz-appearance: textfield;
    box-shadow: none; // 兼容firefox[火狐下会出现红色阴影]
    height: 30px;
    border: 1px solid $hg-border;
    border-radius: 4px;
    background-color: transparent;
  }
  .el-input__suffix {
    display: flex;
    align-items: center;
  }
}
</style>