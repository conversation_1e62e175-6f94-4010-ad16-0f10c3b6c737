import { toRawType } from '../type'

const hasOwnProperty = Object.prototype.hasOwnProperty

export function hasOwn(obj, key) {
  return hasOwnProperty.call(obj, key)
}

export function isEmptyObject(value) {
  return Object.keys(value).length === 0
}

// TODO: 循环引用跳出,自定义处理函数
export function extractProps(target, mixin, deep, cb) {
  for (const key in mixin) {
    const value = mixin[key]

    if (!deep) {
      target[key] = value
      continue
    }

    const targetType = toRawType(target[key])
    const mixinType = toRawType(value)

    if (targetType === mixinType && (targetType === 'Array' || targetType === 'Object')) {
      extractProps(target[key], value, deep)
    } else {
      target[key] = value
    }
  }
  return target
}

export function defineProp(obj, key, value, enumerable) {
  Object.defineProperty(obj, key, {
    value,
    enumerable: !!enumerable,
    configurable: true,
    writable: true
  })
}

export function enumerableProps(obj, properties, enumerable) {
  for (const key of properties) {
    const value = obj[key]
    Object.defineProperty(obj, key, {
      value,
      enumerable: !!enumerable,
      configurable: true,
      writable: true
    })
  }
}
