<template>
  <div ref="dropdown" class="my-dropdown-wrap">
    <div @click="changeVisible"><slot /></div>
    <div ref="dropdownMenu" :class="['my-dropdown-menu', showMenu ? 'show' : '']">
      <slot name="dropdown" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'DropDown',
  components: {
  },
  props: {
  },
  data() {
    return {
      showMenu: false
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    window.addEventListener('click', (e) => {
      const arr = e.path.filter(item => item.className && item.className.indexOf('my-dropdown') !== -1)
      if (!arr.length) {
        this.showMenu = false
      }
    }, true)
  },
  methods: {
    // 展开/关闭面板
    changeVisible() {
      this.showMenu = !this.showMenu
      if (this.showMenu) {
        this.$nextTick(() => {
          this.resize()
          window.addEventListener('resize', this.resize, true)
          this.$emit('visibleChange', this.showMenu)
        })
      } else {
        window.removeEventListener('resize', this.resize, true)
      }
    },
    resize() {
      const dom = this.$refs.dropdownMenu
      dom.style.left = (this.$refs.dropdown.getBoundingClientRect().left - dom.clientWidth + this.$refs.dropdown.clientWidth) + 'px'
    },
    handleClick() {
      this.changeVisible()
    }
  }
}
</script>
<style lang="scss" scoped>
.my-dropdown-wrap {
  position: relative;
  .my-dropdown-menu {
    // visibility: hidden;
    display: none;
    z-index: 100;
    position: fixed;
    margin-top: 15px;
    width: auto;
    padding: 0;
    background: #262629;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.4);
    border-radius: 4px;
    ::before {
      display: block;
      content:'';
      width: 0;
      border-width: 0px 6px 6px 6px;
      border-style: solid;
      border-color: transparent transparent #262629 transparent;
      position:absolute;
      right: 19px;
      top: -6px;
    }
    transition: height .2s;
    -moz-transition: height .2s; /* Firefox 4 */
    -webkit-transition: height .2s; /* Safari and Chrome */
    -o-transition: height .2s; /* Opera */
    &.show {
      display: block;
      // visibility: visible;
    }
  }
}

</style>
