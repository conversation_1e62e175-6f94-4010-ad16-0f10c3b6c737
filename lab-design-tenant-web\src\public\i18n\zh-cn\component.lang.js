// 按钮、组件相关的中文
export default {
  component: {
    title: {
      system: '系统提示',
    },
    tip: {
      pleaseInput: '请输入',
      title: '提示',
    },

    date: {
      start: '开始日期',
      end: '结束日期',
      startTime: '开始时间',
      endTime: '结束时间'
    },

    pagination: {
      sizeLabel: '条/页',
      jumpLabel: '跳至',
      page: '页'
    },
    ortho: {
      iprTitle: 'IPR',
      additionTitle: '添加附件',
      btnShowTooth: '牙位图展示',
      toothTitle: '牙位图-{0}',
      btnAddStep: '新增步数',
      inputStep: '第{0}步',
      cutInput: '片切{0}',
      tips: {
        steps: '第{0}步',
        limitValue: '步数限制1~99步',
        addTip: '{0}号牙位的添加附件操作，请确认是否删除？',
        isEmpty: '数值不能为空',
        stepEmpty: '请填写该操作为"第几步"',
        cutIsEmpty: '请填写片切量',
        cutLimit: '片切量限制0.01~3.00㎜',
        cutMsg: '第{0}步，牙位号{1}~{2}之间的邻面去釉操作，请确认是否删除？'
      },
    },
  }
}