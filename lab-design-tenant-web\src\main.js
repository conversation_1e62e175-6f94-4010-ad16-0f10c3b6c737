import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import i18n from './public/i18n';
import directives from './directives'; // 全局指令
import './components/global-components/componentRegister'; // 注册的全局组件
import './assets/styles/reset.scss';
import * as filters from './filters';
import element from './public/lib/element';
import './assets/icons/svg.js'; // svg图标处理
import MessageMixin from './mixins/message';
import HgViewer from '@/components/global-components/HgViewer';
import VuePhotoZoomPro from './components/ZoomImg';
import VueLazyload from 'vue-lazyload';
import './public/rem'; 
import echart from './public/lib/echart';

// 本地开发环境才开启mock
// (process.env.NODE_ENV === 'dev' && require('./mock'));

Vue.config.productionTip = false;
Vue.use(element, { i18n: (key, value) => i18n.t(key, value) });
Vue.use(directives);
Vue.mixin(MessageMixin);
Vue.use(VuePhotoZoomPro);
Vue.prototype.$echarts = echart;


/**
 * 全局过滤器注册
 */
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});

Vue.prototype.$hgViewer = HgViewer;

Vue.use(VueLazyload, {
  error: require('@/assets/images/common/program_none.svg'),
  loading: require('@/assets/images/common/program_none.svg'),
});

new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App),
}).$mount('#app');
