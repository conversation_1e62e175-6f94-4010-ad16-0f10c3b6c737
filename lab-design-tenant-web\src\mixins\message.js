import { Message } from 'element-ui';

// 全局混入
export default {
  methods: {
    // 操作成功
    $hgOperateSuccess(msg, seconds = 2) {
      this.$message({
        type: 'success',
        message: msg ? msg : this.$t('common.operateSuccess'),
        duration: seconds * 1000,
      });
    },
    $hgOperateFail(msg) {
      this.$message({
        type: 'error',
        message: msg ? msg : this.$t('common.operateFail'),
      });
    },
    $hgOperateWarning(msg,seconds = 2) {
      this.$message({
        type: 'warning',
        message: msg ? msg : this.$t('common.operateWarning'),
        duration: seconds * 1000,
      });
    },
    /**
     * 支持重复出现
     * @param {*} msg 
     */
    $hgOperateRepeatFail(msg) {
      Message({
        type: 'error',
        message: msg ? msg : this.$t('common.operateFail'),
      });
    },

    $askBeforeLeaveDetail(needConfirm, cancelFn, confirmFn) {
      if(needConfirm) {
        const askMsg = this.$t('order.detail.tips.leave');
        const tipTitle = this.$t('component.tip.title');
        this.$confirm( askMsg, tipTitle, {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
          closeOnClickModal: false,
          closeOnPressEscape: false,
          distinguishCancelAndClose: true,
        }).then(() => {
          // cancelFn && cancelFn();
          confirmFn && confirmFn();
        }).catch((action) => {
          if(action === 'confirm') {
            confirmFn && confirmFn();
          }else{
            cancelFn && cancelFn();
          }
        });
      }else {
        confirmFn && confirmFn();
      }
    }
  },
}
