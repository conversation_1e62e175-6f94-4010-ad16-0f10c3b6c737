/**
 * 编辑订单操作
 *  1.可编辑参数、方案
 *  2.可替换新的设计品类
 *  3.参数方案获取下单用户的默认值，不能切换[软件]类型
 */

import { mapGetters } from 'vuex';
import { setRootCode } from '@/public/utils/order';
import { copy } from '@/public/utils';

export default {
  data() {
    return {
      sourceToothDesign: [],  // 订单原始的牙位信息
      // // 租户提交[编辑]是需要传的参数
      requestParam: {
        orderCode: 0,
        orderStatus: 0,
        designStatus: 0,
        designCategoryCode: 0,
        paramList: [],
        toothDesign: [],
        imageMap: {},
      },
      imageBas64Map: {},

    }
  },
  computed: {
    ...mapGetters(['userCode', 'oneDesignList', 'designTypeTree']),
  },
  methods: {
    /**
     * 初始化编辑时需要提交的参数
     */
    initEditRequestParam() {
      const { designStatus } = this.otherInfo;

      this.requestParam.orderCode = this.orderCode;
      this.requestParam.orderStatus = this.orderStatus;
      this.requestParam.designStatus = designStatus;
      this.sourceToothDesign = copy(this.otherInfo.toothDesignList);
    },

    //右上角[完成编辑]-获取牙位图的数据
    getRequestParamBeforeSubmit(requestParam) {
      const { totalToothDesign, totalImageMap, designCategoryCode, totalParamList, totalImageBase64, implantForm = {} } = requestParam;
      console.log('getRequestParamBeforeSubmit-implantForm', implantForm)
      this.requestParam.toothDesign = setRootCode(totalToothDesign);
      this.requestParam.designCategoryCode = designCategoryCode;
      this.requestParam.paramList = totalParamList;
      this.requestParam.imageMap = totalImageMap;
      if (implantForm.implantSystem) {
        this.requestParam.implantSystem = JSON.stringify(implantForm);
      }
      this.imageBas64Map = totalImageBase64;
      // console.log('this.requestParam', this.requestParam);
      // console.log(setRootCode(totalToothDesign));

      console.log('getRequestParamBeforeSubmit-this.requestParam', this.requestParam)
    },

    clearEditParam() {
      this.sourceToothDesign = []; // 订单原始的牙位信息
      // 租户提交[编辑]是需要传的参数
      this.requestParam = {
        orderCode: 0,
        orderStatus: 0,
        designStatus: 0,
        designCategoryCode: 0,
        paramList: [],
        toothDesign: [],
        imageMap: {},
      };
      this.imageBas64Map = {};
    }
  },
}