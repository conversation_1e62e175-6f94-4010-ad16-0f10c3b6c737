<template>
  <div>
    <adminBoardVue v-if="getRoles()"></adminBoardVue>
    <businessBoardVue v-else></businessBoardVue>
  </div>
</template>

<script>
import adminBoardVue from './components/adminBoard';
import businessBoardVue from './components/businessBoard';
import { ROLE_CODE } from '@/public/constants';
import { mapGetters } from 'vuex';
export default {
  name: 'databoard',
  components: { adminBoardVue, businessBoardVue },
  computed: {
    ...mapGetters(['roles']),
  },
  data() {
    return {
      admin: [50018, 50019], //管理员和系统运营
    };
  },
  methods: {
    getRoles() {
      if (
        this.roles.find((item) => {
          return item.roleCode == ROLE_CODE.ADMIN || item.roleCode == ROLE_CODE.SYSTEM_OPER || item.roleCode == ROLE_CODE.DESIGN_OPERATE;
        })
      ) {
        return true;
      }
      // 判断当前得角色展示哪个看板内容
      return false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
