<template>
  <div class="order-detail">
    <div class="order-detail__top">
      <div class="top-left">
        <go-back @handleGoBack="goToOrderList"></go-back>
        <span>{{ $t('common.centerName') }}&nbsp;&nbsp;>&nbsp;&nbsp;<span>{{ $t('common.orderDetail') }}</span></span>
      </div>
      <div class="top-right">
        <operate-button 
          :canOperate="canOperate"
          :orderStatus="orderStatus" 
          :isEdit="isEdit"
          :designerCode="otherInfo.designerCode"
          :designerTypes="otherInfo.designerTypes"
          :iqcCode="otherInfo.iqcUser"
          :oqcCode="otherInfo.oqcUser"
          :isReturnFromClient="otherInfo.isReturnFromClient"
          :isClientRevokeOrder="event.isClientRevokeOrder"
          :isLoadData="loadData"
          @handleEvent="handleEvent"></operate-button>
      </div>
    </div>
    
    <div class="content" v-loading="loadData">
      <div class="content-top">
        <span style="white-space: pre;">{{ $t('order.detail.number', [orderNumber])  }}</span>
        <count-down 
          :stamp="otherInfo.deliveryTime" 
          :completeTime="baseInfo.finishTime" 
          :createdTime="baseInfo.createdTime" 
          :orderState="orderStatus" 
          :isPage="true"></count-down>
        <!-- 4.3.36常规问题 -->
        <hg-button 
          v-permission="['normalQuestion']"
          v-if="isShowCommonProblem" 
          type="danger-secondary"
          size="middle"
          :class="['common-question-btn', markCommonQuestionOrder && 'is-question']"
          :disabled="markCommonQuestionOrder"
          @click="markAsNormalQuestion">
          <img v-show="!markCommonQuestionOrder" src="@/assets/images/order/common-problem.svg" >
          {{ $t('order.detail.btn.normalQuestion') }}
          <hg-icon v-show="markCommonQuestionOrder" icon-name="icon-finish" font-size="14px"></hg-icon>
        </hg-button>
      </div>
      
      <div ref="contentBox" class="content__scroll">
        <step-bar ref="stepBar" :orderStatus="orderStatus"></step-bar>
        <!-- 原因展示区start -->
        <div ref="reasonBox" class="reason-box" v-if="event.showReasonBox && !isEdit">
          <div class="reason-box-title">
            <span><hg-icon icon-name="iconsend_failed" font-size="13px"></hg-icon>{{ event.returnTitle }}</span>
            <span class="reason-box-title-edit" v-show="event.showEditReasonBtn" @click.prevent.stop="handleEditReturnReason">{{ $t('common.btn.edit') }}</span>
          </div>
          <el-row class="reason-box-content">
            <el-col class="base-info-reback-reason"><span>{{ event.returnReason }}</span></el-col>
            <span v-if="event.isTranslateReturnReason">{{ $t('order.detail.title.mySupply', [event.translateContent]) }}</span>
            <span v-if="event.supplyReasonOrNoPassReason">{{ event.supplyReasonOrNoPassReason }}</span>
          </el-row>
        </div>
        <!-- 原因展示区end -->

        <!-- 申请免单start -->
        <div class="client-need-free-box" v-if="freeInfo.show && freeInfo.type=='approving'">
          <div class="left">{{ $t('order.detail.title.askFreeReason') }}</div>
          <div class="reason">
            <p>{{ freeInfo.reason || $t('common.isNull') }}</p>
            <div class="img-ul">
              <div class="img-li" v-for="url in freeInfo.imageList" :key="url">
                <img :src="url" @click.stop="openView(url, freeInfo.imageList)" alt="">
              </div>
            </div>
          </div>
        </div>
        <!-- 申请免单end -->
        <!-- 拒绝免单start -->
        <div class="client-need-free-box rejectFree-box" v-if="freeInfo.show && freeInfo.type=='rejectappro'">
          <div class="left rejectfree-title"><hg-icon icon-name="iconsend_failed" font-size="13px"></hg-icon>{{$t('order.detail.rejectPass')}}：</div>
          <div class="reason">
            <p>{{ freeInfo.reason }}</p>
            <div class="img-ul">
              <div class="img-li" v-for="url in freeInfo.imageList" :key="url">
                <img :src="url" @click.stop="openView(url, freeInfo.imageList)" alt="">
              </div>
            </div>
          </div>
        </div>
        <!-- 拒绝免单end -->

        <base-info 
          ref="baseInfo"
          :baseInfo="baseInfo" 
          :designSoftwareOptinos="designSoftwareOptinos"
          :remarkImageList="file.remarkImageList" 
          :orderStatus="orderStatus" 
          :clientOrgCode="otherInfo.clientOrgCode" 
          :canOperateQC="canUploadFile"
          :hasMulDesigner="hasMulDesigner"
          :isResponsibleDesigner="isResponsibleDesigner"
          @getTranslateContent="getTranslateContent"></base-info>
        
        <!-- 数据加载好之后再生成卡片 -->
        <tooth-info-box 
          ref="toothInfo" 
          v-if="toothInfoMap && JSON.stringify(toothInfoMap) !== '{}'"
          :sourceMap="toothInfoMap" 
          :sourceToothDesign="sourceToothDesign"
          :orgCode="otherInfo.clientOrgCode" 
          :isEdit="isEdit"
          :canEditOrder="canEditOrder"
          :createdUser="otherInfo.createdUser"
          :categoryList="otherInfo.categoryList"
          :deliveryCode="otherInfo.deliveryCode"
          :designerTypes="otherInfo.designerTypes"
          :implantSystem="orderInfo.implantSystem"
          @beforeSubmit="getRequestParamBeforeSubmit"
          @updateToothInfo="updateToothInfo">
          
        </tooth-info-box>
        
        <!-- 设计完成前 -->
        <origin-file 
          v-if="beforeDesign"
          v-model="isDownloadFile"
          :fileItem="file.originFile"
          :cbctFileList="file.cbctFileList"
          :isResponsibleDesigner="isResponsibleDesigner"
          :orderStatus="orderStatus"
          :iqcCode="otherInfo.iqcUser"
          :clientOrgCode="otherInfo.clientOrgCode"
          :showNewTip="isResponsibleDesigner && orderStatus === ORDER_TYPES.PENDING_DESIGN"
          @handleStartToDesign="handleStartToDesign"></origin-file>

        <!-- 设计开始/之后 -->
        <origin-file 
          v-if="!beforeDesign"
          v-model="isDownloadFile"
          :fileItem="file.originFile"
          :cbctFileList="file.cbctFileList"
          :isResponsibleDesigner="isResponsibleDesigner"
          :iqcCode="otherInfo.iqcUser"
          :orderStatus="orderStatus"
          :clientOrgCode="otherInfo.clientOrgCode"
          @handleStartToDesign="handleStartToDesign"></origin-file>

        <!-- 导板种植方案 -->
        <implant
          v-if="hasImplantType"
          ref="implantUpload"
          :implantsScheme="orderInfo.implantsScheme"
          :clientOrgCode="otherInfo.clientOrgCode" 
          :needUpload="canUploadFile"
          :canCompleteFile="canCompleteFile"
          :hasMulDesigner="hasMulDesigner"
          :orderCode="orderCode"
          :orderStatus="orderStatus"
          @submitSuccess="submitImplant"
        ></implant>

        <!-- 设计结果区 -->
        <!-- 正畸 -->
        <orthodontic-upload 
          v-if="isOrthodontic"
          ref="uploadBox" 
          :clientOrgCode="otherInfo.clientOrgCode" 
          :fileList="orthodonticInfo.fileList"
          :historyFileList="orthodonticInfo.historyList"
          :originPateintImageList="orthodonticInfo.originPateintImageList"
          :needUpload="canUploadFile"
          :needUploadImage="canUploadImage"
          :canCompleteFile="canCompleteFile"
          :standarName="orthodonticInfo.originFileName"
          :hiddenFileBox="hiddenOrthUploadBox"
          :showNewTip="isResponsibleExamine && orderStatus === ORDER_TYPES.PENDING_REVIEW"
          :orderStatus="orderStatus"
          :isReturnFromClient="orthodonticInfo.isReturnFromClient"
          @saveProgramInfo="saveProgramInfo"
          @saveProgramInfoInComplete="saveProgramInfoInComplete"
          @submit="submitCompleteOrth"
          >
          
          <orth-program-title slot="programTitle" :createdTime="orthodonticInfo.programCreatedTime" :titleContent="$t('order.ortho.title.newScheme')">
            <span 
              v-if="!Boolean(orthodonticInfo.isAudit) && [8, 11, ORDER_TYPES.PENDING_REVIEW].includes(orderStatus)"
              class="orth-title_tip" slot="showTip">{{ $t('order.ortho.status.pendingReview') }}</span>
          </orth-program-title>

          <orth-view-card 
            slot="programView"
            :isAudit="Boolean(orthodonticInfo.isAudit)"
            :programStatus="orthodonticInfo.programStatus"
            :isGenerateModel="orthodonticInfo.programGenerateModel"
            :programCode="orthodonticInfo.programCode"
            :orderCode="orderCode"
            :isCheck="orthodonticInfo.isCheck"
            :isResponsibleDesigner="isResponsibleDesigner"
            :isCompleteCanFile="canCompleteFile"
            :htmlTaskId="orthodonticInfo.htmlTaskId"
            :taskId="orthodonticInfo.taskId"
            @init="init"></orth-view-card>

          <design-remark 
            slot="designRemark" 
            v-model="orthodonticInfo.designRemarkContent"
            :canEdit="canUploadImage">
          </design-remark>
          <!-- IPR and 添加附件 未说明，仅给设计师提交 -->
          <orthodontic-design-param 
            v-if="orthodonticInfo.orthoDesignParam"
            slot="orthoDesignParam" 
            ref="orthoDesignParamDom"
            :disabled="!canUploadFile && !canCompleteFile" 
            v-model="orthodonticInfo.orthoDesignParam"></orthodontic-design-param>

          <correct-steps
            v-if="isOrthodontic && (canUploadImage || canCompleteFile)"
            slot="correctSteps"
            ref="orthoInput"
            :upperInputValue="orthodonticInfo.upperInputValue" 
            :lowerInputValue="orthodonticInfo.lowerInputValue">
          </correct-steps>
        </orthodontic-upload>

        <!-- 去掉了beforeDesign -->
        <common-upload-box 
          v-if="!isOrthodontic"
          ref="uploadBox" 
          :clientOrgCode="otherInfo.clientOrgCode" 
          :fileList="file.fileList"
          :needUpload="canUploadFile"
          :needUploadImage="canUploadImage"
          :canCompleteFile="canCompleteFile"
          :hasMulDesigner="hasMulDesigner"
          :standarName="file.originFileName"
          :hiddenFileBox="orderStatus === ORDER_TYPES.DESIGNING && !isResponsibleDesigner"
          :showNewTip="isResponsibleExamine && orderStatus === ORDER_TYPES.PENDING_REVIEW"
          :hasImplantType="hasImplantType"
          :orderInfo="orderInfo"
          @submit="submitCompleteFile">
          <design-remark 
            slot="designRemark" 
            v-model="otherInfo.designRemarkContent"
            :canEdit="canUploadImage">
          </design-remark>
        </common-upload-box>
        <!-- 设计结果区 -->

        <!-- 支架-回填下拉框,4.3.61版本删除掉 -->
        <!-- <div v-if="showFillComp && !isEdit" class="fill-box">
          <fill-component 
            v-for="(item, index) in fillCompList" 
            ref="fillComp"
            :key="index"
            :canDelete="item.canDelete"
            :designCode="item.designCode"
            :parentCode="item.parentCode"
            :initSelectCounts="item.initSelectCounts"
            :initSelectCompList="item.initSelectCompList"
            :orderStatus="orderStatus"></fill-component>
        </div> -->
        <time-line :processList="processList" :clientOrgCode='otherInfo.clientOrgCode'></time-line>
      </div>
    </div>

    <batch-dialog 
      ref="handleAssign" 
      :title="'order.operate.assign'"
      :assignType="assignType" 
      :selectSelection="[{orderCode:orderCode, orgCode: otherInfo.clientOrgCode}]"
      @handelbatchList="init"></batch-dialog>
    <!-- 返单弹框 -->
    <return-reason 
      ref="handleReturn" 
      :dialogInfo="returnDialogInfo" 
      :orderCode="orderCode"
      :fillContent="returnDailogContent" 
      @initOrder="init"
      @getConfrmRebackReason="getConfrmRebackReason">
    </return-reason>

    <transition name="file-tips">
      <div class="result-file-tips"  v-show="showExameTips">
        <span>{{ $t('order.detail.tips.fileIsUpload') }}</span>
        <span class="pointer" @click="scrollToDesignFile">
          {{ $t('order.detail.tips.pleaseToExamine') }}
          <img src="../../../assets/images/order/download_tip.gif" alt="">
        </span>
      </div>
    </transition>
    <!-- 联合修复订单保存常规问题 -->
    <MarkNormalProblem :isShow.sync="showUnionNormal" :lastDesignerTypes="lastDesignerTypes" :orderCode="orderCode" @markSuccess="init"></MarkNormalProblem>
    <!-- 联合修复订单指派 -->
    <BatchLeftDrawer v-if="isUnion" :drawer.sync="showUnionAssign" :selectSelection="selectSelection" @batchSuccessUnion="batchSuccessUnion"></BatchLeftDrawer>
  </div>
</template>

<script>
import { ROUTE_NAME, UPDATE_ORDER_STATUS_TYPE, ORDER_TYPES, FILE_TYPES, RPD_PARENT_CODE, UNKNOWN_CODE, ROUTE_PATH, ORTHODONTIC_PARENT_CODE, ROLE_CODE } from '@/public/constants';
import { mapActions, mapGetters } from 'vuex';
import { parseJson } from '@/public/utils';
import { getToothInfo, getToothInfoMap } from '@/public/utils/order';
import OperateMixin from './mixins/operate';
import UpdateMixin from './mixins/updateOrder';
import OrthOperateMixin from './mixins/orthOperate';

import GoBack from '@/components/GoBack';
import OperateButton from './components/OperateButton';
import CountDown from '@/components/CountDown';
import StepBar from './components/StepBar';
import BaseInfo from './components/BaseInfo';
import ToothInfoBox from './components/ToothInfoBox';
import OriginFile from './components/OriginFile';
import TimeLine from './components/TimeLine';
import BatchDialog from '../list/components/batchDialog';
import ReturnReason from './components/ReturnReason';
import CommonUploadBox from './components/CommonUploadBox';
import DesignRemark from './components/DesignRemark';
import FillComponent from './components/FillComponent';
import OrthodonticUpload from './components/OrthodonticUpload';
import CorrectSteps from './components/CorrectSteps';
import OrthViewCard from './components/OrthodonticUpload/OrthViewCard';
import OrthProgramTitle from './components/OrthodonticUpload/OrthProgramTitle';
import OrthodonticDesignParam from '@/components/OrthodonticDesignParam';
import MarkNormalProblem from './components/MarkNormalProblem.vue';
import BatchLeftDrawer from '../list/components/batchLeftDrawer';
import Implant from './components/Implant'

import { getOrderDetail, getUnCheckOrderForMe, getDesignSoftwareAll } from '@/api/order';
import { saveNewFile, reviseProgramme } from '@/api/order/operate'
import { getDownloadUrl } from '@/api/file';

export default {
  name: 'OrderDetail',
  components: { 
    GoBack, 
    OperateButton, 
    CountDown, 
    StepBar, 
    BaseInfo, 
    ToothInfoBox,
    OriginFile, 
    TimeLine, 
    BatchDialog, 
    ReturnReason, 
    CommonUploadBox,
    DesignRemark,
    // FillComponent,
    OrthodonticUpload,
    CorrectSteps,
    OrthViewCard,
    OrthProgramTitle,
    OrthodonticDesignParam,
    MarkNormalProblem,
    BatchLeftDrawer,
    Implant,
  },
  mixins: [OperateMixin, UpdateMixin, OrthOperateMixin],
  data(){
    return {
      ORDER_TYPES,
      designCenterPath: [
        ROUTE_PATH.ORDER_LIST,
        ROUTE_PATH.MANAGE_USER,
        ROUTE_PATH.DATA_BOARD,
        ROUTE_PATH.ORDER_UNVERIFIED,
      ],
      loadData: false,

      orderCode: '1',
      orderStatus: 0,
      orderNumber: '',
      isResponsibleDesigner: false, // 负责的设计师
      isResponsibleSubmitResult: false,
      isUnion: false,
      showUnionNormal: false,
      isResponsibleExamine: false, // 负责的OQC
      isUnknown: false, //是否为未知订单

      event: { // 事件集合
        returnTitle: this.$t('order.detail.title.returnReason'),
        showEditReasonBtn: false, // 显示编辑[返单原因]
        showReasonBox: false, // 显示[返单理由]区域
        returnReason: '', // 返单原因-设计师/客户返单/IQC拒绝设计师返单/OQC审核不通过用此字段
        // iqcSupplyLabel: '我的补充', // label-[IQC补充] 看看有没有必要保留此字段
        isTranslateReturnReason: false, // 对[返单理由]进行补充（这个才是客户看到的返单内容）
        translateContent: '', // IQC译单内容
        supplyReasonOrNoPassReason: '', // 客户返单时，IQC对其的补充说明; 
        isReturnFromClient: false, // 客户返单
        isClientRevokeOrder: false, // 客户主动撤回订单
      },

      // 免单内容
      freeInfo: {
        show: false,
        type: '',
        reason: '',
        imageList: [],
      },

      file: {
        originFileName: '', // 原始文件名
        originFile: null, // 原始文件，单个
        electroFile: null, // 电子单
        remarkImageList: [], // 客户备注图片
        cbctFileName: '',
        cbctFileList: [],
      },

      // 订单基础信息
      baseInfo: {
        designer: '',
        expectTime: 0,
        createTime: 0,
        client: '',
        finishTime: 0,
        levelText: '',
        clientRemark: '',
        translateContent: '',
        isUpdateClientRemark: false,
        groupQC: 0,
        groupQCName: '',
      },

      otherInfo: {  // 订单其他信息
        clientOrgCode: 0,
        designRemarkContent: '',
      },

      processList: [],
      // paramList: [],
      // parameterContent: '[]',

      // 返单窗口，回填设计师返单内容给IQC
      returnDailogContent: {
        returnReason: '',
        returnImageList: [],
      },

      fillCompList: [], //

      autoScrollHeight: 0,
      showExameTips: false,
      isDownloadFile: false,
      isOrthodontic: false, 
      isAuth: false, // 是否有操作权限的订单

      toothInfoMap: {}, // 牙位信息-包含设计类型+参数方案
      // sourceOrderInfo: {}, // 牙位组件用到的对象
      markCommonQuestionOrder: false, // 标记为[常规问题]
      designSoftwareOptinos: [], //软件列表
      hasMulDesigner: false, // 是否有多个设计师设计联合修复订单
      designerTypes: [], // 当前指派的设计师信息
      lastDesignerTypes: [], // 上次指派的设计师信息
      showUnionAssign: false,
      selectSelection: [],
      orderInfo: {},
      totalToothDesignList: []
    }
  },
  computed: {
    ...mapGetters(['userCode', 'authList', 'designTypeTree', 'roles', 'showTipFromDetail']),
    roleCodeList() {
      return this.roles.map(role => role.roleCode);
    },
    influenceOrderStatusOperTypes() { // 影响订单的操作type
      let operTypes = [];
      Object.keys(UPDATE_ORDER_STATUS_TYPE).forEach(key =>{
        operTypes.push(UPDATE_ORDER_STATUS_TYPE[key]);
      });
      return operTypes;
    },
    canUploadFile() { // 能上传文件
      console.log(this.orderStatus, ORDER_TYPES.DESIGNING, this.isResponsibleDesigner, !this.isResponsibleSubmitResult)
      if(this.orderStatus === ORDER_TYPES.DESIGNING && this.isResponsibleDesigner && !this.isResponsibleSubmitResult) {
        return true;
      }
      return false;
    },
    canUploadImage() { // 能上传截图
      if(this.orderStatus === ORDER_TYPES.DESIGNING && this.isResponsibleDesigner && !this.isResponsibleSubmitResult) {
        return true;
      }else if(this.orderStatus === ORDER_TYPES.PENDING_REVIEW && this.isResponsibleExamine) {
        return true;
      }
      return false;
    },
    // 已完成订单有重新编辑文件的权限
    canCompleteFile(){
      // 订单是已完成已免单，且必须是设计师组长可以
      let roleCodeList = this.roles.map((item) => {return item.roleCode})
      if(roleCodeList.includes(50031) && [8, 11].includes(this.orderStatus)){
        return true
      }
      return false
    },
    beforeDesign() { // 不显示设计文件的状态
      const beforeDesignStatusList = [
        ORDER_TYPES.PENDING_TRANSLATE, // 待译单
        ORDER_TYPES.PENDING_ACCEPT, // 待指派
        ORDER_TYPES.PENDING_DESIGN, // 待设计
        ORDER_TYPES.PENDING_RETURN, // 待退回
        ORDER_TYPES.RETURNED // 已退回
      ];
      return beforeDesignStatusList.includes(this.orderStatus);
    },

    showFillComp() {
      if(this.orderStatus === ORDER_TYPES.DESIGNING && this.isResponsibleDesigner) {
        return true;
      } else if(this.orderStatus === ORDER_TYPES.PENDING_REVIEW && this.isResponsibleExamine) {
        return true;
      }
      return false;
    },

    /* categoryList() {
      let categoryList = [];
      this.designTypeTree.forEach(item => {
        const { designCode, enName, level, cnName, iconUrl, hasParas } = item;
        if(designCode !== UNKNOWN_CODE) {
          categoryList.push({ designCode, enName, level, cnName, iconUrl, hasParas });
        }
      });
      return categoryList;
    }, */
    canEditOrder() { // 待译单、待指派、待设计，且有编辑权限，打开页面就预加载牙位图:4.3.13 待审核也可以
      const orderStatusList = [
        ORDER_TYPES.PENDING_TRANSLATE, 
        ORDER_TYPES.PENDING_ACCEPT, 
        ORDER_TYPES.PENDING_DESIGN, 
        ORDER_TYPES.PENDING_RETURN, 
        ORDER_TYPES.PENDING_REVIEW,
      ];
      // 设计师&组长，设计中可以编辑
      if(this.authList.includes('edit')) {
        if(this.orderStatus === ORDER_TYPES.DESIGNING) { // 设计中
          return this.isResponsibleDesigner || this.roleCodeList.includes(ROLE_CODE.DESIGN_LEADER);
        }else {
          return orderStatusList.includes(this.orderStatus);
        }
      }
      return false;
    },

    showLeaveTip() {
      const isDownloadFile = this.hasMulDesigner ? !this.isResponsibleSubmitResult : !this.isDownloadFile
      const showLeaveTip = this.isResponsibleDesigner && this.orderStatus === ORDER_TYPES.DESIGNING && isDownloadFile;
      return showLeaveTip;
    },
    // 是否隐藏正畸上传模块
    hiddenOrthUploadBox() {
      if(this.orderStatus == ORDER_TYPES.DESIGNING) { // 设计中，设计师
        return !this.isResponsibleDesigner;
      }else if(this.orderStatus === ORDER_TYPES.PENDING_REVIEW) { // 待审核 OQC
        return !this.isResponsibleExamine; 
      } else if([8, 11].includes(this.orderStatus) && this.roles.map((item) => { return item.roleCode}).includes(50031)){ // 已完成订单设计师组长并且有一条方案是已完成阶段生成的待审核状态
        console.log('this.orthodonticInfo: ', this.orthodonticInfo, !this.orthodonticInfo.isAudit, this.orthodonticInfo.isCompleted)
        if(!this.orthodonticInfo.isAudit && this.orthodonticInfo.isCompleted){
          return false;
        }
        return true;
      }
      return true;
      // 20240329版本改成都不隐藏
      // return false;
    },

    canOperate() {
      return this.isAuth;
    },

    otherDesignerType() {
      if (this.designerTypes) {
        const codeList = this.designerTypes.filter(item => item.designUser !== this.userCode).map(item => {
          const designTypes = JSON.parse(item.designTypes)
          if (designTypes) {
            return designTypes.map(item => item.code)
          }
          return null
        });
        return codeList.flat()
      }
      return []
    },

    isShowCommonProblem() {
      // 订单状态为待审核或者已完成
      if (this.orderStatus === ORDER_TYPES.PENDING_REVIEW) {
        // 订单存在客户返单或OQC撤回
        const isRetrun = (this.orderInfo.returnTimes && this.orderInfo.returnTimes > 0) || this.orderInfo.isWithdraw
        return this.isResponsibleExamine && isRetrun
      } else if (this.orderStatus === ORDER_TYPES.COMPLETED) {
        return this.isResponsibleExamine
      }
      return false
    },

    hasImplantType({ orderInfo }) {
      if (orderInfo.toothDesign) {
        const { isUnion, designerTypes, toothDesign } = orderInfo
        const totalToothDesign = JSON.parse(toothDesign);
        if (isUnion && designerTypes) {
          const myDesignItem = designerTypes.find(item => item.designUser === this.userCode)
          if (myDesignItem) {
            const myDesignItemTypes = JSON.parse(myDesignItem.designTypes)
            const myDesignItemTypeCodes = myDesignItemTypes.map(item => item.code)
            console.log('myDesignItemTypeCodes', myDesignItemTypeCodes)
            if (this.totalToothDesignList.length) {
              return this.totalToothDesignList.some(item => item.code === 23601)
            }
            if (myDesignItemTypeCodes.some(item => item === 23601)) {
              return totalToothDesign && totalToothDesign.some(item => [23601].includes(item.code));
            } else {
              return false
            }
          } else {
            return totalToothDesign && totalToothDesign.some(item => [23601].includes(item.code));
          }
        } 
  
        if (this.totalToothDesignList.length) {
          return this.totalToothDesignList.some(item => item.code === 23601)
        }
        return totalToothDesign && totalToothDesign.some(item => [23601].includes(item.code));
      }
      return false;
    }
  },
  watch: {
    showLeaveTip(show, oldShow) {
      if(show !== oldShow) {
        this.updateTipFromDetail(show);
      }
    },
  },
  beforeRouteLeave(to, from, next) {
    if(to.path !== ROUTE_PATH.ORDER_DETAIL && this.designCenterPath.includes(to.path) && this.showLeaveTip) {
      const cancelFn = () => { next(false); };
      const confirmFn = () => { this.updateTipFromDetail(false); next(); }
      setTimeout(() => {
        this.$askBeforeLeaveDetail(this.showLeaveTip, cancelFn, confirmFn);
      }, 100);
    }else {
      next();
    }
    
  },
  mounted() {
    window.onbeforeunload = this.handleOnbeforeunload;
    this.checkRouteData();

    if(this.roleCodeList.includes(ROLE_CODE.OQC) && this.$route.query && this.$route.query.auth === 'Y') { // 限定绑定事件的条件
      document.addEventListener('keydown', this.onKeydown);
    }

    if(this.$route.query && this.$route.query.auth === 'Y') { // 未返回响应时，先从路由进行判断
      this.isAuth = true;
    }
  },
  methods: {
    ...mapActions(['updateTipFromDetail']),

    updateToothInfo(toothDesignList) {
      console.log('toothDesignList', toothDesignList)
      this.totalToothDesignList = toothDesignList
    },

    handleEvent({ funtionName, btnItem }) {
      try {
        this[funtionName](btnItem);
      } catch (error) {
        this.$hgOperateFail();
        console.log('该操作未定义',error);
      }
    },

    checkRouteData(){
      const { query } = this.$route;
      if(query && JSON.stringify(query) !== '{}' && query.id) {
        this.orderCode = query.id;
        this.init();
      }else {
        this.$hgOperateFail(this.$t('order.detail.tips.orderIdIsNull'));
        this.goToOrderList();
      }
    },
    goToOrderList(){
      if(this.canOperate) {
        this.$router.push({name: ROUTE_NAME.ORDER_LIST});
      }else {
        this.$router.push({name: ROUTE_NAME.ORDER_UNVERIFIED});
      }
    },

    // 普通订单4.3.61已完成状态设计师组长再次上传设计结果的方法
    async submitCompleteFile(type, obj){
      if(type == 'cancel'){
        this.init()
      }
      // 成功要调接口后再init一次
      if(type == 'success'){
        if(!this.verifyUploadList()) { return; }
        let fileList = this.$refs.uploadBox.getCompList();
        let orderFiles = []
        orderFiles = fileList.flatMap(item => 
          item.fileList.map(file => ({
            fileName: file.fileName || '',
            filePath: file.filePath || '',
            fileSize: file.fileSize || 0,
            fileTime: 0,
            fileType: file.fileType || 0
          }))
        );
        console.log(orderFiles, 77777)
        let parames = {
          orderCode: this.orderCode,
          orderFiles: orderFiles
        }
        const {code, data} = await saveNewFile(parames);
        if(code == 200){
          this.$refs.uploadBox.resetValue();
        }
      }
    },
    // 正畸订单4.3.61已完成状态设计师组长再次上传设计结果生成正畸方案的方法
    async submitCompleteOrth(type, obj){
      if(type == 'delete'){
        this.init()
      } else if(type == 'success') {
        if(!this.verifyOrthoInfo()) { 
          return;
        }
        const { code, data} = await reviseProgramme({orderCode: this.orderCode,programmeCode: this.orthodonticInfo.programCode});
        if(code == 200){
          this.init();
        }
      }
    },
    init() {
      this.loadData = true;
      this.isEdit = false;
      getOrderDetail(this.orderCode).then(res => {
        this.clear();
        this.$nextTick(() => { // 解决请求前clear()导致的闪烁问题
          const { data } = res;
          this.orderInfo = data || {};
          this.initAllData(data);
        });
      }).finally(() => {
        this.loadData = false;
      });
    },

    async initAllData(data) {
      try {
        let { orderNo: orderNumber, orderStatus, designUser, designerTypes, iqcUser, oqcUser, designUserName:designer, createdTime, 
            orgName:client, orgCode:clientOrgCode, confirmTime:finishTime, levelText, remark:clientRemark, translate:translateContent,
          remarkUpdated: isUpdateClientRemark, processRecord:processList, designParas, isCusReturn, cusReason: clientReturnReason, toothImage, 
          toothDesign, orderFiles, designStatus, deliveryTime, designRemark: designRemarkContent, designCategory:designCategoryCode, 
          rpdTypes:fillConetnt, createdUser, patientName, unauthorized, deliveryCode, isCommonQuestions: markCommonQuestionOrder, groupQc, groupQcName, designSoftware, softwareVersion, isUnion, lastDesignerTypes } =  data;

        this.isAuth = !unauthorized; // 值是未认证，所以取反

        processList.reverse(); // 反转一下过程记录
        this.orderNumber = orderNumber;
        this.orderStatus = orderStatus;
        // this.isResponsibleDesigner = designUser === this.userCode;
        this.designerTypes = designerTypes
        this.lastDesignerTypes = lastDesignerTypes
        console.log('this.lastDesignerTypes: ', this.lastDesignerTypes);
        this.isUnion = isUnion
        this.isResponsibleDesigner = designerTypes ? designerTypes.some(item => item.designUser === this.userCode) : designUser === this.userCode;
        // 当订单是联合修复订单且当前设计师已提交设计结果时为true
        if (designerTypes) {
          this.isResponsibleSubmitResult = designerTypes.find(item => item.designUser === this.userCode)?.isDesigned
          console.log('this.isResponsibleSubmitResult: ', this.isResponsibleSubmitResult);
        }
        this.isResponsibleExamine = oqcUser === this.userCode;
        this.markCommonQuestionOrder = Boolean(markCommonQuestionOrder); // 是否标记常规问题
        // this.isUnknown = (parseJson(toothDesign) || []).some(item => item.code === UNKNOWN_CODE); // 包含未知类型都不允许[入检通过]
        const toothDesignList = parseJson(toothDesign) || [];
        if(toothDesignList.some(item => item.code === UNKNOWN_CODE)) {
          this.isUnknown = true;
        }else {
          this.isOrthodontic = toothDesignList.some(item => item.pidCode === ORTHODONTIC_PARENT_CODE);
        }

        this.isOrthodontic = toothDesignList.some(item => item.pidCode === ORTHODONTIC_PARENT_CODE);

        this.baseInfo = { designer, expectTime: deliveryTime, createdTime, client, finishTime, levelText, clientRemark, translateContent,
          isUpdateClientRemark, patientName, groupQC: groupQc === 0 ? '': groupQc, groupQCName: groupQcName, designSoftware: designSoftware === 0 ? '' : designSoftware,
          softwareVersion: softwareVersion === 0 ? '' : softwareVersion, designerTypes };

        if (designerTypes) {
          const designItem = designerTypes.find(item => item.designUser === this.userCode)
          if (designItem) {
            this.baseInfo.groupQC = designItem.groupQc === 0 ? '' : designItem.groupQc
            this.baseInfo.groupQCName = designItem.groupUserName
          } else {
            const nameArr = []
            designerTypes.forEach(item => {
              if (!nameArr.includes(item.groupUserName)) {
                nameArr.push(item.groupUserName)
              }
            })
            this.baseInfo.groupQCName = nameArr.join(',')
          }
        }
        
        const categoryCodes = typeof designCategoryCode === 'string' ? designCategoryCode : String(designCategoryCode);
        const categoryList = categoryCodes.split(',');
        this.otherInfo = { clientOrgCode, deliveryCode, toothImage, toothDesignList, designStatus, deliveryTime, 
          isReturnFromClient: Boolean(isCusReturn), designRemarkContent, designerCode: designUser, designerTypes, iqcUser, oqcUser, designCategoryCode, 
          toothInfo: getToothInfo(parseJson(toothDesign)), createdUser, categoryList, };

        this.processList = [...processList];
        const initReturnParam = {
          processList,
          orderStatus,
          isReturnFromClient: isCusReturn === 1,
          clientReturnReason,
          isResponsibleDesigner: this.userCode === designUser
        };
        this.initReturnBoxContent(initReturnParam);

        if(this.isOrthodontic) {
          this.handleOrthFileAndInfo(orderFiles);
        }else {
          this.classifyFile(orderFiles);
        }
        this.initFillComp(parseJson(toothDesign), fillConetnt);

        /* 获取参数start */
        const totalParamList = designParas ? designParas.map(item => {
          const { program, parameter, software, designCode } = item;
          const programList = parseJson(program) || [];
          const paramItem = parseJson(parameter) || {};
          const softKey = typeof software === 'string' ? software : String(software);
          let paramList = paramItem[softKey] || [];

          if(paramList.constructor !== Array) {
            paramList = [];
          }
          return {
            program: programList,
            parameter: paramList,
            designCode,
            software,
          }
        }) : [];
        this.parameterContent = JSON.stringify(totalParamList);
        /* 获取参数end */

        // const myAssignDesignCodes = designerTypes ? designerTypes.filter(item => item.designUser === this.userCode).map(item => JSON.parse(item.designTypes)[0].code) : []
        const otherAssignDesignCodes = this.otherDesignerType
        const hasMulDesigner = designerTypes ? designerTypes.length > 1 : false
        this.hasMulDesigner = hasMulDesigner
        // 4.3.36 新布局的牙位信息值
        const toothInfoMap = getToothInfoMap({
          sourceToothDesign: toothDesignList, 
          paramList: totalParamList, 
          toothImage, 
          categoryList, 
          hasMulDesigner, 
          isResponsibleDesigner: this.isResponsibleDesigner,
          otherAssignDesignCodes,
          implantSystem: data.implantSystem
        });
        categoryList.forEach(key => {
          const data = toothInfoMap[key];
          this.$set(this.toothInfoMap, key, data);
        });

        console.log('initAllData-this.toothInfoMap', this.toothInfoMap)
        // this.sourceOrderInfo = { deliveryCode, categoryList, createdUser, }; 

        this.$nextTick(() => {
          if(this.isResponsibleExamine && orderStatus === ORDER_TYPES.PENDING_REVIEW) {
            this.showExameTips = true;
            this.$refs.contentBox.addEventListener('scroll', this.scrollEvent);
          }
          this.autoScrollHeight = this.getScrollHeight();
        });
        // 获取所有的设计软件列表
        const designSoftwareOptinos = await getDesignSoftwareAll();
        this.designSoftwareOptinos = designSoftwareOptinos.data || [];
      } catch (error) {
        this.$hgOperateFail(this.$t('order.detail.tips.loadInfoFail'));
        console.log('订单初始化ERROR：', error);
      }
    },

    /**
     * 对返回的文件列表进行分类
     * @param {array} sourceList 文件列表
     */
    classifyFile(sourceList = []) {
      console.log('classifyFile-sourceList', sourceList)
      if( !sourceList || sourceList.length === 0 ) { return; }

      const originFile = sourceList.find(item => item.fileType === FILE_TYPES.ORIGIN_FILE);
      if(originFile) {
        this.file.originFile = originFile;
        const tempName = originFile.fileName.split('.');
        tempName.pop();
        this.file.originFileName = tempName.join('.');
      }

      if (sourceList.some(item => item.fileType === FILE_TYPES.CBCT_FILE)) {
        this.file.cbctFileList = sourceList.filter(item => item.fileType === FILE_TYPES.CBCT_FILE);
      }

      const remarkImageList = sourceList.filter(item => item.fileType === FILE_TYPES.REMARK_IMAGE);
      this.file.remarkImageList = remarkImageList;
      const designFileTypes = [FILE_TYPES.SCREENSHOT, 
        FILE_TYPES.DESIGN_MODEL, FILE_TYPES.DESIGN_FILE, FILE_TYPES.DESIGN_VIDEO, 
        FILE_TYPES.PROSPECTUS, FILE_TYPES.OTHER_FILE, FILE_TYPES.DRILL_FILE];
      this.file.fileList = sourceList.filter(item => designFileTypes.includes(item.fileType));
    },
    
    /** //TODO  待优化
     * 初始化返单box区域的内容
     * @param {array} processList 过程记录
     * @param {number} orderStatus  订单状态
     * @param {boolean} isReturnFromClient  是否客户返单
     * @param {string} clientReturnReason 客户返单原因
     * @param {boolean} isResponsibleDesigner 是否负责的设计师
     */
    initReturnBoxContent({processList = [], orderStatus, isReturnFromClient, clientReturnReason, isResponsibleDesigner}) {
      if(!processList || processList.length === 0) return;
      const recordList = processList.filter(item => this.influenceOrderStatusOperTypes.includes(item.type));
      if(recordList.length === 0) return;

      const { type, content, reasonImage } = recordList[0];

      if(type === UPDATE_ORDER_STATUS_TYPE.RETURN_BY_CLIENT && isReturnFromClient) { //最新操作[客户返单]+isReturnFromClient
        this.event.isReturnFromClient = true;
      }

      if( orderStatus === ORDER_TYPES.RETURNED && type === UPDATE_ORDER_STATUS_TYPE.REVOKE_ORDER_BY_CLIENT) { // 2022.08.16 [已退回]，最新操作是客户主动撤回
        this.event.isClientRevokeOrder = true;
      }
      
      // 这里与原逻辑不一样：待译单 入检通过权限 试试看能不能更适配
      if(orderStatus === ORDER_TYPES.PENDING_TRANSLATE 
        && this.authList.includes('translate') 
        && isReturnFromClient 
        && type === UPDATE_ORDER_STATUS_TYPE.RETURN_BY_CLIENT) { // [入检通过] & 客户返单 & 最新操作[客户返单] 
        
        this.event.showReasonBox = true;
        this.event.returnTitle = this.$t('order.detail.title.clientReturn');
        this.event.returnReason = clientReturnReason;
      
      // 这里与原逻辑不一样：[待退回] 确认返单 试试看能不能更适配
      }else if(orderStatus === ORDER_TYPES.PENDING_RETURN 
        && this.authList.includes('confirmReback') 
        && [UPDATE_ORDER_STATUS_TYPE.REBACK_TO_IQC, UPDATE_ORDER_STATUS_TYPE.REVOKE_RETURNORDER].includes(type)) { // [确认返单] & 最新操作[设计师返单]或者[从客户处撤回]

        this.event.showReasonBox = true;
        this.event.showEditReasonBtn = true;
        this.event.returnTitle = this.$t('order.detail.title.designerReturn');
        this.event.returnReason = content;

        // 要在这里确定当前是[待退单]，获取文件内容，并且回填设计师返单信息
        if(type !== UPDATE_ORDER_STATUS_TYPE.REBACK_TO_IQC) { // IQC有撤回过，就要找最新的内容填充
          const returnItem = processList.find(item => item.type === UPDATE_ORDER_STATUS_TYPE.REBACK_TO_IQC); // 返回第一个
          if(returnItem) {
            const { content, reasonImage } = returnItem;
            const returnImageList = parseJson(reasonImage) || [];
            this.returnDailogContent.returnReason = content;
            this.returnDailogContent.returnImageList = returnImageList;
            this.event.returnReason = content;
          }
        }else {
          const returnImageList = parseJson(reasonImage) || [];
          this.returnDailogContent.returnReason = content;
          this.returnDailogContent.returnImageList = returnImageList;
        }
      
      } else if( isReturnFromClient 
        && [ORDER_TYPES.PENDING_DESIGN, ORDER_TYPES.DESIGNING].includes(orderStatus) 
        && isResponsibleDesigner 
        && type === UPDATE_ORDER_STATUS_TYPE.CONTINUE_DESIGN) {// 设计师：客户返单；待设计、设计中；最新操作:[继续设计]；

        this.event.showReasonBox = true;
        this.event.returnTitle = this.$t('order.detail.title.clientReturnReason');
        this.event.returnReason = clientReturnReason;
        this.event.supplyReasonOrNoPassReason = this.$t('order.detail.title.iqcSupply') + content;
      
      } else if ([ORDER_TYPES.PENDING_DESIGN, ORDER_TYPES.DESIGNING].includes(orderStatus) 
        && isResponsibleDesigner 
        && type === UPDATE_ORDER_STATUS_TYPE.CONTINUE_DESIGN) { // 设计师：待设计、设计中；最新操作：[继续设计]
        
        this.event.showReasonBox = true;
        this.event.returnTitle = this.$t('order.detail.title.iqcReject');
        this.event.returnReason = content;
      }

      if(isResponsibleDesigner && type === UPDATE_ORDER_STATUS_TYPE.NOT_PASS) { // 审核不通过
        this.event.showReasonBox = true;

        if(isReturnFromClient) {
          this.event.returnTitle = this.$t('order.detail.title.clientReturnReason');
          this.event.returnReason = clientReturnReason;
          this.event.supplyReasonOrNoPassReason = this.$t('order.detail.title.notPass') + '：' + content;

        }else {
          this.event.returnTitle = this.$t('order.detail.title.notPass');
          this.event.returnReason = content;
        }
      }

      // 设计中，客户返单-最新操作记录是【继续设计】
      if(isResponsibleDesigner && this.orderStatus === ORDER_TYPES.DESIGNING && type === UPDATE_ORDER_STATUS_TYPE.CONTINUE_DESIGN) {
        this.orthodonticInfo.isReturnFromClient = true;
      }else {
        this.orthodonticInfo.isReturnFromClient = false;
      }

      //4.3.13 申请免单：状态：申请免单、角色：设计运营、操作：最新一条是申请免单
      if(this.orderStatus === ORDER_TYPES.REQUEST_FREE && this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) && type === UPDATE_ORDER_STATUS_TYPE.CLINET_ASK_FOR_FREE) {
        const imageList = parseJson(reasonImage) || [];
        this.freeInfo.show = true;
        this.freeInfo.type = 'approving'
        this.freeInfo.reason = content;
        this.freeInfo.imageList = imageList;
        this.handleImage(imageList);
      }
      //免单不通过
      if(this.orderStatus === ORDER_TYPES.PENDING_CONFIRM && this.roleCodeList.includes(ROLE_CODE.DESIGN_OPERATE) && type === UPDATE_ORDER_STATUS_TYPE.DISAPPROVE_FOR_FREE){
        const imageList = parseJson(reasonImage) || [];
        this.freeInfo.show = true;
        this.freeInfo.type = 'rejectappro'
        this.freeInfo.reason = content;
        this.freeInfo.imageList = imageList;
        this.handleImage(imageList);
      }

    },

    async handleImage(imageS3List = []) {
      if(imageS3List.length === 0) return;

      let reqList = [];
      imageS3List.forEach(s3FileId => {
        const req = new Promise(resovle => {
          const param = {
            s3FileId,
            orgCode: this.clientOrgCode,
            filename: '',
          };
          getDownloadUrl(param, true).then(res => {
            resovle(res.data.url);
          }).catch(err => {});
        });
        reqList.push(req);

      });

      await Promise.all(reqList).then(res => {
        this.freeInfo.imageList = res;
      });
    },

    openView(url, imageList) {
      const index = imageList.findIndex(img => img === url);
      this.$hgViewer.open({
        imgList: imageList,
        initialViewIndex: index,
      });
    },

    /**
     * 初始化回填组件信息
     */
    initFillComp(toothDesignList,fillConetnt) {
      if(toothDesignList.length === 0) { return; }
      
      let compList = [];

      if(fillConetnt) {  // 审核/打回重新设计
        const fillValueList = fillConetnt.split(',');
        const rpdItem = toothDesignList.find(item => item.pidCode === RPD_PARENT_CODE);
        if(rpdItem) {
          const { pidCode, code } = rpdItem;
          let compItem = {
            canDelete: true,
            designCode: code,
            parentCode: pidCode,
            initSelectCompList: [],
          };

          const initSelectCompList = fillValueList.map((item, index) => {
            return {
              key: index,
              isSelect: false,
              value: item,
            }
          });

          compItem.initSelectCompList = initSelectCompList;
          compList.push(compItem);
        }

      }else {
        // 第一版只有支架回填
        const rpdList = toothDesignList.filter(item => item.pidCode === RPD_PARENT_CODE);
        if(rpdList.length > 0) {
          const { pidCode, code } = rpdList[0];
          const toothCount = rpdList.reduce((count, curItem) => {
            if(curItem.tooth) {
              count += curItem.tooth.length;
            }
            return count;
          },0);

          compList.push({
            canDelete: true,
            designCode: code,
            parentCode: pidCode,
            initSelectCounts: toothCount,
          });
        }
      }

      this.fillCompList = compList;
      
    },

    /**
     * 清除数据
     */
    clear() {
      this.orderStatus = 0;
      this.orderNumber = '';
      this.isResponsibleDesigner = false; // 负责的设计师
      this.isResponsibleExamine = false; // 复制的OQC
      this.isUnknown = false;

      this.event = { // 事件集合
        returnTitle: this.$t('order.detail.title.returnReason'),
        showEditReasonBtn: false, // 显示编辑[返单原因]
        showReasonBox: false, // 显示[返单理由]区域
        returnReason: '', // 返单原因-设计师/客户返单/IQC拒绝设计师返单/OQC审核不通过用此字段
        // iqcSupplyLabel: '我的补充', // label-[IQC补充] 看看有没有必要保留此字段
        isTranslateReturnReason: false, // 对[返单理由]进行补充（这个才是客户看到的返单内容）
        translateContent: '', // IQC译单内容
        supplyReasonOrNoPassReason: '', // 客户返单时，IQC对其的补充说明; 
        isReturnFromClient: false, // 客户返单
        isClientRevokeOrder: false, 
      };

      this.file = {
        originFileName: '', // 原始文件名
        originFile: null, // 原始文件，单个
        electroFile: null, // 电子单
        remarkImageList: [], // 客户备注图片
      };

      // 订单基础信息
      this.baseInfo = {
        designer: '',
        expectTime: 0,
        createTime: 0,
        client: '',
        finishTime: 0,
        levelText: '',
        clientRemark: '',
        translateContent: '',
        isUpdateClientRemark: false,
      };

      this.otherInfo = {  // 订单其他信息
        clientOrgCode: 0,
        designRemarkContent: '',
      };

      this.processList = [];
      // this.paramList = [];
      // this.parameterContent = '[]';

      // 返单窗口，回填设计师返单内容给IQC
      this.returnDailogContent = {
        returnReason: '',
        returnImageList: [],
      };

      this.freeInfo = {
        show: false,
        reason: '',
        imageList: [],
      };

      this.fillCompList = []; //
      this.showExameTips = false;
      this.isDownloadFile = false;
      this.markCommonQuestionOrder = false;
      this.updateTipFromDetail(false);

      this.isOrthodontic = false;
      this.toothInfoMap = {};
      // this.sourceOrderInfo = {};
      this.clearOrthInfo();
    },

    // 跳转到指定位置  start
    scrollToDesignFile(){

      // 高度要重新计算了
      this.showExameTips = false;
      const height = this.autoScrollHeight;
      const startHeight = this.$refs.contentBox.scrollTop || 0;
      
      for(let i= Math.floor(startHeight/10); i< Math.floor(height/10); i++){
        setTimeout(() => {
          this.scrollTop(i*10);
        }, i*3);
      }
    },
    scrollTop(height){
      this.$refs.contentBox.scrollTop = height;
    },
    getScrollHeight(){ // 需要滚动到的高度
      const stepDom = this.$refs.stepBar.$el;
      const infoDom = this.$refs.baseInfo.$el;
      const schemeDom = this.$refs.toothInfo?.$el;

      let height = Math.floor(this.$refs.contentBox.offsetTop) + 68;

      if(stepDom){
        height += stepDom.clientHeight;
      }
      if(infoDom){
        height += infoDom.clientHeight;
      }
      if(schemeDom){
        height += schemeDom.clientHeight;
      }
      return height;
    },
    scrollEvent() {
      if(this.showExameTips === false) return;
      const scrollHeight = this.$refs.contentBox.scrollTop + 450;

      if(this.autoScrollHeight < scrollHeight){
        this.showExameTips = false;
      }
    },
    // 跳转到指定位置  end
    // 刷新页面时，当前页面有更新会调起浏览器的提示
    handleOnbeforeunload(){
      if(this.showTipFromDetail){
        return '弹出提示';
      }
    },

    /**
     * 没有数据了则提示：当前已无需要检查的订单
     * 4.3.19：OQC进入任意订单详情都可以通过上下键来快速访问自己负责审核的订单
     */
    onKeydown(e) {
      const keyType = e.key;
        
      if(['ArrowUp', 'ArrowDown'].includes(keyType)) {
        e.preventDefault();
        let param = {
          orderCode: this.orderCode,
          operateType: 2,
        };

        if(keyType === 'ArrowUp') {
          param.operateType = 1;
        }

        this.loadData = true;
        getUnCheckOrderForMe(param).then(res => {
          if(res.data) {
            this.clear();
            this.$nextTick(() => { // 解决请求前clear()导致的闪烁问题
              const { data } = res;
              this.orderCode = data.orderCode;
              this.initAllData(data);
              this.$router.push({ name: ROUTE_NAME.ORDER_DETAIL, query: { auth:'Y', id: data.orderCode } });
            });
          }else {
            this.$hgOperateFail(this.$t('order.detail.tips.noUnExamineOrder'));
          }
        }).finally(() => {
          this.loadData = false;
        });
      } 
    },

  },
  beforeDestroy() {
    this.$refs.contentBox.removeEventListener('scroll', this.scrollEvent);
    window.onbeforeunload = null;
    this.updateTipFromDetail(false);
    document.removeEventListener('keydown', this.onKeydown);
  },
}
</script>

<style lang="scss" scoped>
@import './styles/index.scss';
</style>

<style lang="scss">
@import './styles/global.scss';
</style>