<template>
  <el-popover
    placement="top-start"
    popper-class="addition-box-popover" 
    trigger="manual" 
    :visible-arrow="false" 
    v-model="showPopover">
    <div class="input-box">
      <div class="header">
        <span>{{ $t('component.ortho.additionTitle') }}</span>
        <hg-icon icon-name="hg-icon-im-close" iconfont-name="hg-common-iconfont" font-size="24px" @click="onClose"></hg-icon>
      </div>
      <div class="content">
        <div class="value">
          <i18n path="component.ortho.inputStep">
            <el-input slot="0" type="number" v-model="dataList[0].value" placeholder="1~99" @input="onInput(dataList[0])"></el-input>
          </i18n>
        </div>
        <p class="error">{{ dataList[0].error }}</p>
        <div class="value" v-if="showSecondInput">
          <i18n path="component.ortho.inputStep">
            <el-input slot="0" type="number" v-model="dataList[1].value" placeholder="1~99" @input="onInput(dataList[1])"></el-input>
          </i18n>
        </div>
        <div v-else class="value show-second" @click="showSecondInput=true">
          {{ $t('component.ortho.btnAddStep') }}
        </div>
        <p class="error">{{ dataList[1].error }}</p>
      </div>

      <div class="footer-btn">
        <hg-button v-show="hasInput" type="secondary" @click="onDelete">{{ $t('common.btn.delete') }}</hg-button>
        <hg-button @click="onConfirm">{{ $t('common.btn.confirm') }}</hg-button>
      </div>
    </div>
    <!-- 按钮 -->
    <el-tooltip effect="dark" slot="reference" placement="top-start" popper-class="addition-tooltip-content" :disabled="!hasInput">
      <!-- hover时的内容 -->
      <div slot="content">
        <span>{{ showContent }}</span>
      </div>
      <p 
        :class="{'addition-btn_reference':true, 'is-active': showPopover, 'is-finish': hasInput, 'is-disable': disabled }" 
        @click="openInputBox">{{ inputData.number }}</p>
    </el-tooltip>
  </el-popover>
</template>

<script>
/* 最多只允许两步，先简单处理 */
export default {
  model: {
    prop: 'valueList',
    event: 'update',
  },
  props: {
    inputData: {
      type: Object,
      default() {
        return {
          steps: []
        }
      }
    },
    limitNumber: { // 最少有1个
      type: Number,
      default: 1
    },
    disabled: Boolean,
    valueList: Array,
  },
  data() {
    return {
      sourceSteps: [],
      dataList: [{value: '', error: ''},{value: '', error: ''}],
      showSecondInput: false,
      showPopover: false,
    }
  },
  computed: {
    hasInput() {
      return this.valueList.length > 0;
    },

    showContent() {
      const content = this.valueList.map(item => {
        return this.$t('component.ortho.inputStep', [item]);
      });
      return content.join('；');
    },

    validValue() { //有效值
      const valueList = this.dataList.filter(item => item.value > 0 && item.value < 100).map(item => item.value);
      return valueList;
    },
  },
  watch: {
    showPopover(show) {
      if(show) {
        const { steps } = this.inputData;
        this.sourceSteps = steps;
        steps.forEach((item, index) => {
          this.dataList[index].value = item;
        });
        this.showSecondInput = steps.length > this.limitNumber;
      }else {
        this.init();
      }
    },
  },
  methods: {
    openInputBox() {
      if(this.disabled) {
        return;
      }
      this.showPopover = true;
    },

    onInput(item) {
      const { value } = item;
      if(isNaN(value) || value > 99 || (value !== '' && value < 1) || !(Number.isInteger(Number(value)))) {
        item.error = this.$t('component.ortho.tips.limitValue');
        item.value = '';
      }else {
        item.error = '';
      }
    },

    // 关闭不保留
    onClose() {
      this.$emit('update', this.sourceSteps);
      this.showPopover = false;
    },

    // 删除
    onDelete() {
      const title = this.$t('common.systemTips');
      const message = this.$t('component.ortho.tips.addTip', [this.inputData.number]);

      this.$confirm(message, title, {
        confirmButtonText: this.$t('common.btn.confirm'),
        cancelButtonText: this.$t('common.btn.cancel'),
        distinguishCancelAndClose: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {

        this.$emit('update', []);
        this.showPopover = false;

      }).catch((action) => {});   
    },

    onConfirm() {
      if(this.beforeConfirm()) {
        return;
      }
      const valueList = this.validValue.map(item => String(Number(item))); // 去除多余的0和多余的小数点.0
      this.$emit('update', valueList);
      this.showPopover = false;
    },

    // 错误
    beforeConfirm() {
      let isError = false;
      if(this.validValue.length === 0) { // 没有填
        isError = true;
        this.dataList[0].error = this.$t('component.ortho.tips.isEmpty');
      }else {
        this.dataList.forEach(item => {
          item.error = '';
        });
      }
      return isError;
    },

    init() {
      this.showSecondInput = false;
      this.dataList = [{value: '', error: ''},{value: '', error: ''}];
    }
  }
}
</script>

<style lang="scss">
.el-popover.addition-box-popover {
  padding: 0;
  width: 320px;

  .header {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px;
    line-height: 24px;

    &>span {
      font-weight: bold;
    }
    .hg-icon {
      cursor: pointer;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    border-top: 1px solid #38393D;
    border-bottom: 1px solid #38393D;

    .value {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 16px 0;

      .el-input {
        margin: 0 8px;
        width: 114px;
        
        &>.el-input__inner {
          height: 32px;
          line-height: 32px;
          background-color: #121314;
          border-radius: 4px;
        }
      }
    }

    .show-second {
      cursor: pointer;
      padding: 0 4px;
      border: 1px solid #3D4047;
      min-width: 110px;
      border-radius: 4px;
      line-height: 32px;
    }

    &>.btn {
      cursor: pointer;
      margin: 16px 0;
      width: 112px;
      border-radius: 4px;
      border: 1px solid #3D4047;
      text-align: center;
      line-height: 32px;
    }


    &>.error {
      // margin-left: 90px;
      color: #FF5A5A;
      line-height: 16px;
    }
  }

  .footer-btn {
    padding: 12px 16px;
    text-align: right;

    .hg-button {
      margin-left: 12px;
      padding: 8px 16px;
      font-size: 12px;
    }
  }
}

.el-tooltip.addition-btn_reference {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16px;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  border: 2px solid #38393D;
  background: #54565c33;
  font-size: 18px;
  font-weight: bold;

  &:hover {
    border: 2px solid #3765EA;
    background: rgba(55, 96, 234, 0.40);
  }
}

.el-tooltip.addition-btn_reference.is-finish {
  background-color: #3765EA;
  border-color: #3765EA;
}
.el-tooltip.addition-btn_reference.is-active {
  border: 2px solid #3765EA;
  background: #121314;
}

.el-tooltip.addition-btn_reference.is-disable {
  color: #54565C;
  &:hover {
    background: #54565c33;
    border-color: #38393D;
  }
}

.el-tooltip.addition-btn_reference.is-finish.is-disable {
  background-color: #54565C;
  border-color: #54565C;
  color: #83868F;
}
</style>