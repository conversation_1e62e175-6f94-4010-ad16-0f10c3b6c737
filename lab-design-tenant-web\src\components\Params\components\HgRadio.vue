<template>
  <div class="hg-radio">
    <el-radio-group 
      v-model="selectValue" 
      :disabled="eventDisable"
      @change="handleChange">
      <el-radio v-for="(item, index) in selectList" :key="index" :label="typeof item === 'string' ? item : item.name">
        {{ getI18nName(item, i18nTitle, $getI18nText) }}
      </el-radio>
    </el-radio-group>
  </div>
</template>

<script>
import { getI18nName } from '../utils';

export default {
  model: {
    prop: 'value',
    event: 'update',
  },
  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          max: 3,
          min: 0,
          ranges: []
        }
      }
    },
    i18nTitle: { // i18n需要拼接成i18n国际化文本，数据库就不存太长
      type: String,
      default: '',
    },
    eventDisable: {
      type: Boolean,
      default: false,
    }
  },
  data(){
    return {
      selectValue: null,
    }
  },
  computed: {
    selectList() {
      const { ranges } = this.data;
      if(typeof ranges === 'string') {
        const selectList = JSON.parse(ranges);
        return selectList;
      } else if(ranges.constructor === Array) {
        return ranges;
      }
      return [];
    }
  },
  watch: {
    value(newValue) {
      if(newValue !== this.selectValue) {
        this.selectValue = newValue;
      }
    }
  },
  mounted() {
    this.selectValue = this.value;
  },
  methods: {
    getI18nName,
    handleChange(value) {
      this.$emit('update',value);
    }
  }
}
</script>

<style lang="scss" scoped>
.hg-radio {
  margin-right: 10px;
}
</style>