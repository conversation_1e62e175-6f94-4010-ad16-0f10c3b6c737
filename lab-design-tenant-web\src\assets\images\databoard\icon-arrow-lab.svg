<svg width="206" height="128" viewBox="0 0 206 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_204_28682" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="206" height="128">
<rect width="206" height="128" rx="4" fill="#1D1D1F"/>
</mask>
<g mask="url(#mask0_204_28682)">
<rect opacity="0.6" width="187" height="187" rx="4" transform="matrix(0.694658 0.71934 0.693424 -0.720529 55 188.738)" fill="url(#paint0_linear_204_28682)" fill-opacity="0.6"/>
<rect opacity="0.6" width="187" height="187" rx="4" transform="matrix(-0.707107 -0.707107 -0.680744 0.732522 226.528 212.229)" fill="url(#paint1_linear_204_28682)" fill-opacity="0.6"/>
</g>
<defs>
<linearGradient id="paint0_linear_204_28682" x1="93.5" y1="0" x2="93.5" y2="187" gradientUnits="userSpaceOnUse">
<stop stop-color="#2D2F33" stop-opacity="0"/>
<stop offset="1" stop-color="#2D2F33"/>
</linearGradient>
<linearGradient id="paint1_linear_204_28682" x1="93.5" y1="187" x2="93.5" y2="-1.36333e-06" gradientUnits="userSpaceOnUse">
<stop stop-color="#2D2F33" stop-opacity="0"/>
<stop offset="1" stop-color="#2D2F33"/>
</linearGradient>
</defs>
</svg>
