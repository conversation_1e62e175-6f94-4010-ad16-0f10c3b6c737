<template>
  <div class="input-remark">
    <div class="title">
      <span>{{ $t(remarkContentTittle) }}</span>
    </div>
    <div class="input-box">
      <el-input
        type="textarea"
        :placeholder="canEdit ? $t('order.detail.tips.designRemark') : $t('order.detail.tips.no')"
        v-model="remarkContent"
        maxlength="1600"
        :autosize="{ minRows: rows, maxRows: 99 }"
        show-word-limit
        resize="none"
        :disabled="!canEdit"
        @change="handleChange"
      ></el-input>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DesignRemark',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: String,
    rows: {
      type: Number,
      default: 8
    },
    remarkContentTittle: {
      type: String,
      default: 'order.detail.title.designRemark',
    },
    canEdit: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return  {
      remarkContent: ''
    }
  },
  watch: {
    value(content) {
      this.remarkContent = content;
    }
  },
  mounted() {
    if(this.value) {
      this.remarkContent = this.value;
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value);
    }
  }
}
</script>

<style lang="scss" scoped>
.input-remark {
  margin-bottom: 24px;
  .title {
    margin-bottom: 12px;
    color: $hg-label;
    line-height: 24px;
    font-size: 16px;

    span {
      font-weight: bold;
    }
  }
  /deep/.el-textarea.is-disabled {
    .el-textarea__inner {
      color: #999999;
      background: $hg-border-second;
      border: 1px solid $hg-border-second;
    }
  }
}
</style>