<template>
  <el-popover
    placement="top-start" 
    popper-class="ipr-box-popper" 
    trigger="manual" 
    :visible-arrow="false" 
    v-model="showPopover">
    <div class="input-box">
      <div class="header">
        <span>{{ $t('component.ortho.iprTitle') }}</span>
        <hg-icon icon-name="hg-icon-im-close" iconfont-name="hg-common-iconfont" font-size="24px" @click="onClose"></hg-icon>
      </div>
      <div :class="['content', language !== 'zh' && 'content_en']">
        <!-- 第X步 -->
        <div class="value value_step">
          <i>*</i>
          <i18n path="component.ortho.inputStep" tag="span">
            <el-input slot="0" type="number" v-model="stepValue" placeholder="1~99" @blur="onStepBlur" @input="onStepInput"></el-input>
          </i18n>
        </div>
        <p class="error">{{ stepError }}</p>
        <!-- 片切xxmm -->
        <div class="value value_cut">
          <i>*</i><span>{{ $t('component.ortho.cutInput',['']) }}</span>
          <el-input v-model="cutValue" placeholder="0.01~3.00" @blur="onCutBlur"><span slot="suffix">mm</span></el-input>
        </div>
        <p class="error">{{ cutError }}</p>
      </div>
      <div class="footer-btn">
        <hg-button v-show="initStep && initCut" type="secondary" @click="onDelete">{{ $t('common.btn.delete') }}</hg-button>
        <hg-button @click="onConfirm">{{ $t('common.btn.confirm') }}</hg-button>
      </div>
    </div>
    <!-- 按钮 -->
    <el-tooltip effect="dark" slot="reference" placement="top-start" popper-class="ipr-tooltip-content" :disabled="!itemStep || !itemCut">
      <!-- hover时的内容 -->
      <div slot="content">
        <p>{{ `${inputData.number}-${inputData.nextNumber}` }}</p>
        <p>
          <span>{{ $t('component.ortho.inputStep', [itemStep]) }}</span>
          <span>{{ $t('component.ortho.cutInput', [`${itemCut}mm`]) }}</span>
        </p>
      </div>
      <div 
        :class="{'ipr-btn_reference':true, 'is-active': showPopover, 'is-finish': bothHasValue, 'is-disable': disabled }" 
        @click="openInputBox">
        <span></span>
        <hg-icon icon-name="hg-icon-edit-outline" iconfont-name="hg-common-iconfont" font-size="24px"></hg-icon>
        <hg-icon v-show="bothHasValue && !showPopover" icon-name="hg-icon-tick" iconfont-name="hg-common-iconfont" font-size="18px"></hg-icon>
      </div>
    </el-tooltip>
  </el-popover>
</template>

<script>
import { mapGetters } from 'vuex';
/* IPR-单个牙位配置的popover */
export default {
  props: {
    inputData: {
      require: true,
      type: Object,
    },
    value: Boolean,
    disabled: Boolean,
  },
  data() {
    return {
      initStep: '',
      initCut: '',
      stepError: '',
      cutError: '',
      cutValue: '',
      stepValue: '',
      showPopover: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
    isError() {
      return this.stepError || this.cutError || !this.stepValue || !this.cutValue;
    },
    itemCut() {
      return this.inputData.cutValue;
    },
    itemStep() {
      return this.inputData.step;
    },
    bothHasValue() {
      return this.inputData.cutValue && this.inputData.step;
    },
  },
  watch: {
    showPopover(show) {
      if(show) {
        const { step, cutValue } = this.inputData;
        this.initStep = step;
        this.initCut = cutValue;
        this.stepValue = step;
        this.cutValue = cutValue;
      }else {
        this.stepValue = '';
        this.cutValue = '';
        this.stepError = '';
        this.cutError = '';
      }
    }
  },
  methods: {
    openInputBox() {
      if(this.disabled) {
        return;
      }
      this.showPopover = true;
    },

    onStepInput() {
      console.log('onStepInput');
      const stepValue = Number(this.stepValue);
      if(isNaN(stepValue) || stepValue > 99 || (stepValue !== '' && stepValue < 1) || (!Number.isInteger(stepValue))) {
        this.stepError = this.$t('component.ortho.tips.limitValue');
        this.stepValue = '';
      }else {
        this.stepError = '';
      }
    },

    onStepBlur() {
      /* const stepValue = Number(this.stepValue);
      if(!this.stepValue) {
        this.stepError = this.$t('component.ortho.tips.stepEmpty');
      }else if(isNaN(stepValue) || stepValue < 1 || stepValue > 99) {
        this.stepError = this.$t('component.ortho.tips.limitValue');
      }else {
        this.stepError = '';
      } */
    },

    onCutBlur() {
      const cutValue = Number(this.cutValue);
      if(!this.cutValue) {
        this.cutError = this.$t('component.ortho.tips.cutIsEmpty');
      }else if(isNaN(cutValue) || cutValue < 0.01 || cutValue > 3) {
        this.cutError = this.$t('component.ortho.tips.cutLimit');
      }else {
        this.cutError = '';
      }
    },

    onClose() {
      console.log('onClose');
      this.inputData.step = this.initStep;
      this.inputData.cutValue = this.initCut;
      this.showPopover = false;
    },

    onDelete() {
      const title = this.$t('common.systemTips');
      const { number, nextNumber } = this.inputData;
      const message = this.$t('component.ortho.tips.cutMsg',[this.stepValue, number, nextNumber]);

      this.$confirm(message, title, {
        confirmButtonText: this.$t('common.btn.confirm'),
        cancelButtonText: this.$t('common.btn.cancel'),
        distinguishCancelAndClose: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        this.$set(this.inputData, 'step', '');
        this.$set(this.inputData, 'cutValue', '');
        this.showPopover = false;
      }).catch((action) => {});   
    },

    onConfirm() {
      if(this.isError) {
        !this.stepValue ? this.stepError = this.$t('component.ortho.tips.stepEmpty') : false;
        !this.cutValue ? this.cutError = this.$t('component.ortho.tips.cutIsEmpty') : false;
        return;
      }
      this.$set(this.inputData, 'step', String(Number(this.stepValue))); // 去除多余的0和多余的小数点
      this.$set(this.inputData, 'cutValue', String(Number(this.cutValue)));
      this.showPopover = false;
    },
  }
}
</script>

<style lang="scss">
/* 重写 */
.ipr-box-popper.el-popper {
  padding: 0;
  width: 320px;

  .header {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px;
    line-height: 24px;

    &>span {
      font-weight: bold;
    }
    .hg-icon {
      cursor: pointer;
    }
  }

  .content {
    padding: 16px;
    border-top: 1px solid #38393D;
    border-bottom: 1px solid #38393D;

    &>.value {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 16px;

      &>i {
        padding-right: 8px;
        color: #FF5A5A;
      }

      &:first-of-type {
        margin-top: 0;
      }

      .el-input {
        margin: 0 8px;
        max-width: 144px; 
        
        &>.el-input__inner {
          padding-right: 42px;
          height: 32px;
          line-height: 32px;
          background-color: #121314;
          border-radius: 4px;
        }

        &>.el-input__suffix {
          right: 0;
          padding: 0 8px;
          border-left: 1px dashed #38393D;
          color: #83868F;

          .el-input__suffix-inner {
            line-height: 32px;
          }
        }
      }
    }

    &>.value.value_step {
      .el-input {
        width: 114px;
      }
    }

    &>.error {
      margin-left: 90px;
      padding-top: 8px;
      color: #FF5A5A;
      line-height: 16px;
    }
  }

  .content.content_en {
    .value {
      justify-content: start;
    }

    .value_cut {
      &>span {
        display: inline-block;
        width: 114px;
      }
    }

    .error {
      margin-left: 14px;
    }
  }

  .footer-btn {
    padding: 12px 16px;
    text-align: right;

    .hg-button {
      margin-left: 12px;
      padding: 8px 16px;
      font-size: 12px;
    }
  }
}

/* 每个单元 */
.ipr-btn_reference {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  border: 2px solid #38393D;
  background: #54565c33;

  .hg-icon-edit-outline {
    display: none;
  }

  &:hover {
    border: 2px solid #3765EA;
    background: rgba(55, 96, 234, 0.40);
    .hg-icon-edit-outline {
      display: inline-block;
    }
  }
}

.ipr-btn_reference.is-active {
  border: 2px solid #3765EA;
  &>span {
    padding: 0 6px 22px 6px;
    border-bottom: 2px solid #E4E8F7;
  }

  &:hover {
    background: #54565c33;
    &>span {
      display: inline-block;
    }
    .hg-icon-edit-outline {
      display: none;
    }
  }
}

.ipr-btn_reference.is-finish {
  border-color: #3765EA;
  background-color: #3765EA;

  &:hover {
    .hg-icon-edit-outline {
      display: none;
    }
  }
}

.ipr-btn_reference.is-finish.is-active {
  border: 2px solid #3765EA;
  background: #54565c33;
  &>span {
    padding: 0 6px 22px 6px;
    border-bottom: 2px solid #E4E8F7;
  }
  &:hover {
    &>span {
      display: inline-block;
    }
  }
}

.ipr-btn_reference.is-disable {
  &:hover {
    border: 2px solid  #38393D;
    background: rgba(84, 86, 92, 0.20);

    .hg-icon {
      display: none;
    }
  }
}

.ipr-btn_reference.is-finish.is-disable {
  background-color: #54565C;
  border-color: #54565C;
  &:hover {
    .hg-icon-tick {
      display: inline-block;
    }
  }
}

.ipr-tooltip-content.el-tooltip__popper {
  padding: 0;
  min-width: 180px;
  
  &>div {
    &>p:first-of-type {
      padding: 8px 12px;
      font-size: 14px;
      line-height: 20px;
      border-bottom: 1px solid #38393D;
    }
    &>p:nth-of-type(2) {
      display: flex;
      flex-direction: column;
      padding: 12px 16px;
      color:#B0B0B0;
      font-size: 14px;
      line-height: 20px;
    }
  }
}
</style>