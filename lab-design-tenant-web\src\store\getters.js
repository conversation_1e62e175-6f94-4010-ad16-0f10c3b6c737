export default {
  language: state => state.app.language,
  currentTime: state => state.app.currentTime,
  defaultParamMap: state => state.app.defaultParamMap,
  defaultProgramMap: state => state.app.defaultProgramMap,
  isLoadDefaultParam: state => state.app.isLoadDefaultParam,
  showTipFromDetail: state => state.app.showTipFromDetail,
  utcOffset: state => state.app.utcOffset,
  isSetI18n: state => state.app.isSetI18n,

  userId: state => state.user.userId,
  username: state => state.user.username,
  orgCode: state => state.user.orgCode,
  userCode: state => state.user.userCode,
  roles: state => state.user.roles,
  roleCodes: state => state.user.roles.length > 0 ? state.user.roles.map(item => item.roleCode) : [],
  authList: state => state.user.authList,
  menuList: state => state.user.menus,
  designTypeTree: state => state.user.designTypeTree,
  oneDesignList: state => state.user.oneDesignList,
  aiFunctionsList: state => state.user.aiFunctionsList,
  rootToLeafCodes: state => state.user.rootToLeafCodes,
  leafToRootCodes: state => state.user.leafToRootCodes,

  designTypeSkuTree: state => state.user.designTypeSkuTree, //返回全部设计类型包含skucode的
  oneDesignSkuList: state => state.user.oneDesignSkuList,

  changeRoute: state => state.user.changeRoute,
}