<template>
  <button 
    :class="[
      'hg-button',
      type ? 'hg-button--' + type : '',
      size ? 'hg-button--' + size : '',
      {
        'hg-btn-disabled': disabled,
        'is-loading': loading,
      },
      popperClass,
    ]"
    :style="{...styleClass}"
    @click.stop="handleClick">
    <i class="el-icon-loading" v-if="loading"></i>
    <i :class="icon" v-if="icon && !loading"></i>
    <span v-if="$slots.default">
      <slot></slot>
    </span>
  </button>
</template>

<script>
// 按钮
export default {
  name: 'HgButton',
  props: {
    type: { // 类型 primary secondary text danger danger-secondary
      type: String,
      default: 'primary'
    },
    popperClass: {  // 自定义的class
      type: String,
      default: ''
    },
    size: { // 按钮大小 small middle large
      type: String,
      default: 'large'
    },
    icon: { // 图标
      type: String,
      default: ''
    },
    disabled: Boolean,  // 是否禁用
    styleClass: {
      type: Object,
      default(){
        return {}
      }
    },
    loading: Boolean,
  },
  methods: {
    handleClick(evt){
      this.$emit('click',evt);
    }
  }
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>