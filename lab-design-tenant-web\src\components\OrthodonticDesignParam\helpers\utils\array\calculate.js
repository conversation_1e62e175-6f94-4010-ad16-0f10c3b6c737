import { deepCompare } from '../compare'
import { parsePath } from '../object/index'

/**
 * 根据一个字段作为两个简单数组的的元素的标识,得出第一个数组相对第二个数组的增加和减少项的集合
 * @param target: 参照项
 * @param mixin: 对比项
 * @return { decrease: 减少的项的集合, increase: 增加的项的集合 }
 */
export function calculateArrayCreaseSimple(target, mixin) {
  target = target.slice()
  mixin = mixin.slice()

  for (let i = 0, targetLength = target.length; i < targetLength; i++) {
    const targetItem = target[i]

    let isMixinHad = false

    for (let j = 0, mixinLength = mixin.length; j < mixinLength; j++) {
      const mixinItem = mixin[j]

      isMixinHad = targetItem === mixinItem

      if (isMixinHad) {
        target.splice(i, 1)
        i--
        targetLength--

        mixin.splice(j, 1)
        j--
        mixinLength--
        break
      }
    }
  }

  return {
    decrease: target,
    increase: mixin
  }
}

/**
 * 根据一个字段作为两个数组的的元素的标识,得出第一个数组相对第二个数组的增加和减少项的集合
 * @param target: 参照项
 * @param mixin: 对比项
 * @param accordingExpress: 字段表达式,判断依据
 * @return { decrease: 减少的项的集合, increase: 增加的项的集合 }
 */
export function calculateArrayCrease(target, mixin, accordingExpress) {
  target = target.slice()
  mixin = mixin.slice()

  for (let i = 0, targetLength = target.length; i < targetLength; i++) {
    const targetItem = target[i]
    const targetId = parsePath(targetItem, accordingExpress)

    let isMixinHad = false

    for (let j = 0, mixinLength = mixin.length; j < mixinLength; j++) {
      const mixinItem = mixin[j]

      const mixinId = parsePath(mixinItem, accordingExpress)
      isMixinHad = mixinId === targetId

      if (isMixinHad) {
        target.splice(i, 1)
        i--
        targetLength--

        mixin.splice(j, 1)
        j--
        mixinLength--
        break
      }
    }
  }

  return {
    decrease: target,
    increase: mixin
  }
}

export function getArrayCreaseHandler(options) {
  const { decreaseHandler, increaseHandler, accordingExpress } = options

  let oldList = []

  return function (list) {
    const { decrease, increase } = calculateArrayCrease(
      oldList,
      list,
      accordingExpress
    )

    for (const item of decrease) {
      decreaseHandler(item)
    }

    for (const item of increase) {
      increaseHandler(item)
    }

    oldList = list
  }
}

/**
 * 根据一个字段作为两个数组的的元素的标识和对比字段,深度对比两个数组,得出第一个数组相对于第二个数组差异项的集合
 * @param target: 参照项
 * @param mixin: 对比项
 * @param accordingExpress: 字段表达式,作为判断依据
 * @param compareFields: 对比字段
 * @return { decrease: 减少的项的集合, increase: 增加的项的集合,difference: 不同的项的集合 }
 */
export function calculateArrayCompare(target, mixin, accordingExpress, compareFields) {
  target = target.slice()
  mixin = mixin.slice()
  const difference = []

  for (let i = 0, targetLength = target.length; i < targetLength; i++) {
    const targetItem = target[i]
    const targetId = parsePath(targetItem, accordingExpress)

    let isMixinHad = false

    for (let j = 0, mixinLength = mixin.length; j < mixinLength; j++) {
      const mixinItem = mixin[j]

      const mixinId = parsePath(mixinItem, accordingExpress)
      isMixinHad = mixinId === targetId

      if (isMixinHad) {
        target.splice(i, 1)
        i--
        targetLength--

        mixin.splice(j, 1)
        j--
        mixinLength--

        let isDeepEqual
        if (!compareFields) {
          isDeepEqual = deepCompare(mixinItem, targetItem)
        } else {
          isDeepEqual = compareFields.every((key) => {
            const mixinValue = parsePath(mixinItem, key)
            const targetValue = parsePath(targetItem, key)
            return deepCompare(mixinValue, targetValue)
          })
        }
        if (!isDeepEqual) {
          difference.push(mixinItem)
        }

        break
      }
    }
  }

  return {
    decrease: target,
    increase: mixin,
    difference,
  }
}

/**
 * 根据一个字段作为两个数组的的元素的标识和对比字段,深度对比两个数组,判断是否有不同
 * @param target: 参照项
 * @param mixin: 对比项
 * @param accordingExpress: 字段表达式,作为判断依据
 * @param compareFields: 包含的字段
 */
export function calculateArrayIsDifferent(target, mixin, accordingExpress, compareFields) {
  const { decrease, increase, difference } = calculateArrayCompare(target, mixin, accordingExpress, compareFields)
  const result = !decrease.length && !increase.length && !difference.length
  return !result
}

// 数组元素是否相等(和顺序无关)
export function calculateArrayEquel(left, right) {
  if (left.length !== right.length) {
    return false
  }

  const leftSlice = left.slice()

  for (let i = 0; i < right.length; i++) {
    const item = right[i]
    const index = leftSlice.indexOf(item)

    if (index === -1) {
      return false
    }

    leftSlice.splice(index, 1)
  }

  if (!leftSlice.length) {
    return true
  }

  return false
}

// 左边数组是否包含右边数组
export function calculateArrayContain(left, right) {
  if (left.length < right.length) {
    return false
  }

  const leftSlice = left.slice()

  for (let i = 0; i < right.length; i++) {
    const item = right[i]
    const index = leftSlice.indexOf(item)

    if (index === -1) {
      return false
    }

    leftSlice.splice(index, 1)
  }

  return true
}
