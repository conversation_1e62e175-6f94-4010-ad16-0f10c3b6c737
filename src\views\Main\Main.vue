<template>
  <div class="mian-container">
    <div class="header">
      <Header/>
    </div>
    <div class="content-container pos-rel">
      <div class="main-content">
        <transition name="fade">
          <keep-alive v-if="!$route.meta.reload">
            <router-view />
          </keep-alive>
          <router-view v-else :key="$route.fullPath" />
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
import Header from "@/layout/index.vue";
export default {
  name: "Main",
  components: {
    Header
  },
  created() {
  },
  methods: {
  },
  data () {
    return {
    }
  }
};
</script>

<style lang="scss" scoped>
.mian-container {
  width: 100%;
  height: 100vh;
  background: $hg-background-color;
}

.header {
  height: 60px;
}
.content-container {
  width: 100%;
  height: calc(100vh - 60px);
  padding: 24px;
  box-sizing: border-box;

  .main-content {
    width: 100%;
    height: 100%;
  }
}
</style>
