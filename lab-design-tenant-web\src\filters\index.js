import moment from '@/public/plugins/moment';
import store from '@/store';
/**
 * 格式化HTML显示的时间
 * @param {String/Number} time  时间，可以为时间戳或者正确的时间字符串
 * @param {String} format 格式化
 */
export const dateFormatInHtml = (time, format = 'YYYY-MM-DD HH:mm:ss') => {
  const date = new Date(time);
  if (isNaN(date)) {
    // 非时间
    return time;
  }

  moment.locale();  // 多语言支持的项目，需要先执行这个
  const utcOffset = store.getters.utcOffset;
  return moment(time).utcOffset(utcOffset).format(format);
};

export const formatFileSize = (size, type = 'M') => {
  if (type === 'M') {
    return `${(size / 1024 / 1000).toFixed(2)}M`;
  } else if (type === 'G') {
    return `${(size / 1024 / 1024 / 1000).toFixed(2)}G`;
  } else {
    return `${(size / 1000).toFixed(2)}K`;
  }
};

import { FDINumbers, generalNumbers } from '@/public/constants/index';
/**
 * FDI符号 通用符号 转换
 * @param {*} entry  eg: 18 = 1    18-17-16 = 1-2-3  18,17,16 = 1,2,3
 */
export function showToothNumBySystemIdx(entry) {
  // console.log('调用下', entry);
  const sysIndex = 1; // 租户端只显示1
  if (sysIndex === 1 || entry === '') return entry; //如果是1 FDI就直接return 不做转换

  if (typeof entry === 'string') {
    if (/\d-\d/.test(entry)) {
      const oldArr = entry.split('-'); //eg "18-17-16"
      const newArr = oldArr.map((ele) => translateOneNumber(parseInt(ele))).sort((a, b) => a - b);
      const resultStr = newArr.join('-');
      return resultStr;
    }
    if (/\d,\d/.test(entry)) {
      const oldArr = entry.split(','); // eg  "18,17,16"
      const newArr = oldArr.map((ele) => translateOneNumber(parseInt(ele))).sort((a, b) => a - b);
      const resultStr = newArr.join(',');
      return resultStr;
    }
    const parseIntEntry = parseInt(entry);
    if (typeof parseIntEntry === 'number' && !isNaN(parseIntEntry)) {
      // eg "18"
      return translateOneNumber(parseInt(entry));
    }
    return entry; // eg "upper"  "文字,文字"
  } else if (typeof entry === 'number') {
    const target = translateOneNumber(entry);
    return target;
  } else {
    return entry;
  }

  function translateOneNumber(oldNum) {
    // if(typeof oldNum !== 'number') return oldNum

    const oldIndex = FDINumbers.findIndex((ele) => ele === oldNum);
    if (oldIndex === -1) {
      return oldNum;
    }
    const newNum = generalNumbers[oldIndex];
    return newNum;
  }
}

/**
 * 历史总计显示1000+ 2000+ 1w+
 * @param {Number} total  总数
 * @param {String} isOver 超过千分位计数isOver。最大显示99999，10w,10w+
 */
export const computeNumber = (total, isOver = '') => {
  let number = '';
  if(total == 0) {
    return 0;
  }
  total = Number(total);
  // 数据取余
  const getNumber = (num, math) => {
    return num % math == 0;
  };
  if (!isOver) {
    if(total <= 1000){
      number = total;
    } else if (total > 1000 && total < 10000) {
      number = String(total).slice(0, 1) + '000+';
    } else if (total >= 10000 && total < 100000) {
      number = getNumber(total,10000) ? String(total).slice(0, 1) + 'w' : String(total).slice(0, 1) + 'w+';
    } else if (total >= 100000 && total < 1000000) {
      number = getNumber(total,100000) ? String(total).slice(0, 1) + '0w' : String(total).slice(0, 1) + '0w+';
    } else {
      number = getNumber(total,1000000) ? String(total).slice(0, 1) + '00w' : String(total).slice(0, 1) + '00w+';
    }
  } else {
    if(total <= 99999){
      number = total;
    } else if (total >= 100000 && total < 1000000) {
      number = getNumber(total,100000) ? String(total).slice(0, 1) + '0w' : String(total).slice(0, 1) + '0w+';
    } else {
      number = getNumber(total,1000000) ? String(total).slice(0, 1) + '00w' : String(total).slice(0, 1) + '00w+';
    }
  }
  return number;
};
