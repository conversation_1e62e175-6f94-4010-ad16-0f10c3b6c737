<template>
  <div class="order-detail-page patient-image-card">
    <div class="title">
      <span class="title-name">{{ $t('order.ortho.title.patientPic') }}</span>
      <div class="operate-btn">
        <span :class="{'is-disabled': originPateintImageList.length === 0 }" @click="openSelectDialog">{{ $t('order.ortho.title.selectImage') }}</span>
        <span @click="showUploadBox=true"><hg-icon icon-name="icon-upload-default-lab"></hg-icon>{{ $t('common.btn.upload') }}</span>
      </div>
    </div>
    
    <div v-if="showFileList" class="file-box">
      <div class="face-box" v-if="facePicList.length > 0">
        <p>{{ $t('order.ortho.title.facePic') }}</p>
        <div class="image-ul">
          <div v-for="(img, index) in facePicList" :key="index" class="image-li">
            <img @click.stop="openView(img)" :src="img.pictureUrl" alt="">
            <span v-show="needUpload" @click="deleteImage(FILE_TYPES.FACE_PIC,index)">
              <hg-icon icon-name="icon-close1-lab"></hg-icon>
            </span>
          </div>
        </div>
      </div>

      <div class="intraoral-box" v-if="intraoralPicList.length > 0">
        <p>{{ $t('order.ortho.title.intraPic') }}</p>
        <div class="image-ul">
          <div v-for="(img, index) in intraoralPicList" :key="index" class="image-li">
            <img @click.stop="openView(img)" :src="img.pictureUrl" alt="">
            <span v-show="needUpload" @click="deleteImage(FILE_TYPES.INTRAORAL_PIC,index)">
              <hg-icon icon-name="icon-close1-lab"></hg-icon>
            </span>
          </div>
        </div>
      </div>

      <div class="ct-box" v-if="ctPicList.length > 0">
        <p>{{ $t('order.ortho.title.ctPic') }}</p>
        <div class="image-ul">
          <div v-for="(img, index) in ctPicList" :key="index" class="image-li">
            <img @click.stop="openView(img)" :src="img.pictureUrl" alt="">
            <span v-show="needUpload" @click="deleteImage(FILE_TYPES.CT_PIC,index)">
              <hg-icon icon-name="icon-close1-lab"></hg-icon>
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else-if="needUpload" class="no-upload">
      <span>{{ $t('order.ortho.tips.noPateintImage') }}</span>
    </div>
    
    <div v-else class="no-data">
      <span>{{ $t('file.noData') }}</span>
    </div>

    <select-image-dialog 
      ref="selectImage" 
      :clientOrgCode="clientOrgCode" 
      :originList="originPateintImageList" 
      @updateSelectList="updateSelectList"></select-image-dialog>

    <el-dialog
      custom-class="order-detail-page patient-image-card upload-box"
      class="upload-box" 
      width="548px" 
      ref="uploadBox"
      :append-to-body="false"
      :show-close="false"
      :visible="showUploadBox" 
      :close-on-click-modal="false" 
      :close-on-press-escape="false">
      <p slot="title">{{ $t('common.systemTips') }}</p>

      <div class="content">
        <p>{{ $t('order.ortho.title.uploadPateintImage') }}</p>
        <el-radio-group v-model="selectUploadFileType">
          <el-radio v-for="(item, index) in uploadFileTypeList" :disabled="isUploading" :key="index" :label="item.fileType">{{ $t(item.name) }}</el-radio>
        </el-radio-group>
      </div>

      <div slot="footer" class="footer">
        <hg-button type="secondary" :disabled="isUploading" @click="showUploadBox=false">{{ $t('common.btn.cancel') }}</hg-button>
        <hg-button class="btn-upload-image" :loading="isUploading">
          <el-upload
            class="upload-image-box"
            action="#"
            accept="image/*"
            multiple
            :show-file-list="false"
            :auto-upload="false"
            :on-change="uploadChange">
          </el-upload><span>{{ isUploading ? $t('file.tips.uploading') : $t('common.btn.confirm') }}</span></hg-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import SelectImageDialog from './SelectImageDialog';
import { FILE_TYPES } from '@/public/constants';
import { getDownloadUrl } from '@/api/file';
import UploadNormalFile from '@/public/utils/uploadNormalFile';

export default {
  name: 'PatientImageCard',
  components: { SelectImageDialog },
  props: {
    uploadList: { // 撤回-上一次上传的文件
      type: Array,
      default() {
        return [];
      }
    },
    needUpload: {
      type: Boolean,
      default: true,
    },
    clientOrgCode: {
      type: Number,
      require: true,
    },
    originPateintImageList: { // 原始文件中筛选出来的
      type: Array,
      default() {
        return [];
      }
    },
    compType: Number,
  },
  data() {
    return {
      FILE_TYPES,

      facePicList: [],
      intraoralPicList: [],
      ctPicList: [],

      showUploadBox: false,
      selectUploadFileType: FILE_TYPES.FACE_PIC,

      uploadFileTypeList: [
        { name: 'order.ortho.title.facePic', fileType: FILE_TYPES.FACE_PIC, },
        { name: 'order.ortho.title.intraPic', fileType: FILE_TYPES.INTRAORAL_PIC, },
        { name: 'order.ortho.title.ctPic', fileType: FILE_TYPES.CT_PIC, },
      ],
      uploadPictureList: [], //上传时用的对象

    }
  },
  computed: {
    showFileList() {
      if(this.needUpload) {
        const uploadList = [].concat(this.facePicList, this.intraoralPicList, this.ctPicList);
        if(uploadList.length > 0) {
          return true;
        }
      }
      return false;
    },
    isUploading() {
      return this.uploadPictureList.some(item => item.isUploading);
    },
  },
  watch: {
    uploadList: {
      deep: true,
      handler(list) {
        this.filterPatientPic(list);
      }
    },
    isUploading(uploading) {
      if(!uploading && this.showUploadBox) {
        this.handleUploadList();
      }
    }
  },

  mounted() {
    this.filterPatientPic(this.uploadList);
  },

  methods: {
    //
    filterPatientPic(list) {
      this.facePicList = [];
      this.intraoralPicList = [];
      this.ctPicList = [];
      list.forEach(item => {
        const { fileName, filePath, fileType, fileSize, fileTime } = item;
        let imageItem = {
          fileName,
          filePath,
          fileType,
          fileSize,
          fileTime,
          pictureUrl: '',
          progress: 100,
        }
        if(fileType === FILE_TYPES.FACE_PIC) {
          this.facePicList.push(imageItem);
        }else if(fileType === FILE_TYPES.INTRAORAL_PIC) {
          this.intraoralPicList.push(imageItem);
        }else if(fileType === FILE_TYPES.CT_PIC) {
          this.ctPicList.push(imageItem);
        }
        this.getPicUrl(imageItem, filePath);
      });
    },

    // 获取图片地址
    getPicUrl(imageItem, filePath) {
      const param = {
        s3FileId: filePath,
        orgCode: this.clientOrgCode,
        filename: '',
      };
      getDownloadUrl(param).then(res => {
        const url = res.data.url;
        if(imageItem) {
          this.$set(imageItem, 'pictureUrl', url);
        }
      }).catch(err => {});
    },

    // 删除图片
    deleteImage(type,deleteIndex) {
      if(type === FILE_TYPES.FACE_PIC) {
        this.facePicList.splice(deleteIndex,1);
      }else if(type === FILE_TYPES.INTRAORAL_PIC) {
        this.intraoralPicList.splice(deleteIndex,1);
      }else if(type === FILE_TYPES.CT_PIC) {
        this.ctPicList.splice(deleteIndex,1);
      }
    },
    openSelectDialog() {
      this.$refs.selectImage.isShow = true;
    },

    // [选定图片]-确认
    updateSelectList(selectList) {
      selectList.forEach(item => {
        const { fileName, filePath, fileSize, fileTime, fileType, pictureUrl } = item;
        const data = { fileName, filePath, fileSize, fileTime, fileType, pictureUrl };

        if(fileType === FILE_TYPES.FACE_PIC) {
          this.facePicList.push(data);
        }else if (fileType === FILE_TYPES.INTRAORAL_PIC) {
          this.intraoralPicList.push(data);
        }else if (fileType === FILE_TYPES.CT_PIC) {
          this.ctPicList.push(data);
        }
      });
    },

    // 上传完成自动关闭弹框，然后处理文件
    handleUploadList() {
      this.showUploadBox = false;
      this.uploadPictureList = this.uploadPictureList.filter(item => item.s3FileId);
      this.uploadPictureList.forEach(file => {
        const { fileType, name, size, uid, pictureUrl, s3FileId, progress } = file;
        const data = {
          fileType,
          filePath: s3FileId,
          fileName: name,
          fileSize: size,
          fileTime: uid,
          pictureUrl,
          progress
        };

        if(fileType === FILE_TYPES.FACE_PIC) {
          this.facePicList.push(data);
        }else if (fileType === FILE_TYPES.INTRAORAL_PIC) {
          this.intraoralPicList.push(data);
        }else if (fileType === FILE_TYPES.CT_PIC) {
          this.ctPicList.push(data);
        }

      });

      this.uploadPictureList = []; // 处理完成之后归零
    },

    // 上传动作开始
    uploadChange(file, fileList) {
      try {
        if (!this.verifyFile(file, fileList) ) { return false; }
        console.log('uploadChange');
        this.$set(file, 'isUploading', true);
        this.uploadPictureList.push(file);

        const uploadNormalFile = new UploadNormalFile({file: file.raw, needOrgCode: true, orgCode: this.clientOrgCode});

        uploadNormalFile.onEnd((data) => {
          file.isUploading = false;
          file.s3FileId = data.s3FileId;
          file.pictureUrl = data.url;
          file.fileType = this.selectUploadFileType; // 当前选中
          file.progress = data.progress;
          // this.uploadPictureList = this.uploadPictureList.filter(item => item.s3FileId);
        });

        uploadNormalFile.onError((data) => {
          this.$hgOperateFail(this.$t('file.tips.uploadFail'));
          this.handleRemove(file);
          console.error('data: ', data);
        });

        uploadNormalFile.onStart();

      } catch (error) {
        console.log('error: ', error);
        file.isUploading = false;
        this.handleRemove(file);
      }
    },

    /**
     * 文件校验 这里的文件是文件对象
     * @param { Array } file 文件
     */
    verifyFile(file, fileList) {
      if (file.size === 0) {
        this.$hgOperateFail(this.$t('file.tips.emptyFile'));
        return false;
      } 
      if (file.size / 1024 / 1024 > 50) {
        this.$hgOperateFail(this.$t('file.tips.imageToBig'));
        return false;
      }
      const isImage = file.raw.type.includes('image/');
      if (!isImage) {
        this.$hgOperateFail(this.$t('file.tips.notImage'));
        return false;
      }
      return true;
    },

    /**
     * 移除图片
     * @param file 文件对象
     */
    handleRemove(file) {
      this.uploadPictureList = this.uploadPictureList.filter(item => item.name !== file.name);
    },

    // 提交时，父级调用这个方法获取当前所有图片列表
    getTheLastFileList() {
      const fileList = [].concat(this.facePicList, this.intraoralPicList, this.ctPicList);
      return fileList;
    },

    openView(file) {
      const pictureUrl = file.pictureUrl;
      if(pictureUrl) {
        this.$hgViewer.open({
          imgList: [pictureUrl],
          initialViewIndex: 0
        });
      }
    },


  }
}
</script>

<style lang="scss" scoped>
.patient-image-card {
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .title-name {
      font-weight: bold;
      font-size: 16px;
      color: #F7F8FA;
    }

    .operate-btn {
      color: #3760EA;
      >span {
        cursor: pointer;
        margin-left: 20px;

        .hg-icon {
          padding-right: 8px;
        }
      }

      .is-disabled {
        pointer-events: none;
        color: #54565C;
      }
    }
  }
}

.patient-image-card>.file-box {
  padding: 16px 24px;
  border-radius: 4px;
  border: 1px solid #2D2F33;

  &>div {
    margin-bottom: 10px;
    border-bottom: 1px dashed #2D2F33;

    &:last-of-type {
      margin-bottom: 0;
      border-bottom: none;
    }

    &>p {
      padding-bottom: 12px;
      font-size: 14px;
      line-height: 20px;
      color: #ECECEE;
    }
  }

  .image-ul {
    display: flex;
    flex-wrap: wrap;

    .image-li {
      position: relative;
      margin: 0 20px 10px 0;
      padding: 8px;
      width: 140px;
      height: 140px;
      border-radius: 4px;
      border: 1px solid #2D2F33;

      >img {
        width: 100%;
        height: 100%;
      }

      .hg-icon {
        cursor: pointer;
        position: absolute;
        top: 12px;
        right: 12px;
        color: #737680;
        font-size: 24px;
      }
    }
  }
}

.patient-image-card>.no-upload {
  padding: 24px;
  line-height: 20px;
  border-radius: 4px;
  border: 1px solid #2D2F33;
  color: #737680;
}

.patient-image-card>.no-data {

}

.patient-image-card {
  .upload-box {
    
    .content {
      >p {
        padding-bottom: 24px;
        color: #D7D7D9;
      }

      /deep/.el-radio {
        .el-radio__label {
          color: #83868F;
        }
      }
      /deep/.el-radio.is-checked {
        .el-radio__label {
          color: #E4E8F7;
        }
      }

      .el-radio-group {
        padding-bottom: 12px;
      }
      
    }

    .footer {
      padding-top: 24px;

      .hg-button {
        margin-left: 24px;
        min-width: 104px;
        line-height: 20px;
      }

      .btn-upload-image {
        position: relative;
        >span {
          display: inline-block;
        }

        .upload-image-box {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;

          /deep/.el-upload {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.order-detail-page.patient-image-card.upload-box {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0 !important;

  .el-dialog__body {
    border-bottom: 1px solid #38393D;
  }
}
</style>