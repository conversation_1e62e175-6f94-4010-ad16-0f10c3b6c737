
import axios from '@/api/interface'
function WebsocketClient({ url, token }, broadcast, tokenInvalid) {
    this.url = url;
    this.token = token;
    this.broadcast = broadcast;
    const { ws, statusPromise } = this.createWebSocket(url);
    this.ws = ws;
    this.statusPromise = statusPromise;
    this.lockReconnect = false;
    this.heartCheckTimeout = 30000;
    this.retryCount = 0;
    this.MAX_RETRY_COUNT = 100;
    this.retryTimer = setInterval(() => {
        this.retryCount = 0;
    }, 150000);
}
WebsocketClient.heartReceiveText = 'peng'
WebsocketClient.heartSendText = 'ping'
WebsocketClient.TOKEN_VALID = 'TokenValid'
WebsocketClient.FIRST_LOGIN = 'FirstLogin'
WebsocketClient.prototype.createWebSocket = function (url) {

    const ws = new WebSocket(url + `/${this.token}`);
    const statusPromise = new Promise((resolve, reject) => {
        ws.onopen = () => {
            setTimeout(() => {

                this.openHeartCheck();
            }, this.heartCheckTimeout)
            resolve();
        }
        ws.onerror = () => {
            reject();
        }
    });
    ws.onmessage = (e) => {
        if (e.data === WebsocketClient.heartReceiveText) {
            this.heartCheck && this.heartCheck.receive();
            return;
        }
        if (e.data === WebsocketClient.TOKEN_VALID || e.data === WebsocketClient.FIRST_LOGIN) {
            // window.location.href = `${window.location.origin}/login?TOKEN_VALID=true`
            return;
        }

        try {
            const data = JSON.parse(e.data)[0], result = JSON.parse(data.result);
            if (result.taskType === WebsocketClient.SLICER || result.taskType === WebsocketClient.PTS) {
                this.broadcast();
            }
        } catch (err) {
        }
    }
    ws.onclose = (evnt) => {
        this.clearHeartCheck();
        if (evnt.code != 4500) {
            this.reconnect();//重连
        }
    }
    return {
        statusPromise,
        ws
    }
}
WebsocketClient.prototype.clearHeartCheck = function () {
    clearInterval(this.heartCheckTimer);
    if (this.heartCheck) {
        clearTimeout(this.heartCheck.receiveTimer);
        this.heartCheck = null;
    }
}
WebsocketClient.prototype.openHeartCheck = function () {
    this.heartCheck = {
        receiveTimer: null,
        send: () => {
            this.ws.send(WebsocketClient.heartSendText);
            this.heartCheck.receiveTimer = setTimeout(() => {

                this.heartCheck.receiveTimer = null;
                this.clearHeartCheck();
                this.reconnect();

            }, 10000)
        },
        receive: () => {
            clearTimeout(this.heartCheck.receiveTimer);
            this.heartCheck.receiveTimer = null;
        },
    }
    this.heartCheckTimer = setInterval(() => {

        if (this.heartCheck) {
            this.heartCheck.send();

        } else {
            this.clearHeartCheck();
        }
    }, this.heartCheckTimeout)
}
WebsocketClient.prototype.reconnect = function () {
    console.log('reconnect');
    if (this.lockReconnect) return;
    if (this.retryCount > this.MAX_RETRY_COUNT) {
        window.location.reload();
        return;
    }
    this.retryCount++;

    this.lockReconnect = true;
    setTimeout(() => {     //没连接上会一直重连，设置延迟避免请求过多
        const { ws, statusPromise } = this.createWebSocket(this.url, this.params);
        this.ws = ws;
        this.statusPromise = statusPromise;
        this.lockReconnect = false;
    }, 3000);
}
WebsocketClient.prototype.close = function () {
    return new Promise((resolve) => {
        this.ws.close();
        this.ws.onclose(() => {
            resolve();
        });
    })
}
WebsocketClient.SLICER = 'SLICER_TASK';
WebsocketClient.PTS = 'Gumline_TASK';
export default WebsocketClient;