<template>
  <div>
    <el-drawer custom-class="agent-customer-drawer" :visible.sync="customerListVisible">
      <div class="draw-title" slot="title">{{agentInfo.agentName}} {{$t('agent.customerList')}} ({{customerList.length}})</div>
      <div class="agent-customer-details">
        <div class="customer-header">
          <div class="customer-search">
            <el-input v-model="searchKeyword" :placeholder="$t('agent.customerNamePlaceholder')" @input="handleSearch" prefix-icon="el-icon-search" clearable></el-input>
          </div>
        </div>
        <!-- 内容 -->
        <div class="customer-content" v-loading="loading">
          <!-- 列表 -->
          <div class="depart-table">
            <new-table :header-data="headerData" class="user-table" :loading="tableLoading" :data="customerList">
              <template #orgName="scope">
                <span>{{ scope.row.orgName }}</span>
              </template>
              <template #device="scope">
                <div class="status-icon">
                  <img v-if="scope.row.hasDevice" src="@/assets/imgs/agent/icon_authAble.png">
                  <img v-else-if="scope.row.hasDevice == false" src="@/assets/imgs/agent/icon_authDisable.png">
                </div>
              </template>
              <template #designService="scope">
                <div class="status-icon">
                  <img v-if="scope.row.hasDesignService" src="@/assets/imgs/agent/icon_authAble.png">
                  <img v-else-if="scope.row.hasDesignService == false" src="@/assets/imgs/agent/icon_authDisable.png">
                  <!-- <i :class="scope.row.hasDesignService ? 'el-icon-check status-active' : 'el-icon-remove status-inactive'"></i> -->
                </div>
              </template>
              <template #aiSoftware="scope">
                <div class="status-icon">
                  <img v-if="scope.row.hasAiSoftware" src="@/assets/imgs/agent/icon_authAble.png">
                  <img v-else-if="scope.row.hasAiSoftware == false" src="@/assets/imgs/agent/icon_authDisable.png">
                  <!-- <i :class="scope.row.hasAiSoftware ? 'el-icon-check status-active' : 'el-icon-remove status-inactive'"></i> -->
                </div>
              </template>
            </new-table>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import newTable from '@/components/func-components/newTable.vue';
import { getAgentCustomerList } from '@/api/agent';
import { mapGetters } from "vuex";

export default {
  name: "AgentCustomerList",
  components: { newTable },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    agentInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      searchKeyword: '',
      tableLoading: false,
      loading: false,
      customerList: []
    };
  },
  computed: {
    ...mapGetters(["language"]),
    customerListVisible: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit("update:show", val);
      },
    },
    headerData() {
      return [
        {
          prop: "orgName",
          minWidth: "40%",
          noTip: false,
          getLabel: () => this.$t('agent.customerName'),
        },
        {
          prop: "device",
          minWidth: "20%",
          noTip: false,
          getLabel: () => this.$t('agent.device'),
        },
        {
          prop: "designService",
          minWidth: "20%",
          noTip: false,
          getLabel: () => this.$t('agent.designService'),
        },
        {
          prop: "aiSoftware",
          minWidth: "20%",
          noTip: false,
          getLabel: () => this.$t('agent.aiSoftware'),
        }
      ];
    },
  },
  watch: {
    customerListVisible(newValue) {
      if (newValue) {
        this.getCustomerList();
      }
    }
  },
  methods: {
    handleSearch() {
      // 调用API进行搜索
      this.getCustomerList();
    },
    // 处理代理范围数据
    processRangeType(rangeType) {
      const hasDevice = rangeType && rangeType.includes(0);
      const hasDesignService = rangeType && rangeType.includes(1);
      const hasAiSoftware = rangeType && rangeType.includes(2);
      
      return {
        hasDevice,
        hasDesignService,
        hasAiSoftware
      };
    },
    async getCustomerList() {
      this.loading = true;
      try {
        const params = {
          orgCode: this.agentInfo.orgCode,
          key: this.searchKeyword || ''
        };
        
        const response = await getAgentCustomerList(params);
        
        if (response.code === 200 && response.data) {
          this.customerList = response.data.map(item => {
            const rangeInfo = this.processRangeType(item.rangeType);
            return {
              orgCode: item.orgCode,
              orgName: item.orgName,
              orgSn: item.orgSn,
              rangeType: item.rangeType,
              ...rangeInfo
            };
          });
        } else {
          this.customerList = [];
        }
      } catch (error) {
        console.error('获取客户列表失败:', error);
        this.customerList = [];
        this.$message.error(this.$t('agent.getCustomerListErrorTip'));
      } finally {
        this.loading = false;
      }
    }
  },
};
</script>

<style lang="scss">
.agent-customer-drawer {
  width: 600px !important;
  background-color: #1e1e1e;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
    color: #e4e8f7;
  }
  .el-drawer__body {
    padding: 24px;
    overflow: hidden;
  }
  .agent-customer-details{
    display: flex;
    flex-direction: column;
    height: 100%;
    .customer-header{
      margin-bottom: 24px;
      .customer-search{
        .el-input{
          background: #141519;
          .el-input__inner {
            background: #141519;
            border: 1px solid #38393d;
            color: #e4e8f7;
            &::placeholder {
              color: #8a8b99;
            }
          }
          .el-input__prefix {
            color: #8a8b99;
          }
        }
      }
    }
    .customer-content{
      flex: 1;
      overflow: auto;
      border-radius: 8px;
    }
    .depart-table {
      height: 100%;
      width: 100%;
      .new-table {
        height: 100%;
        background: #27292E;
        overflow: hidden;
        .el-table {
          background: #27292E;
          .el-table__header-wrapper {
            .el-table__header {
              th {
                background: #27292E;
                color: #e4e8f7;
                border-bottom: 1px solid #38393d;
              }
            }
          }
          .el-table__body-wrapper {
            .el-table__body {
              tr {
                background: #27292E;
                &:hover {
                  background: #2f3136 !important;
                }
                td {
                  border-bottom: 1px solid #38393d;
                  color: #e4e8f7;
                }
              }
            }
          }
        }
        .status-icon {
          display: flex;
          justify-content: center; 
          align-items: center;    
          height: 100%;          
        }
      }
    }
  }
}
</style>












