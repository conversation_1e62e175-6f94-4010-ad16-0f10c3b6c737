<template>
  <div class="hg-text-img-checkbox">
    <div 
      v-for="(item, index) in rangeList" 
      :key="item.name+index"
      :class="{'text-img-checkbox-item': true,'is-active': selectValue === item.name}"  
      @click="handleChange(item.name)">
      <span>{{ getI18nName(item, i18nTitle, $getI18nText) }}</span>
      <hg-pic :iconPath="item.icon"></hg-pic>
    </div>
  </div>
</template>

<script>
import HgPic from './HgPic';
import { getI18nName } from '../utils';

export default {
  components: { HgPic },
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return null;
      }
    },
    i18nTitle: {
      type: String,
      default: ''
    },
  },
  data(){
    return {
      selectValue:''
    }
  },
  computed: {
    rangeList() {
      const { ranges } = this.data;
      if(ranges) {
        const rangeList = JSON.parse(ranges);
        return rangeList;
      } 
      return [];
    }
  },
  mounted() {
    this.selectValue = this.value;
  },
  methods: {
    getI18nName,
    handleChange(value) {
      if(this.selectValue !== value) {
        this.selectValue = value;
        this.$emit('update', value);
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.hg-text-img-checkbox {
  display: flex;
  flex-flow: wrap;
  font-size: 12px;

  .text-img-checkbox-item {
    span {
      color: $hg-label;
    }

    .hg-pic {
      cursor: pointer;
      margin-top: 12px;
      margin-bottom: 14px;
      margin-right: 16px;
      width: 120px;
      height: 72px;
      border: none;
      color: $hg-border;
      background: rgba(56, 57, 61, 0.4);

      &:hover {
        border: 2px solid $hg-border;
      }
    }
  }

  .text-img-checkbox-item.is-active {
    span {
      color: $hg-secondary-primary;
    }

    .hg-pic {
      border: 2px solid $hg-main-blue;
      color: $hg-grey;
      /deep/.hg-pic-check {
        display: inline-block;
        border-color: $hg-main-blue $hg-main-blue transparent transparent;
  
        .el-icon-check {
          color:$hg-label;
        }
      }
    }
  }
}
</style>