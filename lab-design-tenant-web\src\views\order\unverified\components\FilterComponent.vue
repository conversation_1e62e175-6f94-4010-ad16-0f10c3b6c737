<template>
  <div class="filter-component">
    <div class="search-item">
      <hg-input 
        class="input" 
        clearable 
        :placeholder="$t('orderList.searchList.keywords')"
        v-model="searchData.keywords" 
        @change="handleChange">
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </hg-input>
    </div>

    <div class="search-item">
      <p>{{ $t('orderList.searchList.designtype') }}</p>
      <design-type-select 
        v-model="searchData.designTypeCodeTree"  
        @onSearch="handleChange"></design-type-select>
    </div>

    <div class="search-item"> 
      <p>{{ $t('orderList.searchList.creatTime') }}</p>
      <date-range-picker valueFormat="timestamp" v-model="searchData.createRangeTime" @change="handleChange"></date-range-picker>
    </div>

    <div class="search-item btn-box">
      <hg-button v-permission="['batchDownload']" :disabled="btnDisabled" @click="handleDownload" :loading="btnLoading">
        <hg-icon iconName="icon-download-lab" ></hg-icon>
        <p class="btn-name">{{ $t('common.btn.download') }}</p>
      </hg-button>
    </div>

    
  </div>
</template>

<script>
import DateRangePicker from '@/components/DateRangePicker';
import DesignTypeSelect from './DesignTypeSelect';

export default {
  components: { DateRangePicker, DesignTypeSelect },
  props: {
    btnDisabled: {
      type: Boolean,
      default: true
    },

    searchData: {
      type: Object,
      require: true,
    },

    btnLoading: Boolean,
  },
  data() {
    return {

    }
  },
  computed: {

  },
  methods: {
    handleChange() {
      this.$emit('onSearch');
    },

    // 下载
    handleDownload() {
      this.$emit('handleBatchDownload');
    },
  }
};
</script>

<style lang="scss" scoped>
.filter-component {
  display: flex;
  flex-wrap: wrap;

  .search-item {
    display: flex;
    align-items: center;
    margin: 0 24px 24px 0;

    &>p {
      padding-right: 12px;
    }
  }

  .input {
    width: 360px;
    /deep/.el-input__inner {
      padding-left: 48px !important;
    }
    /deep/.el-input__icon {
      margin-left: 14px !important;
      font-size: 16px;
    }
    /deep/.el-input__prefix {
      margin-left: 0;
    }
  }
  .el-select {
    width: 300px;
  }
  .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) /deep/.el-radio__inner{
    box-shadow: none;
  }

  .btn-box {
    margin-right: 0 !important;
    flex: 1;
    justify-content: flex-end;

    .hg-button {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
      min-width: 136px;

      /deep/ span {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .btn-name {
        margin-left: 8px;
      }
    }

  }
}
</style>
