import * as THREE from 'three';
import { upperNumbers, lowerNumbers } from './constant';
/**
 * 移除mesh 并清除所占用内存
 * @param {Mesh or Group} mesh
 */
export function disposeChild(mesh) {
  if (mesh instanceof THREE.Mesh) {
    if (mesh.geometry?.dispose) {
      mesh.geometry.dispose(); //删除几何体
    }
    if (mesh.material?.dispose) {
      mesh.material.dispose(); //删除材质
    }
    if (mesh.material?.texture?.dispose) {
      mesh.material.texture.dispose();
    }
  }
  if (mesh instanceof THREE.Group) {
    mesh.clear();
  }
  if (mesh instanceof THREE.Object3D) {
    mesh.clear();
  }
}

/**
 * 重置牙齿模型初始颜色 大小 透明度等
 * @param {} Tv
 */
function ResetTooth(Tv) {
  if (Tv.ToothGroup) {
    const resetColor = 0xcecece;
    const resetOpacity = 1;
    const resetSize = 1;

    Tv.ToothGroup.traverse(mesh => {
      if (mesh.type === 'Mesh' && mesh.name != 'bridge') {
        mesh.material.opacity = resetOpacity;
        mesh.material.color = new THREE.Color(resetColor);
        mesh.scale.set(resetSize, resetSize, resetSize);
      }
    });
  }
}

/**
 * 重置清除桥体group
 * @param {} Tv
 */
function ResetBrige(Tv) {
  disposeChild(Tv.BrigeGroup);
}

/**
 * 重置咬合板
 * @param {*} Tv
 */
function ResetMouthGuard(Tv) {
  const MouthGuard = Tv.scene.getObjectByName('MouthGuard');
  if (MouthGuard) {
    MouthGuard.geometry.dispose(); //清除占用内存
    MouthGuard.material.dispose(); //清除占用内存
    Tv.scene.remove(MouthGuard);
  }
}

/**
 * 重置基台
 * @param {*} Tv
 */
function ResetAbutment(Tv) {
  if(Tv && Tv.scene && Tv.scene.children) {
    const abutMentMeshList = Tv.scene.children.filter(child => child.type === 'Mesh' && child.name.includes('Abutment'));
    abutMentMeshList.forEach(mesh => {
      mesh.geometry.dispose(); //清除占用内存
      mesh.material.dispose(); //清除占用内存
      Tv.scene.remove(mesh);
    }); 
  }
}

// function ResetOther(Tv) {
//   disposeChild(Tv.T);
// }

/**
 * 重置牙龈颜色
 * @param {} Tv
 */
function ResetModel(Tv) {
  const resetColor = 0xcecece;
  if (Tv.scene.getObjectByName('upper') && Tv.scene.getObjectByName('lower')) {
    Tv.scene.getObjectByName('upper').material.color = new THREE.Color(resetColor);
    Tv.scene.getObjectByName('lower').material.color = new THREE.Color(resetColor);
  }
}

/**
 * 重置牙龈颜色
 * @param {} Tv
 */
function ResetBraket(Tv) {
  // const resetColor = 0xcecece;
  if (Tv.scene.getObjectByName('bracket_up_half') && Tv.scene.getObjectByName('bracket_low_half')) {
    Tv.scene.getObjectByName('bracket_up_half').visible = false;
    Tv.scene.getObjectByName('bracket_low_half').visible = false;
  }
}

function ResetBraketWide(Tv) {
  if (Tv.scene.getObjectByName('bracket_up_half_l') && Tv.scene.getObjectByName('bracket_low_half_l')) {
    Tv.scene.getObjectByName('bracket_up_half_l').visible = false;
    Tv.scene.getObjectByName('bracket_low_half_l').visible = false;
  }
}

/**
 * 重置牙龈颜色
 * @param {} Tv
 */
function ResetFour(Tv) {
  const fourArr = ['bracket_low_four_one_left', 'bracket_low_four_one_right', 'bracket_up_four_one_left', 'bracket_up_four_one_right'];
  for (let i = 0; i < fourArr.length; i++) {
    const item = fourArr[i];
    if (Tv.scene.getObjectByName(item)) {
      Tv.scene.getObjectByName(item).visible = false;
    }
  }
}

/**
 * 重置样式
 * @param {ThreeView} Tv
 */
export const resetStyle = function(Tv) {
  if (Tv.scene) {
    ResetTooth(Tv);
    ResetBrige(Tv);
    ResetModel(Tv);
    ResetBraket(Tv);
    ResetBraketWide(Tv);
    ResetFour(Tv);
    ResetMouthGuard(Tv);
    ResetAbutment(Tv);
    // ResetOther()
  }
};

// mesh-模型 color-颜色 zindex-层级 scale-缩放 isBAse是否使用普通材料
// 构建材料
export const createMesh = function(mesh, color, zIndex, scale, isBase) {
  if (!mesh) {
    return false;
  }
  const _color = color || '#5c60a5';
  const centroid = mesh.position;
  const _zIndex = zIndex || 0;
  const _scale = scale || 1;
  let m = null;
  //  true就使用普通材料，否则用反光材料
  if (isBase) {
    m = new THREE.MeshBasicMaterial({ color: _color });
  } else {
    m = new THREE.MeshPhongMaterial({ color: _color });
  }

  let meshGeo = mesh.geometry;
  let geo = new THREE.Mesh(meshGeo, m);
  //  设置位置
  geo.position.set(centroid.x, centroid.y, centroid.z + zIndex);
  // 缩放
  geo.scale.set(_scale, _scale, _scale);
  return geo;
};

/**
 *  获取选中牙齿在总集合的区间索引取值
 *
 *
 * @param {*} Numbers   上颌或下颌的集合
 * @param {*} selectNumbers 选中牙齿集合
 * @returns {max最大索引  min最小索引}
 */
export const getIndexMaximum = function(Numbers, selectNumbers) {
  var indexArr = [];
  for (let a of selectNumbers) {
    indexArr.push(Numbers.indexOf(a));
  }
  var x,
    max = indexArr[0],
    min = indexArr[0];
  for (x in indexArr) {
    if (indexArr[x] > max) {
      max = indexArr[x];
    }

    if (indexArr[x] < min) {
      min = indexArr[x];
    }
  }

  return {
    max,
    min,
    indexArr,
  };
};
/**
 *
 * @param {ThreeView} Tv ThreeView实例
 * @param {*} code 设计类型三级code
 * @param {*} name 牙号
 */
function changeColor(Tv, code, name) {
  let colors = {
    21101: 0xf2e4b6, //牙冠
    21106: 0x91cc66, //牙冠缺失位

    21102: 0xf2e4b6, //临冠
    21107: 0x91cc66, //临冠缺失位
    21103: 0xE6E6CF, //马里兰桥
    21104: 0xe6e6cf, //嵌体
    21105: 0xe6e6cf, //贴面
    20023: 0xe6e6cf, //咬合贴面

    21201: 0xe6b045, //内冠
    21203: 0x91cc66, //内冠缺失位

    21202: 0xe6b045, //解剖型内冠
    21204: 0x91cc66, //解剖型内冠缺失位
    21205: 0xe6b045, // 双层冠

    21401: 0x8470ff,
    21403: 0x8470ff,

    22601: 0xf2e4b6, //活动修复牙冠
    22602: 0x91cc66, //牙冠缺失位

    22701: 0xf2e4b6, //活动修复临冠
    22702: 0x91cc66, //临冠缺失位

    23103: 0xd8b483, // 马泷桥
    23104: 0xef99b3,
    23105: 0xef99b3,

    25002: 0xcecece, // 杂项其他 不变色呢
    22503: 0xcecece, // 杂项其他 不变色呢
    23402: 0xcecece, // 杂项其他 不变色呢
    24405: 0xcecece, // 杂项其他 不变色呢

    23601: 0x84A1E9, // 单孔牙支持式导板

    // 20099: 0xf391a9,
  };

  let mesh = Tv.ToothGroup.getObjectByName(name);
  if (!mesh) return false;
  mesh.material.color = new THREE.Color(colors[code] || 0xffeeea);
}

/**
 *
 * @param {ThreeView} Tv ThreeView实例
 * @param {*} toothDesignItem 设计类型
 */
function handlechangeColor(Tv, toothDesignItem) {
  toothDesignItem.tooth.forEach(name => {
    changeColor(Tv, toothDesignItem.code, name);
  });
}

/**
 *
 * @param {ThreeView} Tv ThreeView实例
 * @param {*} code 设计类型三级code
 * @param {*} name 牙号
 */
function addT(Tv, code, name) {
  let colors = {
    20097: 0x76624c,
    20098: 0x1b315e,
  };

  let sphereMaterial = new THREE.MeshBasicMaterial({ color: colors[code] });
  let geometry = new THREE.CircleGeometry(2);
  let mesh = Tv.ToothGroup.getObjectByName(name);

  mesh.geometry.computeBoundingBox();
  let centroid = new THREE.Vector3();
  centroid.addVectors(mesh.geometry.boundingBox.min, mesh.geometry.boundingBox.max);
  centroid.multiplyScalar(0.5);
  centroid.applyMatrix4(mesh.matrixWorld);
  let o = new THREE.Mesh(geometry, sphereMaterial);
  o.position.set(centroid.x, centroid.y, centroid.z + 10);
  Tv.T.add(o);
}

// 处理基台：会重复生成多个mesh，利用code和tooth做mesh的唯一标识，减少生成；
function handleSundry(Tv, toothDesignItem) {
  const code = toothDesignItem.code;
  toothDesignItem.tooth.forEach(name =>{
    change(name);
  });
  function change(name) {
    const colors = {
      23203: '#61A690',
      23201: '#ffffff',
      23204: '#fff2cb',
    };

    const zIndexs = {
      23203: 4,
    };

    const mesh = Tv.ToothGroup.getObjectByName(name);
    if (!mesh) return false;
    let centroid = mesh.position;
    //   mesh.material.opacity = 0;
    // 构建球体
    let sphereMaterial = new THREE.MeshPhongMaterial({ color: 0x000 });
    let geometry = new THREE.SphereGeometry(1.8);
    let o = new THREE.Mesh(geometry, sphereMaterial);
    o.position.set(centroid.x, centroid.y, centroid.z + 9);
    o.name = `Abutment${code}`;
    Tv.scene.add(o);

    const bg = createMesh(mesh, colors[code], zIndexs[code] || 3, 0.95, code === 23201 || code === 23204);
    bg.name = `Abutment${code}`;
    Tv.scene.add(bg);
  }
}

/**
 * 处理活动义齿
 * @param {*} Tv
 * @param {*} toothDesignItem 设计类型
 */
function handleDenture(Tv, toothDesignItem) {
  toothDesignItem.tooth.forEach(name => {
    changeColor(Tv, toothDesignItem.code, name);
    handleToothModel(Tv, toothDesignItem, name);
  });
}

// ====桥体相关====
let upperVector3s = [
  new THREE.Vector3(156, 98, 16),
  new THREE.Vector3(157, 106, 16),
  new THREE.Vector3(159, 116, 16),
  new THREE.Vector3(161, 124, 16),
  new THREE.Vector3(162, 127, 16),
  new THREE.Vector3(164, 129, 16),
  new THREE.Vector3(166, 131, 16),
  new THREE.Vector3(169, 132, 16),
  new THREE.Vector3(173, 132, 16),
  new THREE.Vector3(176, 131, 16),
  new THREE.Vector3(178, 129, 16),
  new THREE.Vector3(179, 127, 16),
  new THREE.Vector3(180, 124, 16),
  new THREE.Vector3(182, 116, 16),
  new THREE.Vector3(183, 106, 16),
  new THREE.Vector3(184, 97, 16),
];

let lowerVector3s = [
  new THREE.Vector3(186, 84, 16),
  new THREE.Vector3(185.5, 75, 16),
  new THREE.Vector3(184, 67, 16),
  new THREE.Vector3(182, 60, 16),
  new THREE.Vector3(180, 55, 16),
  new THREE.Vector3(178, 53, 16),
  new THREE.Vector3(176, 52, 16),
  new THREE.Vector3(173, 51, 16),
  new THREE.Vector3(170, 51, 16),
  new THREE.Vector3(168, 51, 16),
  new THREE.Vector3(166, 52, 16),
  new THREE.Vector3(162, 55, 16),
  new THREE.Vector3(160, 60, 16),
  new THREE.Vector3(158, 68, 16),
  new THREE.Vector3(156, 76, 16),
  new THREE.Vector3(155, 84, 16),
];

let wireframeMaterial = new THREE.MeshPhongMaterial({
  color: 0xc19b6a,
  opacity: 0.7,
  transparent: true,
});

function divisionToothArray(tooth) {
  let upperArr = [];
  let lowerArr = [];
  tooth.forEach(num => {
    upperNumbers.includes(num) ? upperArr.push(num) : lowerArr.push(num);
  });

  return {
    upperArr,
    lowerArr,
  };
}

function createBridge(option) {
  //添加桥体
  let p1 = getIndexMaximum(option.numbers, option.selectNumbers);
  let pathPositions = option.vertor3s.slice(p1.min, p1.max + 1);
  let path = new THREE.CatmullRomCurve3(pathPositions);
  let tubeGeometry = new THREE.TubeBufferGeometry(path, 64, 0.8, 8, false);
  let mesh = new THREE.Mesh(tubeGeometry, wireframeMaterial);
  mesh.name = option.bridgeName;
  option.that.BrigeGroup.add(mesh);

  //添加小球球
  let spherePositions = option.vertor3s.filter((x, i) => p1.indexArr.includes(i));
  let sphereGemeotry = new THREE.SphereGeometry(1.5);
  let sphereMaterial = new THREE.MeshPhongMaterial({ color: 0xc19b6a });
  spherePositions.forEach(vector3 => {
    let sphere = new THREE.Mesh(sphereGemeotry, sphereMaterial);
    sphere.name = 'sphere';
    sphere.position.set(vector3.x, vector3.y, vector3.z);
    option.that.BrigeGroup.add(sphere);
  });
}

/**
 *桥体
 * @param {ThreeView} Tv ThreeView实例
 * @param {*} toothDesignItem 设计类型
 */
function handleBridge(that, toothDesignItem) {
  let tooth = divisionToothArray(toothDesignItem.tooth);
  if (tooth.upperArr.length >= 2) {
    createBridge({
      that: that,
      numbers: upperNumbers,
      selectNumbers: tooth.upperArr,
      bridgeName: 'upperBridge',
      vertor3s: upperVector3s,
    });
  }

  if (tooth.lowerArr.length >= 2) {
    createBridge({
      that: that,
      numbers: lowerNumbers,
      selectNumbers: tooth.lowerArr,
      bridgeName: 'lowerBridge',
      vertor3s: lowerVector3s,
    });
  }
}
//====

/**
 * 咬合板
 *
 * @param {} Tv
 * @returns
 */
function handleMouthGuard(Tv) {
  const MouthGuard = Tv.scene.getObjectByName('MouthGuard');
  if (MouthGuard) return; //如果存在就不再重复添加

  let boxGeo = new THREE.PlaneGeometry(36, 4);
  let boxMaterial = new THREE.MeshPhongMaterial({ color: 0x007e7d });

  let box = new THREE.Mesh(boxGeo, boxMaterial);
  box.name = 'MouthGuard';
  box.position.set(171, 92, 10);
  Tv.scene.add(box);
}

/**
 *
 * @param {} Tv
 * @param {*} toothDesignItem
 * @param {*} name
 */
function handlePlant1(Tv, toothDesignItem, name) {
  changeColor(Tv, toothDesignItem, name);
  addT(Tv, toothDesignItem, name);
}

/**
 * 种植桥架
 * @param {*} Tv
 * @param {*} Tv
 */
function handlePlant3(Tv, toothDesignItem) {
  handleBridge(Tv, toothDesignItem);
  toothDesignItem.tooth.forEach(name => {
    changeColor(Tv, toothDesignItem.code, name);
  });
}

/**
 * 设置模型颜色
 */
function handleToothModel(Tv, toothDesignItem, name) {
  const fullDenetureCodeList = [22201, 22202, 22203, 22204];
  let upper = Tv.scene.getObjectByName('upper');
  let lower = Tv.scene.getObjectByName('lower');
  const modelColor = '#AF915F';
  if(!upper && !lower) {
    return;
  }
  // 模型codeTODO:牙模类都是按case计算的，选中时牙位图的上下颌都需要改变颜色,全口义齿跟局部义齿区分上下颌
  if (fullDenetureCodeList.includes(toothDesignItem.code)) {
    if (upperNumbers.includes(name)) {
      upper.material.color = new THREE.Color(modelColor);
    } else if (lowerNumbers.includes(name)) {
      lower.material.color = new THREE.Color(modelColor);
    }
  } else {
    upper.material.color = new THREE.Color(modelColor);
    lower.material.color = new THREE.Color(modelColor);
  }
}

/**
 * 分割牙模
 * @param {*} Tv
 * @param {*} toothDesignItem
 */
function handleSplinModel(Tv, toothDesignItem) {
  toothDesignItem.tooth.forEach(name => {
    handleToothModel(Tv, toothDesignItem, name);
    changeColor(Tv, toothDesignItem.code, name);
  });
}

/**
 *半口支架
 * @param {*} Tv
 * @param {*} toothDesignItem
 */
function Stent(Tv, toothDesignItem) {
  console.log('toothDesignItem: ', toothDesignItem);
  const colors = {
    21303: 0x007e7d,
    22501: 0x007e7d,
    23401: 0x007e7d,
    22101: 0xbbf195,
    24102: 0x6ed6ff,
    24301: 0x8989fe,
    24302: 0xbabaff,
    24402: 0xaf915f,
    24403: 0xbf7c7b,
    22502: 0xFF9760,
    23403: 0x628BB2,
    23404: 0xA1A36E,
    23601: 0x84A1E9
  };
  const renderColor = colors[toothDesignItem.code] || 0xbbf195;
  if (toothDesignItem.tooth.some(ele => upperNumbers.includes(ele))) {
    const o1 = Tv.scene.getObjectByName('bracket_up_half');
    if (o1) {
      o1.visible = true;
      o1.material.color = new THREE.Color(renderColor);
    }
  }
  if (toothDesignItem.tooth.some(ele => lowerNumbers.includes(ele))) {
    const o2 = Tv.scene.getObjectByName('bracket_low_half');
    if (o2) {
      o2.visible = true;
      o2.material.color = new THREE.Color(renderColor);
    }
  }
}

function StentAndChangeColor(Tv, toothDesignItem) { 
  Stent(Tv, toothDesignItem)
  handlechangeColor(Tv, toothDesignItem)
}

function StentWide(Tv, toothDesignItem) {
  const colors = {
    23106: 0xEF99B3,
  };
  const renderColor = colors[toothDesignItem.code] || 0xbbf195;
  if (toothDesignItem.tooth.some(ele => upperNumbers.includes(ele))) {
    const o1 = Tv.scene.getObjectByName('bracket_up_half_l');
    o1.visible = true;
    o1.material.color = new THREE.Color(renderColor);
  }
  if (toothDesignItem.tooth.some(ele => lowerNumbers.includes(ele))) {
    const o2 = Tv.scene.getObjectByName('bracket_low_half_l');
    o2.visible = true;
    o2.material.color = new THREE.Color(renderColor);
  }
}

/**
 *局部支架（1/4）
 * @param {*} Tv
 * @param {*} toothDesignItem
 */
function localStent(Tv, toothDesignItem) {
  if (toothDesignItem.tooth.includes(11)) {
    const o1 = Tv.scene.getObjectByName('bracket_up_four_one_left');
    if (o1) {
      o1.visible = true;
      o1.material.color = new THREE.Color(0xbbf195);
    }
  }
  if (toothDesignItem.tooth.includes(21)) {
    const o2 = Tv.scene.getObjectByName('bracket_up_four_one_right');
    if (o2) {
      o2.visible = true;
      o2.material.color = new THREE.Color(0xbbf195)
    }
  }
  if (toothDesignItem.tooth.includes(41)) {
    const o2 = Tv.scene.getObjectByName('bracket_low_four_one_left');
    if (o2) {
      o2.visible = true;
      o2.material.color = new THREE.Color(0xbbf195);
    }
  }
  if (toothDesignItem.tooth.includes(31)) {
    const o2 = Tv.scene.getObjectByName('bracket_low_four_one_right');
    if (o2) {
      o2.visible = true;
      o2.material.color = new THREE.Color(0xbbf195);
    }
  }
}

/**
 * 正畸带环
 * @param {*} Tv
 * @param {*} toothDesignItem
 */
 function localOrtho(Tv, toothDesignItem) {
  if (toothDesignItem.tooth.includes(11)) {
    const o1 = Tv.scene.getObjectByName('bracket_up_four_one_left');
    if (o1) {
      o1.visible = true;
      o1.material.color = new THREE.Color(0x44C2C2);
    }
  } 
  if (toothDesignItem.tooth.includes(21)) {
    const o2 = Tv.scene.getObjectByName('bracket_up_four_one_right');
    if (o2) {
      o2.visible = true;
      o2.material.color = new THREE.Color(0x44C2C2)
    }
  }
  if (toothDesignItem.tooth.includes(41)) {
    const o2 = Tv.scene.getObjectByName('bracket_low_four_one_left');
    if (o2) {
      o2.visible = true;
      o2.material.color = new THREE.Color(0x44C2C2);
    }
  }
  if (toothDesignItem.tooth.includes(31)) {
    const o2 = Tv.scene.getObjectByName('bracket_low_four_one_right');
    if (o2) {
      o2.visible = true;
      o2.material.color = new THREE.Color(0x44C2C2);
    }
  }
}

export const actions = {
  21101: handlechangeColor,
  // 马里兰桥
  21103: handlePlant3,
  21106: handlechangeColor,
  21102: handlechangeColor,
  21403: handlechangeColor,

  21501: handleBridge,
  23501: handleBridge,

  // 21301: handleSundry,

  21303: Stent,
  22101: Stent,
  22501: Stent,
  23401: Stent,
  24102: Stent,
  24301: Stent,
  24302: Stent,
  24402: Stent,
  24403: Stent,
  23403: Stent,
  23404: Stent,
  23601: StentAndChangeColor,

  22102: localStent,
  24406: localOrtho,

  21401: handleSplinModel,

  21402: handleToothModel,
  21404: handleToothModel,
  22401: handleToothModel,
  23301: handleToothModel,
  24303: handleMouthGuard,
  24401: handleToothModel,
  24501: handleToothModel,

  // 20017: handleSundry,
  // 20018: handleSundry,
  // 20019: handleSundry,
  // 20020: handleSundry,
  // 20021: handleSundry,

  23201: handleSundry,
  23203: handleSundry,
  23204: handleSundry,

  22201: handleDenture,
  22202: handleDenture,
  22203: handleDenture,
  22204: handleDenture,

  22502: Stent, // 哥特式托盘
  23106: StentWide,

  20089: handleMouthGuard,

  23103: handlePlant3,
  23104: handlePlant3,
  23105: handlePlant3,
  // 20098: handlePlant1,



  default: handlechangeColor,
};
