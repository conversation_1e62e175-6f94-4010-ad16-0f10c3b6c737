export default {
  orderList: {
    searchList: {
      keywords: '请输入订单号/客户编码/订单名',
      designtype: '类型',
      designTypeHolder: '全部设计类型',
      statusType: '状态',
      statusHolder: '全部状态',
      creatTime: '创建时间',
      finishTime: '完成时间',
      returnedType: '返单类型',
      designHolder: '全部软件',
      select1: '订单号/订单文件/客户编码',
      select2: '部门',
      pleaseSearch: '请搜索',
      all: '全部',
      me: '仅我处理',
      none: '未分配'
    },
    btnList: {
      downFiles: '下载文件',
      batchAll: '批量指派',
      examine: '由我检查',
      translate: '批量译单',
      translateByMe: '由我译单',
      submitTranslate: '确认要负责所选订单的译单工作吗？',
      submitExamine: '确认要负责所选订单的审核工作吗',
      noDesign: '设计类型”未知“的订单，需单独处理',
      allTranslate: '被选中订单状态必须全部为"待译单"',
      allExamine: '被选中订单状态必须全部为"待检查"',
      allOrder: '所选订单的状态必须一致',
      selectOrder: '请选择需要指派的订单',
      translateUser: '所选订单状态为“待译单”、且“当前负责人”为空',
      examineUser: '所选订单必须为“待检查”且“当前负责人”为空',
      downError: '下载出错',
      IQCowne: '所选订单IQC负责人必须为自己',
      noCaozuo:'所选订单状态为“待译单”，IQC不能进行指派操作',
      submit: '确认',
      cannotAssignDesigner: '当前用户不能对所选订单指派设计师',
      cannotAssignIQC: '当前用户不能对所选订单指派IQC',
      submitBatchTranslate: '确定将所选中的订单设为译单完成吗？',
      newOrderStatus: '新增数据统计',
      orderTime: '请选择时间'
    },
    order: {
      orderNo: '订单号',
      orderfiles: '订单文件',
      orgName: '客户编码',
      designTypeCodes: '设计类型',
      designBy: '当前处理人',
      timeCost: '剩余时间',
      select: '请选择',
      selectHolder: '关键字搜索名称或者设计组',
      rang: '负责范围',
      rangFirst: '推荐',
      noS3FileId: '订单没有S3FileId',
      dealWithClient: '客户处理',
      DesignOperations:'设计运营',
      unknown:'（未分配）',
      orderType: '订单类型',
      out: '超时 ',
      rest: '剩余 ',
      complete: '用时 ',

      multiOrder: '联合修复指派',
      designCode: '设计品类',
      assignDesigner: '指派设计师',
      reset: '重置',
      errorAssign: '必须完成所有指派才能提交。',
      istuichu: '检测到未指派的设计品类，退出将不保留当前所有操作，确认是否退出？', 
      systemTips: '系统提示',
      assignSuccess: '指派成功',
      assignerror: '指派失败',

      dept: '客户部门',
      unknownorder: '未知',
      union: '联合修复'
    },
    error: {
      download: '订单【{0}】下载失败，请求超时！',
    }
  }
}