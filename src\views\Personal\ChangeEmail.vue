<template>
  <Popup :show="show" :popup-title="$t('personal.changeEmail')" :is-use-ele="true" :loading="loading" @cancel="cancel" @submit="submitForm('pwRuleForm')">
    <div slot="popupContent" class="change-password custom-form">
      <el-form ref="pwRuleForm" :model="changePasswordObj" :rules="rules">
        <el-form-item prop="email" class="password-label">
          <template slot="label">
            <span :title="$t('personal.newEmail')">{{ $t('personal.newEmail') }}</span>
          </template>
          <el-input v-model="changePasswordObj.email" :placeholder="$t('personal.InputNewEmail')" :title="changePasswordObj.email ? '' : $t('personal.InputNewEmail')">
            <!-- <i slot="suffix" :class="['iconfont', showOldPassword ? 'icon-preview-on' : 'icon-preview-off']" @click="showOldPasswordFunc" /> -->
          </el-input>
        </el-form-item>
        <el-form-item prop="code" class="password-label">
          <template slot="label">
            <span :title="$t('personal.emailCode')">{{ $t('personal.emailCode') }}</span>
          </template>
          <el-input v-model="changePasswordObj.code" :placeholder="$t('personal.InputEmailCode')" :title="changePasswordObj.code ? '' : $t('personal.InputEmailCode')">
            <!-- <i slot="suffix" :class="['iconfont', showNewPassword ? 'icon-preview-on' : 'icon-preview-off']" @click="showNewPasswordFunc" /> -->
          </el-input>
          <div
            v-if="isSend"
            class="send-btn finger"
            @click="regCheck"
          >{{ $t('personal.sendCode') }}</div>
          <div
            v-else
            class="send-btn"
          >{{ sendmsg }}</div>
        </el-form-item>
      </el-form>
    </div>
  </Popup>
</template>

<script>
import Popup from '@/components/func-components/Popup'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { refreshLabel } from '@/assets/script/refreshLabel.js'
import {
  regCheck,
  sendUpdateEmailCode,
  updateEmailByCode
} from '@/api/organization'

export default {
  name: 'ChangeEmail',
  components: {
    Popup
  },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    var checkEmail = (rule, value, callback) => {
      if (value !== '' && !COMMON_CONSTANTS.EMAIL_RULE.test(value)) {
        return callback(new Error(this.$t('personal.emailError')))
      } else if (this.regCheckBack) {
        this.regCheckBack = false
        return callback(new Error(this.$t('personal.emailExist')))
      }
      callback()
    }
    var checkCode = (rule, value, callback) => {
      if (this.regCheckCodeBack) {
        this.regCheckCodeBack = false
        return callback(new Error(this.$t('personal.codeError')))
      }
      callback()
    }
    return {
      changePasswordObj: {
        email: '',
        code: ''
      },
      rules: {
        email: [
          { required: true, message: this.$t('personal.emailNull'), trigger: 'blur' },
          { validator: checkEmail, trigger: 'blur' }
        ],
        code: [
          { required: true, message: this.$t('personal.codeNull'), trigger: 'blur' },
          { validator: checkCode, trigger: 'blur' }
        ]
      },
      showOldPassword: false, // 是否显示原密码
      showNewPassword: false, // 是否显示新密码
      showEnsurePassword: false, // 是否显示确认密码
      oldPasswordType: 'password',
      newPasswordType: 'password',
      ensurePasswordType: 'password',
      loading: false,
      regCheckBack: false,
      regCheckCodeBack: false,
      isSend: true,
      sendmsg: ''
    }
  },
  computed: {
  },
  watch: {
    show(val) {
      if (val) {
        refreshLabel('password-label')
      } else {
        // this.resetForm('pwRuleForm')
      }
    },
    changePasswordObj: {
      handler: function(val, oldVal) {
        this.changePasswordObj.email = val.email
        this.changePasswordObj.code = val.code
      },
      deep: true
    }
  },
  methods: {
    async regCheck() {
      if (this.changePasswordObj.email === '') {
        this.$refs.pwRuleForm.validateField('email')
        return
      }
      try {
        const params = {
          email: this.changePasswordObj.email
        }
        this.isSend = false
        let timer = 60
        this.sendmsg = timer + 's'
        this.timeFun = setInterval(() => {
          --timer
          this.sendmsg = timer + 's'
          if (timer === 0) {
            this.isSend = true
            this.sendmsg = this.$t('personal.sendCode')
            clearInterval(this.timeFun)
          }
        }, 1000)
        const { code } = await regCheck(params)
        if (code === 200) {
          try {
            // 发送验证码
            sendUpdateEmailCode(params)
          } catch (error) {
            console.error(error)
            clearInterval(this.timeFun)
            this.isSend = true
          }
        }
      } catch (error) {
        if (error.msg === '用户已存在') {
          this.regCheckBack = true
          this.$refs.pwRuleForm.validateField('email')
        }
        console.error(error, this.regCheckBack)
        clearInterval(this.timeFun)
        this.isSend = true
      }
    },
    async submitForm() {
      if (this.changePasswordObj.email === '' || this.changePasswordObj.code === '') {
        this.$refs.pwRuleForm.validateField('email')
        this.$refs.pwRuleForm.validateField('code')
        return
      }
      try {
        const params = {
          email: this.changePasswordObj.email,
          code: this.changePasswordObj.code
        }
        const { code, data } = await updateEmailByCode(params)
        console.log(code, data)
        if (code === 200) {
          this.$MessageAlert({
            text: this.$t('personal.changeEmailSuccess'),
            type: 'success'
          })
          this.$emit('update:show', false)
          this.$emit('changeSuccess')
        }
      } catch (error) {
        console.error(error)
        this.regCheckCodeBack = true
        this.$refs.pwRuleForm.validateField('code')
      }
    },
    // resetForm(formName) {
    //   this.$refs[formName].resetFields()
    // },
    cancel() {
      this.loading = false
      this.$emit('update:show', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-form {
  ::v-deep .el-form .el-input__inner {
    padding: 0 48px 0 24px !important;
  }
  .icon-preview-off {
    color: #38393D;
  }
  .send-btn {
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        user-select: none;
        width: 100px;
        height: 40px;
        border: 1px solid $hg-border-color;
        border-radius: 4px;
        margin-left: 12px;
        color: $hg-primary-fontcolor;
        display: flex;
        align-items: center;
        justify-content: center;
        i {
          margin-right: 8px;
        }
        // &:hover {
        //   color: $hg-button-hover-fontcolor;
        //   i {
        //     color: $hg-button-hover-fontcolor;
        //   }
        // }
        &:active {
          color: $hg-button-active-fontcolor;
          i {
            color: $hg-button-active-fontcolor;
          }
        }
      }
}
</style>
