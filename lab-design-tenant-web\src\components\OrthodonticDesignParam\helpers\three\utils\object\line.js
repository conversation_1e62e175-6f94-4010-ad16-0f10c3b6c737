import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry'
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial'
import { Line2 } from 'three/examples/jsm/lines/Line2'

export function createLineGeometry(position) {
  const geometry = new LineGeometry()
  geometry.setPositions(position)
  return geometry
}

export function createLineGeometryByVectors(vectors) {
  const position = []

  for (const vector of vectors) {
    const { x, y, z } = vector
    position.push(x, y, z)
  }

  return createLineGeometry(position)
}

export function createLineMaterial(color, linewidth, dashed = false) {
  linewidth = linewidth / 1000

  return new LineMaterial({
    color,
    linewidth,
    dashed,
    // worldUnits: true,
    // alphaToCoverage: true,
  })
}

export function createLineObjectByGeometry(geometry, color, linewidth, dashed) {
  const material = createLineMaterial(color, linewidth, dashed)
  const object = new Line2(geometry, material)

  object.computeLineDistances()

  return object
}

export function createLineObject(position, color, linewidth, dashed) {
  const geometry = createLineGeometry(position)

  const object = createLineObjectByGeometry(geometry, color, linewidth, dashed)

  return object
}

export function createLineObjectByVectors(vectors, color, linewidth, dashed) {
  const position = []

  for (const vector of vectors) {
    const { x, y, z } = vector
    position.push(x, y, z)
  }

  return createLineObject(position, color, linewidth, dashed)
}
