<template>
  <div class="design-type-select">
    <el-cascader
      clearable
      collapse-tags
      popper-class="design-type-cascader"
      :placeholder="$t('orderList.searchList.designTypeHolder')"
      :options="optionList"
      :props="{ multiple: true, value: 'designCode', label: 'label' }"
      v-model="designTypeCodes"
      @change="handleChange"></el-cascader>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { copy } from '@/public/utils';
import { UNION_TYPE_CODE } from '@/public/constants';

export default {
  name: 'DesignTypeSelect',
  model: {
    prop: 'designTypeCodeTree',
    event: 'update',
  },
  props: {
    designTypeCodeTree: {
      type: Array,
      default() {
        return []
      }
    },
  },
  data() {
    return {
      designTypeCodes: [],
    };
  },
  computed: {
    ...mapGetters(['language', 'designTypeTree']),
    optionList() {
      let resultList = copy(this.designTypeTree) || [];
      resultList = resultList.filter(item => item.designCode !== 25001); // 一级类不需要未知

      const unknownItem = {
        children: '',
        cnName: '未知',
        label: this.$t('orderList.order.unknownorder'),
        designCode: 25001,
        enName: 'Unknown',
        hasParas: 0,
        iconUrl: '',
        level: 3,
        parentCode: 99999,
      };

      resultList.forEach(item => {
        if(!item.children.some(item => item.designCode === 25001)) { // 二级类拼接未知
          item.children.push(unknownItem);
        }
      });

      resultList = this.updateChildrenValue(resultList);
      resultList.push({
        children: '',
        cnName: '联合修复',
        label: this.$t('orderList.order.union'),
        designCode: UNION_TYPE_CODE,
        enName: 'Combined Restorations',
        iconUrl: '',
        level: 1,
      })

      return resultList;

    }
  },
  watch: {
    designTypeCodeTree(propData) {
      if(JSON.stringify(propData) !== JSON.stringify(this.designTypeCodes)) {
        this.designTypeCodes = propData;
      }
    },
  },
  methods: {

    updateChildrenValue(dataList) {
      dataList.forEach(item => {
        item.label = this.$t(`apiCommon.${item.designCode}`)
        if(item.children.length === 0) {
          item.children = '';
        }else {
          this.updateChildrenValue(item.children);
        }
      });

      return dataList;
    },

    handleChange(data) {
      this.$emit('update', data);
      this.$emit('onSearch');
    },
  },
};
</script>

<style lang="scss" scoped>
  .design-type-select {
  /deep/.el-cascader__tags .el-tag {
    width: 65%;
    background: #1d1d1f;
    color: #fff;
    &:last-child {
      width: 25%;
    }
    &:first-child {
      width: 65%;
    }
    .el-tag__close {
      background-color: #1d1d1f;
      &::before{
        font-size: 16px;
        margin-top: 1px;
      }
    }
  }
}
</style>

<style lang="scss">
.design-type-cascader {
  box-shadow: 0px 0px 16px rgba(18, 19, 20, 0.32), 0px 8px 24px rgba(18, 19, 20, 0.2), 0px 12px 32px rgba(18, 19, 20, 0.12);
  border: solid 1px #1d1d1f;
  background: #1d1d1f;
  .el-cascader-panel {
    background: #1d1d1f;
  }
  .el-cascader-menu {
    border-right: solid 1px #38393d;
    &:last-child {
      border-right: none;
    }
  }
  .el-cascader-node:not(.is-disabled):hover,
  .el-cascader-node:not(.is-disabled):focus {
    background: #262629;
  }
  .is-empty {
    height: 80px;
  }
}
.design-type-cascader[x-placement^='bottom'] .popper__arrow::after {
  border-bottom-color: #1d1d1f;
}
.design-type-cascader[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #1d1d1f;
}
</style>
