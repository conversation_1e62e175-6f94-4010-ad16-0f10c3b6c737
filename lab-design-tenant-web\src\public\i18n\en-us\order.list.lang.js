export default {
  orderList: {
    searchList: {
      keywords: 'Enter Order ID/Client ID/Order Name.',
      designtype: 'Type',
      designTypeHolder: 'All',
      statusType: 'Status',
      statusHolder: 'All',
      creatTime: 'Created Time',
      finishTime: 'Finished Time',
      returnedType: 'Returned Type',
      designHolder: 'All',

      select1: 'Order No./Original file/Client ID',
      select2: 'Department',
      pleaseSearch: 'Please enter the keyword',
      all: 'All',
      me: 'By me only',
      none: 'None'
    },
    btnList: {
      downFiles: 'File Download',
      batchAll: 'Multi-assign',
      examine: 'Review',
      translate: 'Multi-translate',
      translateByMe: 'Translate',
      submitTranslate: 'Confirm to translate the selected order?',
      submitExamine: 'Confirm to review the selected order?',
      noDesign: 'Unknown type orders need to be processed separately.',
      allTranslate: 'The selected order status must all be "To Be Translated".',
      allExamine: 'The selected order status must all be "To be audited".',
      allOrder: 'The selected order status must be the same.',
      selectOrder: 'Please select the order to be assigned',
      translateUser: 'The selected order status is "Pending Translate" and "Current Person in Charge" is empty.',
      examineUser: 'The selected order status is "To be audited" and "Current Person in Charge" is empty.',
      downError: 'Download Error!',
      IQCowne: 'IQC responsible person must be yourself.',
      noCaozuo:'IQC cannot assign the selected orders while waiting to be translated.',
      submit: 'Confirm',
      cannotAssignDesigner: 'Current user cannot send the selected order to the designer.',
      cannotAssignIQC: 'Current user cannot send the selected order to IQC.',
      submitBatchTranslate: 'Sure to change the selected order to "Translated"?',
      newOrderStatus: 'New Order Stats',
      orderTime: 'Please select the date range.'
    },
    order: {
      orderNo: 'Order No.',
      orderfiles: 'Original File',
      orgName: 'User ID',
      designTypeCodes: 'Design Type',
      designBy: 'Handler',
      timeCost: 'Remaining Time',
      select: 'Select',
      selectHolder: 'Please enter keywords',
      rang: 'Responsible Range',
      rangFirst: 'Recommend',
      noS3FileId: 'No S3 Filed in Order',
      dealWithClient: 'Customer',
      DesignOperations:'Design Operations',
      unknown:'None',
      orderType: 'Order Type',
      out: 'Delayed ',
      rest: 'RestTime ',
      complete: 'TotalTime ',

      multiOrder: 'Multi-assign for Combine Restorations Orders',
      designCode: 'Design application',
      assignDesigner: 'Assign',
      reset: 'reset',
      errorAssign: 'Please assign all these orders before submitting.',
      istuichu: 'Unassigned design application(s) detected. Closing will discard all these assignments. Sure to close?', 
      systemTips: 'System hint',
      assignSuccess: 'Assigned successfully!',
      assignerror: 'Failed to assign.',

      dept: 'Department',
      unknownorder: 'Unknown',
      union: 'Combined Restorations'
    },
    error: {
      download: 'Failed to download the order 【{0}】. Request timed out!',
    }
  }
}