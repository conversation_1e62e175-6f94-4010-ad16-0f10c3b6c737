.el-carousel__indicators--horizontal {
  bottom: 25px!important;
  left: 80%!important;
  transform: translateX(-50%);
}
/*elementui的下拉选择框样式全局覆盖*/
.el-select-dropdown, .el-cascader__dropdown {
  border-radius: 4px !important;
  background-color: #1D1D1F !important;
  border: none !important;
  box-shadow: 0px 12px 32px 0px #121314,0px 8px 24px 0px #121314,0px 0px 16px 0px #121314 !important;
  .popper__arrow, .popper__arrow::after {
    border-top-color: #1D1D1F !important;
    border-bottom-color: #1D1D1F !important;
  }
  .el-select-dropdown__item {
    padding: 0 24px !important;
    background-color: #1D1D1F !important;
    &.selected {
      color: #E4E8F7;
      font-weight: normal;
      background-color: transparent !important;
    }
    &:hover {
      background-color: #262629 !important;
    }
  }
  .el-cascader-menu {
    border-right-color: #38393D;
  }
}
.el-tooltip__popper.is-dark {
  border-radius: 4px;
  background: #262629;
  box-shadow: 0px 12px 32px 0px #121314,0px 8px 24px 0px #121314,0px 0px 16px 0px #121314 !important;
  color: #83868F;
  font-size: 12px;
  .popper__arrow {
    display: none;
  }
}

// 表单样式
.custom-form {
  width: auto !important;
  .el-form {
    .el-form-item {
      display: flex;
      // align-items: center;
      justify-content: center;
      margin-top: 20px;
      margin-bottom: 0;
      &:nth-of-type(1) {
        margin-top: 0px;
      }
      .el-form-item__label {
        max-width: 150px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #E4E8F7;
      }
      .el-form-item__content {
        display: flex;
        align-items: center;
        width: 315px;
        margin-left: 12px !important;
        // flex-direction: column;
        flex-wrap: wrap;
        .el-input {
          flex: 1;
          .el-input__inner {
            background-color: transparent;
            color: #E4E8F7;
            padding: 0 24px;
            &::placeholder {
              color: #83868F;
            }
            &:hover {
              border: 1px solid #54565C;
            }
            &:focus {
              border-color: #E4E8F7;
            }
          }
          .el-input__suffix {
            right: 24px;
            color: #E4E8F7;
            cursor: pointer;
          }
          .el-form-item__error {
            padding: 2px 0;
          }
        }
        .is-disabled {
          .el-input__inner {
            background-color: rgba(84,86,92,0.25);
            border: 1px solid #38393D;
            color: #54565C;
            &::placeholder {
              color: #54565C;
            }
            &:hover {
              border: 1px solid #38393D;
            }
          }
        }
        .el-radio {
          .el-radio__input {
            .el-radio__inner {
              background-color: transparent;
              border-color: #54565C;
            }
            .el-radio__inner:after {
              width: 8px;
              height: 8px;
              background-color: #3054cc;
            }
            &.is-disabled {
              .el-radio__inner {
                background: #38393D;
              }
            }
          }
          &.is-checked {
            .el-radio__label {
              color: #E4E8F7;
            }
          }
          &.is-disabled {
            .el-radio__input {
              + span.el-radio__label {
                color: #606266;
              }
            }
          }
        }
        .el-form-item__error {
          position: relative;
          width: 100%;
          top: 0;
          text-align: left;
        }
      }
      &.is-error { // el-form中的el-select
        .el-input__inner:hover, .el-input__inner:focus {
          border: 1px solid #C74040 !important;
        }
        .el-select, .el-select.is-focus {
          .el-input__inner {
            border: 1px solid #C74040 !important;
          }
        }
      }
    }
  }
}

.el-input {
  .el-input__inner {
    background-color: transparent;
    color: #E4E8F7;
    padding: 0 24px;
    &::placeholder {
      color: #83868F;
    }
    &:hover {
      border: 1px solid #54565C;
    }
    &:focus {
      border-color: #E4E8F7;
    }
  }
  .el-input__suffix {
    right: 24px;
    color: #E4E8F7;
    cursor: pointer;
  }
  .el-form-item__error {
    padding: 2px 0;
  }
}

// 级联选择器样式
.el-cascader__dropdown {
  .el-cascader-node.is-selectable.in-active-path {
    color: #E4E8F7;
    font-weight: normal;
  }
  .el-cascader-node.is-selectable.in-checked-path {
    color: #3054cc;
  }
}

// 输入框计算器样式
.el-input-number {
  .el-icon-arrow-down, .el-icon-arrow-up {
    &:hover {
      color: #3054cc;
    }
  }
}

// 树节点展开icon
.el-tree-node__content>.el-tree-node__expand-icon {
  font-size: 12px !important;
}


// 按钮
.hg-button.el-button.el-button--default {
  background-color: #3760EA;
  color: #DADFED;
  border: none;
  height: 40px;

  &:hover {
    color: #E4E8F7;
    background-color: #3760EB;
  }
}
.hg-button.el-button.el-button--default.is-disabled {
  background-color: rgba(84, 86, 92, 0.20);
  color: #54565C;
}
//------el-loading样式覆盖 start----
.el-loading-mask{
  background-color: rgba(18, 19, 20, 0.8);
}
//------el-loading样式覆盖 end----
