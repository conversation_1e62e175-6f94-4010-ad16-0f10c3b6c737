export function traverseTreeParent(object, cb, parentKey = 'parent') {
  let parent = object[parentKey]

  while (parent) {
    const result = cb(parent)
    if (!result) {
      return false
    }
    parent = parent[parentKey]
  }

  return true
}

export function traverseTreeChildren(object, cb, childrenKey = 'children') {
  if (!(object instanceof Array)) {
    object = [object]
  }

  function _traverse(children, parent) {
    for (const item of children) {
      const result = cb(item, parent)
      if (!result) {
        return false
      }

      const _children = item[childrenKey]
      if (_children && _children.length) {
        const result = _traverse(_children, item)
        if (!result) {
          return false
        }
      }
    }

    return true
  }

  return _traverse(object, parent)
}

// TODO: 增加return机制
/**
 * 递归树形结构
 * @param treeData: 树形数据
 * @param cb: 回调函数
 * @param childrenKey: 树形数据的children字段
 */
export function recursionTree(treeData, cb, childrenKey = 'children') {
  if (!(treeData instanceof Array)) {
    treeData = [treeData]
  }

  _recursion(treeData, null)

  function _recursion(children, parent) {
    const length = children.length

    for (let i = 0; i < length; i++) {
      const item = children[i]

      const isLast = i === length - 1

      item.__parent__ = parent
      item.__isLast__ = isLast

      const _children = item[childrenKey]

      if (_children && _children.length) {
        _recursion(_children, item)
      } else {
        const isLast = item.__isLast__
        delete item.__isLast__
        delete item.__parent__

        cb(item, parent, isLast)

        if (isLast) {
          while (parent) {
            const isLast = parent.__isLast__
            delete parent.__isLast__
            const p = parent.__parent__
            delete parent.__parent__

            cb(parent, p, isLast)

            parent = isLast ? p : null
            if (!parent) {
              break
            }
          }
        }
      }
    }
  }
}

export function getTreeRecursionList(treeData) {
  const recursionObjectList = []
  recursionTree(treeData, (item, parent) => {
    recursionObjectList.push({
      item,
      parent,
    })
  })

  return recursionObjectList
}
