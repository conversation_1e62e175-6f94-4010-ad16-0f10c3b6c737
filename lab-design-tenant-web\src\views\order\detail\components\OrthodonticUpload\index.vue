<template>
  <div class="order-detail-page orthodontic-upload">
    <order-title langName="resultFile">
      <span v-show="showNewTip" class="new-file-icon">NEW</span>
      <div v-show="isReturnFromClient || canCompleteFile" class="operate-btn" @click.stop="showUploadBox=true"> 
        <hg-icon icon-name="icon-add-lab" font-size="24px"></hg-icon>{{ $t('common.btn.create') }}
      </div>
    </order-title>

    <div v-if="showUpload"  class="upload-box">
      
      <!-- 4.2.6手动触发生成3D方案 needUpload父节点传入：负责设计师且设计中editUpload -->
      <!-- 4.3.63新增订单完成可以新增方案，并增加删除和提交按钮canCompleteFile -->
      <div class="btn_create-program">
        <el-button v-if="canCompleteFile && hiddenFileBox" plain class="btn_delete-program" @click="submitCompleteOrth('delete')">{{$t('common.btn.delete')}}</el-button>
        <el-button type="primary" v-if="editUpload" @click.stop.prevent="clickToCreateProgram" :loading="createProgramLoading">
          {{ $t('order.ortho.btnCreateProgram') }}
        </el-button>
        <!-- 分开按钮，因为已完成的要调新接口 -->
        <el-button type="primary" v-if="canCompleteFile" @click.stop.prevent="clickToCreateProgramInComplete" :loading="createProgramLoading">
          {{ $t('order.ortho.btnCreateProgram') }}
        </el-button>
        <el-button v-if="canCompleteFile" type="primary" class="btn_delete-program" @click="submitCompleteOrth('submit')">{{$t('order.detail.btn.finish')}}</el-button>
      </div>
      
      <slot name="programTitle"></slot>
      <!-- 正畸方案 -->
      <slot name="programView"></slot>

      <div v-for="(item, index) in compList" :key="index" class="upload-li">
        <patient-image-card 
          ref="uploadCard"
          v-if="item.compType === UPLOAD_COMP_TYPE.PATIENT_BOX" 
          :name="$t(item.name)"
          :compType="item.compType"
          :uploadList="item.uploadList"
          :acceptType="item.acceptType"
          :necessary="item.necessary"
          :clientOrgCode="clientOrgCode"
          :needUpload="editImageUpload || canCompleteFile"
          :originPateintImageList="originPateintImageList"></patient-image-card>

        <slot v-else-if="item.compType === UPLOAD_COMP_TYPE.DESIGN_REMARK" name="designRemark"></slot>
        <slot v-else-if="item.compType === UPLOAD_COMP_TYPE.CORRECT_STEP" name="correctSteps"></slot>
        <slot v-else-if="item.compType === UPLOAD_COMP_TYPE.ORTHONTIC_DESIGN_PARAM" name="orthoDesignParam"></slot>

        <upload-card 
          v-else 
          ref="uploadCard"
          :name="$t(item.name)"
          :compType="item.compType"
          :uploadList="item.uploadList"
          :fileType="item.fileType"
          :uploadTip="item.uploadTip"
          :acceptType="item.acceptType"
          :necessary="item.necessary"
          :standarName="item.standarName"
          :fileSize="item.fileSize"
          :limit="item.limit"
          :clientOrgCode="clientOrgCode"
          :needUpload="editUpload || canCompleteFile"></upload-card>
      </div>

    </div>

    <!-- 历史文件 -->
    <div v-if="showHistoryCard" class="history-ul">
      <history-card 
        class="history-li" 
        v-for="(history, index) in historyFileList" 
        :key="index"
        :number="!showUpload ? index : index+1"
        :showNewContent="!showUpload && index === 0"
        :canCompleteFile="canCompleteFile"
        :clientOrgCode="clientOrgCode"
        :historyItem="history"
        :orderStatus="orderStatus"
        @creat="clickToCreateProgramInComplete"></history-card>
    </div>

    <div v-if="(!showUpload && historyFileList.length === 0)" class="no-data" >
      {{ $t('order.detail.noData') }}
    </div>   
  </div>
</template>

<script>
import OrderTitle from '../OrderTitle';
import UploadCard from './UploadCard';
import PatientImageCard from './PatientImageCard';
import HistoryCard from './HistoryCard';
import OrthProgramTitle from './OrthProgramTitle';
import { UPLOAD_COMP_TYPE, FILE_TYPES, ORDER_TYPES } from '@/public/constants';

export default {
  name: 'OrthodonticUpload',
  components: { OrderTitle, UploadCard, PatientImageCard, HistoryCard, OrthProgramTitle },
  props: {
    needUpload: Boolean,  // 是否允许上传
    needUploadImage: Boolean,  // 是否允许上传图片
    canCompleteFile: Boolean, // 设计完成，设计师组长可以新增方案
    clientOrgCode: {  // 客户组织
      type: Number,
      require: true,
    },
    fileList: {   // 上传的文件，当前最新方案的文件         
      type: Array,
      default() {
        return [];
      }
    },
    standarName: String,  // 标准文件名-和原始文件校验文件名是否一致
    hiddenFileBox: Boolean, // 设计中-非设计师需要隐藏
    showNewTip:Boolean, // 是否显示NEW
    historyFileList: {  // 历史订单文件
      type: Array,
      default() {
        return [];
      }
    },
    originPateintImageList: { // 原始文件-患者照片
      type: Array,
      default() {
        return [];
      }
    },
    orderStatus: Number, //订单状态
    isReturnFromClient: Boolean,
  },
  
  data() {
    return {
      UPLOAD_COMP_TYPE,
      editUpload: this.needUpload,
      editImageUpload: this.needUploadImage,
      defaultCompList:[
        { name: 'file.title.design', fileType: FILE_TYPES.DESIGN_FILE, compType: 1, uploadList: [], necessary:true, standarName: '' },
        { 
          name: 'file.title.model', fileType: FILE_TYPES.DESIGN_MODEL, compType: 1, uploadList: [], necessary:true, 
          uploadTip: 'file.tips.model', 
          acceptType: '.stl,.zip,.dcm,.obj,.ply' 
        },{ 
          name: 'file.title.correct', fileType: FILE_TYPES.CORRECT_FILE, compType: 1, uploadList: [], necessary:true, 
          uploadTip: 'file.tips.correct', 
          acceptType: '.html',
          fileSize: 50,
          limit: 1,
        },{
          name: 'order.detail.title.designRemark', compType: 3,
        },
        { name: 'order.ortho.title.patientPic', fileType: [FILE_TYPES.FACE_PIC, FILE_TYPES.INTRAORAL_PIC, FILE_TYPES.CT_PIC ], compType: 4, uploadList: [], necessary:false,},
        { name: '', compType: 6, necessary:false, },
        { name: 'order.ortho.title.correctStep', compType: 5, necessary:false,},
      ],

      showUploadBox: false,
      createProgramLoading: false,
    }
  },
  watch: {
    needUpload(newValue) {
      this.editUpload = newValue;
    },
    needUploadImage(newValue) {
      this.editImageUpload = newValue;
    }
  },
  computed: {
    compList() {
      console.log(this.fileList)
      this.defaultCompList.map(comp => {
        const { fileType } = comp;
        if(fileType) {
          const fileList = this.fileList.filter(file => {
            if(typeof fileType === 'number') {
              return file.fileType === fileType;
            } else {
              return fileType.includes(file.fileType);
            }
          });

          if(fileType === FILE_TYPES.DESIGN_FILE) {
            comp.standarName = this.standarName;
          }
          comp.uploadList = fileList;
        }
        return comp;
      });
      console.log('defaultCompList', this.defaultCompList)
      return this.defaultCompList;
    },
    showHistoryCard() {
      if(this.hiddenFileBox && this.orderStatus === ORDER_TYPES.DESIGNING) { // 设计中，其他人看不到历史看板
        return false;
      }else{
        return this.historyFileList.length > 0;
      }
    },
    // 是否隐藏上传入口：正畸需要点击才出现
    showUpload() {
      console.log('this.hiddenFileBox:', this.hiddenFileBox)
      if(this.isReturnFromClient || (this.canCompleteFile && this.hiddenFileBox)) {
        return this.showUploadBox;
      } else {
        return !this.hiddenFileBox;
      }
    },
  },

  methods: {
    getCompList() { // 提取正畸上传的所有文件
      let compList = [];
      const domList = this.$refs.uploadCard || [];
      if(domList.length > 0) {
        domList.forEach(dom => {
          const { compType, necessary, fileType, uploadFileList } = dom;
          if(compType === 1) {
            const data = {
              compType,
              necessary,
              fileType,
              fileList: uploadFileList,
            };

            compList.push(data);
          }else if (compType === 4 && dom.getTheLastFileList) {
            const fileList = dom.getTheLastFileList();
            const data = {
              compType,
              necessary,
              fileType,
              fileList,
            };
            compList.push(data);
          }
        });
      }
      return compList;
    },
    
    // 点击生成3D方案 
    async clickToCreateProgram() {
      const finishLoading = () => {
        this.createProgramLoading = false;
      };
      this.createProgramLoading = true;
      this.$emit('saveProgramInfo', finishLoading);
    },

    // 已完成方案点击生成3D方案
    async clickToCreateProgramInComplete(){
      const finishLoading = () => {
        this.createProgramLoading = false;
      };
      this.createProgramLoading = true;
      this.$emit('saveProgramInfoInComplete', finishLoading);
    },


    // 删除新加的设计结果
    submitCompleteOrth(type){
      if(type == 'delete'){
        this.$confirm(this.$t('order.detail.tips.nosaveResult'), this.$t('bill.leftDrawer.reminding'), {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
        }).then(() => {
          this.showUploadBox = false;
          // this.$emit('submit', 'delete'); // 重新请求数据
        }).catch(() => {

        });
      } else if(type == 'submit'){
        this.$emit('submit', 'success')
      }
    }
  }
}
</script>

<style lang="scss" scoped>          
.orthodontic-upload {
  &>div {
    margin-bottom: 24px;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
  .order-title {
    margin-bottom: 0;
    padding: 24px;
    background-color: #1B1D22;
  }

  .no-data {
    padding: 24px;
    background-color: #1B1D22;
  }
  .el-button.is-plain:focus{
    background: transparent;
    color: #fff;
  }
}

.orthodontic-upload>.order-title {
  position: relative;
  display: flex;
  align-items: center;

  .operate-btn {
    cursor: pointer;
    display: flex;
    position: absolute;
    top: 16px;
    right: 24px;
    padding: 4px 12px;
    border-radius: 2px;
    border: 1px solid #3760EA;
    line-height: 22px;
    font-size: 12px;
  }

  .new-file-icon {
    color: $hg-white;
    background: $hg-new-tag;
    border-radius: 7px;
    padding: 0 5px;
    margin-left: 10px;
    font-size: 12px;
    height: 14px;
    line-height: 14px;
  }
}

.orthodontic-upload>.upload-box {
  position: relative;
  padding: 24px;
  border-radius: 4px;
  background: #1D1D1F;

  .upload-li {
    margin-bottom: 24px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  .btn_create-program {
    position: absolute;
    top: 24px;
    right: 24px;
  }
  // .btn_delete-program{
  //   position: absolute;
  //   top: 24px;
  //   right: 100px;
  // }

}

.orthodontic-upload>.history-ul {
  .history-li {
    margin-bottom: 24px;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
  /deep/.el-collapse-item{
    position: relative;
    margin-bottom: 20px;
    .el-collapse-item__header{
      background: #27292E;
      display: flex;
      color: #fff;
      padding-left: 48px;
      .orth-program-title{
        margin-bottom: 0;
      }
    }
    .el-collapse-item__content{
      color: #fff;
    }
    .el-collapse-item__arrow {
      position: absolute;
      left: 16px;
    }
  }
  
}
.history-card>.orth-program-title {
  .orth-title_tip {
    margin-left: 8px;
    padding: 4px 12px;
    color: #EF9B34;
    font-size: 12px;
    border-radius: 12px;
    background: #EF9B3433;
  }
}
</style>