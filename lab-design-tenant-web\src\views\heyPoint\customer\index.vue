<template>
  <div class="customer-list">
    <filterComponent :conditions="conditions" @searchData="searchData"></filterComponent>
    <hg-table
      class="customer-table"
      maxHeight="100%"
      :data="tableInfo.data"
      :header-data="tableInfo.headerData"
      :loading="tableInfo.loading"
      v-bind="$attrs"
      @sortChange="sortChange"
    >
      <!-- 客户名称 -->
      <template #orgName="{ row }">
        <span class="org-name" @click.prevent="toDetailPage(row)">{{row.orgName}}</span>
      </template>
      <!-- 客户编码 -->
      <template #orgSn="{ row }">
        <span >{{row.orgSn}}</span>
      </template>
      <!-- 结算类型 -->
      <template #settlementType="{ row }">
        <span>{{ row.settlementType | settlementType }}</span>
      </template>
      <!-- 折扣率 -->
      <template #discountRate="{ row }">
        <span>{{ `${row.discountRate}% off` }}</span>
      </template>
      <!-- 总黑豆余额 -->
      <template #total="{ row }">
        <span>{{ row.total | capitalize }}</span>
      </template>
      <!-- 信用值 -->
      <template #creditScore="{ row }">
        <span>{{ row.creditScore | capitalize }}</span>
      </template>
      <!-- 可用信用值 -->
      <template #availableCredit="{ row }">
        <span>{{ row.availableCredit | capitalize }}</span>
      </template> 
    </hg-table>

    <pagination 
      class="table-footer"
      :total="tableInfo.total"
      :initPageIndex="tableInfo.page"
      :initPageSize="tableInfo.pageSize"
      :disabled="tableInfo.loading"
      @onSearch="searchData"
    ></pagination>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
import filterComponent from './components/filter.vue';
import hgTable from '@/components/HgTable';
import pagination from '@/components/Pagination';
import { getCustomerList } from '@/api/heypoint';
import { settlementType, capitalize } from '@/filters/heypoint';

export default {
  name: 'HeyPointCustomer',
  components: {
    filterComponent,
    hgTable,
    pagination
  },
  filters: {
    settlementType,
    capitalize
  },
  data() {
    return {
      tableInfo: {
        total: 0,
        pageSize: 20,
        page: 1,
        loading: false,
        sortType: '',
        orderByColumn: '',
        data: [],
        headerData: [
          {
            prop: 'orgName',
            getLabel: () => {
              return this.lang('customerName');
            },
          },
          {
            prop: 'orgSn',
            getLabel: () => {
              return this.lang('customerNo');
            },
          },
          {
            prop: 'settlementType',
            getLabel: () => {
              return this.lang('settlementType');
            },
          },
          {
            prop: 'total',
            getLabel: () => {
              return this.lang('totalBalance');
            },
          },
          {
            prop: 'creditScore',
            sortable: true,
            getLabel: () => {
              return this.lang('creditValue');
            },
          },
          {
            prop: 'availableCredit',
            sortable: true,
            getLabel: () => {
              return this.lang('availableCredit');
            },
          },
          {
            prop: 'discountRate',
            getLabel: () => {
              return this.lang('discountRate');
            },
          },
          {
            prop: 'remark',
            getLabel: () => {
              return this.lang('remark');
            },
          },
        ],
      },
      conditions: {
        orgName: '',
        rangeType: null,
        settlementType: null,
      }
    }
  },
  created() {
    if (this.$route.query && this.$route.query.orgCode) {
      this.updateCondition();
    } else {
      this.searchData('filter', this.conditions);
    }
  },
  methods: {
    lang: getLang('heypoint.customer'),

    /**
     * 更新过滤项参数
     */
    updateCondition() {
      const query = this.$route.query;
      const keys = ['rangeType', 'settlementType'];
      const tableKeys = ['page', 'pageSize'];
      const newCondition = {};
      Object.keys(query).forEach(item => {
        if (query[item]) {
          if (keys.includes(item)) {
            newCondition[item] = Number(query[item]);
          } else if (tableKeys.includes(item)) {
            this.tableInfo[item] = Number(query[item]);
          } else {
            newCondition[item] = query[item];
          }
        }
      });
      this.conditions = {...this.conditions, ...newCondition};
      this.getList();
    },

    /**
     * 跳转至详情页
     * @param row 当前行信息
     */
    toDetailPage(row) {
      console.log('row: ', row);
      const query = {};
      const errArr = ['', null, undefined];
      Object.keys(this.conditions).forEach(item => {
        if (this.conditions[item] && !errArr.includes(this.conditions[item])) {
          query[item] = this.conditions[item];
        }
      });
      query.page = this.tableInfo.page;
      query.pageSize = this.tableInfo.pageSize;
      this.$router.push({path: '/heyPoint/customer/detail', query: {...query, orgCode: row.orgCode}});
    },

    /**
     * 点击排序，分页信息需要初始化为第一页
     * @param prop 排序字段
     * @param order 升序降序
     */
    sortChange(props) {
      const { prop, order } = props;
      const orderMap = new Map([
        ['descending', {sortType: 'desc', orderByColumn: prop}],
        ['ascending', {sortType: 'asc', orderByColumn: prop}],
        [null, {sortType: '', orderByColumn: ''}]
      ]);

      this.tableInfo = {...this.tableInfo, ...orderMap.get(order), page: 1};
      this.getList();
    },

    /**
     * 搜索
     * @param type 类型
     * @param conditions 搜索条件
     */
    searchData(type, conditions) {
      if (type === 'filter') {
        this.tableInfo.page = 1;
      } else if (type === 'page') {
        this.tableInfo.page = conditions.pageIndex;
        this.tableInfo.pageSize = conditions.pageSize;
      }
      this.getList();
    },

    /**
     * 获取列表
     */
    async getList() {
      try {
        this.tableInfo.loading = true;
        const params = {
          ...this.conditions,
          pageSize: this.tableInfo.pageSize,
          pageNum: this.tableInfo.page,
          orderByColumn: this.tableInfo.orderByColumn,
          sortType: this.tableInfo.sortType
        };
        const { data } = await getCustomerList(params);
        this.tableInfo.total = data.total;
        this.tableInfo.data = data.records;
      } catch (error) {
        console.log('error: ', error);
      } finally {
        this.tableInfo.loading = false;
      }
    }
  },
}
</script>

<style lang="scss">
.customer-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .customer-table {
    flex: 1;
    max-height: calc(100% - 58px - 60px);
    .org-name {
      cursor: pointer;
      color: #4477FB;
    }
  }
  .table-footer {
    border-top: 1px solid $hg-border;
  }
}
</style>