<template>
  <el-input
    :placeholder="placeholder"
    :size="size"
    :resize="resize"
    :form="form"
    :disabled="disabled"
    :readonly="readonly"
    :type="type"
    :validateEvent="validateEvent"
    :suffixIcon="suffixIcon"
    :prefixIcon="prefixIcon"
    :label="label"
    :clearable="clearable"
    :showPassword="showPassword"
    :showWordLimit="showWordLimit"
    :tabindex="tabindex"
    :maxlength="maxlength"
    :minlength="minlength"
    :rows="rows"
    v-model="textValue"
    @blur="(event) => $emit('blur',event)"
    @focus="(event) => $emit('focus',event)"
    @change="(value) => $emit('change',value)"
    @clear="$emit('clear')"
    @input="handleInput">
    <!-- 兼容el-input -->
    <template slot="prefix" v-if="$slots.prefix">
      <slot name="prefix"></slot>
    </template>

    <template slot="suffix" v-if="$slots.suffix">
      <slot name="suffix"></slot>
    </template>
  </el-input>
</template>

<script>
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'

export default {
  model: {
    prop: 'inputText',
    event: 'update'
  },
  data() {
    return {
      textValue: '',
    }
  },
  props: {
    inputText: [String, Number],
    type: {
      type: String,
      default: 'text'
    },
    maxlength: Number,
    minlength: Number,
    showWordLimit: {
      type: Boolean,
      default: false
    },
    placeholder: String,
    clearable: {
      type: Boolean,
      default: false
    },
    showPassword: {
      type: Boolean,
      default: false
    },
    disabled: Boolean,
    size: String,
    suffixIcon: String,
    prefixIcon: String,
    rows: Number,
    autosize: {
      type: [Boolean, Object],
      default: false
    },
    readonly: Boolean,
    resize: String,
    form: String,
    label: String,
    tabindex: String,
    validateEvent: {
      type: Boolean,
      default: true
    },
    replaceChar: Boolean,
    replaceCharReg: RegExp,
  },
  watch: {
    inputText: {
      immediate: true,
      handler(inputValue) {
        if(inputValue!== this.textValue) {
          this.textValue = inputValue;
        }
      }
    }
  },
  methods: {
    handleInput(event) {
      if(this.replaceChar) {
        let banReg = COMMON_CONSTANTS.SPECIAL_CHAR_REGEX;
        if(this.replaceCharReg) {
          banReg = this.replaceCharReg;
        }
        this.textValue = this.textValue.replace(banReg,'');
      }
      this.$emit('update', this.textValue);
      this.$emit('input', this.textValue); // 这里回调父级定义的input事件
    }
  }
}
</script>
