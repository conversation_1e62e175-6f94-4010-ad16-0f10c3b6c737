<template>
  <div class="order-title">
    <p>{{ $t(`order.detail.title.${langName}`) }}</p>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'OrderTitle',
  props: {
    langName: String
  }
}
</script>

<style lang="scss" scoped>
.order-title {
  padding-bottom: 24px;
  color: $hg-secondary-primary;
  font-size: 16px;
  line-height: 20px;
  >p {
    font-weight: bold;
  }
  border-bottom: 1px dashed #38393D;
}
</style>