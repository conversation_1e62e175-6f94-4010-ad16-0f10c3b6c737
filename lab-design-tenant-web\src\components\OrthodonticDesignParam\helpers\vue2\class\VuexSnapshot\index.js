// 适合vuex架构的快照
import Stack from '@/components/OrthodonticDesignParam/helpers/class/Stack/index'

import {
  fastClone,
  deepCompare,
  isEmptyObject,
  parsePath,
  setPathValue,
} from '@/components/OrthodonticDesignParam/helpers/utils/index'

export default class VuexSnapshot {
  constructor(store, includes, options = {}) {
    const { max = Infinity, needDeepCompare = false, debug = false } = options

    this.store = store
    this.includes = includes
    this.max = max
    this.needDeepCompare = needDeepCompare
    this.debug = debug

    this.registerRecordStore = new Map()
    this.dynamicRegisterRecordStore = new Map()
    this.dynamicRegisterRecordResetStack = new Stack()

    this.undoRecordStack = new Stack({ max })
    this.redoRecordStack = new Stack({ max })

    this.waitRecordData = Object.create(null)
    this.waitFunctionalStack = new Stack()

    this.tempStack = new Stack()

    this.properties = {
      isCanUndo: false,
      isCanRedo: false
    }

    this._initSubscribe()
  }

  destroy() {
    this._cancelSubscribe()

    this.registerRecordStore.clear()
    this.dynamicRegisterRecordStore.clear()

    this.undoRecordStack.clear()
    this.redoRecordStack.clear()
    this.dynamicRegisterRecordResetStack.clear()

    this.waitRecordData = null
    this.waitFunctionalStack.clear()

    this.tempStack.clear()

    this.store = null
  }

  _initMutationTypeToStateFieldStore() {
    this.mutationTypeToStateFieldStore = Object.create(null)
    this.storeModules = this.store._modules.root._rawModule.modules

    for (const includeMutation of this.includes) {
      this._setMutationTypeToStateFieldStore(includeMutation)
    }
  }

  _setMutationTypeToStateFieldStore(includeMutation) {
    for (const moduleName in this.storeModules) {
      const item = this.storeModules[moduleName]
      const { mutations, namespaced = false } = item
      for (const localMutationType in mutations) {
        const mutationType = namespaced ? `${moduleName}/${localMutationType}` : localMutationType
        if (includeMutation === mutationType) {
          const mutationSetter = mutations[localMutationType]
          const string = mutationSetter.toString()
          const stateField = string.replace(/[\s\S]*?\.(.*)?=[\s\S]*/ig, '$1').trim()
          const expression = `${moduleName}.${stateField}`
          this.mutationTypeToStateFieldStore[mutationType] = expression
          return
        }
      }
    }
  }

  _initSubscribe() {
    this._initMutationTypeToStateFieldStore()

    this._cancelSubscribe = this.store.subscribe((mutation) => {
      if (!this.isRegister || this.committing) {
        return
      }
      const { type: field, payload: value } = mutation
      if (this.mutationTypeToStateFieldStore[field]) {
        this.waitRecordData[field] = value
      }
    })
  }

  _getLastRecordValueByField(field) {
    const size = this.undoRecordStack.getSize()
    let value
    let i = 0
    while (i !== size) {
      const recordData = this.undoRecordStack.getByIndex(i)
      const { data } = recordData
      if (data && data.hasOwnProperty(field)) {
        value = data[field]
        i = size
      } else {
        i++
      }
    }
    value = value || this.registerRecordStore.get(field)
    return value
  }

  register() {
    for (const field of this.includes) {
      const stateField = this.mutationTypeToStateFieldStore[field]
      const value = parsePath(this.store.state, stateField)
      this.registerRecordStore.set(field, fastClone(value))
    }

    this.dynamicRegisterRecordStore.clear()

    this.undoRecordStack.clear()
    this.redoRecordStack.clear()

    this.waitRecordData = Object.create(null)
    this.waitFunctionalStack.clear()

    this._updateAbility()

    this.isRegister = true
  }

  hasRegisterRecordField(field) {
    return this.includes.includes(field)
  }

  getRegisterRecordField(field) {
    return this.registerRecordStore.get(field)
  }

  addRegisterRecordField(field, value, reset) {
    if (!this.hasRegisterRecordField(field)) {
      this.includes.push(field)
      this._setMutationTypeToStateFieldStore(field)
    }

    const stateField = this.mutationTypeToStateFieldStore[field]

    value = value !== undefined ? value : parsePath(this.store.state, stateField)

    setPathValue(this.store.state, stateField, value)

    this.registerRecordStore.set(field, fastClone(value))

    this.dynamicRegisterRecordStore.set(field, true)

    if (reset) {
      this.dynamicRegisterRecordResetStack.unshift({
        field,
        reset,
      })
    }
  }

  unshiftFunctional(functional) {
    this.waitFunctionalStack.unshift(functional)
  }

  record(message) {
    if (!this.isRegister) {
      return
    }

    if (this.needDeepCompare) {
      for (const field in this.waitRecordData) {
        const newVal = this.waitRecordData[field]
        const oldVal = this._getLastRecordValueByField(field)
        if (deepCompare(newVal, oldVal)) {
          delete this.waitRecordData[field]
        }
      }
    }

    const isHasData = !isEmptyObject(this.waitRecordData)
    const isHasFunctional = !!this.waitFunctionalStack.getSize()

    if (this.debug && !isHasData && !isHasFunctional) {
      console.warn('nothing to record')
    }

    const recordData = {
      message,
    }

    if (isHasData) {
      const data = fastClone(this.waitRecordData)
      recordData.data = data
    }

    if (isHasFunctional) {
      const functionalStackStore = this.waitFunctionalStack.getStore().slice()
      recordData.functionalStackStore = functionalStackStore
    }

    this.undoRecordStack.unshift(recordData)
    this.redoRecordStack.clear()

    this.waitRecordData = Object.create(null)
    this.waitFunctionalStack.clear()

    this._updateAbility()
  }

  undo() {
    if (!this.isRegister) {
      return
    }

    if (!this.properties.isCanUndo) {
      return
    }

    this.committing = true

    const recordData = this.undoRecordStack.shift()
    this.redoRecordStack.unshift(recordData)

    this._updateAbility()

    const { data, functionalStackStore } = recordData

    if (data) {
      for (const field in data) {
        const value = this._getLastRecordValueByField(field)
        this.store.commit(field, value)
      }
    }

    if (functionalStackStore) {
      this.tempStack.set({ store: functionalStackStore.slice() })
      this.tempStack.consume((item) => {
        const { undo } = item
        undo()
        return true
      })
    }

    this.committing = false
  }

  redo() {
    if (!this.isRegister) {
      return
    }

    if (!this.properties.isCanRedo) {
      return
    }

    this.committing = true

    const recordData = this.redoRecordStack.shift()
    this.undoRecordStack.unshift(recordData)

    this._updateAbility()

    const { data, functionalStackStore } = recordData

    if (data) {
      for (const field in data) {
        const value = data[field]
        this.store.commit(field, value)
      }
    }

    if (functionalStackStore) {
      this.tempStack.set({ store: functionalStackStore.slice() })
      this.tempStack.consume((item) => {
        const { redo } = item
        redo()
        return true
      })
    }

    this.committing = false
  }

  reset() {
    if (!this.isRegister) {
      return
    }

    if (!this.properties.isCanUndo && !this.properties.isCanRedo) {
      return
    }

    const hasResetFields = Object.create(null)

    this.dynamicRegisterRecordResetStack.consume((item) => {
      const { field, reset } = item
      const value = this.getRegisterRecordField(field)
      reset(value)
      hasResetFields[field] = true
      return true
    })

    const data = {}

    for (let i = 0; i < this.includes.length; i++) {
      const field = this.includes[i]

      if (this.dynamicRegisterRecordStore.has(field)) {
        this.includes.splice(i, 1)
        i--
      }

      if (hasResetFields[field]) {
        continue
      }

      data[field] = this.getRegisterRecordField(field)
    }

    for (const field in data) {
      const value = data[field]
      this.store.commit(field, value)
    }

    this.undoRecordStack.consume((item) => {
      const { functionalStackStore } = item

      if (functionalStackStore) {
        this.tempStack.set({ store: functionalStackStore })
        this.tempStack.consume((item) => {
          const { undo } = item
          undo()
          return true
        })
      }

      return true
    })

    this._clear()
  }

  clear() {
    this.committing = true

    this._clear()

    this.committing = false
  }

  _clear() {
    for (const [field, value] in this.registerRecordStore) {
      this.store.commit(field, value)
    }

    this.dynamicRegisterRecordStore.clear()

    this.undoRecordStack.clear()
    this.redoRecordStack.clear()

    this.waitRecordData = Object.create(null)
    this.waitFunctionalStack.clear()

    this.tempStack.clear()

    this._updateAbility()
  }

  _updateAbility() {
    {
      const size = this.undoRecordStack.getSize()
      this.properties.isCanUndo = !!size
    }

    {
      const size = this.redoRecordStack.getSize()
      this.properties.isCanRedo = !!size
    }
  }
}
