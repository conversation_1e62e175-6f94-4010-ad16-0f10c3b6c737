import Vue from 'vue'
import ElementUI from 'element-ui'
import { Loading, Message } from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '@/lib/theme/index.css'
import { OverlayScrollbarsPlugin } from 'overlayscrollbars-vue'
import 'overlayscrollbars/css/OverlayScrollbars.css'
import registryMessageAlert from '@/components/func-components/MessageAlert/index'
import VueButton from '@/components/func-components/VueButton/index'
import registryDialog from '@/components/func-components/Dialog'

// 公共弹窗注册
Vue.use(registryMessageAlert)
// 公共按钮注册
Vue.use(VueButton)
Vue.use(registryDialog)

// 全局注册
Vue.use(ElementUI)

Vue.prototype.$message = Message
Vue.prototype.$el_loading = Loading.service
Vue.use(OverlayScrollbarsPlugin)
