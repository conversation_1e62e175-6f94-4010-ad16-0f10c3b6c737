import store from '@/store'

// 注意，我们这里写的自定义指令，传递内容是一个数组，也就说，按钮权限可能是由
// 多个因素决定的，如果你的业务场景只由一个因素决定，自定义指令也可以不传递一个数组，
// 只传递一个字符串就可以了
const permission = {
  bind: (el, binding) => { // 每当指令绑定到元素上的时候，会立即执行这个bind函数，只执行一次
    // 获取用户使用自定义指令绑定的内容
    const { value } = binding
    const rightsType = value[0] // 权限类型：增删改查的按钮
    const action = value[1] // 对应权限所做的操作：按钮置灰不可用disabled、隐藏按钮delete等
    // 获取用户所有的权限
    const permission = store.state.permission
    // console.log('permission：', permission, 4444)
    if (rightsType) {
      // 判断传递进来的按钮权限，用户是否拥有
      const hasPermission = permission.some((item) => {
        return rightsType === item
      })
      if (!hasPermission) {
        // 当用户没有这个按钮权限时，置灰/隐藏这个按钮(可扩展更多操作)
        switch (action) {
          case 'delete':
            el.style.display = 'none'
            break
          case 'disabled':
            if (el.disabled !== undefined) {
              el.disabled = true
            } else {
              el.classList.add('disable-btn') // 需要自定义.disable-btn的样式，样式名不能随意更改
            }
            break
          default:
            break
        }
      }
    } else {
      throw new Error('permission指令使用规则错误')
    }
  }
}

export default permission
