// font-size
$hg-normal-fontsize: 14px;
$hg-medium-fontsize: 16px;
$hg-large-fontsize: 24px;
$hg-small-fontsize: 12px;

// color
$hg-main-black: #1D1D1F;
$hg-main-blue: #3054cc;
$hg-primary-fontcolor: #E4E8F7;
$hg-secondary-fontcolor: #83868F;
$hg-disable-fontcolor: #54565C;
$hg-tag-bg-color: #2D2F33;
$hg-tag-fontcolor: #2E313D;
$hg-active-fontcolor: #ffffff;
$hg-border-color: #38393D;
$hg-border-radius2: 2px;
$hg-border-radius4: 4px;
$hg-background-color: #121213;
$hg-link-color: #5169B8;
$hg-success-color: #6CC740;
$hg-warning-color: #C78840;
$hg-error-color: #C74040;
$hg-normal-status-color: #535B7A;
$hg-hover-bg-color: #262629;
$hg-active-iconcolor: #83868F;
$hg-button-hover-fontcolor: #3760EB;
$hg-button-active-fontcolor: #2B4BB8;
// 新增Figma设计颜色变量
$hg-search-bg-color: #0C0C0E;
$hg-search-border-color: #3D4047;
$hg-sidebar-bg-color: #1B1D22;

$hg-main-margin: 24px;
$hg-main-padding: 24px;
$hg-height-60: 60px;
$hg-height-58: 58px;
$hg-height-56: 56px;
$hg-height-44: 44px;
$hg-height-40: 40px;
$hg-height-34: 34px;
$hg-height-32: 32px;

// 遮罩层的背景色
@mixin shade-background {
  background-color: rgba($color: $hg-background-color, $alpha: 0.8);
}
// 鼠标移上可点击的按钮改变字体颜色
@mixin hover {
  color: $hg-button-hover-fontcolor;
}
// 鼠标点击按钮后的字体颜色
@mixin active {
  color: $hg-button-active-fontcolor;
}

// 自动以样式
@mixin box-shadow {
  box-shadow: 0px 4px 12px rgba(18, 19, 20, 0.35), 0px 0px 12px rgba(18, 19, 20, 0.08);
}

@mixin bg-box-shadow {
  box-shadow: 0px 12px 32px 0px #121314,0px 8px 24px 0px #121314,0px 0px 16px 0px #121314 !important;
}

/**
* 公共样式设置
*/
html, body {
  font-size: 14px;
}
* {
  box-sizing: border-box;
}
.pos-rel{
  position: relative;
}
.pos-abs{
  position: absolute;
}
.pos-abs-center{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}

.finger {
  cursor: pointer;
}

.fwb {
  font-weight: bold;
}

.space-between {
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.flex-start{
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: flex-start;
  align-items: flex-start;
}

.flex-end{
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
}

.flex-items-center {
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
}

.container {
  background: $hg-background-color;
}
.flex-center {
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: center;
  justify-content: center;
}

@keyframes FadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-50%, -150%, 0);
    transform: translate3d(-50%, -150%, 0)
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(-50%, 0, 0);
    transform: translate3d(-50%, 0, 0)
  }
}

@keyframes FadeOutUp {
  0% {
    opacity: 1;
    -webkit-transform: translate3d(-50%, 0, 0);
    transform: translate3d(-50%, 0, 0)
  }
  100% {
    opacity: 0;
    -webkit-transform: translate3d(-50%, -150%, 0);
    transform: translate3d(-50%, -150%, 0)
  }
}

@keyframes OpacityDown {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes OpacityUp {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.border{
  border: 1px solid #38393D;
  border-radius: 4px;
}

.requiredInput{
  color: $hg-error-color !important;
}

.disable-btn { // 权限控制只读时添加的样式
  color: $hg-disable-fontcolor !important;
  cursor: not-allowed;
  pointer-events: none;
  &:hover {
    color: $hg-disable-fontcolor !important;
  }
}

@import './commonicon.scss'; // css
