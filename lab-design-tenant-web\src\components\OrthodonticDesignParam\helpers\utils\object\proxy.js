import { isPlainObject } from '../type'
import { hasOwn, defineProp } from './other'

let temp

export function proxyProp(target, origin, nativeProperty, aliasProperty, enumerable) {
  defineProp(target, '__proxyProp__', true)

  aliasProperty = aliasProperty || nativeProperty

  Object.defineProperty(target, aliasProperty, {
    enumerable: !!enumerable,
    configurable: true,
    get() {
      const isFunction = origin[nativeProperty] instanceof Function

      temp = origin

      return isFunction ? origin[nativeProperty].bind(origin) : origin[nativeProperty]
    },
    set(newVal) {
      origin[nativeProperty] = newVal
    }
  })
}

export function proxyProps(target, origin, properties) {
  for (const property of properties) {
    const isObject = isPlainObject(property)

    if (isObject) {
      for (const nativeProperty in property) {
        const aliasProperty = property[nativeProperty]
        proxyProp(target, origin, nativeProperty, aliasProperty)
      }
    } else {
      proxyProp(target, origin, property)
    }
  }
}

export function getProxyOrigin(target, property) {
  if (!target.__proxyProp__) {
    return target
  }

  if (!hasOwn(target, property)) {
    return target
  }

  // eslint-disable-next-line
  const getterValue = target[property]

  if (!hasOwn(temp, property)) {
    return target
  }

  return temp
}
