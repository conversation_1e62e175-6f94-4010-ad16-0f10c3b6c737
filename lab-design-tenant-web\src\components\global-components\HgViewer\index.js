import viewerjs from 'viewerjs';
import 'viewerjs/dist/viewer.css';
import './index.scss';

class HgViewer {

  constructor() {
    this.viewer;
  }

  open(options) {

    if(this.viewer) {
      this.viewer.destroy();
    }

    this.node = document.createElement('div');
    const imgList = options.imgList;
    let node = this.node;

    if (imgList) {
      imgList.forEach(item => {
        let child = document.createElement('img');
        if(typeof item === 'string') {
          child.src = item;
        }else {
          const { src, fileUrl, name } = item;
          child.src = src || fileUrl;
          child.alt = name;
        }
        node.append(child);
      });
    }
    
    this.init(options);

    this.viewer.show();
    
    setTimeout(() => {
      const viewerBtnList = this.viewer.toolbar.querySelectorAll('ul>li');
      viewerBtnList.forEach(dom => {
        const iDom = document.createElement('i');
        iDom.className = `hg-common-iconfont hg-icon-${dom.className.split(' ')[0]}`;
        dom.appendChild(iDom);
      })
    }, 0);
    
  }

  init(options) {

    let toolbar = {
      zoomIn: {
        size: 'large',
        click: () => {
          this.viewer.zoom(0.1, true);
        }
      },
      zoomOut: {
        size: 'large',
        click: () => {
          this.viewer.zoom(-0.1, true);
        }
      },
      oneToOne: {
        size: 'large',
        click: () => {
          this.viewer.toggle();
        }
      },
      reset: {
        size: 'large',
        click: () => {
          this.viewer.reset();
        }
      },
      prev: {
        size: 'large',
        click: () => {
          this.viewer.prev(true);
        }
      },
      play: {
        size: 'large',
        click: () => {
          this.viewer.play(true);
        }
      },
      next: {
        size: 'large',
        click: () => {
          this.viewer.next(true);
        }
      },
      rotateLeft: {
        size: 'large',
        click: () => {
          this.viewer.rotate(-90);
        }
      },
      rotateRight: {
        size: 'large',
        click: () => {
          this.viewer.rotate(90);
        }
      },
      flipHorizontal: {
        size: 'large',
        click: () => {
          this.viewer.scaleX(-this.viewer.imageData.scaleX || -1);
        }
      }, 
      flipVertical: {
        size: 'large',
        click: () => {
          this.viewer.scaleY(-this.viewer.imageData.scaleY || -1);
        }
      },
    };

    if(options.download) {
      toolbar.download = {
        size: 'large',
        click: () => {
          const a = document.createElement('a');
          a.href = this.viewer.image.src;
          a.download = this.viewer.image.alt;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        }
      };
    }

    this.viewer = new viewerjs(this.node, {
      zIndex: 9999,
      hide: () => {
        if (options.hide) {
          options.hide();
        }
      },
      toolbar,
      initialViewIndex: options.initialViewIndex || 0
    });
  }

}

export default new HgViewer();