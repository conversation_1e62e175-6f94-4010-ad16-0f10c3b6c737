
import i18n from '@/public/i18n/index';
/**
 * 获取账单状态
 * @param {*} value 状态值
 * @returns {string} 状态名称
 */
export const billStatus = (value) => {
  const list = new Map([
    [1, i18n.t('bill.unsettled')], //待结算
    [2, i18n.t('bill.settled')]     //已结算
  ]);
  return list.get(value);
};

/**
 * 获取结算类型
 * @param {*} value 类型值
 * @returns {string} 类型名称
 */
 export const settlementType = (value) => {
  const list = new Map([
    [0, i18n.t('heypoint.customer.monthly')],  //月结
    [1, i18n.t('heypoint.customer.prepaidMonth')]  //充值
  ]);
  return list.get(value);
};

/**
 * 获取结算币种
 * @param {*} value 类型值
 * @returns {string} 类型名称
 */
 export const settlementCurrency = (value) => {
  const list = new Map([
    [0, i18n.t('bill.info.USD')],
    [1, i18n.t('bill.info.CNY')],
    [2, i18n.t('bill.info.EUR')],
    [3, i18n.t('bill.info.JPY')],
    [4, i18n.t('bill.info.AUD')]
  ]);
  return list.get(value);
};

/**
 * 获取结算币种英文缩写
 * @param {*} value 类型值
 * @returns {string} 类型名称
 */
 export const settlementCurrencyAbbreviation = (value) => {
  const list = new Map([
    [0, 'USD'],
    [1, 'CNY'],
    [2, 'EUR']
  ]);
  return list.get(value);
};

/**
 * 每3位数加逗号
 * @param {*} val 数值
 * @returns {string} 加逗号后的值
 */
export function capitalize(val) {
  return (+val || 0).toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
}

/**
 * 给字符串前面加减号
 * @param {*} val 数值
 * @returns {string} 加减号后的值
 */
export function addMinusSign(val) {
  if (val && val !== '0.00') {
    return '-' + val;
  } else {
    return '0.00';
  }
}

/**
 * 获取操作类型
 * @param {*} operatType 操作类型code
 * @returns 
 */
export function operatTypeFilter(operatType) {
  if (!operatType) return '';
  const typeMap = {
    1: i18n.t('heypoint.customer.operate.recharge'),
    2: i18n.t('heypoint.customer.operate.give'),
    3: i18n.t('heypoint.customer.operate.overdue'),
    4: i18n.t('heypoint.customer.operate.consume'),
    5: i18n.t('heypoint.customer.operate.refund'),
  };
  return typeMap[operatType];
}

/**
 * 获取操作类型
 * @param {*} rechargeSource 来源code
 * @returns 
 */
 export function rechargeSourceFilter(rechargeSource) {
  if (!rechargeSource) return '';
  const typeMap = {
    'Normal': '',
    'RedeemCode': i18n.t('heypoint.operateLog.redeemCode'),
    'DiscountCode': i18n.t('heypoint.operateLog.discountCode'),
  };
  return typeMap[rechargeSource];
}