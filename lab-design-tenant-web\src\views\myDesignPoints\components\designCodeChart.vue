<template>
  <div class="design-code">
    <div class="design-header">
      <span class="title">{{lang('designfenbu')}}</span><span class="date">{{date}}</span>
    </div>

    <div id="design-chart" class="design-chart"></div>
    <div class="all-chart-details">
      <!-- <div id="design-chart" class="design-chart"></div> -->
      <div class="chart-details">
        <p v-for="item in designTypeProportions" :key="item.designCode" class="list" @click="openChart(item)">
          <span :style="'background:' + item.itemStyle.color" class="point1"></span>{{item.name}}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
import { mapGetters } from 'vuex';
  export default {
    name: 'designCodeChart',
    data() {
      return {
        
      }
    },
    props: {
      date: String,
      designTypeProportions: Array
    },
    computed: {
      ...mapGetters(['language']),
    },
    mounted () {

    },
    watch: {
      designTypeProportions: {
        handler(newVal, oldVal) {
          this.getMonthChart();
        }
      }
    },
    methods: {
      lang: getLang('designpoints'),
      getMonthChart() {
        let designChart = this.$echarts.init(document.getElementById('design-chart'));
        //配置图表
        let option = {
          tooltip: {
            trigger: 'item',
            backgroundColor: '#27292E',
            borderColor: '#27292E',
            position: (point)=> ([point[0], point[1]]),
            formatter: (param) => {
              let content = `<p style="color: #fff;">${param.marker} ${param.name}<span style="margin-left: 16px;color: #fff;">${param.data.qty} | ${(param.data.proportion * 100).toFixed(2)}%</span></p>`;
              return content;
            },
          },
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 12,
                  color: '#AAADB3',
                  formatter: (params) => {
                    // 控制换行逻辑，这里是英文遇到空格换行
                    if (this.language == 'en') {
                        return params.name.replace(/ /g, '\n');
                    }
                    return params.name;
                  },
                }
              },
              // labelLine: {
              //   show: false
              // },
              data: this.designTypeProportions
            }
          ]
        };
        designChart.setOption(option);
        // 饼图的点击事件
        designChart.on('click', (params) => {
          if (params.componentType === 'series') {
            if (params.seriesType === 'pie') {
              const dataIndex = params.dataIndex;
              const data = params.data;
              if(data.designCode != 'other'){
                this.$emit('click', data);
              }
            }
          }
        });
      },
      openChart(data){
        if(data.designCode != 'other'){
          this.$emit('click', data);
        }
      }
    },
  }
</script>

<style lang="scss" scoped>
.design-code{
  position: relative;
  // margin-top: 24px;
  min-width: 554px;
  height: 400px;
  padding: 20px;
  .design-header{
    position: relative;
    display: flex;
    align-items: center;
    .title{
      color: #AAADB3;
      margin-right: 8px;
    }
    .date{
      color: #AAADB3;
    }
    .all-points{
      position: absolute;
      right: 38px;
      display: flex;
    }
  }
  .design-chart{
    display: inline-block;
    width: 70%;
    height: 100%;
  }
  .all-chart-details{
    position: absolute;
    right: 0;
    top: 48px;
    .chart-details{
      display: inline-block;
      min-width: 160px;
      .list{
        margin-right: 6px;
        padding: 4px;
        cursor: pointer;
      }
      .point1{
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }
    }
  }
}
</style>