import request from '../axios';
import { server } from '@/config';

const axios = request.axios;

/**
 * 获取租户端订单状态列表（用做查询条件）
 */
export const getOrderStatus = () => {
  return axios.get(`${server.systemCommonServer}/getOrderStatus`, { params: { side: 'tenant' } });
};

/**
 * 获取全部设计类型
 */
export const getAllDesignTypes = () => {
  return axios.get(`${server.systemCommonServer}/getDesignAll`);
};

/**
 * 获取默认的黑格参数方案
 */
export const getDefaultParam = () => {
  return axios.get(`${server.systemCommonServer}/getHgPara`);
};

/**
 * 根据code获取参数方案
 */
export const getParamByCode = ({designCode,orgCode,userCode}) => {
  const data = {
    designTypeCode: designCode,
    orgCode,
    userCode,
  };
  return axios.post(`${server.systemCommonServer}/designParaDetail`, data);
};


/**
 * 获取支架下拉选择类型
 */
export const getRpdSelectList = () => {
  return axios({
    url: `${server.orderServer}/rpd`,
    method: 'GET'
  });
};

// 退出登陆
export const logout = () => {
  return axios({
    url: '/user-basic/user/v1/logout',
    method: 'POST'
  });
};

// 根据组织和交期获取设计类型
export const getDesignTypeByDelivery = ({orgCode, deliveryCode}) => {
  const data = {
    orgCode,
    deliveryCode,
  };
  return axios.post(`${server.systemCommonServer}/getDesignAllyDeliveryCode`, data);
}

// 获取全部设计类型，根据skucode来区分三级类的
export const getDesignSkuAll = () => {
  return axios.get(`${server.designerPoints}/getDesignSkuAll`);
};

// 获取i18n字典列表
export const getI18nDict = () => {
  return axios({
    url: `${server.usercommon}/getI18nDict`,
    method: 'GET'
  })
}