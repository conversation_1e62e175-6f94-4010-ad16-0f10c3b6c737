<template>
  <div class="radio-group">
    <span
      class="radio-item"
      v-for="item in option"
      :key="item.value"
      @click="changeValue(item.value)"
    >
      <span class="radio-circle" :class="{ active: value === item.value }">
        <span class="selected"></span>
      </span>
      <span>{{ item.label }}</span>
    </span>
  </div>
</template>
<script>
export default {
  name: "RadioGroup",
  props: {
    keyName: {
      type: String,
      default: ''
    },
    modelName: {
      type: String,
      default: ''
    },
    option: {
      type: Array,
      default() {
        return [];
      },
    },
    value: {
      type: String,
      default: ''
    },
  },
  data() {
    return {};
  },
  methods: {
    changeValue(val) {
      this.$emit("change", {
        keyName: this.keyName,
        modelName: this.modelName,
        val
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.radio-group {
  display: inline-block;
  > * {
    display: inline-block;
  }
  .radio-item {
    color: $hg-secondary-fontcolor;
    display: inline-flex;
    align-items: center;
    margin-right: 32px;
    .radio-circle {
      width: 12px;
      height: 12px;
      box-sizing: border-box;
      border: 1px solid $hg-border-color;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-right: 10px;
      &.active {
        color: $hg-primary-fontcolor;
        .selected {
          background-color: $hg-main-blue;
          width: 6px;
          height: 6px;
          border-radius: 50%;
        }
      }
    }
  }
}
</style>
