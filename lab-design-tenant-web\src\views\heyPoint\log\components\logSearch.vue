<template>
  <div class="search-content">
    <el-form :inline="true" :model="logSearch" class="demo-form-inline">
      <el-form-item>
        <!-- @input="handleInput" -->
        <el-autocomplete
          v-model="logSearch.orgName"
          :placeholder="$t('heypoint.customer.customerNameNo')"
          value-key="orgName"
          popper-class="log-org-search"
          :fetch-suggestions="querySearchAsync"
          @select="handleSelect"
          clearable
          @change="changeSelect"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item :label="$t('heypoint.customer.operate.type')" class="special-item">
        <el-select v-model="logSearch.operationType" :placeholder="$t('heypoint.customer.operate.all')" @change="search" clearable>
          <el-option v-for="(type, index) in typeOptions" :key="index" :label="$t(type.label)" :value="type.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('heypoint.operateLog.user')">
        <el-select v-model="logSearch.createUserCode" :placeholder="$t('heypoint.customer.operate.all')" @change="search" clearable>
          <el-option v-for="(agent, index) in agentList" :key="index" :label="agent.createdUserName" :value="agent.createdUser"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('heypoint.customer.operate.settlementType')">
        <el-select v-model="logSearch.settlementType" :placeholder="$t('heypoint.customer.operate.all')" @change="search" clearable>
          <el-option v-for="(settlement, index) in settlementTypeList" :key="index" :label="$t(settlement.label)" :value="settlement.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('heypoint.customer.date')" class="special-date">
        <date-range-picker valueFormat="timestamp" :format="'yyyy-MM-dd HH:mm:ss'" v-model="logSearch.date" @change="search"></date-range-picker>
      </el-form-item>
      <!-- <el-form-item :label="$t('heypoint.customer.operate.settlementType')">
        <el-radio v-model="logSearch.settlementType" :label="0" @change="search">{{$t('heypoint.customer.monthly')}}</el-radio>
        <el-radio v-model="logSearch.settlementType" :label="1" @change="search">{{$t('heypoint.customer.prepaidMonth')}}</el-radio>
      </el-form-item> -->
       <el-form-item :label="$t('heypoint.operateLog.source')">
        <el-select v-model="logSearch.rechargeSource" :placeholder="$t('heypoint.customer.operate.all')" @change="search" clearable>
          <el-option v-for="(source, index) in rechargeSourceList" :key="index" :label="$t(source.label)" :value="source.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import DateRangePicker from '@/components/DateRangePicker';
import { searchAccountWater, findCreateUserName } from '@/api/heypoint';
import { SPECIAL_CHAR_REGEX } from '@/public/constants';

export default {
  name: 'logSearch',
  components: {
    DateRangePicker,
  },
  data() {
    return {
      logSearch: {
        orgCode: null,
        orgName: '',
        operationType: null,
        createUserCode: null,
        date: [0, 0],
        settlementType: null,
        rechargeSource: null
      },
      agentList: [], //经办人
      typeOptions: [
        //操作类型
        { name: '全部', label: 'heypoint.customer.operate.all', value: null },
        { name: '充值', label: 'heypoint.customer.operate.recharge', value: 1 },
        { name: '赠送', label: 'heypoint.customer.operate.give', value: 2 },
        { name: '过期', label: 'heypoint.customer.operate.overdue', value: 3 },
      ],
      rechargeSourceList: [//来源
        { name: '全部', label: 'heypoint.customer.operate.all', value: null },
        { name: '兑换码', label: 'heypoint.operateLog.redeemCode', value: 'RedeemCode' },
        { name: '优惠券', label: 'heypoint.operateLog.discountCode', value: 'DiscountCode' },
      ],
      settlementTypeList: [//来源
        { name: '全部', label: 'heypoint.customer.operate.all', value: null },
        { name: '月结', label: 'heypoint.customer.monthly', value: 0 },
        { name: '预充', label: 'heypoint.customer.prepaidMonth', value: 1 },
      ],
      lastQueryList: [],
      lastQuerySrting: '',
    };
  },
  mounted() {
    this.findCreateUserName();
  },
  methods: {
    // 客户名远程搜索
    querySearchAsync(queryString, callback) {

      // const replaceString = queryString.replace(SPECIAL_CHAR_REGEX, '');
      const replaceString = queryString
      
      if(this.lastQuerySrting === replaceString && queryString !== '') {
        callback(this.lastQueryList);
        return;
      }
      let data = {
        orgName: replaceString,
      };
      searchAccountWater(data).then((res) => {
        if (res.code === 200) {
          this.lastQuerySrting = replaceString;
          this.lastQueryList = res.data;
          callback(res.data);
        }
      });
      
    },
    // 选择下拉项
    handleSelect(item) {
      this.logSearch.orgCode = item.orgCode;
      this.search();
    },
    // 清空下拉项 enter键，都要区分值，处理
    changeSelect() {
      this.logSearch.orgCode = null;
      this.search();
    },

    // 获取经办人
    findCreateUserName() {
      findCreateUserName().then((res) => {
        if (res.code == 200) {
          this.agentList = res.data;
        }
      });
    },
    search() {
      this.logSearch.createUserCode = this.logSearch.createUserCode === '' ? null : this.logSearch.createUserCode;
      this.logSearch.operationType = this.logSearch.operationType == '' ? null : this.logSearch.operationType;
      this.logSearch.rechargeSource = this.logSearch.rechargeSource == '' ? null : this.logSearch.rechargeSource;
      // 构建搜索的条件
      let searchList = {
        createUserCode: this.logSearch.createUserCode,
        endTime: this.logSearch.date[1] == 0 ? 0 : this.logSearch.date[1]/1000,
        operationType: this.logSearch.operationType,
        orgCode: this.logSearch.orgCode,
        settlementType: this.logSearch.settlementType,
        startTime: this.logSearch.date[0] == 0 ? 0 : this.logSearch.date[0]/1000,
        date: this.logSearch.date,
        orgName: this.logSearch.orgName,
        rechargeSource: this.logSearch.rechargeSource
      };
      this.$emit('getSearchList', searchList);
    },
    handleInput(value) {
      this.logSearch.orgName = value.replace(SPECIAL_CHAR_REGEX,'');
    }
  },
};
</script>

<style lang="scss" scoped>
.search-content {
  width: 100%;
  /deep/.el-form {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .el-form-item {
      margin-right: 20px;
    }
    .special-date{
      .el-date-editor{
        width: 430px;
      }
    }
  }
}
</style>
<style lang="scss">
.log-org-search {
  background-color: $hg-main-black;
  border: 1px solid $hg-main-black;
  li:hover {
    background-color: #262629;
    color: #83868f;
  }
}
.log-org-search[x-placement^='bottom'] .popper__arrow::after {
  border-top-width: 0;
  border-bottom-color: $hg-main-black;
}
.log-org-search[x-placement^='bottom'] .popper__arrow {
  border-top-width: 0;
  border-bottom-color: $hg-main-black;
}
</style>
