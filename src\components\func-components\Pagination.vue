<!--
  * 组件名称 Pagination
  * @desc 公用分页组件
  * <AUTHOR>
  * 组件说明: 组件的total由父组件传入，currentPage和currentPageSize传回给父组件
-->
<template>
  <div ref="container" :class="['pagination-container']" :style="pageStyle">
    <el-pagination
      ref="paginationPage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="page"
      :page-sizes="pageSizes"
      :page-size="psize"
      :layout="paginationLayout"
      :total="total"
    >
      <p class="go-page">{{ $t("common.skipTo") }}</p>
      <div class="edit-input">
        <Input
          type="Number"
          size="medium"
          textAlign="center"
          paddingLeft="6px"
          paddingRight="6px"
          :inputContent.sync="inputValue"
          @enterInput="enterInput"
          @blurInput="blurInput"
        />
      </div>
      <p class="page-text">{{ $t("common.page") }}</p>
    </el-pagination>
  </div>
</template>

<script>
import Input from "@/components/func-components/Input";

export default {
  name: "Pagination",
  components: {
    Input,
  },
  props: {
    total: {
      type: Number,
      default: 0,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageNo: {
      type: Number,
      default: 1,
    },
    totalPages: {
      type: Number,
      default: 1,
    },
    pageSizes: {
      type: Array,
      default: ()=>{
        return [4, 6, 8, 10, 20]
      }
    },
    pageStyle: {
      type: Object,
      default: () => {
        return { background: '#1D1D1F' }
      }
    },
    showTotal: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    paginationLayout() {
      return this.showTotal 
        ? "total, prev, pager, next, sizes, slot"
        : "prev, pager, next, sizes, slot"
    }
  },
  data() {
    return {
      page: this.pageNo,
      psize: this.pageSize,
      inputValue: "",
    };
  },
  watch: {
    pageNo(val) {
      if (val != this.inputValue) {
        this.inputValue = "";
      }
      this.page = val;
      this.$nextTick(() => {
        this.$refs.paginationPage.internalCurrentPage = val;
      });
    },
    pageSize(val) {
      this.psize = val;
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleSizeChange(val) {
      this.inputValue = "";
      this.$emit("changePageSize", val);
    },
    handleCurrentChange(val) {
      this.$emit("changePageNo", val);
    },
    enterInput(val) {
      this.blurInput(val);
    },
    blurInput(val) {
      if (val > this.totalPages) {
        this.inputValue = this.page;
      } else {
        this.handleCurrentChange(val * 1);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 58px;
  padding: 0 16px;
  background: $hg-main-black;
  border-top: 1px solid $hg-border-color;
  
  ::v-deep .el-pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    
    .el-pagination__total {
      color: $hg-primary-fontcolor;
      font-size: 14px;
      margin-right: auto;
      order: -1;
      font-weight: normal;
    }
    
    .btn-prev,
    .btn-next {
      width: 32px;
      height: 32px;
      border: 1px solid $hg-border-color;
      border-radius: 4px;
      background: transparent;
      color: $hg-primary-fontcolor;
      margin: 0 4px;
      
      &:disabled {
        background: $hg-border-color;
        color: $hg-disable-fontcolor;
      }
    }
    
    .el-pager {
      margin: 0 8px;
      
      li {
        width: 32px;
        height: 32px;
        border: 1px solid $hg-border-color;
        border-radius: 4px;
        background: transparent;
        color: $hg-primary-fontcolor;
        margin: 0 4px;
        line-height: 30px;
        
        &:hover {
          border-color: $hg-main-blue;
        }
        
        &.active {
          background: $hg-main-blue;
          border-color: $hg-main-blue;
          color: white;
        }
      }
    }
    
    .el-pagination__sizes {
      margin-left: 16px;
      
      .el-input {
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          background: transparent;
          border: 1px solid $hg-border-color;
          border-radius: 4px;
          color: $hg-primary-fontcolor;
          text-align: center;
        }
      }
    }
    
    .edit-input {
      width: 44px;
      margin: 0 8px;
    }
    
    .go-page,
    .page-text {
      color: $hg-primary-fontcolor;
      font-size: 14px;
      margin: 0 4px;
    }
  }
}
</style>

