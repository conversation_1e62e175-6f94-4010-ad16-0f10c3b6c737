import AWS from 'aws-sdk';
import { getDownloadUrl } from '@/api/file';
/**
 * 订单批量下载后端直接返回可下载的链接
 * @param {String} fileUrl 可以直接下载的链接
 * @param {String} fileName 文件名
 */
export const directDown = (fileUrl, fileName) => {
  return new Promise(resovle => {
    ajaxGetBlob(fileUrl)
    .then((blob) => {
      handleSaveByBlod(blob, fileName);
      resovle(true);
    })
    .catch((err) => {
      // this.$message.warning('下载错误')
    });
  });
}

/**
 * 请求文件地址获取blob类型数据
 * @param {*} url 文件地址
 * @param {*} progress 进度回调函数
 */
export const ajaxGetBlob = (url, progress) => {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest();
    // 20240424新增了token，不走白名单
    const token = window.localStorage.getItem('AccessToken')
    xhr.open('GET', url, true);
    xhr.setRequestHeader('authorization', `Bearer ${token}`)
    xhr.responseType = 'blob';
    xhr.onload = () => {
      // console.log('xhr: ', xhr, xhr.getResponseHeader('Content-Disposition'));
      if (xhr.status === 200) {
        resolve(xhr.response);
      }
    };
    xhr.onprogress = (e) => {
      progress && progress(((e.loaded / e.total) * 100) | 0);
    };

    xhr.send();
  });
};

/**
 * 通过bold对象保存文件 （可重命名）
 * @param {*} blob blob数据
 * @param {*} filename 文件名称
 */

const handleSaveByBlod = (blob, filename) => {
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename);
  } else {
    const link = document.createElement('a');
    const body = document.querySelector('body');
    link.href = window.URL.createObjectURL(blob);
    link.download = filename;
    // fix Firefox
    link.style.display = 'none';
    body.appendChild(link);
    link.click();
    body.removeChild(link);
    window.URL.revokeObjectURL(link.href);
  }
};

/**
 * 获取文件md5及buffer
 * @param file 文件对象
 */
export function getFileInfo(file) {
  //读取文件内容
  const fr = new FileReader();
  return new Promise((resolve) => {
    fr.readAsArrayBuffer(file);
    fr.onload = function() {
      const md5 = AWS.util.crypto.md5(this.result, 'base64');
      resolve({ md5: md5, fileArrayBuffer: this.result });
    }
  });
}

/**
 * 获取文件后缀名
 * @param {*} fileName 文件名称
 * @returns 
 */
export function getSuffix(fileName) {
  return fileName
    .split('.')
    .pop()
    .toLowerCase();
}

/**
 * 获取文件名，无后缀
 * @param {*} fileName 
 */
export function getFileNameNoSuffix(fileName) {
  if(fileName.indexOf('.') > -1) {
    let tempName = fileName.split('.');
    tempName.pop();
    return tempName.join('.');
  }
  
  return fileName;
}

/**
 * 动态创建iframe下载
 * @param {*} url 下载地址
 * @param {*} triggerDelay 触发的延迟时间
 */
export const createIFrameDownLoad = (url, triggerDelay, callback) => {
  if(typeof url !== 'string') {
    return;
  }
  setTimeout(function() {
    // 动态添加a,设置href,然后删除
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    callback && callback();
  }, triggerDelay);
};

// 为了解决大文件和网络慢出现漏文件下载情况
export const createIFrameDownLoad2 = (url, triggerDelay, length, index, callback) => {
  if(typeof url !== 'string') {
    return;
  }
  var iframe = document.createElement('iframe');
  iframe.style.display = 'none';
  iframe.src = url ;
  iframe.className=`${triggerDelay}Name${index}`
  document.body.appendChild(iframe);
  iframe.click();
  setTimeout(function() {
    document.body.removeChild(document.getElementsByClassName(`${triggerDelay}Name${index}`)[0])        
  },length * 10000)
};

/**
 * 根据s3id获取文件地址
 * @param {*} s3FileId s3id
 * @param {*} fileName 文件名称
 * @returns 
 */
export const getFileUrlByS3ID = async ({s3FileId, fileName, orgCode}) => {
  try {
    const params = {
      s3FileId,
      filename: fileName,
      orgCode
    };
    const { data } = await getDownloadUrl(params);
    return data.url || '';
  } catch (error) {
    console.error('error: ', error);
    return '';
  }
}