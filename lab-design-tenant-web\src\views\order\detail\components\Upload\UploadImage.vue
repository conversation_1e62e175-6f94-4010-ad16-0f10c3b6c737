<template>
  <div class="order-detail-upload-image">
    <p class="title">{{ name }}<span>{{ necessary && needUpload ? ' *' : '' }}</span></p>
    <el-upload
      v-if="needUpload"
      class="upload-image-box"
      action="#"
      accept="image/*"
      list-type="picture-card"
      multiple
      :file-list="uploadFileList"
      :auto-upload="false"
      :on-change="uploadChange">
        <i slot="default" class="el-icon-plus"></i>
        <div class="tips">{{ $t('file.title.image') }}</div>
        <div  
          class="img-item"
          slot="file" 
          slot-scope="{file}" 
          :class="{'is-uploading': file.isUploading}" 
          @dblclick="handlePictureCardPreview(file, uploadFileList)">

          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
          <span class="el-upload-list__item-actions">
            <span
              v-if="!file.isUploading"
              class="el-upload-list__item-close"
              @click="handleRemove(file)">
              <i class="el-icon-close" ></i>
            </span>

            <div class="upload-again" v-if="!file.isUploading && !file.isReload" @click="uploadAgain(file, uploadFileList)">
              {{ $t('file.btn.reUpload') }}
            </div>

            <el-progress
              class="upload-progress"
              :percentage="file.percentage" 
              :color="customColor" 
              v-show="file.isUploading"></el-progress>
          </span>
        </div>
    </el-upload>

    <div v-else class="upload-image-show">
      <div 
        v-for="(file, index) in uploadFileList" 
        :key="index"
        class="img-item" 
        @click="handleClickPreview(file, uploadFileList)">

        <img class="" :src="file.url" alt="">
      </div>
    </div>
  </div>
</template>

<script>
import variables from '@/assets/styles/export.scss';
import UploadNormalFile from '@/public/utils/uploadNormalFile';
import { getDownloadUrl } from '@/api/file';

export default {
  name: 'UploadImage',
  props: {
    uploadList: {
      type: Array,
      default() {
        return [];
      }
    },
    acceptType: String,
    name: String,
    needUpload: {
      type: Boolean,
      default: true,
    },
    clientOrgCode: {
      type: Number,
      require: true,
    },
    fileType: {
      type: Number,
      require: true
    },
    necessary: Boolean,
  },
  data() {
    return {
      i18nTipTitle: 'file.tips',
      customColor: variables.hgDefaultText,
      uploadFileList: [],
    }
  },
  watch: {
    uploadList: {
      deep: true,
      handler(list) {
        if (list.length) {
          this.uploadFileList = [];
          list.forEach(imageItme => {
            const item = {
              isUploading: false,
              percentage: 100,
              progress: 100,
              url: '',
              isReload: true,
              name: imageItme.fileName,
              pictureUrl: '',
              fileName: imageItme.fileName,
              filePath: imageItme.filePath,
              fileType: imageItme.fileType,
              fileSize: imageItme.fileSize,
            };
            this.uploadFileList.push(item);
            this.getImageUrl(imageItme.filePath);
          });
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    /**
     * 文件校验 这里的文件是文件对象
     * @param { Array } file 文件
     */
    verifyFile(file, fileList) {
      if (file.size === 0) {
        this.$hgOperateFail(this.$t(`${this.i18nTipTitle}.emptyFile`));
        this.handleRemove(file);
        return false;
      } 
      if (file.size / 1024 / 1024 > 50) {
        this.$hgOperateFail(this.$t(`${this.i18nTipTitle}.imageToBig`));
        this.handleRemove(file);
        return false;
      }
      const isImage = file.raw.type.includes('image/');
      if (!isImage) {
        this.$hgOperateFail(this.$t(`${this.i18nTipTitle}.notImage`));
        this.handleRemove(file);
        return false;
      }

      if (this.uploadFileList.some(item => item.name === file.name)) {
        this.$hgOperateFail(this.$t(`${this.i18nTipTitle}.imageRepeat`));
        this.uploadFileList = fileList.filter(item => item.filePath);
        return false;
      }
      return true;
    },
    /**
     * 上传
     */
    async uploadChange(file, fileList, isReUpload) {
      try {
        if (!isReUpload && !this.verifyFile(file, fileList) ) { return false; }
        this.$set(file, 'isUploading', true);

        const uploadNormalFile = new UploadNormalFile({file: file.raw, orgCode: this.clientOrgCode, needOrgCode: true});

        uploadNormalFile.onUploadProgress((progressEvent) => {
          let complete = ((progressEvent.loaded / progressEvent.total) * 100) | 0;
          file.percentage = complete;
          file.progress = complete;
          if (complete === 100) {
            setTimeout(() => {
              file.isUploading = false;
            }, 1000);
          }
        });

        uploadNormalFile.onEnd((data) => {
          file.filePath = data.s3FileId;
          file.fileName = data.fileName;
          file.fileType = this.fileType;
          file.fileSize = data.fileSize;
          file.pictureUrl = data.url;
          this.uploadFileList = fileList.filter(item => item.filePath);

          // 如果有上传文件，则设计文件，设计STL，设计截图为必传文件
          const errArr = ['', null, undefined];
          if (!errArr.includes(this.necessary) && this.necessary === false) {
            this.$emit('necessaryFileUploadSuccess')
          }
        });

        uploadNormalFile.onError((data) => {
          console.error('data: ', data);
        });

        uploadNormalFile.onStart();
      } catch (error) {
        console.log('error: ', error);
        file.isUploading = false;
        this.uploadFileList = fileList;
        this.handleRemove(file);
      }
    },
    /**
     * 重新上传
     * @param file 文件对象
     */
    uploadAgain(file, fileList) {
      this.uploadChange(file, fileList, true);
    },
    /**
     * 预览图片
     * @param file 当前文件
     * @param fileList 上传的文件
     */
    handlePictureCardPreview(file, fileList) {

      const fileIndex = fileList.findIndex(item => item.pictureUrl === file.pictureUrl);
      this.$hgViewer.open({
        imgList: fileList.map(item => item.pictureUrl),
        initialViewIndex: fileIndex
      });
    },

    handleClickPreview(file, fileList) {
      const fileIndex = fileList.findIndex(item => item.pictureUrl === file.pictureUrl);
      this.$hgViewer.open({
        imgList: fileList.map(item => item.pictureUrl),
        initialViewIndex: fileIndex
      });
    },

    getImageUrl(filePath) {
      const param = {
        s3FileId: filePath,
        orgCode: this.clientOrgCode,
        filename: '',
      };
      getDownloadUrl(param).then(res => {
        if(res.code === 200){
          const url = res.data.url;
          const imageItem = this.uploadFileList.find(item => item.filePath === filePath);
          if(imageItem) {
            this.$set(imageItem, 'url', url);
            this.$set(imageItem, 'pictureUrl', url);
          }
        }
      }).catch(err => {
        console.log('获取设计截图失败',err);
      });
    },

    /**
     * 移除图片
     * @param file 文件对象
     */
    handleRemove(file) {
      this.uploadFileList = this.uploadFileList.filter(item => item.name !== file.name);
    },
  }
}
</script>

<style lang="scss" scoped>
.order-detail-upload-image {
  .title {
    margin-bottom: 12px;
    font-weight: bold;
    font-size: 16px;  
    line-height: 24px;
    span {
      color: $hg-red;
    }
  }
  
  .upload-image-show {
    display: flex;
    flex-wrap: wrap;

    .img-item {
      padding: 8px;
      margin: 8px 20px 8px 0px;
      width: 140px;
      height: 140px; 
      border-radius: 2px;
      background-color: $hg-hover;

      img{
        width: 124px;
        height: 124px;
        border-radius: 2px;
      }
    }
    
  }
}
</style>

<style lang="scss">
.order-detail-upload-image {
  .upload-image-box {
    display: flex;
    justify-content: flex-end;
    flex-direction: row-reverse;
    margin-bottom: -8px;

    .el-upload--picture-card {
      background-color: $hg-hover;
      border-radius: 2px;
      width: 140px;
      height: 140px;
      display: flex;
      line-height: unset;
      border: 1px dashed $hg-disable;
      color: $hg-secondary-text;
      align-content: center;
      flex-wrap: wrap;
      .el-icon-plus {
        font-size: 14px;
        width: 100%;
        margin-bottom: 5px;
      }
      .tips {
        flex: 1;
      }
    }
    .el-upload-list--picture-card {
      flex: 1;

      .el-upload-list__item {
        width: 140px;
        height: 140px;
        padding: 8px;
        border-radius: 2px;
        background-color: $hg-hover;
        margin: 0 0 8px 20px;
        border: 0;
        .img-item {
          width: 124px;
          height: 124px;
          img {
            display: block;
            border-radius: 2px;
          }
        }
        .is-uploading {
          .el-upload-list__item-actions {
            opacity: 1;
            .upload-progress {
              top: 45%;
              width: 88px;
              .el-progress__text {
                top: 100%;
                left: 0;
                margin-left: 0;
                margin-top: 12px;
                font-size: 14px !important;
                color: $hg-default-text-color;
              }
            }
          }
        }
        .el-upload-list__item-actions {
          transition: none;
          background-color: rgba(0, 0, 0, 0.85);
          cursor: pointer;
          .el-icon-close {
            right: 16px;
            top: 16px;
            display: block;
            color: $hg-label;
          }
          .upload-again {
            position: absolute;
            bottom: 24px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            text-decoration: underline;
            color: $hg-default-text-color;
            margin-top: 10px;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>