// 用户端  /lab_user_login
// 租户端  /lab_tenant_login
import { loginPath } from '@/api/baseurl.config'
function getQueryVariable(variable) {
  var query = window.location.search.substring(1);
  var vars = query.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    if (pair[0] == variable) { return pair[1]; }
  }
  return (false);
}
const redirectLogin = function() {
  const AccessToken = window.localStorage.getItem("AccessToken");
  const userInfo = window.localStorage.getItem("userInfo");
  if (userInfo) window.localStorage.removeItem("userInfo");
  if (AccessToken) window.localStorage.removeItem("AccessToken");
  var RedirectionUrl = getQueryVariable('redirection')
  if(RedirectionUrl){
    window.location.href = window.location.origin + loginPath + "/index.html" + "?redirection=" + encodeURIComponent(RedirectionUrl)
  }else{

    window.location.href = window.location.origin + loginPath + "/index.html" + "?redirection=" + encodeURIComponent(window.location.pathname + window.location.hash);
  }
};

const getToken = function() {
  const AccessToken = window.localStorage.getItem("AccessToken");
  if (AccessToken) return AccessToken;
  redirectLogin();
};

const setToken = function (c_name, value, min) {
  localStorage[c_name] = typeof(value) == 'string' ? value : JSON.stringify(value)
}

export { redirectLogin, getToken, setToken };
