<template>
  <div v-if="show" class="edit-custom-box">
    <div class="header">
      <p class="back-btn border finger" @click="goBack"><i class="iconfont icon-arrow-back" />{{ $t('customer.goback') }}</p>
      <p class="header-title">{{ $t('customer.editCutomer') }}</p>
    </div>
    <div class="content-box">
      <div class="content-left">
        <div class="title">
          <p class="title-text">{{ $t('customer.regularInfo') }}</p>
          <!-- 待审核和审核不通过状态下都不显示保存按钮，待审核状态下编辑信息后由点击审核按钮去保存信息 && editCustomerObj.status != 0 -->
          <!-- <VueButton
            v-if="editCustomerObj.status != -1 && editCustomerObj.status != 0"
            v-btn-control
            v-permission="['editCustomer', 'disabled']"
            class="title-btn"
            width="104"
            type="primary"
            sizes="big"
            @click.native="saveCustomInfoFunc('editCustomerRuleForm', 'save')"
          >
            {{ $t('customer.save') }}
          </VueButton> -->
          <el-button v-if="editCustomerObj.status != -1 && editCustomerObj.status != 0 && saveBtn" v-permission="['editCustomer', 'disabled']" class="title-btn" :loading="editLoading" type="primary" @click="saveCustomInfoFunc('editCustomerRuleForm', 'save')">{{ $t('customer.save') }}</el-button>
        </div>
        <div class="edit-custom-form custom-form">
          <el-form ref="editCustomerRuleForm" class="left-form" :model="editCustomerObj" :label-width="$i18n.locale == 'zh' ? '100px' : '160px'" :rules="rules" :disabled="editCustomerObj.status === -1 || permission.includes('editCustomer')===false">
            <!-- 公司名称 -->
            <el-form-item :label="$t('customer.companyName')" prop="orgName" class="edit-customer-label">
              <hg-input v-model="editCustomerObj.orgName" />
              <CRMList :searchName="editCustomerObj.orgName" @update="setBasicInfo">
                <el-button class="hg-button" :disabled="!(editCustomerObj.orgName && editCustomerObj.orgName.replace(/^\s+/,''))" slot="reference">{{ $t('customer.searchCRM') }}</el-button>
              </CRMList>
            </el-form-item>
            <!-- 编码 -->
            <el-form-item :label="$t('customer.orgSn')" prop="orgSn" class="edit-customer-label">
              <el-input v-model="editCustomerObj.orgSn" type="text" :disabled="isDisableCode" />
            </el-form-item>
            <!-- 时区 -->
            <el-form-item :label="$t('timezone.timezone')" prop="tzCode" class="edit-customer-label">
              <div class="input-box"><Select :select-options="timezoneList" :value="editCustomerObj.tzCode" @change="changeTimezone" /></div>
            </el-form-item>
            <!-- 区域 -->
            <el-form-item :label="$t('customer.area')" prop="areaCode" class="edit-customer-label">
              <div class="input-box">
                <el-cascader style="width: 100%;" v-model="editCustomerObj.areaCode" :props="{label: 'label', value: 'areaCode'}" :options="areaList" @change="changeArea"></el-cascader>
              </div>
            </el-form-item>

            <!-- 助记码 -->
            <el-form-item :label="$t('customer.memorySn')" class="edit-customer-label">
              <el-input v-model="editCustomerObj.memorySn" type="text" :disabled="isDisableCode" :placeholder="$t('customer.memoryTips')" />
            </el-form-item>
            <!-- S3节点配置 -->
            <!-- <el-form-item :label="$t('customer.s3Setting')" prop="bucketCode" class="edit-customer-label">
              <div class="input-box"><Select :select-options="bucketList" :value="editCustomerObj.bucketCode" @change="changeBucket" /></div>
            </el-form-item> -->
            <!-- 客户级别 -->
            <el-form-item :label="$t('customer.customLevel')" :class="$i18n.locale == 'zh' ? '' : 'edit-customLevel-label'">
              <div class="input-box"><Select :select-options="customLevelList" :value="editCustomerObj.customLevel" @change="changeCustomLevel" /></div>
            </el-form-item>
            <!-- 客户类型 -->
            <el-form-item :label="$t('customer.customType')" class="edit-customer-label">
              <div class="input-box">
                <Select :select-options="customTypeList" :value="editCustomerObj.customType" @change="changeCustomType" />
              </div>
            </el-form-item>
            <!-- 公司地址 -->
            <el-form-item :label="$t('customer.companyAddress')" prop="orgAddress" class="edit-customer-label">
              <el-input v-model="editCustomerObj.orgAddress" type="text" />
            </el-form-item>
            <!-- 负责人 -->
            <el-form-item :label="$t('customer.leader')" class="edit-customer-label">
              <el-input v-model="editCustomerObj.orgLeader" type="text" />
            </el-form-item>
            <!-- <el-form-item :label="$t('customer.phone')" class="edit-customer-label">
              <el-input type="text" v-model="editCustomerObj.contactNumber"></el-input>
            </el-form-item> -->
            <!-- 邮箱 -->
            <el-form-item :label="$t('customer.email')" class="edit-customer-label">
              <el-input v-model="editCustomerObj.email" type="text" />
            </el-form-item>
            <!-- 手机号码 -->
            <el-form-item :label="$t('org.phone')" prop="mobile" class="edit-customer-label">
              <div class="area-code"><Select :select-options="countryListArrayComputed" :value="editCustomerObj.mobilePrefix" :placeholder="$t('org.areaCode')" @change="changeAreaCode" /></div>
              <el-input v-model="editCustomerObj.contactNumber" type="text" :placeholder="$t('org.phonePlaceholder')" :title="editCustomerObj.contactNumber ? '' : $t('org.phonePlaceholder')" />
            </el-form-item>
            <!-- 币种 -->
            <el-form-item :label="$t('customer.currencyType')" prop="settlementCurrency" class="edit-customer-label">
              <div class="input-box"><Select :select-options="currencyList" :value="editCustomerObj.settlementCurrency" @change="changeCurrencyType" /></div>
            </el-form-item>
            <!-- 结算方式 -->
            <el-form-item :label="$t('customer.settleTypes')" class="edit-customer-label">
              <el-radio v-model="editCustomerObj.settlementType" :label="0" @change="changeSttleTypes">{{ $t('customer.settleByMonth') }}</el-radio>
              <el-radio v-model="editCustomerObj.settlementType" disabled :label="1" @change="changeSttleTypes">{{ $t('customer.settleByDeposit') }}</el-radio>
            </el-form-item>
            <!-- 信用值 -->
            <el-form-item :label="$t('customer.creditValue')" prop="creditScore" class="edit-customer-label">
              <div class="input-box"><el-input-number v-model="editCustomerObj.creditScore" :min="0" controls-position="right" /></div>
            </el-form-item>
            <!-- 折扣率 -->
            <el-form-item :label="$t('customer.discountRate')" prop="discountRate" class="edit-customer-label">
              <el-input v-model="editCustomerObj.discountRate" type="number" :disabled="editCustomerObj.status !== 0">
                <span slot="suffix">% off</span>
              </el-input>
            </el-form-item>
          </el-form>

          <el-form ref="editRightRuleForm" class="right-form" label-position="left" :label-width="$i18n.locale == 'zh' ? '80px' : '186px'" :model="editCustomerObj" :rules="rules" :disabled="editCustomerObj.status === -1 || permission.includes('editCustomer')===false">

            <!-- 关联业务人员 -->
            <el-form-item :class="$i18n.locale == 'zh' ? '' : 'edit-business-label'" class="edit-customer-label">
              <template slot="label">
                <span style="color: #C74040;">*</span> {{ $t('customer.saleman') }}
              </template>
              <div class="input-box" style="width: 96%;">
                <Select :placeholder="$t('customer.salemanPlaceholder')" :select-options="businessUserList" :value="editCustomerObj.businessUserCodes" :is-multiple="true" @change="changeSaleman" />
              </div>
            </el-form-item>
            <!-- 收款信息 -->
            <el-form-item :class="$i18n.locale == 'zh' ? '' : 'edit-business-label'" class="edit-customer-label">
              <template slot="label">
                <span style="color: #C74040;">*</span> {{ $t('customer.pay') }}
              </template>
              <div class="input-box" :class="isShowError ? 'bank-info' : ''" style="position: relative;width: 96%;">
                <Select :select-options="bankInfoList" :placeholder="$t('customer.bankError')" :value="editCustomerObj.accountCode" @change="changeBankInfo" />
                <span v-if="isShowError" class="error-tips">{{ $t('customer.bankError') }}</span>
              </div>
            </el-form-item>
            <div v-if="nowSelectBank" class="bank-info-box">
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankName') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.bankName }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankLocation') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.bankLocal }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankAdress') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.bankAddress }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankCode') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.bankSn }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankAccouont') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.accountName }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankNumber') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.accountSn }}</li>
              </ul>
            </div>

            <!-- 账单抬头 -->
            <el-form-item class="edit-billFrom-label">
              <template slot="label">
                <span style="color: #C74040;">*</span> {{ $t('customer.billFrom') }}
              </template>
              <div class="input-box" :class="isShowHeaderError ? 'bank-info' : ''" style="position: relative;width: 96%;">
                <Select :select-options="headerInfoList" :placeholder="$t('customer.bankError')" :value="editCustomerObj.headerCode" @change="changeHeaderInfo" />
                <span v-if="isShowHeaderError" class="error-tips">{{ $t('customer.bankError') }}</span>
              </div>
            </el-form-item>
            <div v-if="nowSelectHeader" class="bank-info-box">
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.tel') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectHeader.tel }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.mail') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectHeader.mail }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.address') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectHeader.address }}</li>
              </ul>
            </div>
          </el-form>
        </div>
      </div>
      <div class="content-right">
        <div class="title">
          <p class="title-text">{{ $t('customer.customerNumber') }}</p>
          <div v-if="editCustomerObj.status == 0" v-permission="['editCustomer', 'delete']" class="pass-btn">
            <VueButton
              class="unpass-btn"
              width="104"
              sizes="big"
              @click.native="examineCustomerFunc(0)"
            >
              {{ $t('customer.unpass') }}
            </VueButton>
            <VueButton
              width="104"
              type="primary"
              sizes="big"
              @click.native="beforeExamine"
            >
              {{ $t('customer.pass') }}
            </VueButton>
          </div>
        </div>
        <div class="custom-form">
          <el-form :model="editCustomerObj" @submit.native.prevent>
            <el-form-item :label="$t('customer.number')" class="edit-customer-label">
              <el-input v-model="editCustomerObj.email" type="text" :disabled="true" />
            </el-form-item>
            <el-form-item :label="$t('customer.password')" class="edit-customer-label">
              <el-input v-model="password" type="password" :disabled="true" />
              <VueButton
                v-permission="['editCustomer', 'disabled']"
                class="edit-password-btn"
                width="104"
                sizes="big"
                icon="icon_refresh_nor iconfont-24"
                @click.native="resetPasswordFunc"
              >
                {{ $t('customer.reset') }}
              </VueButton>
            </el-form-item>
            <el-form-item :label="$t('customer.status')" class="edit-customer-label">
              <div v-if="editCustomerObj.status === 1 || editCustomerObj.status === 2">
                <el-radio v-model="editCustomerObj.status" :disabled="permission.includes('editCustomer')===false" :label="1" @change="changeStatusFunc">{{ $t('customer.enable') }}</el-radio>
                <el-radio v-model="editCustomerObj.status" :disabled="permission.includes('editCustomer')===false" :label="2" @change="changeStatusFunc">{{ $t('customer.disable') }}</el-radio>
              </div>
              <p v-if="editCustomerObj.status === 0" class="wait-examine">{{ $t('customer.approvalPending') }}</p>
              <p v-if="editCustomerObj.status === -1" class="unpass">{{ $t('customer.statusUnpass') }}</p>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import { mapState } from 'vuex'
import Select from '@/components/func-components/Select'
import { refreshLabel } from '@/assets/script/refreshLabel.js'
import { resetPassword } from '@/api/organization'
import { editCustomer, clearNewFlag, examineCustomer, getAllCurrency, getBankList, refreshBankAccount, getHeaderList } from '@/api/customer'
import { clipboardFunc, clipDestroy } from '@/assets/script/clipBoardFunc.js'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { getTimezoneList, getBucketCodeList, getBusinessUserList, getAreaList } from '@/api/common'
// import { myTimeFormat } from '@/assets/script/formatDate.js'
import CRMList from './CRMList'
import HgInput from '@/components/func-components/HgInput'

export default {
  name: 'EditCustomer',
  components: {
    Select,
    CRMList,
    HgInput
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    editCustomer: {
      type: Object,
      default: () => {
        return {}
      }
    },
    areaCodeArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    saveBtn: {
      type: Boolean,
      default: true
    },
  },
  data() {
    var checkOrgSn = (rule, value, callback) => {
      if (value !== '' && !COMMON_CONSTANTS.CUSTOMER_CODE_RULE.test(value)) {
        return callback(new Error(this.$t('customer.orgSnErro')))
      } else {
        callback()
      }
    }
    var checkDiscountRate = (rule, value, callback) => {
      const reg = /^[0-9]*[1-9]*$/
      value = Number(value)
      if (value !== '' && (!reg.test(value) || value > 100 || value < 0)) {
        return callback(new Error(this.$t('customer.discountRateErro')))
      } else {
        callback()
      }
    }
    return {
      showCRM: false,
      nowSelectBank: null,
      nowSelectHeader: null,
      editCustomerObj: {
        orgSn: '',
        tzCode: '',
        areaCode: '',
        orgName: '',
        accountCode: '',
        headerCode: '',
        memorySn: '',
        // bucketCode: ''
        // settlementType: 0, // 0是月结，1是充值
        // discountRate: 0, // 折扣率
        // businessUserCodes: [] // 关联业务人员，多选
      },
      rules: {
        orgSn: [
          { required: true, message: this.$t('customer.orgSnPlaceholder') },
          { validator: checkOrgSn, trigger: 'blur' }
        ],
        tzCode: [
          { required: true, message: this.$t('timezone.timezoneErr') }
        ],
        areaCode: [
          { required: true, message: this.$t('customer.selectArea') }
        ],
        // bucketCode: [
        //   { required: true, message: this.$t('customer.s3Placeholder') }
        // ],
        discountRate: [
          { validator: checkDiscountRate, trigger: 'blur' }
        ],
        settlementCurrency: [
          { required: true, message: this.$t('customer.currencyTypePlaceholder') }
        ],
        creditScore: [
          { required: true, message: this.$t('customer.creditPlaceholder') }
        ],
        businessUserCodes: [
          { required: true, message: this.$t('customer.businessTips'), trigger: 'change' }
        ],
      },
      // 收款信息
      bankInfoList: [],
      // 抬头
      headerInfoList: [],
      // 客户等级列表
      customLevelList: [
        {
          value: 0,
          label: this.$t('customer.normal')
        },
        {
          value: 1,
          label: 'VIP'
        },
        {
          value: 2,
          label: 'VVIP'
        }
      ],
      // 客户类型列表
      customTypeList: [
        {
          value: 0,
          label: this.$t('customer.undirectSales')
        },
        {
          value: 1,
          label: this.$t('customer.directSales')
        },
        {
          value: 2,
          label: this.$t('customer.testUser')
        },
        {
          value: 3,
          label: this.$t('customer.disaster')
        },
        {
          value: 4,
          label: this.$t('customer.agencyuser')
        }
      ],
      password: '123456', // 密码不显示，默认给一串数字即可
      timezoneList: [],
      areaList: [],
      // oss区域列表
      bucketList: [],
      businessUserList: [],
      // 币种列表
      currencyList: [],
      editLoading: false,
      disableCode: false, // 禁止编码输入
      isShowError: false,
      isShowHeaderError: false
    }
  },
  computed: {
    ...mapState({
      permission: (state) => state.permission
    }),
    countryListArrayComputed() { // 根据目前的中英文状态返回相对应的中英文区号
      const countryListArrayNew = []
      this.areaCodeArr.forEach((item) => {
        if (this.$i18n.locale === 'zh') {
          item.label = item.countryName + ' +' + item.mobilePrefix
        } else {
          item.label = item.countryEn + ' +' + item.mobilePrefix
        }
        item.value = item.mobilePrefix
        countryListArrayNew.push(item)
      })
      return countryListArrayNew
    },
    isDisableCode() {
      return this.editCustomerObj.isSynchronized || this.disableCode
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.isShowError = false;
        this.isShowHeaderError = false;
        this.editCustomerObj = JSON.parse(JSON.stringify(this.editCustomer))
        this.editCustomerObj.accountCode = this.editCustomerObj.bankInfo && this.editCustomerObj.bankInfo.accountCode ? this.editCustomerObj.bankInfo.accountCode : '';
        this.editCustomerObj.headerCode = this.editCustomerObj.headerInfo && this.editCustomerObj.headerInfo.headerCode ? this.editCustomerObj.headerInfo.headerCode : '';
        this.editCustomerObj.areaCode = this.editCustomerObj.areaCode ? this.editCustomerObj.areaCode.split(',') : '';
        this.nowSelectBank = this.editCustomerObj.bankInfo || null;
        this.nowSelectHeader = this.editCustomerObj.headerInfo || null;
        !this.editCustomerObj.mobilePrefix ? this.editCustomerObj.mobilePrefix = '+86' : ''; // 如果没有区号则默认选中中国的区号
        if (this.editCustomerObj.businessUserInfos && this.editCustomerObj.businessUserInfos.length) {
          this.editCustomerObj.businessUserCodes = this.editCustomerObj.businessUserInfos.map((item) => {
            return item.userCode
          })
        }
        // this.getBucketCodeListFunc()
        Promise.allSettled([this.getBusinessUserListFunc(), this.getTimezoneListFunc(), this.getAreaList(), clearNewFlag({ orgCode: this.editCustomer.orgCode }), this.getAllCurrencyFunc(), this.getBankList(), this.getHeaderList()])
        setTimeout(() => {
          refreshLabel('edit-customer-label')
        }, 10)
      } else {
        this.resetForm('editCustomerRuleForm')
        // this.reserForm('editRightRuleForm')
        this.disableCode = false
      }
    }
  },
  mounted() {
  },
  methods: {
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    goBack() {
      this.$emit('update:show', false)
    },
    saveCustomInfoFunc(formName, type) {
      if (!this.editCustomerObj.accountCode) {
        this.isShowError = true
        return
      }
      if(!this.editCustomerObj.headerCode){
        this.isShowHeaderError = true
        return
      }

      // 业务人员超过两个人提示
      if (this.editCustomerObj.businessUserCodes && this.editCustomerObj.businessUserCodes.length > 1) {
        this.$message.error(this.$t('customer.bussinessSelect'));
        return
      }
      // 没有选择业务人员的时候提示
      if (!this.editCustomerObj.businessUserCodes || this.editCustomerObj.businessUserCodes.length == 0 || this.editCustomerObj.businessUserCodes == 90002) {
        this.$message.error(this.$t('customer.businessTips'))
        return
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (type === 'save') {
            this.editLoading = true
            const editData = Object.assign({}, this.editCustomerObj, { isBackupCrmOrg: Number(this.disableCode) })
            this.$emit('submit', editData, type)
          } else if (type === 'pass') {
            this.examineCustomerFunc(1, type)
          }
        } else {
          this.editLoading = false
          return false
        }
      })
    },

    setBasicInfo(info) {
      this.editCustomerObj.orgSn = info.orgSn;
      this.editCustomerObj.orgName = info.orgName;
      this.editCustomerObj.orgAddress = info.orgAddress;
      this.editCustomerObj.memorySn = info.memorySn;
      this.disableCode = true;
      this.getRefreshBankAccount();
      // 币种同步
      if(info.settlementCurrency && info.settlementCurrency != this.editCustomerObj.settlementCurrency){
        this.$confirm(this.$t('customer.updatePrice'), this.$t('customer.remainding'), {
          confirmButtonText: this.$t('customer.updateBtn'),
          cancelButtonText: this.$t('common.cancel'),
          type: 'warning'
        }).then(() => {
          this.editCustomerObj.settlementCurrency = info.settlementCurrency;
          // 这里又要单独更新一波抬头
          this.getRefreshBankAccount();
        }).catch(() => {})
      }
    },

    // 获取币种列表
    getAllCurrencyFunc() {
      getAllCurrency().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.currencyList = res.data
            this.currencyList.forEach((item) => {
              item.label = item.currency
              item.lable_en = item.currencyEn
              item.value = item.settlementCurrency
            })
          }
        }
      })
    },
    // 获取银行信息列表
    getBankList() {
      getBankList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.bankInfoList = res.data
            this.bankInfoList.forEach((item) => {
              item.label = item.description
              item.value = item.accountCode
            })
          }
        }
      })
    },
    // 获取账单抬头列表
    getHeaderList(){
      getHeaderList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.headerInfoList = res.data
            this.headerInfoList.forEach((item) => {
              item.label = item.title
              item.value = item.headerCode
            })
          }
        }
      })
    },
    // 获取银行信息详情
    getRefreshBankAccount(){
      this.nowSelectBank = null;
      let data = {
        "isBackupCrmOrg": Number(this.disableCode),
        "orgCode": this.editCustomerObj.orgCode,
        "orgSn": this.editCustomerObj.orgSn,
        "settlementCurrency": this.editCustomerObj.settlementCurrency
      }
      refreshBankAccount(data).then((res) => {
        if (res.code === 200) {
          this.nowSelectBank = res.data.bankInfo ? res.data.bankInfo : null;
          this.editCustomerObj.accountCode = res.data.bankInfo ? res.data.bankInfo.accountCode : '';
          this.nowSelectHeader = res.data.headerInfo ? res.data.headerInfo : null;
          this.editCustomerObj.headerCode = res.data.headerInfo ? res.data.headerInfo.headerCode : '';
        }
      })
    },
    // 获取业务人员列表
    getBusinessUserListFunc() {
      return new Promise((resolve) => {
        getBusinessUserList().then((res) => {
          if (res.code === 200) {
            if (res.data != null && res.data.length) {
              this.businessUserList = res.data
              this.businessUserList.forEach((item) => {
                item.label = item.realName
                item.value = item.userCode
              })
            }
          }
        })
      })
    },
    // 获取时区信息列表
    getTimezoneListFunc() {
      getTimezoneList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.timezoneList = res.data
            this.timezoneList.forEach((item) => {
              item.label = this.$i18n.locale === 'zh' ? item.tzNameCn : item.tzNameEn;
              item.value = item.tzCode
            })
          }
        }
      })
    },
    // 获取区域列表
    async getAreaList(){
      const loop = (arr) => {
        arr.forEach((item) => {
          item.label =  this.$i18n.locale === 'zh' ? item.nameCn : item.name;
          if(item.children){
            loop(item.children)
          }
        })
        return arr
      }
      const { code, data } = await getAreaList();
      if(code == 200){
        this.areaList = loop(data);
        console.log(this.areaList)
      }
    },
    // 选择时区
    changeTimezone(value) {
      this.editCustomerObj.tzCode = value
    },
    // 选择区域
    changeArea(value){
      this.editCustomerObj.areaCode = value
    },
    // 获取S3节点配置列表
    getBucketCodeListFunc() {
      getBucketCodeList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.bucketList = res.data
            this.bucketList.forEach((item) => {
              item.label = item.aliasCn
              item.lable_en = item.aliasEn
              item.value = item.bucketCode
            })
          }
        }
      })
    },
    // 选择S3节点配置
    changeBucket(value) {
      this.editCustomerObj.bucketCode = value
    },
    // 选择客户等级
    changeCustomLevel(value) {
      this.editCustomerObj.customLevel = value
    },
    // 选择收款信息
    changeBankInfo(value) {
      this.isShowError = false;
      this.editCustomerObj.accountCode = value;
      this.nowSelectBank = this.bankInfoList.find((item) => { return item.accountCode === value }) || null;
    },
    // 选择账单抬头
    changeHeaderInfo(value){
      this.isShowHeaderError = false;
      this.editCustomerObj.headerCode = value;
      this.nowSelectHeader = this.headerInfoList.find((item) => { return item.headerCode === value }) || null;
    },
    // 选择客户类型
    changeCustomType(value) {
      this.editCustomerObj.customType = value
    },
    // 选择区号
    changeAreaCode(value) {
      // select获取到的值是value，但是显示的是label，所以将显示的值变成数字类型，就能只获取到区号，然后在区号前面加上"+"号
      this.editCustomerObj.mobilePrefix = '+' + Number(value)
    },
    // 选择币种
    changeCurrencyType(value) {
      this.editCustomerObj.settlementCurrency = value;
      this.getRefreshBankAccount();
    },
    // 选择结算方式
    changeSttleTypes() {

    },
    // 选择关联业务人员
    changeSaleman(value) {
      this.editCustomerObj.businessUserCodes = value
    },
    // 重置用户密码
    resetPasswordFunc() {
      this.$Dialog({
        title: this.$t('customer.resetPassword'),
        message: this.$t('customer.resetPasswordConfirm'),
        ensureBtnText: this.$t('customer.confirm'),
        confirmAction: () => {
          // 点击确认按钮后的操作
          this.ensureResetPassword()
        }
      })
    },
    ensureResetPassword() {
      resetPassword({
        'userCode': this.editCustomerObj.adminUserCode
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$Dialog({
              message: this.$t('customer.resetSuccessTip', { password: res.data }),
              ensureBtnText: this.$t('customer.copy'),
              cancelBtnText: this.$t('customer.close'),
              updateShow: false,
              init: () => {
                clipboardFunc(res.data)
              },
              cancelAction: () => {
                // 点击关闭按钮后的操作
                clipDestroy() // 销毁剪贴板实例
              }
            })
          }
        }
      })
    },
    // 通过审核前，先判断编辑信息是否符合保存条件
    beforeExamine() {
      this.saveCustomInfoFunc('editCustomerRuleForm', 'pass')
    },
    // 通过/不通过审核
    examineCustomerFunc(result, type) {
      const isPass = result ? this.$t('customer.pass') : this.$t('customer.unpass')
      this.$Dialog({
        title: this.$t('customer.examineTitle'),
        message: this.$t('customer.examineTip', { isPass: isPass }),
        ensureBtnText: this.$t('customer.confirm'),
        confirmAction: () => {
          // 4.3.29更改为，先保存，再调用审核
          if (result === 1) {
            const editData = Object.assign({}, this.editCustomerObj, { isBackupCrmOrg: Number(this.disableCode) })
            this.$emit('submit', editData, type, result)
          } else {
            this.editCustomerObj.status = -1
            // this.resetForm('editCustomerRuleForm')
            // this.reserForm('editRightRuleForm')
            // console.log(result, 111111)
            this.ensureExamine(result);
          }
          /* // 点击确认按钮后的操作
          this.ensureExamine(result).then(() => {
            if (result === 1) { // 审核通过
              this.$emit('submit', this.editCustomerObj, type)
            } else { // 审核不通过
              this.resetForm('editCustomerRuleForm')
            }
          }) */
        }
      })
    },


    // 审核客户 status: -1审核不通过，0待审核，1审核通过（启用），2审核通过（禁用）
    ensureExamine(result) {
      examineCustomer({
        'orgCode': this.editCustomerObj.orgCode,
        'isPass': result
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$MessageAlert({
              text: this.$t('customer.operateSuccessTip', { operate: this.$t('customer.examineTitle') }),
              type: 'success'
            })
            this.editCustomerObj.status = result === 0 ? -1 : 1

            if (result !== 1) {
              this.resetForm('editCustomerRuleForm')
              // this.reserForm('editRightRuleForm')
            }
          }
        }
      })
    },
    // 点击禁用/启用按钮，启用即开通
    changeStatusFunc(value) {
      this.$Dialog({
        title: this.$t('customer.editCustomerTitle'),
        message: this.$t('customer.operateConfirm', { operate: value === 1 ? this.$t('customer.enableText') : this.$t('customer.disableText') }),
        ensureBtnText: this.$t('customer.confirm'),
        confirmAction: () => {
          // 点击确认按钮后的操作
          this.ensureChangeStatus()
        },
        cancelAction: () => {
          this.editCustomerObj.status = value === 1 ? 2 : 1
        }
      })
    },
    ensureChangeStatus() {
      editCustomer({
        'orgCode': this.editCustomerObj.orgCode,
        'status': this.editCustomerObj.status,
        'accountCode': this.editCustomerObj.bankInfo.accountCode,
        'headerCode': this.editCustomerObj.bankInfo.headerCode,
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$MessageAlert({
              text: this.$t('customer.operateSuccessTip', { operate: this.editCustomerObj.status === 1 ? this.$t('customer.enable') : this.$t('customer.disable') }),
              type: 'success'
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-custom-box {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0;
  background: $hg-background-color;
  padding: 14px 24px 70px 24px;
  overflow-x: auto;
  overflow-y: hidden;
  .header {
    margin-bottom: 14px;
    display: flex;
    align-items: center;
    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 12px;
      height: $hg-height-32;
      color: $hg-secondary-fontcolor;
      i {
        margin-right: 8px;
      }
    }
    .header-title {
      color: $hg-primary-fontcolor;
      margin-left: 24px;
    }
  }
  .content-box {
    display: flex;
    height: 100%;
    .content-left, .content-right {
      flex: 1;
      margin-right: 24px;
      height: 100%;
      border-radius: 4px;
      background: $hg-main-black;
      box-shadow: 0px 12px 32px 0px $hg-background-color,0px 8px 24px 0px $hg-background-color,0px 0px 16px 0px $hg-background-color;
      padding: 16px 24px;
      .title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        height: 40px;
        .title-text {
          color: $hg-primary-fontcolor;
          font-weight: bold;
          font-size: 16px;
        }
        .pass-btn {
          margin-left: auto;
          display: flex;
          .unpass-btn {
            margin-right: 24px;
          }
        }
        .title-btn {
          margin-left: auto;
          width:104px;
          background-color: #3054cc;
        }
      }
      ::v-deep .custom-form .el-form .el-form-item {
        justify-content: flex-start;
        // .el-form-item__content {
        //   width: 320px;
        // }
      }
      .edit-custom-form, .custom-form {
        display: flex;
        height: calc(100% - 56px);
        overflow: hidden;
        overflow-y: scroll;
        .input-box {
          position: relative;
          // width: 320px;
          width: 100%;
          ::v-deep .el-input-number {
            width: 100%;
            .el-input__inner {
              text-align: left;
            }
            .el-input-number__decrease, .el-input-number__increase {
              background: transparent;
              border-color: $hg-border-color;
            }
          }
          .error-tips{
            position:absolute;
            bottom: -30px;
            color:#C74040;
            font-size:12px;
          }
        }
        .area-code {
          width: 96px;
          margin-right: 12px;
        }

        .crm-content {
          position: absolute;
        }
      }
      .edit-custom-form{
        width: 900px!important;
      }
      .left-form{
        width: 50%;
      }
      .right-form{
        width: 50%;
        padding-left: 20px;
        .bank-info-box{
          display: flex;
          background: #000;
          width: 372px;
          min-height: 160px;
          padding: 16px 12px;
          flex-direction: column;
          justify-content: center;
          color: #C4C8CD;
          margin-top: 16px;
          border-radius: 4px;
          // margin-left: 20px;
          // margin: 20px auto;
          ul{
            line-height: 24px;
            .bank-title{
              flex: 1;
              float: right;
            }
            .bank-content{
              flex: 3;
            }
            .en-title{
              flex: 1.5;
            }
            .en-content{
              flex: 3;
            }
          }
        }
      }
      .edit-password-btn {
        margin-left: 12px;
      }
      .wait-examine, .unpass {
        font-weight: 700;
        font-size: 16px;
      }
      .wait-examine {
        color: $hg-warning-color;
      }
      .unpass {
        color: $hg-error-color;
      }
    }
    .content-right {
      flex: 1;
      height: 100%;
      border-radius: 4px;
      background: $hg-main-black;
      margin-right: 0;
      box-shadow: 0px 12px 32px 0px $hg-background-color,0px 8px 24px 0px $hg-background-color,0px 0px 16px 0px $hg-background-color;
    }
  }
}
</style>
<style>
.bank-info .el-input__inner{
  border: 1px solid #C74040!important;
}
.el-message-box{
  background: #141519;
  border-color: #3D4047;
  box-shadow: none;
}
.el-message-box__header .el-message-box__title{
  font-size: 14px;
}
.el-message-box__content{
  color: #E4E8F7;
}
.el-message-box__btns .el-button--small{
  background: #141519;
  color: #E4E8F7;
  border-color: #3D4047;
}
.el-message-box__btns .el-button--primary{
  background: #3760EA;
  color: #E4E8F7;
  border-color: #3760EA;
}
.edit-billFrom-label .el-form-item__label{
  width: 80px!important;
}
/* .edit-payInfo-label .el-form-item__label{
  width: 160px!important;
} */
.edit-business-label .el-form-item__label{
  width: 180px!important;
}
</style>
