import * as THREE from 'three'
import Feature from '../core.js'

export function getProperties() {
  return {
    text: '',
    textHeight: 0,
    color: 'rgba(255, 255, 255, 1)',
    backgroundColor: '',
    padding: 0,
    borderWidth: 0,
    borderRadius: 0,
    borderColor: 'rgba(255, 255, 255, 1)',
    strokeWidth: 0,
    strokeColor: 'rgba(255, 255, 255, 1)',
    fontWeight: 'normal',
    fontFace: 'system-ui',
    fontSize: 90,
    center: [0.5, 0.5],
    size: 1,
    boxWidth: 0
  }
}

export default class SpriteText extends Feature {
  defineProperties() {
    return getProperties()
  }

  defineObject() {
    // 精灵是一个总是面朝着摄像机的平面，通常含有使用一个半透明的纹理。
    return new THREE.Sprite(new THREE.SpriteMaterial({
      side: THREE.DoubleSide,
      transparent: true
    }))
  }

  created() {
    this._canvas = document.createElement('canvas')
    this._ctx = this._canvas.getContext('2d')
  }

  destroy() {
    this._canvas.width = this._canvas.height = 0
    this._ctx.fillRect(0, 0, 0, 0)
    this._canvas = null
    super.destroy()
  }

  refresh() {
    const canvas = this._canvas
    const ctx = this._ctx

    let {
      text,
      textHeight,
      color,
      backgroundColor,
      padding,
      borderWidth,
      borderRadius,
      borderColor,
      strokeWidth,
      strokeColor,
      fontWeight,
      fontFace,
      fontSize,
      center,
      size,
      boxWidth,
      otherboxWidth
    } = this.properties

    text = String(text)

    const border = Array.isArray(borderWidth) ? borderWidth : [borderWidth, borderWidth]
    const relBorder = border.map(b => b * fontSize * 0.1)

    borderRadius = Array.isArray(borderRadius) ? borderRadius : [borderRadius, borderRadius, borderRadius, borderRadius]
    const relBorderRadius = borderRadius.map(b => b * fontSize * 0.1)

    padding = Array.isArray(padding) ? padding : [padding, padding]
    const relPadding = padding.map(p => p * fontSize * 0.1)

    const lines = text.split('\n')
    const font = `${fontWeight} ${fontSize - 4}px ${fontFace}`

    ctx.font = font
    const innerWidth = (boxWidth > 0 ? boxWidth : Math.max(...lines.map(line => ctx.measureText(line).width))) + otherboxWidth
    const innerHeight = (fontSize + 2) * lines.length
    canvas.width = innerWidth + relBorder[0] * 2 + relPadding[0] * 2
    canvas.height = innerHeight + relBorder[1] * 2 + relPadding[1] * 2

    if (borderWidth) {
      ctx.strokeStyle = borderColor
      if (relBorder[0]) {
        const hb = relBorder[0] / 2
        ctx.lineWidth = relBorder[0]
        ctx.beginPath()
        ctx.moveTo(hb, relBorderRadius[0])
        ctx.lineTo(hb, canvas.height - relBorderRadius[3])
        ctx.moveTo(canvas.width - hb, relBorderRadius[1])
        ctx.lineTo(canvas.width - hb, canvas.height - relBorderRadius[2])
        ctx.stroke()
      }

      if (relBorder[1]) {
        const hb = relBorder[1] / 2
        ctx.lineWidth = relBorder[1]
        ctx.beginPath()
        ctx.moveTo(Math.max(relBorder[0], relBorderRadius[0]), hb)
        ctx.lineTo(canvas.width - Math.max(relBorder[0], relBorderRadius[1]), hb)
        ctx.moveTo(Math.max(relBorder[0], relBorderRadius[3]), canvas.height - hb)
        ctx.lineTo(canvas.width - Math.max(relBorder[0], relBorderRadius[2]), canvas.height - hb)
        ctx.stroke()
      }

      if (borderRadius) {
        const cornerWidth = Math.max(...relBorder)
        const hb = cornerWidth / 2
        ctx.lineWidth = cornerWidth
        ctx.beginPath()
        const arr = [
          !!relBorderRadius[0] && [relBorderRadius[0], hb, hb, relBorderRadius[0]],
          !!relBorderRadius[1] && [canvas.width - relBorderRadius[1], canvas.width - hb, hb, relBorderRadius[1]],
          !!relBorderRadius[2] && [canvas.width - relBorderRadius[2], canvas.width - hb, canvas.height - hb, canvas.height - relBorderRadius[2]],
          !!relBorderRadius[3] && [relBorderRadius[3], hb, canvas.height - hb, canvas.height - relBorderRadius[3]]
        ]

        arr.filter(d => d).forEach(([x0, x1, y0, y1]) => {
          ctx.moveTo(x0, y0)
          ctx.quadraticCurveTo(x1, y0, x1, y1)
        })
        ctx.stroke()
      }
    }

    if (backgroundColor) {
      ctx.fillStyle = backgroundColor
      if (!borderRadius) {
        ctx.fillRect(relBorder[0], relBorder[1], canvas.width - relBorder[0] * 2, canvas.height - relBorder[1] * 2)
      } else {
        ctx.beginPath()
        ctx.moveTo(relBorder[0], relBorderRadius[0])

        const arr = [
          [relBorder[0], relBorderRadius[0], canvas.width - relBorderRadius[1], relBorder[1], relBorder[1], relBorder[1]],
          [canvas.width - relBorder[0], canvas.width - relBorder[0], canvas.width - relBorder[0], relBorder[1], relBorderRadius[1], canvas.height - relBorderRadius[2]],
          [canvas.width - relBorder[0], canvas.width - relBorderRadius[2], relBorderRadius[3], canvas.height - relBorder[1], canvas.height - relBorder[1], canvas.height - relBorder[1]],
          [relBorder[0], relBorder[0], relBorder[0], canvas.height - relBorder[1], canvas.height - relBorderRadius[3], relBorderRadius[0]],
        ]

        arr.forEach(([x0, x1, x2, y0, y1, y2]) => {
          ctx.quadraticCurveTo(x0, y0, x1, y1)
          ctx.lineTo(x2, y2)
        })
        ctx.closePath()
        ctx.fill()
      }
    }

    ctx.translate(...relBorder)
    ctx.translate(...relPadding)

    ctx.font = font
    ctx.fillStyle = color
    ctx.textBaseline = 'bottom'

    const drawTextStroke = strokeWidth > 0
    if (drawTextStroke) {
      ctx.lineWidth = strokeWidth * fontSize / 10
      ctx.strokeStyle = strokeColor
    }

    lines.forEach((line, index) => {
      const lineX = relPadding[0] * 0.1
      const lineY = index !== 0 ? (index + 1.1) * fontSize + relPadding[1] * 0.1 : (index + 1) * fontSize + relPadding[1] * 0.1
      // 单独设置每一行的样式
      const font = index !== 0 ? `${fontWeight} ${fontSize - 4.5}px ${fontFace}` : `${fontWeight + 100} ${fontSize - 4.5}px ${fontFace}`

      ctx.font = font
      drawTextStroke && ctx.strokeText(line, lineX, lineY)
      ctx.fillText(line, lineX, lineY)
    })

    if (this.object.material.map) {
      this.object.material.map.dispose()
    }

    const texture = new THREE.Texture(canvas)
    texture.minFilter = THREE.LinearFilter
    texture.colorSpace = THREE.SRGBColorSpace
    texture.needsUpdate = true
    this.object.material.map = texture
    this.object.material.transparent = true
    // this.object.material.color = 0xff0000

    const yScale = textHeight * lines.length + border[1] * 2 + padding[1] * 2

    const x = size * yScale * canvas.width / canvas.height
    const y = size * yScale
    const z = 0
    this.object.scale.set(x, y, z)

    if (center) {
      this.object.center = center
    }
  }

  getWidth() {
    return this._canvas.width * 0.1
  }

  getHeight() {
    return this._canvas.height * 0.1
  }
}
