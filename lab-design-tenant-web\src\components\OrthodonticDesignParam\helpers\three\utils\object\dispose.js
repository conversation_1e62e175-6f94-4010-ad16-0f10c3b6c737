import {
  getTreeRecursionList,
} from '@/components/OrthodonticDesignParam/helpers/utils/index'

import { disposeMaterial } from '../material'

function disposeRecursionObjectList(recursionObjectList) {
  for (const data of recursionObjectList) {
    const { item, parent } = data

    if (item.__dispose__) {
      continue
    }

    if (parent) {
      parent.remove(item)
    }

    if (item.isLight) {
      item.dispose()
    }

    if (item.isMesh) {
      const { geometry, material } = item
      geometry.dispose()

      if (material instanceof Array) {
        for (const item of material) {
          disposeMaterial(item)
        }
      } else {
        disposeMaterial(material)
      }
    }

    if (item.isObject3D) {
      item.clear()
    }

    item.__dispose__ = true
  }
}

export function disposeObject(object) {
  if (object.__dispose__) {
    return
  }

  const recursionObjectList = getTreeRecursionList(object)

  recursionObjectList.pop()

  disposeRecursionObjectList([{ item: object, parent: object.parent }])
  disposeRecursionObjectList(recursionObjectList)
}

export function disposeObjectChildren(object) {
  const recursionObjectList = getTreeRecursionList(object)

  recursionObjectList.pop()

  disposeRecursionObjectList(recursionObjectList)
}
