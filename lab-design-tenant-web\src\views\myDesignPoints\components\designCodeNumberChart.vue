<template>
  <div class="month-points">
    <div class="month-header">
      <span class="title">{{lang('designNum')}}</span><span class="date">{{monthsArray[0]}}~{{monthsArray[monthsArray.length - 1]}}</span>
      <!-- <div class="all-points">
        <p class="list"><span class="point1"></span>月总指标点数</p>
        <p class="list"><span class="point2"></span>月完成点数</p>
      </div> -->
    </div>
    <div id="designCode-num-chart" class="month-chart"></div>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
  export default {
    name: 'designCodeNumberChart',
    props: {
      monthsArray: Array,
      designTypeQuantities: Object
    },
    data() {
      return {
        
      }
    },
    watch: {
      designTypeQuantities: {
        handler(newVal, oldVal) {
          this.getMonth<PERSON><PERSON>();
        }
      }
    },
    mounted () {
      
    },
    methods: {
      lang: getLang('designpoints'),
      getMonth<PERSON>hart() {
        let designCodeNumberChart = this.$echarts.init(document.getElementById('designCode-num-chart'));
        //配置图表
        let option = {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.monthsArray
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: this.designTypeQuantities.designTypeName,
              type: 'line',
              data: this.designTypeQuantities.designTypeAllQuanty,
              color: [this.designTypeQuantities.color]
            }
          ]
        };
        designCodeNumberChart.setOption(option);
      }
    },
  }
</script>

<style lang="scss" scoped>
.month-points{
  // margin-top: 24px;
  min-width: 554px;
  height: 400px;
  padding: 20px;
  .month-header{
    position: relative;
    display: flex;
    align-items: center;
    .title{
      color: #AAADB3;
      margin-right: 8px;
    }
    .date{
      color: #AAADB3;
    }
    .all-points{
      position: absolute;
      right: 38px;
      display: flex;
      .list{
        margin-right: 6px;
        padding: 4px;
      }
      .point1{
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #5F8AFF;
        margin-right: 8px;
      }
      .point2{
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #00B860;
        margin-right: 8px;
      }
    }
  }
  .month-chart{
    width: 100%;
    height: 100%;
  }
}
</style>