import request from '../axios'
import { server } from '@/config'

const axios = request.axios

/**
 * 获取余额提醒配置
 */
export const getHeypointSetting = (params) => {
  return axios({
    url: `${server.heypointServer}/balanceConfig/selectBySettlementType`,
    method: 'GET',
    params
  });
};

/**
 * 获取客户信息列表
 */
export const getCustomerList = (data) => {
  return axios({
    url: `${server.heypointServer}/account/searchAccount`,
    method: 'POST',
    data
  });
};

/**
 * 获取客户信息详情
 */
export const getCustomerDetails = (params) => {
  return axios({
    url: `${server.heypointServer}/account/getAccountByOrgCode`,
    method: 'get',
    params
  });
};

/**
 * 修改客户信息详情
 */
export const updateCustomerDetails = (data) => {
  return axios({
    url: `${server.heypointServer}/account/addOrUpdate`,
    method: 'post',
    data,
    isHandlerError: true
  });
};

/**
 * 获取黑豆账户的操作记录
 */
 export const getCustomerOperationLog = (data) => {
  return axios({
    url: `${server.heypointServer}/accountwater/tenant/searchAccountWater`,
    method: 'post',
    data
  });
};

/**
 * 黑豆账户充值/赠送黑豆
 */
export const rechargeOrGift = (data) => {
  return axios({
    url: `${server.heypointServer}/account/recharge`,
    method: 'post',
    data
  });
};

/**
 * 获取批量赠送模板
 */
export const getGiftTemplate = (data) => {
  return axios({
    url: `${server.heypointServer}/account/getExportDemo`,
    method: 'post',
    data
  });
};

/**
 * 获取批量充值模板
 */
export const getRechargeTemplate = (data) => {
  return axios({
    url: `${server.heypointServer}/account/getExportRechargeDemo`,
    method: 'post',
    data
  });
};

/**
 * 批量赠送黑豆
 */
 export const batchGiftHeypoints = (params) => {
  return axios({
    url: `${server.heypointServer}/account/exportGiftS3`,
    method: 'get',
    params
  });
};

/**
 * 批量充值黑豆
 */
export const batchRechargeHeypoints = (params) => {
  return axios({
    url: `${server.heypointServer}/account/exportRechargeS3`,
    method: 'get',
    params
  });
};

/**
 * 获取账单管理列表
 */
export const getBillManagerList = (data) => {
  return axios({
    url: `${server.heypointServer}/bill/searchBill`,
    method: 'post',
    data
  });
};

/**
 * 获取账单详情
 */
export const getBillDetails = (params) => {
  return axios({
    url: `${server.heypointServer}/bill/searchBillByNo`,
    method: 'get',
    params,
  });
};

/**
 * 获取账单详情的设计品类
 */
export const getBillDesignType = (params) => {
  return axios({
    url: `${server.heypointServer}/billdetail/searchBillDesignTypeByNo`,
    method: 'get',
    params
  });
}

/**
 * 导出账单excel
 */
export const getBillExcel = (params) => {
  return axios({
    url: `${server.heypointServer}/bill/exportBillExcel`,
    method: 'get',
    params
  });
}

/**
 * 新增余额配置
 */
export const addOrUpdateBalance = (data) => {
  return axios({
    url: `${server.heypointServer}/balanceConfig/addOrUpdateBalanceConfig`,
    method: 'POST',
    data
  });
}

/**
 * 黑豆日记找经办人
 */
export const findCreateUserName = (params) => {
  return axios({
    url: `${server.heypointServer}/accountwater/tenant/findCreateUserName`,
    method: 'GET',
    params
  });
};

/**
 * 黑豆日记找用户机构
 */
 export const searchAccountWater = (params) => {
  return axios({
    url: `${server.heypointServer}/accountwater/tenant/findOrgName`,
    method: 'GET',
    params
  });
};

/**
 * 黑豆日记查询操作列表
 */
export const searchOperationLog = (data) => {
  return axios({
    url: `${server.heypointServer}/accountwater/tenant/searchOperationLog`,
    method: 'POST',
    data
  });
};

/**
 * 黑豆日记查询汇总信息
 */
 export const operationLogTotal = (data) => {
  return axios({
    url: `${server.heypointServer}/accountwater/tenant/operationLogTotal`,
    method: 'POST',
    data
  });
};

/**
 * 清空余额配置
 */
export const removeAll = (id) => {
  return axios({
    url: `${server.heypointServer}/balanceConfig/removeAll`,
    method: 'POST',
    data:{
      id
    }
  });
}
// 新++++++++++++++++++++++++++++++++++++++++++

// 帐单列表
export const searchManualBill = (data) => {
  return axios({
    url: `${server.heypointServer}/manualbill/searchManualBill`,
    method: 'POST',
    data
  });
};

// 帐单列表
export const getManualBillById = (params) => {
  return axios({
    url: `${server.heypointServer}/manualbill/getManualBillById`,
    method: 'GET',
    params
  });
};

  // 导出帐单列表
  export const exportManualBill = (data) => {
    return axios({
      url: `${server.heypointServer}/manualbill/exportManualBill`,
      method: 'POST',
      data
    });
  };

  // 批量下推销售单
  export const batchPushCrm = (data) => {
    return axios({
      url: `${server.heypointServer}/manualbill/batchPushCrm`,
      method: 'POST',
      data
    });
  };  

    // 下推变更单
    export const pushUpdateCrm = (params) => {
      return axios({
        url: `${server.heypointServer}/manualbill/pushUpdateCrm`,
        method: 'GET',
        params
      });
    };  

  pushUpdateCrm

// 提交人工账单
export const uploadBillExcel = (params) => {
  return axios({
    url: `${server.heypointServer}/manualbill/sumbmitBillExcel`,
    method: 'GET',
    params
  });
};
// 检查人工账单
export const checkBillExcel = (params) => {
  return axios({
    url: `${server.heypointServer}/manualbill/checkBillExcel`,
    method: 'GET',
    params
  });
};

// 撤回账单
export const recallBillExcel = (params) => {
  return axios({
    url: `${server.heypointServer}/manualbill/recallBillExcel`,
    method: 'GET',
    params
  });
};
// 下推crm
export const pushCrm = (params) => {
  return axios({
    url: `${server.heypointServer}/manualbill/pushCrm`,
    method: 'GET',
    params
  });
};
// 获取最新申诉原因
export const getAppealReason = (params) => {
  return axios({
    url: `${server.heypointServer}/manualbill/getAppealReason`,
    method: 'GET',
    params
  });
};
// 撤回CRM
export const recallCrm = (params) => {
  return axios({
    url: `${server.heypointServer}/manualbill/recallCrm`,
    method: 'GET',
    params
  });
};
// 获取订单下推失败原因
export const getPushCrmErrorInfo = (params) => {
  return axios({
    url: `${server.heypointServer}/manualbill/getPushCrmErrorInfo`,
    method: 'GET',
    params
  });
};


