<template>
  <div class="LoadingBox">
    <div class="Loading_bg"></div>
    <div class="LoadingText flex-start">
      <img
        class="MA_icon pos-abs"
        src="/images/loading_cricle_final.gif"
      />
      <span>{{ loadingText ? loadingText : $t('common.loading') }}</span>
      <slot name="percent"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    loadingText: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="scss" scoped>
.LoadingBox {
  position: relative;
  z-index: 99;
  .Loading_bg {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: #121314;
    opacity: 0.8;
    z-index: 99;
  }
  .LoadingText {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
    font-size: $hg-normal-fontsize;
    line-height: 16px;
    padding: 11px 24px 16px 60px;
    margin: auto;
    background: $hg-main-black;
    color: $hg-primary-fontcolor;
    box-shadow: 0px 0px 16px rgba(18, 19, 20, 0.32),
      0px 8px 24px rgba(18, 19, 20, 0.2), 0px 12px 32px rgba(18, 19, 20, 0.12);
    border-radius: $hg-border-radius4;
    .MA_icon {
      width: 24px;
      height: 24px;
      top: 50%;
      left: 27px;
      transform: translateY(-50%);
    }
    .LoadingPercent {
      margin-left: 30px;
    }
  }
  .MessageAlert.OpacityDown {
    -webkit-animation-duration: 0.6s;
    animation-duration: 0.6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: OpacityDown;
    animation-name: OpacityDown;
  }
  .MessageAlert.OpacityUp {
    -webkit-animation-duration: 0.6s;
    animation-duration: 0.6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: OpacityUp;
    animation-name: OpacityUp;
  }
}
</style>
