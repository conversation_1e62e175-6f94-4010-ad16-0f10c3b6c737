<template>
  <div v-if="detailRow && processDrawer"> 
    <el-drawer custom-class="process-drawer" v-loading="splitLoading" :visible.sync="processDrawer">
      <div class="draw-title" slot="title">{{detailRow.orgName}}-{{detailRow.billDate}}</div>
      <!-- 无数据 -->
      <!-- <div v-if="detailRow.zeroOrderNum" class="no-details">
        {{lang('leftDrawer.noOrder')}}
      </div> -->
      <!-- <div style="height: 100%;" v-else> -->
      <div style="height: 100%;">
        <!-- 按钮组 -->
        <div class="all-btn">
          <div class="btn-list">
            <span v-for="item in btnList" v-if="item.status.includes(detailRow.status)" :key="item.value" @click="changeModel(item.value)" :class="['btn', nowSelect == item.value ? 'active-btn' : '']">{{ item.name }}</span>
          </div>
          <div class="right-btn">
            <el-button @click="downBill">{{$t('common.download')}}</el-button>
            <!-- <el-button v-for="(item, index) in billBtnList" v-if="item.status.includes(detailRow.status)&&(item.value=='submit'&&!detailRow.zeroOrderNum)" v-permission="item.permission" :type="item.primary" :key="index" @click="operate(item)">{{ item.name }}</el-button> -->
            <!-- 零订单账单时，确认按钮不需要 -->
            <el-button v-for="(item, index) in billBtnList" v-if="item.status.includes(detailRow.status)&&((item.value=='submit'&&!detailRow.zeroOrderNum)||item.value!='submit')" v-permission="item.permission" :type="item.primary" :key="index" @click="operate(item)">{{ item.name }}</el-button>
          </div>
        </div>
        <div class="table-body" id="table-body" v-loading="loadingSheet">
          <!-- excel表 -->
          <div v-show="nowSelect === 'bill' || nowSelect === 'detail'" id="luckysheet" class="luckysheet"></div>
          <!-- <billSheet v-if="nowSelect === 'bill'" :files="files" @loadingSheet="loadingSheetClick"></billSheet>
          <detailSheet v-if="nowSelect === 'detail'" :files="files" @loadingSheet="loadingSheetClick"></detailSheet> -->
          <!-- 客诉原因 -->
          <div class="custom-reson" v-if="nowSelect === 'reason' && detailRow.status == 3">
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" :placeholder="lang('leftDrawer.reason')"  v-model="customReason"></el-input>
            <div class="img-list">
              <div class="img" v-for="(img, index) in appealImgList" :key="index" @click.stop="openView(img)">
                <img :src="img.url" />
                <div class="img-dialog">
                  <hg-icon icon-name="icon-preview-on-lab"></hg-icon>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </el-drawer>
    <uploadbox :dialogVisible.sync="dialogVisible" :clientOrgCode="clientOrgCode" @uploadSuccess="uploadSuccess" @downSystemBill="downSystemBill" :acceptType="'.xlsx'"></uploadbox>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
import uploadbox from "./uploadbox.vue";
import { getDownloadUrl, getBatchDownloadUrl } from '@/api/file';
import { uploadBillExcel, checkBillExcel, getManualBillById,recallBillExcel, pushCrm, getAppealReason, recallCrm,pushUpdateCrm } from '@/api/heypoint';
import { createIFrameDownLoad } from '@/public/utils/file.js'
import LuckyExcel from 'luckyexcel';
import billSheet from './billSheet.vue';
import detailSheet from './detailSheet.vue';
import { mapGetters } from 'vuex';
export default {
  name: "billdrawer",
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    detailRow: {
      type: Object,
      default: null
    }
  },
  components: {
    uploadbox,
    billSheet,
    detailSheet
  },
  data() {
    return {
      nowSelect: "bill",
      dialogVisible: false,
      // 管理员,系统运营，GTM  'manualBilling', 'billSubmit', 'billReback'
      // 业务人员     'rebackGTM', 'pushCRM'

      permissionList: [
        "billDownLoad",
        "manualBilling",
        "billSubmit",
        "billReback",
        "rebackGTM",
        "pushCRM",
      ],
      btnList: [
        {
          name: this.lang('leftDrawer.bill'),
          value: "bill",
          status: [0,1,2,3]
        },
        {
          name: this.lang('leftDrawer.detail'),
          value: "detail",
          status: [0,1,2,3]
        },
        {
          name: this.lang('leftDrawer.reason'),
          value: "reason",
          status: [3],
        },
      ],
      // status 草稿2 待确认1 申诉中3 已确认0
      billBtnList: [
        {
          name: this.lang('leftDrawer.manual'),
          value: "bill",
          status: [2, 3],
          permission: ["manualBilling"],
        },
        {
          name: this.lang('leftDrawer.submit'),
          value: "submit",
          primary: "primary ",
          status: [2, 3],
          permission: ["billSubmit"],
        },
        {
          name: this.lang('leftDrawer.reback'),
          value: "reback",
          status: [1, 0],
          permission: ["billReback"],
        },
        {
          name: this.lang('leftDrawer.gtm'),
          value: "backGTM",
          status: [1],
          permission: ["rebackGTM"],
        },
        // {
        //   name: this.lang('leftDrawer.crm'),
        //   value: "pushCRM",
        //   primary: "primary ",
        //   status: [0,1,2,3],
        //   permission: ["pushCRM"],
        // },
        {
          name: this.lang('leftDrawer.pushUpdateCRM'),
          value: "pushChangeOrder",
          primary: "primary",
          status: [1, 0],
          // permission: ["pushChangeOrder"],
          permission: ["pushChangeOrder"],
        },
      ],
      clientOrgCode: null,
      customReason: "",
      files: [],
      loadingSheet: false,
      uploadData: null, //上传的信息，提交时需要传s3id
      submitS3Id: '',
      appealImgList: {}, //申诉图片
      exportJsonDetails: {},
      firstSheet: null,
      splitLoading: false,
    };
  },
  mounted () {
    // this.$nextTick(() =>{
    //       this.getDownloadUrl();
    //     })
  },
  watch: {
    processDrawer(newValue, oldValue) {
      if(newValue){
        this.$nextTick(() =>{
          // if(!this.detailRow.zeroOrderNum){
          //   this.getDownloadUrl();
          //   if(this.detailRow.status == 3){
          //     this.getAppealReasonList();
          //   }
          // }
          this.getDownloadUrl();
          if(this.detailRow.status == 3){
            this.getAppealReasonList();
          }
        })
      } else {
        window.luckysheet.destroy();
      }
    },
  },
  computed: {
    ...mapGetters(['language']),
    processDrawer: {
      get() {
        return this.drawer;
      },
      set(val) {
        this.$emit("update:drawer", val);
      },
    },
  },
  methods: {
    lang: getLang('bill'),
    // 切换模块
    changeModel(value) {
      if(value != 'reason'){
        let index = 0;
        value === 'bill' ? index = 0 : index = 1;
        this.nowSelect = value;
        window.luckysheet.setSheetActive(index);
      } else {
        this.nowSelect = value;
      }
    },
    // 下载
    downBill() {
      this.splitLoading = true;
      getDownloadUrl({filename: `${this.detailRow.orgName}_【${this.detailRow.billDate}】`, orgCode: 100765,s3FileId: this.detailRow.manualS3Id}).then((res)=>{
        createIFrameDownLoad(res.data.url)
        this.splitLoading = false;
      })
    },
    // 按钮操作
    operate(item) {
      // 人工出账
      if (item.value === "bill") {
        this.dialogVisible = true;
      } else if(item.value === 'submit'){
        if(this.detailRow.zeroOrderNum){
          console.log("name");
          return;
        }
        // 提交账单
        // console.log(this.submitS3Id, '当前的s3id')
        const parames = {
          s3Id: this.submitS3Id,
          orgCode: this.detailRow.orgCode,
          billNo: this.detailRow.billNo
        }
        this.$confirm(this.lang('leftDrawer.submitTips'), this.lang('leftDrawer.reminding'), {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
        }).then(async() => {
          this.splitLoading = true;
          try {
            const { code } = await uploadBillExcel(parames);
            if(code == 200){
              this.$message.success(this.lang('leftDrawer.successful'));
              this.processDrawer = false;
              this.splitLoading = false;
              this.$emit('submitSuccess')
            } else {
              this.splitLoading = false;
            }
            
          } catch (error) {
            if(error.code == '11010037'){
              let str = this.language == 'zh' ? JSON.parse(error.message).zh : JSON.parse(error.message).en;
              this.$confirm(str, this.lang('leftDrawer.reminding'), {
                confirmButtonText: this.$t('common.btn.confirm'),
                cancelButtonText: this.$t('common.btn.cancel'),
                type: 'warning',
              }).then(() => {
                this.splitLoading = false;
              }).catch(() => {this.splitLoading = false;});
            }
          }
        }).catch(() => {this.splitLoading = false;});
      } else if(item.value === 'reback'){
        const parames = {
          orgCode: this.detailRow.orgCode,
          billNo: this.detailRow.billNo
        }
        this.$confirm(this.lang('leftDrawer.backTips'), this.lang('leftDrawer.reminding'), {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
        }).then(() => {
          this.splitLoading = true
          recallBillExcel(parames).then((res) => {
            if(res.code === 200){
              // this.$message.success('撤销成功');
              this.$message.success(this.lang('leftDrawer.backsuccess'));
              this.processDrawer = false;
              this.splitLoading = false;
              this.$emit('submitSuccess')
            } else {
              this.splitLoading = false;
            }
          })
        }).catch(() => {this.splitLoading = false;});
      } else if(item.value === 'backGTM'){
        const parames = {
          orgCode: this.detailRow.orgCode,
          billNo: this.detailRow.billNo
        }
        this.$confirm(this.lang('leftDrawer.GTMTips'), this.lang('leftDrawer.reminding'), {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
        }).then(() => {
          this.splitLoading = true
          recallCrm(parames).then((res) => {
            if(res.code === 200){
              this.$message.success(this.lang('leftDrawer.successful'));
              this.processDrawer = false;
              this.splitLoading = false
              this.$emit('submitSuccess')
            } else {
              this.splitLoading = false;
            }
          })
        }).catch(() => {this.splitLoading = false;});
      } else if(item.value === 'pushCRM'){
        const parames = {
          orgCode: this.detailRow.orgCode,
          billNo: this.detailRow.billNo
        }
        this.$confirm(this.lang('leftDrawer.pushCRMtips'), this.lang('leftDrawer.reminding'), {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
        }).then(async() => {
          this.splitLoading = true;
          try {
            const { code,data } = await pushCrm(parames);
            if(code === 200 && data){
              this.splitLoading = false;
              this.$message.success(this.lang('leftDrawer.pushSuccess'));
              this.processDrawer = false;
              this.$emit('submitSuccess')
            } else {
              this.splitLoading = false;
              this.$message.error(this.lang('leftDrawer.pushUpdataError'));
              this.$emit('submitSuccess', 'error', parames)
            }
          } catch (error) {
            if(error.code == '11010037'){
              let str = this.language == 'zh' ? JSON.parse(error.message).zh : JSON.parse(error.message).en;
              this.$confirm(str, this.lang('leftDrawer.reminding'), {
                confirmButtonText: this.$t('common.btn.confirm'),
                cancelButtonText: this.$t('common.btn.cancel'),
                type: 'warning',
              }).then(() => {
                this.splitLoading = false;
              }).catch(() => {this.splitLoading = false;});
            }
          }
        }).catch(() => {this.splitLoading = false;});
      }else if(item.value === 'pushChangeOrder'){
        const parames = {
          orgCode: this.detailRow.orgCode,
          billNo: this.detailRow.billNo
        }
        this.$confirm(this.lang('leftDrawer.pushCRMtips'), this.lang('leftDrawer.reminding'), {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
        }).then(async() => {
          this.splitLoading = true;
          try{
            const { data } = await pushUpdateCrm(parames);
            this.splitLoading = false;
            if(!data){
              let str = this.lang('leftDrawer.pushUpdataError');
              this.$confirm(str, this.lang('leftDrawer.reminding'), {
                confirmButtonText: this.$t('common.btn.confirm'),
                cancelButtonText: this.$t('common.btn.cancel'),
                type: 'warning',
              }).then(() => {
                this.splitLoading = false;
              }).catch(() => {this.splitLoading = false;});
            }else{
              this.$message.success(this.lang('leftDrawer.pushSuccess'));
            }
          }catch(error){
            this.splitLoading = false;
            if(error.code == '11010037'){
              let str = this.language == 'zh' ? JSON.parse(error.message).zh : JSON.parse(error.message).en;
              this.$confirm(str, this.lang('leftDrawer.reminding'), {
                confirmButtonText: this.$t('common.btn.confirm'),
                cancelButtonText: this.$t('common.btn.cancel'),
                type: 'warning',
              }).then(() => {
                this.splitLoading = false;
              }).catch(() => {this.splitLoading = false;});
            }else if(error.code == '11010039' || error.code == '11010051'){
               let str = this.language == 'zh' ? JSON.parse(error.message).zh : JSON.parse(error.message).en;
              this.$confirm(str, this.lang('leftDrawer.reminding'), {
                confirmButtonText: this.$t('common.btn.confirm'),
                cancelButtonText: this.$t('common.btn.cancel'),
                type: 'warning',
              }).then(() => {
                this.splitLoading = false;
              }).catch(() => {this.splitLoading = false;});
            }else{
               let str = this.language == 'zh' ? '下推变更单失败' : 'send Change Order failed';
              this.$confirm(str, this.lang('leftDrawer.reminding'), {
                confirmButtonText: this.$t('common.btn.confirm'),
                cancelButtonText: this.$t('common.btn.cancel'),
                type: 'warning',
              }).then(() => {
                this.splitLoading = false;
              }).catch(() => {this.splitLoading = false;});
            }
          }
        });
      }
    },
    // 一个检查账单的操作
    async gocheckBillExcel(){
      const parames = {
        s3Id: this.submitS3Id,
        orgCode: this.detailRow.orgCode,
        billNo: this.detailRow.billNo
      }
      const {data, code, message} = await checkBillExcel(parames);
      if(code == 200 && data.length > 0){
        let str = ''
        if(this.language == 'zh'){
          data.forEach((item) => {
            str += item.zh + '；'
          })
        } else {
          data.forEach((item) => {
            str += item.en + '；'
          })
        }
        str = str.slice(0, str.length - 1)
        this.$confirm(str, this.lang('leftDrawer.reminding'), {
          confirmButtonText: this.$t('common.btn.confirm'),
          cancelButtonText: this.$t('common.btn.cancel'),
          type: 'warning',
        }).then(() => {}).catch(() => {});
      }

      const result = await getManualBillById({id:this.detailRow.id});
      if(result&&result.data && result.data.manualS3Id){
        this.detailRow.manualS3Id = result.data.manualS3Id;
        this.getDownloadUrl();
      } 

    },
    // 人工出账上传成功
    uploadSuccess(data){
      this.files = [];
      this.submitS3Id = data.data.s3FileId
      const file = new File([data.file.raw], 'Kelley Dental Laboratory_【2401】.xlsx', { type: data.file.raw.type });
      this.files.push(file);
      this.loadingSheet = true;
      this.uploadExcel(this.files, 'isHandUpload')
      this.gocheckBillExcel()
      // setTimeout(() => {
      //   window.luckysheet.setSheetActive(0)
      // }, 500);
    },
    // 下载系统账单
    downSystemBill(){
      getDownloadUrl({filename: `${this.detailRow.orgName}_【${this.detailRow.billDate}】_系统`, orgCode: 100765,s3FileId: this.detailRow.s3Id}).then((res)=>{
        createIFrameDownLoad(res.data.url)
      })
    },
    // 获取申诉原因
    async getAppealReasonList(){
      const parames = {
        orgCode: this.detailRow.orgCode,
        billNo: this.detailRow.billNo
      }
      const res = await getAppealReason(parames);
      this.customReason = res.data.reason ? res.data.reason : '';
      if(res.data.picS3ids && res.data.picS3ids.length > 0){
        let newImgList = []
        res.data.picS3ids.forEach((item) =>{
          let obj = { s3FileId: item }
          newImgList.push(obj)
        })
        const { data } = await getBatchDownloadUrl(newImgList);
        this.appealImgList = data
      }
    },
    ajaxGetBlob(url, progress){
      return new Promise((resolve) => {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.responseType = 'blob';
        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(xhr.response);
          }
        };
        xhr.onprogress = (e) => {
          progress && progress(((e.loaded / e.total) * 100) | 0);
        };

        xhr.send();
      });
    },

    // 获取文件
    getDownloadUrl(){
      this.files = []
      this.loadingSheet = true;
      this.submitS3Id = this.detailRow.manualS3Id
      getDownloadUrl({filename: "Kelley Dental Laboratory_【2401】", orgCode: 100765,s3FileId: this.detailRow.manualS3Id}).then((res)=>{
        this.ajaxGetBlob(res.data.url).then((resolve)=>{
          const file = new File([resolve], 'Kelley Dental Laboratory_【2401】.xlsx', { type: resolve.type });
          this.files.push(file);
          this.uploadExcel(this.files)
          // setTimeout(() => {
          //   window.luckysheet.setSheetActive(0)
          // }, 500);
        })
      })
    },
    uploadExcel(files, isHandUpload) {
        if (files == null || files.length == 0) return alert("没有文件等待导入");
        LuckyExcel.transformExcelToLucky(files[0], (exportJson, luckysheetfile) => {
          console.log(exportJson, 55555)
          if (exportJson.sheets == null || exportJson.sheets.length == 0) return alert("读取excel文件内容失败, 目前不支持XLS文件!");
          exportJson.sheets[0].config.rowhidden = {0: 0, 1: 0, 2: 0, 3: 0}; //隐藏行
          // 人工上传不对表格做任何操作
          if(!this.detailRow.isChangeCurrency && !isHandUpload){
            exportJson.sheets[0].config.colhidden = { 13: 0 }; //隐藏列
            if(this.detailRow.agent){
              // 不是代理商隐藏部门列
              exportJson.sheets[1].config.colhidden = { 12: 0 }; //隐藏列
            } else {
              exportJson.sheets[1].config.colhidden = { 0: 0, 12: 0 }; //隐藏列
            }
          }
          if(this.detailRow.isChangeCurrency && !isHandUpload){
            if(!this.detailRow.agent){
              // 代理商隐藏部门列
              exportJson.sheets[1].config.colhidden = { 0: 0 }; //隐藏列
            }
          }
          exportJson.sheets[0].zoomRatio = 0.90;
          exportJson.sheets[0].images = [] // 隐藏不必要的图片
          exportJson.sheets[0].status = 1; // 这里强改当前文档的激活状态，设置第一个表永远是激活
          exportJson.sheets[1].status = 0;
          // exportJson.sheets[2].luckysheet_select_save = []
          // exportJson.sheets[0].luckysheet_select_save = []
          exportJson.sheets[0].config.borderInfo = [{
            rangeType: "range",
            borderType: "border-all",
            style: "3",
            color: "#f5f5f5",
            range: [{
              row: [],
              column: [],
            }],
          }];
          exportJson.sheets[1].frozen = {
            type: 'rangeColumn',
            range: {row_focus: 1, column_focus: 1}
          }

          this.exportJsonDetails = exportJson.sheets[1];
          this.firstSheet = exportJson.sheets[0]
          const arr = [exportJson.sheets[0], exportJson.sheets[1]];
          window.luckysheet.destroy();
          window.luckysheet.create({
            data: arr,
            autoFormatw: true,
            title: exportJson.info.name,
            userInfo: exportJson.info.name.creator,
            container: "luckysheet", // 设定DOM容器的id
            showtoolbar: false, // 是否显示工具栏
            showinfobar: false, // 是否显示顶部信息栏
            showstatisticBar: false, // 是否显示底部计数栏
            sheetBottomConfig: false, // sheet页下方的添加行按钮和回到顶部按钮配置
            allowEdit: false, // 是否允许前台编辑
            enableAddRow: false, // 是否允许增加行
            enableAddCol: false, // 是否允许增加列
            sheetFormulaBar: false, // 是否显示公式栏
            enableAddBackTop: false, // 返回头部按钮
            showsheetbar: false, // 是否显示底部sheet页按钮
            enableAddRow: false, //允许添加行
            row: 1, //行数
            column: 1, //列数
            rowHeaderWidth: 0,
            columnHeaderHeight: 0,
            devicePixelRatio: 2,
            enablePage: false,
            // 自定义配置底部sheet页按钮
            showsheetbarConfig: {
              add: false,
              menu: false,
            },
          });
          this.loadingSheet = false;
          const height = document.querySelector('#table-body').clientHeight
          document.querySelector('#luckysheet').style.height = (height - 40) + 'px';
          this.nowSelect = 'bill';
          // console.log(luckysheet.getAllSheets(), window.luckysheet.getCellValue(15, 0), 3333)
          setTimeout(()=>{
            this.handelDetails(exportJson.sheets[0].data)
          }, 500)
        });
    },
    handelDetails(data){
      // 纯中文或者纯英文阻止，value统一转为字符串
      const handelValue = function(value){
        value = String(value)
        if(/^[\u4e00-\u9fa5]+$/.test(value) || /^[a-zA-Z]+$/.test(value)){
          return value
        }
        return value.replace(/\b(\d+)\.0\b/g, "$1")
      }
      // console.log(window.luckysheet.getCellValue(12, 7), 1111111)
      for(let i = 15;i < data.length - 8;i++){
        let arr = [0, 5, 6, 7, 9, 11, 12]
        arr.forEach((item) => {
          if(window.luckysheet.getCellValue(i, item)){
            // window.luckysheet.setCellFormat(i, item, "ct", {fa:"@", t:"s"}) // 先转成文本格式，然后重新赋值
            let value = window.luckysheet.getCellValue(i, item)
            if(value !== null){
              window.luckysheet.setCellFormat(i, item, "ct", {fa:"@", t:"s"}) // 先转成文本格式，然后重新赋值
              if(item == 6 || item == 12){
                window.luckysheet.setCellValue(i, item, value)
              } else {
                window.luckysheet.setCellValue(i, item, handelValue(value))
              }
            }
          }
        })
      }
      window.luckysheet.setSheetActive(0);
    },
    openView(file) {
      const pictureUrl = file.url;
      if(pictureUrl) {
        this.$hgViewer.open({
          imgList: [pictureUrl],
          initialViewIndex: 0
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/.process-drawer {
  width: 1400px !important;
  background-color: $hg-main-black;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
  }
  .el-drawer__body {
    padding: 24px;
  }
  .all-btn {
    display: flex;
    justify-content: space-between;
  }
  .btn-list {
    display: inline-block;
    // width: 140px;
    height: 40px;
    padding: 4px;
    border-radius: 4px;
    background: #262629;

    .btn {
      display: inline-block;
      vertical-align: top;
      // width: 64px;
      padding: 0 16px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      color: #e4e8f7;
      font-size: 16px;
      cursor: pointer;
      &:last-child {
        margin-left: 2px;
      }
    }
    .active-btn {
      border-radius: 4px;
      background: #3760ea;
      font-weight: bold;
    }
  }
  .el-button:focus{
    background-color: #3760EA;
    border-color: #3760EA;
  }
  .no-details {
    display: flex;
    margin-top: 10px;
    height: calc(100% - 45px);
    background: #27292e;
    justify-content: center;
    align-items: center;
  }
  .luckysheet{
    width: 1300px;
    height: 700px;
  }
  .table-body {
    margin-top: 10px;
    height: calc(100% - 70px);
    overflow: auto;
    background: #27292e;
    padding: 24px;
    .el-textarea .el-textarea__inner {
      background: $hg-main-black;
    }
    .img-list {
      display: flex;
      width: 100%;
      height: 150px;
      flex-wrap: wrap;
      .img {
        position: relative;
        width: 200px;
        height: 150px;
        margin-top: 20px;
        margin-right: 20px;
        cursor: pointer;
        img {
          width: 200px;
          height: 150px;
        }
      }
      .img-dialog {
        display: none;
        position: absolute;
        width: 200px;
        height: 150px;
        cursor: pointer;
        background: rgba(0, 0, 0, 0.6);
        top: 0;
        left: 0;
        justify-content: center;
        align-items: center;
      }
      .img:hover {
        .img-dialog {
          display: flex;
        }
      }
    }
  }
}
</style>
