import http from 'axios';
import SparkMD5 from 'spark-md5';
import { getMultipartUploadInfo, completePartUpload, completeMultipartUpload } from '@/api/file';
import { getSuffix } from '@/public/utils/file';
const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;

class SliceUpload {

  constructor(option) {

    // 文件需要返回的属性
    this.s3FileId = null;
    this.fileName = option.file.name || '';
    this.fileSize = option.file.size || 0;
    this.filePath = null; // s3FileId
    this.fileType = option.fileType || 0;
    this.fileTime = option.file.uid || 0;
    this.uploadStatus = 'pending';
    this.progress = 0;
    this.uid = option.file.uid;
    this.md5 = null;
    this._orgCode = option.orgCode || 0;
    this._file = option.file;
    this._finishCount = 0; // 完成个数
    this._s3Uid = 0;      // _file的预授权流水号
    this._sliceCount = 0; // 分片个数

    this._sliceSize = 5242880; // 分片大小
    this._repeatTime = 10; // 重复发送请求尝试次数
    this._maxUploadTime = 5; // 最大上传个数
    this._cancelTokenList = []; // 分片上传的source放在这里
    this._uploadList = [];
    this._isCancel = false;

    this.callbackFn = null;
    this.progressFn = null;
    this.checkMd5Fn = null;
    this.errorFn = null;
  }

  async onStart() {
    await this._getMd5();
  }

  onCheckMd5(fn) {
    this.checkMd5Fn = fn;
  }

  onUpload() {
    this._init();
  }

  // 需要先定义onEnd方案
  onSuccess(fn) {
    this.callbackFn = fn;
  }

  onError(fn) {
    this.errorFn = fn;
  }

  cancelUpload() {
    this._isCancel = true;
    this._cancelTokenList.forEach(fn => fn.cancel());
  }

  async _init() {
    try {
      const param = { fileName: this.fileName, md5: this.md5, suffix: getSuffix(this.fileName), orgCode: this._orgCode, size: this._file.size };
      const res = await getMultipartUploadInfo(param);
      const { urlUuid: s3Uid, slicerNum: sliceCount, isExist, s3FileId, uploadPathList } = res.data;
      
      if(isExist || s3FileId) { // 文件已存在s3 
        this.s3FileId = s3FileId;
        this.filePath = s3FileId;
        this._finishCount = sliceCount;
        this.uploadStatus = 'success';
        this.progress = 100;
        this.callbackFn && this.callbackFn(this);

      }else {
      
        const finishCount = uploadPathList.reduce((sum,curItem) => {
          if(curItem.uploaded) {
            return ++sum;
          }else {
            return sum;
          }
        },0);

        this._finishCount = finishCount;
        this._s3Uid = s3Uid;
        this._sliceCount = sliceCount;

        const beUploadList = uploadPathList.filter(item => !item.uploaded);
        if(beUploadList.length !== 0) {
          this._uploadList = beUploadList;
          await this._handleUpload();
        }

        const { code, data } = await completeMultipartUpload({fileName: this.fileName, orgCode: this._orgCode, s3Uid: this._s3Uid});
        if(code === 200) {
          this.s3FileId = data.s3FileId;
          this.filePath = data.s3FileId;
          this.uploadStatus = 'success';
          this.progress = 100;
          this.callbackFn && this.callbackFn(this);
        }else {
          this.uploadStatus = 'fail';
          this.errorFn && this.errorFn(this);
        }  

      }

    } catch (error) {
      console.log('SliceUpload-init error: ',error);
      this.errorFn && this.errorFn(this);
    }
  }

  /**
   * 获取主文件的md5
   */
  async _getMd5() {
    try {
      this.progress = 1; // 读取文件的md5需要时间
      const md5 = await this._getMd5ByFile(this._file);
      this.md5 = md5;
      this.checkMd5Fn(this); // 检查md5
    } catch (error) {
      console.log('SliceUpload-getFileMd5 error: ',error);
      this.errorFn && this.errorFn(this);
    }
  }

  _getMd5ByFile(file) {
    return new Promise((resolve) => {
      const spark = new SparkMD5.ArrayBuffer();
      const fileReader = new FileReader();
      fileReader.readAsArrayBuffer(file);
      fileReader.onload = function(e){
        spark.append(e.target.result);
        const md5 = spark.end();
        resolve(md5);
      }
    });
  }

  async _handleUpload() {
    return new Promise((resolve, reject) => {
      
      const uploadFuntion = async () => {
        if( this._uploadList.length === 0 || this._isCancel ) { return; }

        const {url, partIndex  } = this._uploadList.shift();
        const result = await this._uploadPartByBuffer(url, partIndex);
        if(result) {
          this._finishCount++;
          this.progress = parseInt( (this._finishCount / this._sliceCount) * 100 ); //假进度
          if(this.progress > 99) {
            this.progress = 99;
          }
          if(this._finishCount === this._sliceCount) { // 当前完成上传个数 等于 需要上传的个数
            console.log('完成上传'); 
            resolve();
          }else {
            uploadFuntion();
          }
        }else {
          reject('分片上传失败');
          this.errorFn && this.errorFn(this);
        }
      }

      for (let i = 0; i < this._maxUploadTime; i++) {
        uploadFuntion();
      }

    });
  }

  async _uploadPartByBuffer(url, partIndex) {
    const start = (partIndex - 1) * this._sliceSize;
    const end = (start + this._sliceSize) >= this.fileSize ? this.fileSize : (start + this._sliceSize); // start + chunkSize 是否大于文件的size
    const partBuffer = blobSlice.call(this._file, start, end); 
    const md5 = await this._getMd5ByFile(partBuffer);

    const source = http.CancelToken.source();
    this._cancelTokenList.push(source);

    await http({
      timeout: 60 * 60 * 1000,//设置超时时长 一个小时
      url,
      method: 'PUT',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: partBuffer,
      cancelToken: source.token,
    });

    const param = { partMd5: md5, partIndex, s3Uid: this._s3Uid };
    const { code } = await completePartUpload(param);
    if(code !== 200 && this._repeatTime !== 0) {
      this._repeatTime--;
      await this._uploadPartByBuffer(url, partIndex);
    }else if( code !== 200 ){
      return false;
    }else {
      return true;
    } 
  }

}

export default SliceUpload;