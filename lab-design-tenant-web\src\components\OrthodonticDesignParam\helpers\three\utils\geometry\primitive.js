import * as THREE from 'three'

export function createTriangleGeometry(a, b, c) {
  const args = [a, b, c]

  const geometry = new THREE.BufferGeometry()

  const array = args.reduce((prev, next) => {
    const { x, y, z } = next
    prev.push(x, y, z)
    return prev
  }, [])
  const vertices = new Float32Array(array)
  geometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3))
  geometry.setIndex([0, 1, 2])
  return geometry

}
