<template>
  <div class="component-plane">
    <div class="title" v-if="title || slotTitle">
      <slot name="title" v-if="slotTitle"></slot>
      <h3 class="text" v-else>{{title}}</h3>
    </div>
    <div class="content" :style="planeStyle">
      <slot v-if="slotTitle" name="content"></slot>
      <slot v-else></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Plane',
  props: {
    title: {
      type: String,
      default: ''
    },
    slotTitle: {
      type: Boolean,
      default: false
    },
    planeStyle: {
      type: Object,
      default: () => {
        return { background: '#1D1D1F' }
      }
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
  .component-plane {
    width: 100%;
    height: auto;
    margin-bottom: $hg-main-margin;
    .title {
      width: 100%;
      height: auto;
      font-size: $hg-medium-fontsize;
      color: $hg-main-blue;
      font-weight: 600;
      padding-bottom: 12px;
      background-color: $hg-background-color;
      .text {
        height: 48px;
        line-height: 48px;
      }
    }
    .content {
      background-color: $hg-main-black;
      padding: 12px 24px;
      border-radius: $hg-border-radius4;
    }
  }
</style>
