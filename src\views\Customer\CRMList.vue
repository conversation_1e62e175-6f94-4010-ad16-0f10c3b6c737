<template>
  <el-popover
    popper-class="crm-list"
    width="640"
    trigger="click"
    v-model="show"
    @show="onSearchList"
    @hide="onHide">
    <div class="content" v-loading="loading">
      <el-table
        tooltip-effect="dark"
        max-height="340px"
        :data="dataList"
        @row-click="onSelect">
        <el-table-column width="64" v-slot="scope" class-name="radio-box">
          <span :class="{'radio-item': true, 'is-select': selectOne === scope.row.orgSn}"><span></span></span>
        </el-table-column>
        <el-table-column show-overflow-tooltip min-width="100" property="orgSn" :label="$t('customer.orgCode')"></el-table-column>
        <el-table-column show-overflow-tooltip min-width="220" property="orgName" :label="$t('customer.orgName')"></el-table-column>
        <el-table-column show-overflow-tooltip min-width="240" property="orgAddress" :label="$t('customer.companyAddress')"></el-table-column>
      </el-table>
      <el-button class="hg-button btn-confirm" :disabled="selectOne === 0" @click="onConfirm">{{ $t('common.confirm') }}</el-button>
    </div>
    <template slot="reference" v-if="$slots.reference">
      <slot name="reference"></slot>
    </template>
  </el-popover>
</template>

<script>
import { getCRMList } from '@/api/customer';

export default {
  name: 'CRMList',
  props: {
    searchName: {
      type: String,
      default: ''
    },
    orgInfo: {
      type: Object,
      require: true
    }
  },
  data() {
    return {
      loading: false,
      show: false,
      selectOne: 0,
      dataList: [],
    }
  },
  methods: {
    onSearchList() {
      this.dataList = [];
      this.loading = true;
      getCRMList(this.searchName).then(res => {
        const { code, data } = res;
        if(code === 200) {
          this.dataList = data;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    onSelect(row) {
      console.log(row);
      const { orgSn } = row;
      if(orgSn !== this.selectOne) {
        this.selectOne = orgSn;
      }
    },
    onHide() {
      this.selectOne = 0;
    },
    onConfirm() {
      const selectItem = this.dataList.find(item => item.orgSn === this.selectOne);
      if(selectItem) {
        this.$emit('update', selectItem);
        this.show = false;
        this.selectOne = 0;
      }

    }
  }
}
</script>

<style lang="scss" scoped>

.crm-list>.content {
  .radio-item {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #83868F;
    border-radius: 50%;
  }

  .radio-item.is-select {
    border-color: #3765EA;
    &>span {
      margin: 2px;
      display: inline-block;
      border: 6px solid #3765EA;
      border-radius: 50%;
    }
  }

  ::v-deep .radio-box>.cell {
    display: flex;
    justify-content: center
  }

  .btn-confirm {
    float: right;
    margin: 12px 24px 16px;
  }

}

</style>


<style lang="scss">
.crm-list.el-popover {
  padding: 0;
  background-color: #262629;
  border-color: #262629;
  border-radius: 2px;
  box-shadow: 0 0.85714rem 2.28571rem 0 #121314, 0 0.57143rem 1.71429rem 0 #121314, 0 0 1.14286rem 0 #121314;

  .content>.el-table {
    border-radius: 4px;
    background: #1D1D1F;
    &::before {
      height: 0; // 去掉莫名切莫的线
    }

      .el-table__fixed{
      &::before {
        height: 0; // 去掉莫名切莫的线
      }
    }
    .el-table__header-wrapper {
      .el-table__header>thead>tr {
        background: transparent;
        th {
          height: 48px;
          font-size: 12px;
          color: #54565C;
          background: transparent;
          border-bottom: 1px solid #38393D;

          // 第一个th 和 最后一个th
          &:first-of-type {
            padding-left: 12px;
          }

          &:nth-last-of-type(2) {
            div {
              padding-right: 18px;
            }
          }
        }

        th > div {
          line-height: 16px;
        }
      }
    }
    .el-table__header-wrapper {
      .el-table__header>thead>tr {
        background: transparent;
        th {
          height: 48px;
          font-size: 12px;
          color: #54565C;
          background: #1D1D1F;
          border-bottom: 1px solid #38393D;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body>tbody>tr {
        background: transparent;
        &:hover {
          background-color: #262629;
          td {
            background-color: #262629;
          }
        }
      }

      .el-table__row>td {
        cursor: pointer;
        height: 56px;
        // color: $hg-secondary-text;
        color: #E4E8F7;
        font-size: 14px;
        border-bottom: 1px dashed #2d2f33;

        &:first-of-type {
          padding-left: 12px;
        }

        &:last-of-type {
          div {
            padding-right: 16px;
          }
        }
      }
    }

  }

  .popper__arrow {
    border-bottom-color: #262629;
    &::after {
      border-bottom-color: #262629;
    }
  }
}
</style>
