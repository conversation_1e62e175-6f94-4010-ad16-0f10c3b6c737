<template>
  <div class="file-item">
    <div class="file-item-content">
      <!-- <hg-icon :icon-name="getIcon" color="#3760EA"></hg-icon> -->
      <img class="hg-icon" :src="getIcon" alt="">
      <div @mouseenter="showMask=true" @mouseleave="showMask=false" class="file-content">
        <span>{{ item.fileName }}</span>
        <span v-if="needMask && item.fileSize > 0" class="file-item-size">{{ item.fileSize | formatFileSize }}</span>
        <slot name="eventBtn"></slot>

        <div v-show="needMask && showMask" class="file-mask">
          <span @click="handleDelete"><hg-icon icon-name="icon-close1-lab" font-size="24px"></hg-icon></span>
          <span class="down-btn" v-if="needDownload" @click="handleDownload"><hg-icon icon-name="icon-download-lab" font-size="18px"></hg-icon></span>

        </div>
      </div>

      <p class="upload-percent" v-show="item.progress !== 100 && !item.filePath" :style="{width: item.progress + '%'}"></p>
    </div>
    <slot name="loadModel"></slot>
  </div>
</template>

<script>
import { FILE_TYPES } from '@/public/constants';

export default {
  name: 'FileItem',
  props: {
    item: {
      type: Object,
      default() {
        return null
      }
    },
    needMask:{
      type: Boolean,
      default: true,
    },
    needDownload: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      showMask: false,
    }
  },
  computed: {
    getIcon() {
      const { fileType, fileName } = this.item;
      let icon = () => require('@/assets/images/order/icon_folder.svg');
      switch(fileType) {
        case FILE_TYPES.DESIGN_MODEL:
          icon = () => require('@/assets/images/order/icon_model.svg');
        break;
        case FILE_TYPES.DESIGN_VIDEO:
          icon = () => require('@/assets/images/order/icon_video.svg');
        break;
        case FILE_TYPES.PROSPECTUS:
        case FILE_TYPES.IMPLANT_FILE:
        case FILE_TYPES.DRILL_FILE:
          {
            let suffix = fileName.split('.').pop();
            suffix = suffix.toLowerCase();
            if(['pdf'].includes(suffix)){
              icon = () => require('@/assets/images/order/icon_pdf.svg');
            }else if (['ppt', 'pptx'].includes(suffix)){
              icon = () => require('@/assets/images/order/icon_ppt.svg');
            }else if (['doc', 'docx'].includes(suffix)) {
              icon = () => require('@/assets/images/order/icon_doc.svg');
            }
          }
        break;
        default:break;
      }

      return icon();
    }
  },
  methods: {
    handleDelete() {
      this.$emit('handleRemove', this.item);
    },

    handleDownload() {
      this.$emit('handleDownload', this.item);
    }
  }
}
</script>

<style lang="scss" scoped>
.file-item > .file-item-content {
  position: relative;
  display: flex;
  border-radius: 4px;
  background: $hg-border-second;

  &>.hg-icon {
    margin: 8px 16px;
    margin-right: 0;
    width: 64px;
    height: 64px;
    font-size: 64px;
    background: $hg-main-black;
  }

  .upload-percent {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 2px;
    background: $hg-main-blue;
  }

  .file-content {
    position: relative;
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    margin-right: 16px;

    >span:first-of-type {
      padding-left: 16px;
    }
    .file-item-size {
      padding-right: 8px;
      color: $hg-secondary-text;
    }
  }

  .file-mask {
    position: absolute;
    z-index: 2;
    height: 100%;
    width: 100%;
    opacity: 0.6;
    background: #000000;

    .hg-icon {
      position: absolute;
      display: inline-block;
      cursor: pointer;
      right: 16px;
      top: 30%;
      // width: 24px;
      // height: 24px;
      font-size: 24px;
      background: transparent;
      color: $hg-label;
    }
    > .down-btn {
      .icon-download-lab {
        top: 34%;
        right: 52px;
      }
    }
  }

  .show-mask {
    display: flex;
    justify-content: flex-end;
  }

}
</style>