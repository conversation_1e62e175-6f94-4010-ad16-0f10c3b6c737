import Vue from 'vue'
import VueI18n from 'vue-i18n'
import zh from './config/zh.js'
import en from './config/en.js'
import enLocale from 'element-ui/lib/locale/lang/en'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
import { getStore } from '@/assets/script/utils.js'
Vue.use(VueI18n)
let i18nDictList = getStore('i18nDictList') || {apiZh: {}, apiEn: {}};

let i18n = new VueI18n({
  locale: getStore('lang') || window.navigator.language.slice(0,2),
  silentFallbackWarn: true,//抑制警告
  fallbackLocale: 'zh',
  messages: {
    'zh': Object.assign(zh, zhLocale, i18nDictList.apiZh),
    'en': Object.assign(en, enLocale, i18nDictList.apiEn),
  }
})

export default i18n
