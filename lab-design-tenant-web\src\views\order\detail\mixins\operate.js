 /**
  * 订单操作
  *   待译单：入检通过、编辑、IQC返单、指派IQC、由我译单
  *   待指派：编辑、指派设计师
  *   待设计：编辑、指派设计师、退回订单
  *   设计中：指派设计师、设计完成、退回订单
  *   待审核：指派OQC、（设计师）撤回、由我检查、审核通过、审核不通过
  *   待确认：（OQC/管理员/运营人员）撤回
  *   待退回：确认退回、继续设计、编辑
  *   已退回：（管路员/运营人员/IQC）撤回
  *   申请免单：（设计运营）同意免单、不同意免单
  *   常规问题：OQC(oqcUser)
  */

import { mapGetters } from 'vuex';
import { 
  submitTranslation, 
  updateIQC, 
  updateOQC,
  submitEdit, 
  submitDesign, 
  revokeDesignFromOQC, 
  approveDesign, 
  revokeDesignFromClient,
  rebackOrderToClient,
  startToDesign,
  cancelRebackOrder,
  submitDesignForOrtho,
  approveForFree,
  disapproveForFree,
  setNormalQuestion,
  setQC,
} from '@/api/order/operate';
import { FILE_TYPES, UNKNOWN_CODE } from '@/public/constants';
import { base64toFile } from '@/public/utils';
import UploadNormalFile from '@/public/utils/uploadNormalFile';

export default {
  data(){
    return {
      isEdit: false,
      assignType: '',
      returnDialogInfo: {
        title: 'order.detail.title.returnReason',
        placeholder: 'order.detail.tips.pleaseInputReturnReason',
        maxlength: 100,
      },
      uploadCompList: [], // 上传的组件集合
      fillValueList: [], // 回填的值

      checkName: {
        [FILE_TYPES.SCREENSHOT]: 'file.title.screenshot',
        [FILE_TYPES.DESIGN_FILE]: 'file.title.design',
        [FILE_TYPES.DESIGN_MODEL]: 'file.title.model',
        [FILE_TYPES.CORRECT_FILE]: 'file.title.correct',
        [FILE_TYPES.DRILL_FILE]: 'file.title.drill'
      },
    }
  },
  computed: {
    ...mapGetters(['userCode']),
  },
  methods: {
    /**
     * 提交种植方案成功
     */
    submitImplant() {
      this.init()
    },

    /**
     * 译单 又名 入检通过
     */
    handleTranslate(btnItem){
      const translateContent = this.event.translateContent;
      const sendParam = {
        requestParam: [this.orderCode, translateContent],
        btnItem
      }
      this.sendRequest(sendParam, submitTranslation);
    }, 
    // 获取IQC译单内容
    getTranslateContent(translateContent) {
      this.event.translateContent = translateContent;
    },

    //编辑订单
    handleEdit(){
      this.isEdit = true;
      this.initEditRequestParam();
    },

    /**
     * IQC返单：弹框-输入内容-提交-返单给客户
     */
    handleReback(){
      this.returnDialogInfo = {
        title: 'order.detail.title.returnReason',
        maxlength: 200,
        placeholder: 'order.detail.tips.pleaseInputReasonToClient',
      };
      const handleReturnDom = this.$refs.handleReturn;
      if(handleReturnDom) {
        handleReturnDom.needImage = true;
        handleReturnDom.isShow = true;
        handleReturnDom.eventType = this.isAuth ? 'rebackOrderToClient' : 'rebackToClientForUnauth';
      }
    },

    /**
     * 指派IQC：打开弹框-选中-调用
     */
    handleAssignIQC(){
      console.log('指派IQC');
      this.assignType = 'IQC';
      this.$refs.handleAssign.batchPeopleDialog = true;
    },
    
    /**
     * 由我译单
     */
    handleTranslateByMe(btnItem){
      const askMsg = this.$t('order.operate.askTranslateByMe');
      const title = this.$t('order.operate.translateByMe');
      this.$confirm(askMsg,title,{
        confirmButtonText: this.$t('common.btn.confirm'),
        cancelButtonText: this.$t('common.btn.cancel'),
      }).then(() => {
        const sendParam = {
          requestParam: [this.orderCode],
          btnItem
        }
        this.sendRequest(sendParam, updateIQC);
      }).catch(() => {});
    },
    
    /**
     * 打开指派设计师弹框
     */
    handleAssignDesigner(){
      this.assignType = 'DESIGNER';
      if (this.isUnion) {
        // 如果是联合修复订单
        this.selectSelection = [{
          orderNo: this.orderNumber,
          orderCode: this.orderCode,
          toothDesign: this.orderInfo.toothDesign,
          orgCode: this.orderInfo.orgCode,
        }]
        this.showUnionAssign = true
      } else {
        this.$refs.handleAssign.batchPeopleDialog = true;
      }
    },

    /**
     * 批量指派设计师成功
     */
    batchSuccessUnion() {
      this.showUnionAssign = false
      this.init()
    },

    /**
     * 设计师发起退单请求
     */
    handleRebackByDesigner() {
      console.log('设计师退回订单');
      this.returnDialogInfo = {
        title: 'order.detail.title.returnReason',
        maxlength: 200,
        placeholder: 'order.detail.tips.pleaseInputReasonToIQC',
      };
      const handleReturnDom = this.$refs.handleReturn;
      if(handleReturnDom) {
        handleReturnDom.needImage = true;
        handleReturnDom.isShow = true;
        handleReturnDom.eventType = 'rebackOrderToIQC';
      }
    },
    
    /**
     * 指派OQC
     */
    handleAssignOQC(){
      this.assignType = 'OQC';
      this.$refs.handleAssign.batchPeopleDialog = true;
    },

    /**
     * 设计师提交设计结果
     * 4.3.28 提交时包含[未知]，不允许提交
     */
    async handleFinish(btnItem){
      console.log('完成设计');

      // 设计软件，软件版本，组内QC转必填 20240327
      if(!this.baseInfo.designSoftware){
        this.$hgOperateWarning(this.$t('order.detail.tips.selectsoftware'));
        return
      }
      if(!this.baseInfo.softwareVersion){
        this.$hgOperateWarning(this.$t('order.detail.tips.selectversion'));
        return
      }
      // 本版本暂未开放
      // if(!this.baseInfo.groupQC){
      //   this.$hgOperateWarning('请选择组内QC');
      //   return
      // }

      if(this.isUnknown) {
        this.$hgOperateWarning(this.$t('order.detail.tips.pleaseSelectBeforeSubmit'));
        return;
      }

      if (this.orderInfo.implantSystem && this.hasImplantType) {
        const { isUnion, designerTypes } = this.orderInfo
        if (isUnion) {
          const myDesignItem = designerTypes.find(item => item.designUser === this.userCode)
          const myDesignItemTypes = JSON.parse(myDesignItem.designTypes)
          const myDesignItemTypeCodes = myDesignItemTypes.map(item => item.code)
          if (myDesignItemTypeCodes.some(item => item === 23601)) {
            if (!this.orderInfo.implantsScheme || this.orderInfo.implantsScheme.currentState === -1) {
              this.$hgOperateWarning(this.$t('order.detail.implant.designFinishLimit'));
              return;
            }
          }
        } else {
          if (!this.orderInfo.implantsScheme || this.orderInfo.implantsScheme.currentState === -1) {
            this.$hgOperateWarning(this.$t('order.detail.implant.designFinishLimit'));
            return;
          }
        }
      }

      if(this.isOrthodontic) {
        console.log('正畸订单-设计完成');
        if(!this.verifyOrthoInfo()) { return; }
        
        const sendParam = {
          requestParam:  [this.orderCode],
          btnItem
        }
        this.sendRequest(sendParam, submitDesignForOrtho);

      }else {
        console.log(this.baseInfo, 55555)
        if(!this.verifyUploadList()) { return; }
        let requestFileList = [];
        const compList = this.uploadCompList;
        compList.forEach(item => {
          const fileList = item.fileList;
          fileList.forEach(file => {
            const { fileName, filePath, fileSize, fileType } = file;
            requestFileList.push({ fileName, filePath, fileSize, fileType });
          });
        });

        const emptyList = requestFileList.filter(item => !item.filePath);
        console.log('emptyList', emptyList);
        if(emptyList.length > 0) {
          const nameList = emptyList.map(item => item.fileName);
          this.$hgOperateWarning(this.$t('file.tips.isUploading', [nameList.join('，')]));
          return;
        }

        const fn = () => {
          let param = {
            orderCode: this.orderCode, 
            remarkContent: this.otherInfo.designRemarkContent || '', 
            designSoftware: this.baseInfo.designSoftware,
            softwareVersion: this.baseInfo.softwareVersion,
            groupQC: this.baseInfo.groupQC,
            orderFiles: requestFileList, 
            backfillList: this.fillValueList.join(),
          };
          
          const orthoDom = this.$refs.orthoInput;
          if(this.isOrthodontic && orthoDom ) {
            param.upperNum = orthoDom.upperValue;
            param.lowerNum = orthoDom.lowerValue;
            param.programmeRemark = this.orthodonticInfo.designRemarkContent || '';
          }
  
          console.log(param);
          const sendParam = {
            requestParam: [param],
            btnItem,
          };
  
          // 如果设计师有选组内QC，才发送请求：先保存QC，再保存设计结果，不用改动太多
          //(20240327跟设计软件一起再detail传值，不在单独处理)
          let successFun = () => {};
          // const { groupQC, groupQCName } = this.baseInfo;
          // if(groupQC || (!groupQC && groupQCName)) {
          //   const paramCode = groupQC || 0;
          //   successFun = async () => { await setQC(this.orderCode, paramCode); };
          // }
          this.sendRequest(sendParam,submitDesign);
        }

        if (this.orderInfo.implantSystem && this.hasImplantType) {
          if (this.orderInfo.isUnion) {
            const myDesignItem = this.orderInfo.designerTypes.find(item => item.designUser === this.userCode)
            const myDesignItemCodeList = JSON.parse(myDesignItem.designTypes)
            if (myDesignItemCodeList.some(item => [23601].includes(item.code)) && this.orderInfo.implantsScheme.currentState === 0) {
              this.$confirm( this.$t('order.detail.implant.designFinishImplantTips'), this.$t('component.tip.title'), {
                confirmButtonText: this.$t('common.btn.confirm'),
                cancelButtonText: this.$t('common.btn.cancel'),
                type: 'warning',
                closeOnClickModal: false,
                closeOnPressEscape: false,
                distinguishCancelAndClose: true,
              }).then(() => {
                fn()
              }).catch((action) => {
                if(action === 'confirm') {
                  fn()
                }
              });
            } else {
              fn()
            }
          } else {
            if (this.orderInfo.implantsScheme.currentState === 0) {
              this.$confirm( this.$t('order.detail.implant.designFinishImplantTips'), this.$t('component.tip.title'), {
                confirmButtonText: this.$t('common.btn.confirm'),
                cancelButtonText: this.$t('common.btn.cancel'),
                type: 'warning',
                closeOnClickModal: false,
                closeOnPressEscape: false,
                distinguishCancelAndClose: true,
              }).then(() => {
                fn()
              }).catch((action) => {
                if(action === 'confirm') {
                  fn()
                }
              });
            } else {
              fn()
            }
          }
        } else {
          // let param = {
          //   orderCode: this.orderCode, 
          //   remarkContent: this.otherInfo.designRemarkContent || '', 
          //   designSoftware: this.baseInfo.designSoftware,
          //   softwareVersion: this.baseInfo.softwareVersion,
          //   groupQC: this.baseInfo.groupQC,
          //   orderFiles: requestFileList, 
          //   backfillList: this.fillValueList.join(),
          // };
          
          // const orthoDom = this.$refs.orthoInput;
          // if(this.isOrthodontic && orthoDom ) {
          //   param.upperNum = orthoDom.upperValue;
          //   param.lowerNum = orthoDom.lowerValue;
          //   param.programmeRemark = this.orthodonticInfo.designRemarkContent || '';
          // }
  
          // console.log(param);
          // const sendParam = {
          //   requestParam: [param],
          //   btnItem,
          // };
  
          // // 如果设计师有选组内QC，才发送请求：先保存QC，再保存设计结果，不用改动太多
          // //(20240327跟设计软件一起再detail传值，不在单独处理)
          // let successFun = () => {};
          // // const { groupQC, groupQCName } = this.baseInfo;
          // // if(groupQC || (!groupQC && groupQCName)) {
          // //   const paramCode = groupQC || 0;
          // //   successFun = async () => { await setQC(this.orderCode, paramCode); };
          // // }
          // this.sendRequest(sendParam,submitDesign);
          fn()
        }

        
      }
    },

    /**
     * 提交前文件校验
     */
    verifyUploadList() {
      
      // 必传文件
      this.uploadCompList = this.$refs.uploadBox.getCompList();
      const necessaryList = this.uploadCompList.filter(comp => comp.necessary);
      let verifyResult = true;
      
      for(let i = 0; i < necessaryList.length; i++) {
        const data = necessaryList[i];
        if(data.fileList.length === 0) {
          verifyResult = false;
          this.$hgOperateWarning(this.$t('file.tips.upload', [this.$t(this.checkName[data.fileType])]));
          break;
        }
      }

      let fileUploadingList = [];
      this.uploadCompList.forEach(item => {
        const fileList = item.fileList || [];
        fileList.forEach(file => {
          if(!file.filePath || file.progress !== 100) {
            fileUploadingList.push(file.fileName);
          }
        });
      });
      if(fileUploadingList.length > 0) {
        this.$hgOperateWarning(this.$t('file.tips.isUploading', [fileUploadingList.join('，')]));
        verifyResult = false;
      }
      
      // 支架-下拉框 4.3.61版本已经去掉该下拉回填
      // const fillCompList = this.$refs.fillComp;
      // if(fillCompList && fillCompList.length > 0 && verifyResult) {
      //   let fillValueList = fillCompList.reduce((list, curDom) => {
      //     list = list.concat(curDom.selectCompList);
      //     return list;
      //   },[]);

      //   fillValueList = fillValueList.map(item => item.value).filter(item => item);
      //   if(fillValueList.length === 0) {
      //     verifyResult = false;
      //     this.$hgOperateFail(this.$t('order.detail.tips.pleaseSelectRPDType'));
      //   }else {
      //     this.fillValueList = fillValueList;
      //   }
      // }

      return verifyResult;

    },

    /**
     * 对正畸订单的方案数据进行校验
     * 1.校验上传组件；2.校验必传文件；3.校验步数；
     */
    verifyOrthoInfo(fromBtnType) {
      
      // 点击[设计完成]
      if(fromBtnType !== 'createProgramBtn') { // 这里还需要校验是否生成了方案 & 是否查看了方案
        const { isCheck, programGenerateModel, programCode } = this.orthodonticInfo;
        if(!programCode) {
          this.$hgOperateWarning(this.$t('order.ortho.tips.submitNoProgram'));
          return false;
        }
        if(programCode && programGenerateModel === 0) {
          this.$hgOperateWarning(this.$t('order.ortho.tips.creatingProgram'));
          return false;
        }
        if(programCode && programGenerateModel === 2) {
          this.$hgOperateWarning(this.$t('order.ortho.tips.programFailTips'));
          return false;
        }
        if(programGenerateModel === 1 && !isCheck) {
          this.$hgOperateWarning(this.$t('order.ortho.tips.viewBeforeFinish'));
          return false;
        }

      }else { // 点击[生成3D预览方案]
        const compList = this.$refs.uploadBox.getCompList();
      
        //正畸交互
        if(compList.length === 0) {
          this.$hgOperateWarning(this.$t('order.ortho.tips.pleaseAddScheme'));
          return false;
        }

        this.uploadCompList = compList;
        const necessaryList = compList.filter(comp => comp.necessary);

        const noUploadItem = necessaryList.find(item => item.fileList.length === 0); // 必传但没传
        if(noUploadItem) {
          this.$hgOperateWarning(this.$t('file.tips.upload', [this.$t(this.checkName[noUploadItem.fileType])]));
          return false;
        }

        let fileUploadingList = [];
        compList.forEach(item => {
          const fileList = item.fileList || [];
          fileList.forEach(file => {
            if(!file.filePath) {
              fileUploadingList.push(file.fileName);
            }
          });
        });
        if(fileUploadingList.length > 0) {
          this.$hgOperateWarning(this.$t('file.tips.isUploading', [fileUploadingList.join('，')]));
          return false;
        }

        const orthoDom = this.$refs.orthoInput;
        if(orthoDom.showError) {
          this.$alert(this.$t('order.ortho.tips.pleaseInputStep'), this.$t('common.systemTips') ,{
            closeOnClickModal: false,
            closeOnPressEscape: false,
          });
          return false;
        }

      }

      return true;
    },

    /**
     * 设计师撤回设计结果
     */
    handleRevokeByDesigner(btnItem) {
      const sendParam = {
        requestParam: [this.orderCode],
        btnItem,
      };
      this.sendRequest(sendParam, revokeDesignFromOQC);
    },

    /**
     * 由我检查
     */
    handleExamineByMe(btnItem) {
      const askMsg = this.$t('order.operate.askExamineByMe');
      const title = this.$t('order.operate.examineByMe');
      this.$confirm(askMsg,title,{
        confirmButtonText: this.$t('common.btn.confirm'),
        cancelButtonText: this.$t('common.btn.cancel'),
      }).then(() => {
        const sendParam = {
          requestParam: [this.orderCode],
          btnItem,
        };
        this.sendRequest(sendParam,updateOQC);
      });
    },

    /**
     * OQC 审核通过 
     */
    handlePass(btnItem){

      if(!this.verifyUploadList()) { return; }
      
      let param = {
        orderCode: this.orderCode, 
        remarkContent: this.otherInfo.designRemarkContent || '' , 
        orderFiles: [], 
        backfillList: this.fillValueList.join(),
      };

      let requestFileList = [];
      const compList = this.$refs.uploadBox.getCompList();

      if(this.isOrthodontic) { // 正畸
        const orthImageItem = compList.find(comp => comp.compType === 4);
        if(orthImageItem) {
          const fileList = orthImageItem.fileList || [];
          fileList.forEach(file => {
            const { fileName, filePath, fileSize, fileType } = file;
            requestFileList.push({ fileName, filePath, fileSize, fileType });
          }); 
        }

        const orthoDom = this.$refs.orthoInput;
        if(orthoDom ) {
          param.upperNum = orthoDom.upperValue;
          param.lowerNum = orthoDom.lowerValue;
          param.programmeRemark = this.orthodonticInfo.designRemarkContent || '';
        }

      }else { // 普通订单
        // 设计截图
        const imageItem = compList.find(comp => comp.fileType === FILE_TYPES.SCREENSHOT);
        if(imageItem) {
          const fileList = imageItem.fileList || [];
          fileList.forEach(file => {
            const { fileName, filePath, fileSize, fileType } = file;
            requestFileList.push({ fileName, filePath, fileSize, fileType });
          });
        }
      }
      
      param.orderFiles = requestFileList;

      const sendParam = {
        requestParam: [param],
        btnItem,
      };

      this.sendRequest(sendParam, approveDesign);
    },

    /**
     * OQC：审核设计结果不通过
     */
    handleNotPass() {
      console.log('审核不通过');
      this.returnDialogInfo = {
        title: 'order.detail.title.notPassReason',
        maxlength: 200,
        placeholder: 'order.detail.tips.pleaseInputNotPassReason',
      };
      const handleReturnDom = this.$refs.handleReturn;
      if(handleReturnDom) {
        handleReturnDom.isShow = true;
        handleReturnDom.eventType = 'repulseDesign';
      }
    },

    /**
     * 从客户处撤回设计结果
     */
    handleRevokeFromClient(btnItem) {
      this.$confirm(this.$t('order.operate.askConfirmRevoke'), this.$t('common.systemTips'), {
        confirmButtonText: this.$t('common.btn.cancel'),
        cancelButtonText: this.$t('common.btn.confirm'),
        distinguishCancelAndClose: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {

      }).catch((action) => {
        if(action === 'cancel') {
          const sendParam = {
            requestParam: [this.orderCode],
            btnItem,
          };
          this.sendRequest(sendParam, revokeDesignFromClient);
        }
      });
    },

    /**
     * 确认[设计师]的返单要求，退回订单给客户
     */
    handleConfirmReback(btnItem) {
      if(!this.event.isTranslateReturnReason) {
        this.$hgOperateWarning(this.$t('order.detail.tips.pleaseHandleDesignerReturn'));
        return;
      }
      const returnImage = this.$refs.handleReturn.uploadPictureList.map(item => item.s3FileId) || [];
      const param = {
        orderCode: this.orderCode, 
        returnImage, 
        returnReason: this.event.translateContent
      };

      const sendParam = {
        requestParam: [param],
        btnItem,
      };

      this.sendRequest(sendParam, rebackOrderToClient);
    },

    /**
     * 关闭弹框-显示[我的补充]
     */
    getConfrmRebackReason(reasonContent) {
      this.event.isTranslateReturnReason = true;
      this.event.translateContent = reasonContent;
    },

    /**
     * 看完[设计师]返单要求，点击[编辑]，输入返单原因+图片，这部分才是给客户看到的[order.detail.title.returnReason]
     */
    handleEditReturnReason() {
      this.returnDialogInfo = {
        title: 'order.detail.title.returnReason',
        maxlength: 200,
        placeholder: 'order.detail.tips.pleaseInputReasonToClient',
      };
      const handleReturnDom = this.$refs.handleReturn;
      if(handleReturnDom) {
        handleReturnDom.isShow = true;
        handleReturnDom.needImage = true;
        handleReturnDom.eventType = 'confirmReback';
      }
    },
    
    /**
     * 打开弹框，输入拒绝设计师返单要求的理由
     */
    handleContinueDesign() {
      console.log('继续设计');
      const { isReturnFromClient } = this.event;
      this.returnDialogInfo = {
        title: isReturnFromClient ? 'order.detail.title.supplement' : 'order.detail.title.rejectReturn',
        maxlength: 200,
        placeholder: isReturnFromClient ? 'order.detail.tips.pleaseSupplyClientReturen' : 'order.detail.tips.pleaseRejectDesigner',
      };
      const handleReturnDom = this.$refs.handleReturn;
      if(handleReturnDom) {
        handleReturnDom.isShow = true;
        handleReturnDom.eventType = 'continueToDesign';
      }
    },
    
    /**
     * 撤回[已退回]订单
     */
    handleCancelReback(btnItem) {
      const sendParam = {
        requestParam: [this.orderCode],
        btnItem,
      };
      this.sendRequest(sendParam, cancelRebackOrder);
    },

    /**
     * [完成编辑]
     * @param {object} btnItem 
     */
    async handleUpdateOrder(btnItem) {
      this.$refs.toothInfo.getRequestParamBeforeSubmit();

      const toothDesign = this.requestParam.toothDesign || [];
      console.log('handleUpdateOrder', this.requestParam);
      if(toothDesign.length === 0) {
        this.$hgOperateFail(this.$t('order.detail.tips.pleaseSelectDesignType'));
        return;
      }

      const hasUnknown = toothDesign.some(item => item.code === UNKNOWN_CODE);
      if(hasUnknown) {
        this.$hgOperateFail(this.$t('order.detail.tips.dontSubmitUnknow'));
        return;
      }

      btnItem.isLoading = true; // OK
      // 提交之前需要先把截图上传
      // if(this.toothImageBase64) {
      //   const imagePath = await this.submitToothImg(this.toothImageBase64);
      //   this.requestParam.toothImage = imagePath;
      //   if(!imagePath) {
      //     this.$hgOperateFail(this.$t('file.tips.uploadToothImgFail'));
      //   }
      // }
      const keyList = Object.keys(this.imageBas64Map);
      for(let i = 0; i < keyList.length; i++ ) {
        const key = keyList[i];
        const base64 = this.imageBas64Map[key];
        if(base64) {
          const imagePath = await this.submitToothImg(base64);
          this.requestParam.imageMap[key] = imagePath;
          if(!imagePath) {
            this.$hgOperateFail(this.$t('file.tips.uploadToothImgFail'));
          }
        }
      }

      //Attention 这里的请求参数在updateOrder
      const sendParam = {
        requestParam: [this.requestParam],
        btnItem,
      };
      this.sendRequest(
        sendParam,
        submitEdit,
        () => { this.isEdit = false; this.clearEditParam(); },
      );
    },

    // 取消编辑也重新获取一次订单详情，因为可能被修改了
    handleCancelUpdate() {
      this.isEdit = false;
      this.init();
      this.clearEditParam();
    },

    /**
     * 触发[待设计]->[设计中]
     */
    handleStartToDesign(btnItem) {
      const sendParam = {
        requestParam: [this.orderCode],
        btnItem,
      };
      const errorFun = () => {
        this.init()
      }
      this.sendRequest(sendParam, startToDesign, null, errorFun);
    },
    
    /**
     *  同意免单
     */
    handleApproveForFree(btnItem) {
      const sendParam = {
        requestParam: [this.orderCode],
        btnItem,
      };
      this.sendRequest(sendParam, approveForFree);
    },

    /**
     * 不同意免单
     */
    handleDisapproveForFree(btnItem) {
      // 打开弹窗
      this.returnDialogInfo = {
        title: this.$t('order.detail.rejectPassReason'),
        maxlength: 200,
        placeholder: this.$t('order.detail.rejectPassReasonTips'),
      };
      const handleReturnDom = this.$refs.handleReturn;
      if(handleReturnDom) {
        handleReturnDom.needImage = true;
        handleReturnDom.isShow = true;
        handleReturnDom.eventType = 'disapproveForFree';
      }
      // const sendParam = {
      //   requestParam: [this.orderCode],
      //   btnItem,
      // };
      // this.sendRequest(sendParam, disapproveForFree);
    },
    /**
     * 发送请求：所以响应处理类似的都放在这里
     * @param {function} requestFun 请求体本体
     * @param {function} successFun 成功回调
     * @param {function} errorFun 失败回调
     * @param {function} finalFun fianlly回调
     */
    sendRequest({ requestParam, btnItem },requestFun, successFun, errorFun, finalFun) {
      if(btnItem) {
        btnItem.isLoading = true;
      }
      console.log(requestParam, 555555)
      requestFun(...requestParam).then(async() => {
        this.$hgOperateSuccess();
        successFun && await successFun();
        this.init();
      }).catch(err => {
        const { code, message } = err;
        if(code && message) {
          this.$hgOperateFail(message);
        }
        errorFun && errorFun();
        console.log('err:',requestFun,err);
        
      }).finally(() => {
        if(btnItem && btnItem.isLoading) {
          btnItem.isLoading = false;
        }
        finalFun && finalFun();
      });
    },

    /**
     * 上传提交牙位图base64到S3
     * @param toothImageBase64 牙位图base64
     */
    submitToothImg(toothImageBase64) {
      return new Promise((resolve) => {
        let file = base64toFile(toothImageBase64);
        let uploadFile = new UploadNormalFile({ file, expirationInDays: 0 });

        uploadFile.onEnd((data) => {
          resolve(data.s3FileId);
        });

        uploadFile.onError(() => {
          resolve(''); // 上传失败
        });
        uploadFile.onStart();
      });
    },

    handelbatchList() {
      this.$hgOperateSuccess();
      this.init();
    },

    // 4.3.36 OQC标记[常规问题]
    markAsNormalQuestion() {
      // 如果是联合修复订单
      this.showUnionNormal = true
      // if (this.isUnion) {
      //   this.showUnionNormal = true
      // } else {
      //   this.$confirm(this.$t('order.detail.tips.confirmQuestion'), this.$t('common.systemTips'), {
      //     confirmButtonText: this.$t('common.btn.confirm'),
      //     cancelButtonText: this.$t('common.btn.cancel'),
      //     distinguishCancelAndClose: true,
      //     closeOnClickModal: false,
      //     closeOnPressEscape: false,
      //   }).then(() => {
      //     setNormalQuestion(this.orderCode).then(res => {
      //       this.markCommonQuestionOrder = true;
      //       this.$hgOperateSuccess();
      //       this.init();
      //     }).catch(err=> {
      //       this.$hgOperateFail();
      //     });
      //   }).catch((action) => {});
      // }
    },

  },
}
