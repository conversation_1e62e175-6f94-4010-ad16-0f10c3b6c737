export default {
  param: {
    yes: 'Yes',
    no: 'No',
    threeShape: '3Shape',
    exoCad: 'EXO CAD',
    riosDesign: 'Rios Design Studio',
    title: {
      introduction: 'Application Introduction',
      programInDetail: 'Optional Design Scheme',
      paramInDetail: 'Optional Parameters',
      setting: 'Parameter Configuration',
      parameter: 'Design Parameter',
      program: 'Design Scheme',
    },
    version: 'Design Software',
    software: {
      shape: '3Shape',
      exoCad: 'EXO CAD',
      riosDesign: 'Rios Design Studio',
    },
    tip: {
      leave: 'Current modification has not been saved, confirm to leave and abandon the modifications?',
    },
    noParameter: 'None',
    noProgram: 'None',
    info: {
      anatomicalCoping: "It's a framework which is cutback from a full anatomical crown. It can reserve the space for the porcelain accurately, or keep part of the full anatomy and stack porcelain on the other.",
      anatomicBridge: 'An integrated anatomical bridge designed by software.',
      anatomicBridgeWax: 'An integrated anatomical bridge with waxup scan provided by the customer.',
      bitePlate: "It's a removable appliance, usually composed of rigid resin, used to guide the mandibular arch to a special occlusal relationship and then guide the condyle to a specified position",
      bracketRemoval: 'Remove the brackets and add base for intraoral scanning data, so that it could be stored and thermoformed after printing.',
      coping: 'It refers to making a framework with a primary anatomy on the prepared teeth, and then fabricate the colored restorations with final teeth anatomy by stacking porcelain after 3D printing or milling.',
      copyDentureFull: "It's a replica of the old denture, which is export 3D model based on the solid or scanning data. The number of teeth is more than or equal to seven units.",
      copyDenturePartial: "It's a replica of the old denture, which is export 3D model based on the solid or scanning data. The number of teeth is less than seven units.",
      crown: 'A full anatomical crown.',
      fixedTray: "A instrument carrying the impression material to be placed in the mouth to take the impression, which is made according to the special situation and needs of the patient's mouth.",
      fullDentureCarded: 'The removable denture base which is designed according to the library of the carded teeth (export along with the model of carded teeth), and the teeth should be more than or equal to seven units.',
      fullDenturePrinted: 'The removable denture base and the anatomy of the denture teeth which are designed according to the intraoral conditions, and the teeth should be more than or equal to seven units.',
      fullRPD: 'Used for partial teeth missing, a restoration that the patient can remove and wear by themselves.',
      implantModel: 'Generate a digital model after the design of abutment and screw-retained crown, and then generate the hole and gum to place the analog. Used for the implant case which is intraoral scanned.',
      implantTray: "A instrument carrying the impression material to be placed in the mouth to take the impression, which is made according to the special situation and needs of the patient's mouth.",
      inlay: 'A single local restoration used when natural teeth are partially damaged.',
      nesbit: 'Used for one to three teeth missing, a restoration that the patient can remove and wear by themselves.',
      nightGuide: 'Protect the teeth from excessive wear caused by bruxism. Many patients have the habit of grinding teeth when sleeping. It will not only cause severe wear of the teeth, but also cause discomfort of the joints. The teeth can be protected and the joints can be protected by wearing the nightguard.',
      partialDeneturePrinted: 'The removable denture base and the anatomy of the denture teeth which are designed according to the intraoral conditions, and the teeth should be less than seven units.',
      removableModel: 'A digital dental model used for removable restorations.',
      removableTray: "A instrument carrying the impression material to be placed in the mouth to take the impression, which is made according to the special situation and needs of the patient's mouth.",
      sectionedModel: 'A digital model with  removable die.',
      segmentation: "Add base and perform teeth segmentation for intraoral scanning data, then the customer can import the design file and start to design directly, saving the customer's pre-processing time of the clear aligner.",
      splint: 'The occlusion and the balance of the two joints can be changed by the splint. Especially for patients with TMD, the disordered bite relationship can be correct at the early stage, so that the joint can achieve a balanced state and fix the TMD.',
      studyModel: 'Add base for intraoral scanning data, so that it could be stored and thermoformed after printing.',
      temporary: 'A transitional temporary crown used before the final repair is completed.',
      tryInFull: 'A try-in denture used before the final removable denture, used for rapid wearing and then modify the original plan according to the wearing effect. The teeth should be more than or equal to seven units.',
      tryInPartial: 'A try-in denture used before the final removable denture, used for rapid wearing and then modify the original plan according to the wearing effect. The teeth should be less than seven units.',
      unsectionedModel: 'A digital model with fixed die.',
      veneer: 'A restoration that is directly or indirectly overlaid with materials to restore the normal shape and improve the color of a tooth with preserved vital pulp, minimal or no grinding of teeth.',
      postAndCore: '当患者的牙齿缺损太大或是只剩少部分或者只剩根部时，需先制作用于固位的桩核后再进行上部牙冠修复',
      telescope: '是指用套筒冠作为固位体的可摘局部义齿。套筒冠义齿有内冠、外冠组成。内冠粘固在基牙上，外冠与可摘局部义齿连成整体',
      removableCrown: 'A full anatomical crown.',
      removableAntomicalCoping: '通过全解剖形态回切而成的基底冠，可以更精确预留上瓷空间，也可以保留部分全解剖形态，其他部分上瓷',
    },
  }
}