/**
 * 接口环境配置
 */

let server = {};
let staticResourcesUrl = process.env.VUE_APP_STATIC_URL; //静态资源
let orthToolUrl = `${window.location.origin}/lab_ortho_web/#/orthodonticPreview?side=tenant`; // 正畸预览工具地址

// 基础接口地址
function initBaseServer() {
  let baseUrl = process.env.VUE_APP_BASE_URL;

  if (!['local'].includes(process.env.NODE_ENV)) {
    server = {
      baseUrl,
      systemCommonServer: '/design-basic-service/systemCommon/v1', // 两端共用的服务
      orderServer: '/design-basic-service/tenant/order/v1', // 订单基础服务
      multiUploadServer: '/design-file-service/multipartUpload/v1', // 说s3 分片上传
      heypointServer: '/design-settlement-service', // 黑豆服务
      userServer: '/design-basic-service/tenant/staff-duty/v1', //人员管理
      orgServer: '/user-basic/org/v1', //组织架构
      normalUploadServer: '/design-file-service/platformS3/v1', // s3 不分片上传
      normalUploadServerV3: '/design-file-service/platformS3/v3', // 文件服务v3版本
      databoardServer: '/design-basic-service/tenant/statistics/v1', //数据看板
      multiUploadServerV4: '/design-file-service/multipartUpload/v4', // 新s3 分片上传
      orderFoler: '/design-basic-service/client/orderFolder/v1', // 文件夹服务
      designSet: '/design-basic-service/tenant/designerSet/v1', // 设计师点数
      designerPoints: '/design-basic-service/tenant/designerPoints/v1',
      designUser: '/user-basic/labUser/v1',
      pointsStatistics: '/design-basic-service/tenant/PointsStatistics/v1',

      usercommon: '/user-basic/noAuth/common/v1'
    };
  } else {
    server = {
      baseUrl,
      systemCommonServer: '/systemCommon',
      orderServer: '',
      multiUploadServer: '/multiS3',
      heypointServer: '/heypoint',
      userServer: '',
      orgServer: '',
      normalUploadServer: '/normalUpload',
      databoardServer: '',
      orderFoler: '/orderFolder',
      designSet: '/designerSet',
      designerPoints: '/designerPoints',
      designUser: '/labUser',
      pointsStatistics: '/pointsStatistics',
      usercommon: '/usercommon'
    };
  }

  if(process.env.NODE_ENV === 'dev') {
    orthToolUrl === 'https://dev-tenant-lab.heygears.com/lab_ortho_web/#/orthodonticPreview?side=tenant';
  } 
}

initBaseServer();

export { server, staticResourcesUrl, orthToolUrl };
