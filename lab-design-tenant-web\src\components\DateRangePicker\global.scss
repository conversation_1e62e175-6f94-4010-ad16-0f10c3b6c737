.date-range-picker {
  background: $hg-background;
  box-shadow: 0px 0px 16px rgba(18, 19, 20, 0.32), 0px 8px 24px rgba(18, 19, 20, 0.2), 0px 12px 32px rgba(18, 19, 20, 0.12);
  border-radius: 4px;
  border: none;

  table {
    color: $hg-label;
    font-size: 14px;
  }

  .el-picker-panel__body-wrapper {
    .el-date-range-picker__time-header {
      border-bottom-color: $hg-border;
      .el-date-range-picker__time-picker-wrap {
        .el-time-panel {
          background-color: $hg-main-black;
          border-color: $hg-border;
          .el-time-panel__content {
            &:after {
              border: none;
            }
            .el-time-spinner__item {
              color: $hg-primary-text;
              &:hover:not(.disabled):not(.active) {
                background: $hg-hover;
              }
              &.active:not(.disabled) {
                color: $hg-label;
              }
            }
          }
          .el-time-panel__footer {
            border-color: $hg-border;
            .el-time-panel__btn {
              color: $hg-label;
            }
          }
        }
      }
    }

    .el-date-range-picker__content.is-left {
      border-right-color: $hg-border;
    }
    .el-date-range-picker__content th {
      color: $hg-label;
      border-bottom: 1px dashed $hg-border;
    }

    .el-date-range-picker__content>.el-date-range-picker__header>div {
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
      color: $hg-label;
    }
    .el-date-range-picker__content>.el-date-range-picker__header>button {
      color: $hg-label;
    }

    .el-date-table td.next-month, 
    .el-date-table td.prev-month {
      color: $hg-secondary-text;
    }

  }

  .el-picker-panel__footer {
    background: $hg-main-black;
    border-top-color: $hg-border;

    .el-button.el-button--default {
      padding: 0;
      margin-left: 24px;
      width: 44px;
      height: 24px;
      line-height: 24px;
      font-size: 12px;
      background: $hg-main-blue;
      border: none;
      color: $hg-primary-text;

      &:hover {
        color: $hg-btn-primary-hover-text;
        background: $hg-btn-primary-hover;
      }

      &:active {
        color: $hg-btn-primary-active-text;
        background: $hg-btn-primary-active;
      }
    }

    .el-button.el-button--default.is-disabled {
      color: $hg-btn-disabled-text;
      background: $hg-btn-disabled;

      &:hover {
        color: $hg-btn-disabled-text;
        background: $hg-btn-disabled;
      }
    }

    .el-button.el-button--text {
      padding: 0;
      width: 44px;
      height: 24px;
      line-height: 24px;
      font-size: 12px;
      background: transparent;
      border: none;
      color: $hg-primary-text;
    }
  }
}

.date-range-picker.el-popper[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: $hg-main-black;
  &::after {
    border-bottom-color: $hg-main-black;
  }
}
.date-range-picker.el-popper[x-placement^='top'] .popper__arrow {
  border-top-color: $hg-main-black;
  &::after {
    border-top-color: $hg-main-black;
  }
}


// 被选中状态下的css
.date-range-picker {
  .el-date-table td.start-date div, 
  .el-date-table td.end-date div {
    background-color: rgba(48, 84, 204, 0.12);
    span {
      background: $hg-main-blue;
      border-radius: 2px;
    }
  }

  .el-date-table td.start-date div {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .el-date-table td.end-date div {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }


  .el-date-table td.in-range div {
    background-color: rgba(48, 84, 204, 0.12);
  }

  .el-input.is-disabled .el-input__inner {
    background-color: transparent;
  }
}