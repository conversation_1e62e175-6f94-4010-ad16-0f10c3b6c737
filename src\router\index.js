import Vue from 'vue'
import Router from 'vue-router'
import store from '@/store'
import { getToken } from '@/assets/script/token.js'
import { getMenus, loadStaticMenu } from './initMenus.js'

Vue.use(Router)

const routers = [
  {
    path: '/',
    redirect: '/main'
  },
  {
    path: '/main',
    name: 'Main',
    redirect: { name: 'Home' },
    meta: {
      reload: true
    },
    component: () => import('@/views/Main/Main.vue'),
    children: [
      {
        path: '/home',
        name: 'Home',
        redirect: { name: 'Personal' },
        meta: {
          reload: true
        },
        component: () => import('@/views/Home/Home.vue'),
        children: [
          {
            path: '/organization',
            name: 'Organization',
            meta: {
              reload: true,
              showNav: 'hide'
            },
            component: () =>
              import(
                /* webpackChunkName: "organization" */ '@/views/Organization/Organization.vue'
              )
          },
          {
            path: '/personal',
            name: 'Personal',
            meta: {
              reload: true,
              showNav: 'hide',
              showUserInfo: 'hide'
            },
            component: () =>
              import(
                /* webpackChunkName: "personal" */ '@/views/Personal/Personal.vue'
              )
          },
          {
            path: '/customer',
            name: 'Customer',
            meta: {
              reload: true,
              showNav: 'hide'
            },
            component: () =>
              import(
                /* webpackChunkName: "customer" */ '@/views/Customer/Customer.vue'
              ),
            children: [
              {
                path: '/setProcess',
                name: 'SetProcess',
                meta: {
                  reload: true,
                  showNav: 'hide'
                },
                component: () =>
                  import(
                    /* webpackChunkName: "mian" */ '@/views/Customer/SetProcess.vue'
                  )
              },
              {
                path: '/customOrg',
                name: 'CustomOrg',
                meta: {
                  reload: true,
                  showNav: 'hide'
                },
                component: () =>
                  import(
                    /* webpackChunkName: "customOrg" */ '@/views/Customer/CustomOrg.vue'
                  )
              }
            ]
          },
          {
            path: '/agentManagement',
            name: 'AgentManagement',
            meta: {
              reload: true,
              showNav: 'hide'
            },
            component: () =>
              import(
                /* webpackChunkName: "agentManagement" */ '@/views/AgentManagement/AgentManagement.vue'
              ),
            children: [
              {
                path: '/agent-edit',
                name: 'AgentEdit',
                component: () => import('@/views/AgentManagement/EditAgent.vue'),
                meta: {
                  title: '编辑代理商',
                  keepAlive: false
                }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/404',
    name: '404',
    meta: {
      reload: true
    },
    component: () => import('@/views/404.vue')
  },
  { path: '*', redirect: '/404', hidden: true }
]

const constantRoutes = routers // defaultRoutesData
window.routesMenu = routers[1].children[0].children // defaultRoutesData[1].children[0].children
const createRouter = () =>
  new Router({
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

// 整合当前用户拥有的所有角色的菜单权限
const activeMenus = getMenus()
const staticMenus = loadStaticMenu(activeMenus, window.routesMenu)
const allMenus = activeMenus.concat(staticMenus)
// 获取当前账户所拥有的所有按钮权限
let permissionBtn = []
const obj = {}
activeMenus.forEach((item) => {
  permissionBtn = permissionBtn.concat(item.operationTypes)
})
permissionBtn = permissionBtn.reduce((arr, cur) => {
  obj[cur.operName] ? '' : (obj[cur.operName] = true && arr.push(cur.operName))
  return arr
}, [])
store.commit('PERMISSION', permissionBtn)

router.beforeEach((to, from, next) => {
  const rights = allMenus.filter((item) => to.path.indexOf(item.path) !== -1)
  // 如果用户已经登录并且拥有该菜单权限则可以跳转，否则跳转到404
  if (rights.length) {
    const token = getToken()

    if (token) {
      next()
    }
  } else {
    next('/404')
  }
})

export default router
