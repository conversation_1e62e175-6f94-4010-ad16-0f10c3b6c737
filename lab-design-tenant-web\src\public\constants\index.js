/**
 * 常量，非配置项
 */
module.exports = {

  // 前端配置的联合修复code，因后端没有配，并且筛选条件是传isUnion的字段
  UNION_TYPE_CODE: 999999,

  /** 特殊字符的正则 */
  SPECIAL_CHAR_REGEX: /[^a-zA-Z0-9！@￥……（）——【】；·‘’、：“|，。《》？_.`~!@#$%^&*()+\-=[\]{};':"|,.<>?/\\ \u4e00-\u9fa5]/g,

  ROLE_CODE: {
    ADMIN: 50018, // 系统管理员
    SYSTEM_OPER: 50019, // 系统运营
    DESIGNER: 50021,  // 设计师
    DESIGN_LEADER: 50031, // 设计师组长
    IQC: 50033, // IQC
    OQC: 50034, // OQC
    DESIGN_OPERATE: 50020, // 设计运营
  },

  /**
   * 牙齿号
   */
  upperNumbers: [ 18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28 ],
  lowerNumbers: [ 38, 37, 36, 35, 34, 33, 32, 31, 41, 42, 43, 44, 45, 46, 47, 48 ],
  upperLeftNumber: [18,17,16,15,14,13,12,11],
  upperRightNumber: [21,22,23,24,25,26,27,28],
  lowerLeftNumber: [48,47,46,45,44,43,42,41],
  lowerRightNumber: [31,32,33,34,35,36,37,38],

  UNKNOWN_CODE: 25001, // 未知类型的code

  OTHER_CODE: 25002, // 其他类型的code

  RPD_PARENT_CODE: 22100,//支架父级code

  DENTURE_PARENT_CODES: [22200, 22300], // 义齿父级code

  ORTHODONTIC_PARENT_CODE: 24100, // 隐形正畸父级code

  /**
   * @description 路由地址
   */
  ROUTE_PATH: {
    ORDER_LIST: '/order',
    ORDER_DETAIL: '/order/detail',
    DATA_BOARD: '/databoard',
    MANAGE_USER: '/configuration/user',
    BILL: '/billManager',
    BILL_DETAIL: '/billManager/detail',
    HEYPOINT_CUSTOMER: '/heyPoint/customer',
    HEYPOINT_CUSTOMER_INFO: '/heyPoint/customer/detail',
    HEYPOINT_SETTING: '/heyPoint/setting',
    HEYPOINT_LOG: '/heyPoint/log',
    ORDER_UNVERIFIED: '/order/unverified',
    POINTS_ALLOCATION: '/configuration/pointsAllocation',
    DESIGNER_POINTS: '/designerPoints',
    MY_DESIGN_POINTS: '/myDesignPoints',
    CONFIGURATION: '/configuration'
  },
  /**
   * @description 路由名称
   */
  ROUTE_NAME: {
    ORDER_LIST: 'OrderList',
    ORDER_DETAIL: 'OrderDetail',
    DATA_BOARD: 'DataBoard',
    MANAGE_USER: 'ManageUser',
    BILL: 'BillManager',
    BILL_DETAIL: 'BillDetails',
    HEYPOINT_CUSTOMER: 'Customer',
    HEYPOINT_CUSTOMER_INFO: 'CustomerInfo',
    HEYPOINT_SETTING: 'BalanceSetting',
    HEYPOINT_LOG: 'OperateLobg',
    ORDER_UNVERIFIED: 'OrderUnauthorized',
    DESIGNER_POINTS: 'DesignerPoints',
    MY_DESIGN_POINTS: 'MyDesignPoints'
  },

  /**
   * @description 订单状态,用来判断当前订单的状态
   */
  ORDER_TYPES: {
    PENDING_TRANSLATE: 1, //待翻译
    PENDING_ACCEPT: 2, //待指派
    PENDING_DESIGN: 3, //待设计
    DESIGNING: 4, //设计中
    PENDING_RETURN: 5, //待退回
    PENDING_REVIEW: 6, //待审核
    PENDING_CONFIRM: 7, //待确认
    COMPLETED: 8, //已完成
    RETURNED: 9, // 已退回
    REQUEST_FREE: 10, // 申请免单/待免单
    APPLY_FREE: 11, // 已免单
  },

  /**
   * @description
   * 订单返回的文件类型：1.原始文件 2.电子单 3.设计截图 4.设计模型文件 5.设计文件  
   *    6.设计视频 7.计划书 8.其他 9.打包的结果文件 10.客户备注图片 21.正畸解析出来的图片 
   *    22.面部图片 23.口内照片 24.X光照片 13.正畸-矫治计划书
   * https://wiki.heygears.com/pages/viewpage.action?pageId=37652098 
   */
  FILE_TYPES: {
    ORIGIN_FILE: 1,
    ELECTRON_FILE: 2,
    SCREENSHOT: 3,
    DESIGN_MODEL: 4,
    DESIGN_FILE: 5,
    DESIGN_VIDEO: 6,
    PROSPECTUS: 7,
    OTHER_FILE: 8,
    RESULT_ZIP: 9,
    REMARK_IMAGE: 10,
    ORTH_ORIGIN_PIC: 21, 
    FACE_PIC: 22,
    INTRAORAL_PIC: 23,
    CT_PIC: 24,
    CORRECT_FILE: 13, 
    CBCT_FILE: 27,
    IMPLANT_FILE: 28,
    DRILL_FILE: 29,
  },

  /**
   * @description 过程记录 https://wiki.heygears.com/pages/viewpage.action?pageId=37651771
   */
  PROCESS_TYPE: {
    CREATED_ORDER: 1, // 建单
    CLIENT_UPDATE_ORDER: 2, // 客户修订
    CLINET_UPDATE_REMARK: 3,  // 客户修订备注
    CLIENT_CONFIRM_ORDER: 4,  // 确认订单
    CLIENT_CANCEL_ORDER: 5, //客户取消订单
    REVOKE_ORDER_BY_CLIENT: 6, // 客户撤回订单
    DOWNLOAD_RESULT: 7, // 客户下载结果文件
    RETURN_BY_CLIENT: 8, // 客户返单
    ASSIGN_IQC: 10,   // 批量指派IQC
    ASSIGN_DESIGNER: 11,  // 批量指派设计师
    ASSIGN_OQC: 12,       // 批量指派OQC
    BATCH_TRANSLATE_ORDER: 13,  // 批量译单
    REBACK_TO_CLIENT: 14,   // IQC返单给客户
    TRANSLATED_ORDER: 15,   // IQC译单通过
    CONTINUE_DESIGN: 16,  // 继续设计
    START_TO_DESIGN: 17,  // 开始设计
    FINISH_DESIGN: 18,    // 完成设计
    REBACK_TO_IQC: 19,     // 设计师退单给IQC
    REVOKE_BY_DESIGNER: 20, // 设计师撤回设计
    CONFIRM_PASS: 21, // 审核通过
    NOT_PASS: 22,     // 审核不通过
    REVOKE_FROM_CLIENT: 23, // 从客户处撤回设计
    AUTO_ASSIGN_IQC: 24,  // 自动派单给IQC
    REVOKE_RETURNORDER: 25, // 撤回[已退回]订单
    EDIT_ORDER_BY_DESIGN: 26, // 设计这边编辑订单 
    CLINET_ASK_FOR_FREE: 27, // 客户申请免单
    APPROVE_FOR_FREE: 28, // 同意免单
    DISAPPROVE_FOR_FREE: 29, // 拒绝免单
    EXAMINE_BY_MYSELF_OQC: 30, // 由我检查
    IS_NORMAL_QUESTION: 31, //存在常规问题
    FILL_DESIGN: 33, // 自动过滤设计品类
    SUBMIT_IMPLANT: 36, // 设计师提交了种植方案
    CONFIRM_IMPLANT: 37, // 客户确认了种植方案
    RETURN_IMPLANT: 38, // 客户返单种植方案
  },

  /**
   * @description 改变订单状态的操作type
   */
  UPDATE_ORDER_STATUS_TYPE: {
    CREATED_ORDER: 1,
    CLIENT_UPDATE_ORDER: 2, // 客户修订
    CLIENT_CONFIRM_ORDER: 4, // 确认订单
    CLIENT_CANCEL_ORDER: 5, //客户取消订单
    REVOKE_ORDER_BY_CLIENT: 6, // 客户撤回订单
    RETURN_BY_CLIENT: 8, // 客户返单
    ASSIGN_IQC: 10,
    ASSIGN_DESIGNER: 11,
    ASSIGN_OQC: 12,
    BATCH_TRANSLATE_ORDER: 13,
    REBACK_TO_CLIENT: 14,
    TRANSLATED_ORDER: 15,
    CONTINUE_DESIGN: 16,
    START_TO_DESIGN: 17,
    FINISH_DESIGN: 18,
    REBACK_TO_IQC: 19,
    REVOKE_BY_DESIGNER: 20,
    CONFIRM_PASS: 21,
    NOT_PASS: 22,
    REVOKE_FROM_CLIENT: 23,
    // AUTO_ASSIGN_IQC: 24,
    REVOKE_RETURNORDER: 25,
    CLINET_ASK_FOR_FREE: 27, // 客户申请免单
    DISAPPROVE_FOR_FREE: 29
  },

  /**
   * @description 上传组件类型：1.普通上传卡片 2.图片上传组件 3.备注输入框 4.患者照片 5.矫治步数
   */
  UPLOAD_COMP_TYPE: {
    NORMAL_CARD: 1,
    IMAGE_BOX: 2,
    DESIGN_REMARK: 3,
    PATIENT_BOX: 4,
    CORRECT_STEP: 5,
    ORTHONTIC_DESIGN_PARAM: 6,
  },
  
  /**
   * @description FDI符号
   */
  FDINumbers: [18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28, 38, 37, 36, 35, 34, 33, 32, 31, 41, 42, 43, 44, 45, 46, 47, 48],
  /**
   * @description 通用符号
   */
  generalNumbers: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32],
}