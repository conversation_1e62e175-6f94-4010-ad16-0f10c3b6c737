import {
  _matrix4,
  composeMatrix4,
  getPremultiplyMatrix4,
  getRotateRroundApplyMatrix,
} from '../matrix4'

// 右乘
export function multiplyMatrix4Object(object, matrix) {
  const resultMatrix = _matrix4.copy(object.matrix).multiply(matrix)
  const applyMatrix = getPremultiplyMatrix4(object.matrix.clone(), resultMatrix)
  object.applyMatrix4(applyMatrix)
}

export function getObjectPositionByWorldPosition(object, worldPosition, position) {
  position = position ? position.copy(worldPosition) : worldPosition.clone()
  return position.applyMatrix4(_matrix4.copy(object.parent.matrixWorld).invert())
}

export function setObjectPositionByWorldPosition(object, worldPosition) {
  const position = getObjectPositionByWorldPosition(object, worldPosition)
  object.position.copy(position)
}

export function rotateAroundObject(object, rotationPoint, rotationAxis, rotationAngle) {
  object.updateMatrix()
  const { translate } = composeMatrix4(object.matrix)
  const applyMatrix = getRotateRroundApplyMatrix(translate, rotationPoint, rotationAxis, rotationAngle)
  object.applyMatrix4(applyMatrix)
}
