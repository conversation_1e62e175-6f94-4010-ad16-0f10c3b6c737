import { toothNumbers, cardposition,cardpositionEn, iprcardpositionEn, colorList, orgincolorList, fileCardcenter, iprcardposition, iprfileCardcenter } from './constant';
// 分配颜色
export const assignColors = function (newdata) {
    const lang = window.localStorage.getItem('lang')
    let colorIndex = 0;
    let colorMap = {};
    let data = []
    // 将data的数据重置一遍，防止序号对不上
    toothNumbers.forEach((item) => {
        if(newdata.find((it) => {return it.number === item})){
            data.push(newdata.find((it) => {return it.number === item}))
        }
    })

    for (let i = 0; i < data.length; i++) {
        let steps = data[i].steps;
        data[i].center = fileCardcenter[data[i].number]
        if (steps.length > 0) {
            data[i].endpoint = lang === 'zh' ? cardposition[data[i].number] : cardpositionEn[data[i].number]
            let stepString = steps.join(',');
            let orginString = steps.join(',');
            // eslint-disable-next-line no-prototype-builtins
            if (!colorMap.hasOwnProperty(stepString)) {
                colorMap[stepString] = colorList[colorIndex];
                colorMap[orginString] = orgincolorList[colorIndex];
                colorIndex = (colorIndex + 1) % colorList.length;
            }
            data[i].color = colorMap[stepString];
            data[i].orgincolor = colorMap[orginString];
        }
    }

    return data;
}

// ipr的分配颜色
export const iprAssignColors = function (newdata) {
    const lang = window.localStorage.getItem('lang')
    const colorMap = {};
    let index = 0;
    let data = []
    // 将data的数据重置一遍，防止序号对不上
    toothNumbers.forEach((item) => {
        if(newdata.find((it) => {return it.number === item})){
            data.push(newdata.find((it) => {return it.number === item}))
        }
    })
  
    for (const obj of data) {
        obj.center = iprfileCardcenter[obj.number]
        obj.endpoint = lang == 'zh' ? iprcardposition[obj.number] : iprcardpositionEn[obj.number]
      if (obj.step !== undefined && obj.step !== '') {
        // eslint-disable-next-line no-prototype-builtins
        if (!colorMap.hasOwnProperty(obj.step)) {
          colorMap[obj.step] = colorList[index % colorList.length];
          colorMap['orgin' + obj.step] = orgincolorList[index % colorList.length];
          index++;
        }
        obj.color = colorMap[obj.step];
        obj.orgincolor = colorMap['orgin' + obj.step];
      }
    }
  
    return data;
  }