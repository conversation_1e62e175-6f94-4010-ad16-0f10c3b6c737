/**
 * 获取一些用户信息
 */
import { getStore } from '@/public/utils/storage';
import { parseJson } from '@/public/utils';
import { redirectLogin } from '@/public/utils/token';
import { IS_LOCAL_MODEL, FUNTION_CODE } from '@/public/constants/setting';
import store from '@/store';

/**
 * 用户中心已配
 */
/* const AUTO_LIST = {
  '系统管理员': ['download','translate','reback','edit','assignIQC','assignDesigner','assignOQC','revokeFromClient','confirmReback','continueDesign','cancelReback','batchDownload',' ','batchAssignIQC','batchAssignOQC'],
  'IQC': ['download','translateByMe','translate','reback','edit','assignDesigner','confirmReback','continueDesign','cancelReback','batchDownload','batchAssignDesigner','batchTranslateByMe', 'translateByMe'],
  'OQC': ['download','examineByMe','pass','notPass','revokeFromClient','batchDownload','batchExamineByMe'],
  '设计师': ['download','assignDesigner','rebackByDesigner','finish','revokeByDesigner','batchDownload'],
  '设计师组长': ['download','rebackByDesigner','finish','revokeByDesigner','batchDownload'],
} */

const funtionCodes = [FUNTION_CODE.DESIGN_CENTER, FUNTION_CODE.BILL_CENTER, FUNTION_CODE.HEYPOINT_CENTER];

// 获取用户信息
function initUserInfo() {
  if(IS_LOCAL_MODEL) return;
  
  let userInfo = getStore('userInfo');
  // 没有用户信息就重新登录
  if (!userInfo) {
    redirectLogin();
    return;
  }

  userInfo = parseJson(userInfo);
  if (userInfo.tenant && userInfo.tenant.organizations && userInfo.tenant.organizations.length > 0) {
    //TODO 组织有多个，先取第一个吧
    const org = userInfo.tenant.organizations[0];
    userInfo.orgCode = org.orgCode;

    store.commit('INIT_USER_INFO', userInfo);
  }

  if (userInfo.solutions && userInfo.solutions.length > 0 && userInfo.solutions[0].roles && userInfo.solutions[0].roles.length > 0) {
    const roles = userInfo.solutions[0].roles;
    initRoles(roles);
  }
}

// 初始化角色 提取设计中心的角色
function initRoles(roles) {
  const tenantRoles = roles.filter((item) => {
    const designCenterList = item.functions.filter(fun => funtionCodes.includes(fun.functionTypeCode));
    if(designCenterList.length > 0) {
      return true;
    }
    return false;
  });
  const myRoles = tenantRoles.map((item) => {
    const { roleCode, roleName } = item;
    return { roleCode, roleName };
  });
  store.commit('INIT_USER_ROLE', myRoles);
  
  // 获取菜单列表
  getMenusByRoles(tenantRoles);
}

// 从userInfo中提取 角色-菜单
function getMenusByRoles(roles = []) {
  if(!roles && roles.length === 0) return;

  let menus = [];
  let aiToolsFunctions = [];
  roles.forEach(role => {               // 遍历 角色
    const funtions = role.functions || [];
    funtions.forEach(fun => {           // 遍历角色下的function
      //console.log('fun: ', fun);
      if(funtionCodes.includes(fun.functionTypeCode)) {       
        menus = menus.concat(fun.menus);
      }
      // ai工具的functions
      if (fun.site === 4 || fun.functionTypeCode === 33) {
        aiToolsFunctions = aiToolsFunctions.concat(fun);
      }
    });
  });

  let menusCode = [];
  const resultMenus = menus.filter(item => {
    const menuCode = item.menuCode;
    if(!menusCode.includes(menuCode)) {
      menusCode.push(menuCode);
      return true;
    } else {
      return false;
    }
  });

  let functionsPath = []
  const resultAiFunction = aiToolsFunctions.filter((item) => {
    const funPath = item.path;
    if (!functionsPath.includes(funPath)) {
      functionsPath.push(funPath);
      return true;
    } else {
      return false;
    }
  });
  //console.log('resultMenus: ', resultMenus);
  store.commit('INIT_MENU_LIST', resultMenus);
  store.commit('INIT_AI_FUNCTIONS_LIST', resultAiFunction);
  initOperateAuth(menus);

  // 设计类型是一个基础，因此数据要尽早加载
  store.dispatch('initDesignTypeTree');
  store.dispatch('initDesignTypeSkuTree');
}

function initOperateAuth(menus = []){
  let authList = [];
  menus.forEach(menu => {
    // const list = AUTO_LIST[role.roleName] || [];
    const list = menu.operationTypes.map(operate => operate.operName);
    authList = authList.concat(list);
  });

  const repeatSet = new Set(authList);
  authList = Array.from(repeatSet);
  console.log(authList, 1111)
  store.commit('INIT_AUTH_LIST', authList);
}




function loadLocalMenu() {
  const menuChildren = [{
    path: '/order',
    name: 'OrderList',
    component: () => import('../views/order/list/index.vue'),
  },{
    path: '/order/detail',
    name: 'OrderDetail',
    component: () => import('../views/order/detail/index.vue'),
  },{
    path: '/user',
    name: 'ManageUser',
    component: () => import('../views/configuration/user/index.vue'),
  },{
    path: '/temp',
    name: 'temp',
    component: () => import('../views/temp/index.vue'),
  },
  {
    path: '/heyPoint',
    name: 'HeyPoint',
    redirect: '/heyPoint/customer'
  },{
    path: '/heyPoint/customer',
    name: 'Customer',
    component: () => import('../views/heyPoint/customer/index.vue'),
  },
  {
    path: '/heyPoint/customer/detail',
    name: 'CustomerInfo',
    component: () => import('../views/heyPoint/customer/detail.vue'),
  },
  {
    path: '/heyPoint/setting',
    name: 'BalanceSetting',
    component: () => import('../views/heyPoint/setting/index.vue'),
  },
  {
    path: '/heyPoint/log',
    name: 'OperateLobg',
    component: () => import('../views/heyPoint/log/index.vue'),
  },
  {
    path: '/billManager',
    name: 'BillManager',
    component: () => import('../views/billManager/index.vue'),
  },
  {
    path: '/billManager/detail',
    name: 'BillDetails',
    component: () => import('../views/billManager/detail.vue'),
  },
  {
    path: '/configuration/pointsAllocation',
    name: 'PointsAllocation',
    component: () => import('../views/configuration/pointsAllocation/index.vue'),
  },
  {
    path: '/designerPoints',
    name: 'DesignerPoints',
    component: () => import('../views/designerPoints/index.vue'),
  },
  {
    path: '/myDesignPoints',
    name: 'MyDesignPoints',
    component: () => import('../views/myDesignPoints/index.vue'),
  },]
  return menuChildren;
}


export { 
  initUserInfo, 
  loadLocalMenu 
};
