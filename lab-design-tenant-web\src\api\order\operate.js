import request from '../axios';
import { server } from '@/config';

const axios = request.axios;

/**
 * 入检通过
 * @param {string} orderCode  订单编号
 * @param {string} translateContent IQC译单内容
 */
export const submitTranslation = (orderCode,translateContent) => {
  const data = {
    operateType: 1,
    orderCode,
    translate: translateContent
  };
  return axios.post(`${server.orderServer}/operate`, data);
};

/**
 * 提交编辑
 * @param {*} param0 
 */
export const submitEdit = ({
  orderCode,
  orderStatus,
  designStatus,
  designCategoryCode,
  paramList,
  toothDesign,
  imageMap,
  implantSystem
}) => {
  const data = {
    orderCode,
    orderStatus,
    designStatus,
    designCategoryCode,
    designPara: paramList,
    toothDesign,
    toothImage: JSON.stringify(imageMap),
    implantSystem
  };
  return axios.post(`${server.orderServer}/editOrder`, data);
};

/**
 * 由我译单-> 更新IQC
 * @param {string} orderCode 订单编号
 */
export const updateIQC = (orderCode) => {
  const data = {
    operateType: 1, // 由我译单的type 1.由我译单 2.由我检查 
    orderCodes: [orderCode]
  };
  return axios.post(`${server.orderServer}/batchOperate`, data);
};

/**
 * 返单给IQC
 * @param {string} orderCode 订单编号
 * @param {array} returnImage 返单图片[s3FileId]
 * @param {string} returnReason 返单原因
 */
export const rebackOrderToIQC = ({orderCode, returnImage, returnReason}) => {
  const data = {
    operateType: 5,
    orderCode,
    returnReason,
    returnFiles:returnImage,
  };
  return axios.post(`${server.orderServer}/operate`, data);
};

/**
 * 由我审核
 * @param {*} orderCodeList 
 */
export const updateOQC = (orderCode) => {
  return axios.post(`${server.orderServer}/reviewedByMe`, [orderCode]);
}

/**
 *  撤回设计（从OQC）
 * @param {string} orderCode 订单编号
 */
export const revokeDesignFromOQC = (orderCode) => {
  return axios.post(`${server.orderServer}/operate`, { operateType: 10, orderCode });
};

/**
 * 提交设计
 * @param {string} orderCode  订单编号
 * @param {string} remarkContent  设计师填写备注内容
 * @param {array} orderFiles  设计师上传的设计文件{fileName,filePath,fileSize,fileTime,fileType}
 * @param {array} backfillList 回填列表
 */
export const submitDesign = ({orderCode, remarkContent, orderFiles, backfillList,designSoftware, softwareVersion, groupQC}) => {
  const data = {
    operateType: 4,
    orderCode,
    orderFiles,
    designRemark: remarkContent,
    rpdTypes: backfillList,
    designSoftware,
    softwareVersion,
    groupQc: groupQC
  };
  return axios.post(`${server.orderServer}/operate`, data);
};

/**
 * 审核通过
 * @param {string} orderCode  订单编号
 * @param {string} remarkContent  设计师填写备注内容
 * @param {array} orderFiles  OQC修改的图片
 * @param {array} backfillList 回填列表
 */
export const approveDesign = ({orderCode, remarkContent, orderFiles, backfillList,upperNum, lowerNum,programmeRemark}) => {
  const data = {
    operateType: 6,
    orderCode,
    orderFiles,
    designRemark: remarkContent,
    rpdTypes: backfillList,
    upperNum,
    lowerNum,
    programmeRemark,
  };
  return axios.post(`${server.orderServer}/operate`, data);
};

/**
 * 审核不通过
 * @param {string} orderCode 订单编号
 * @param {string} noPassReason OQC不通过理由
 */
export const repulseDesign = ({orderCode, returnReason}) => {
  return axios.post(`${server.orderServer}/operate`, { operateType: 7, orderCode, returnReason });
};

// 从客户处撤回设计
export const revokeDesignFromClient = (orderCode) => {
  return axios.post(`${server.orderServer}/operate`, { operateType: 9, orderCode });
};

//  确认返单
export const rebackOrderToClient = ({orderCode, returnImage, returnReason}) => {
  return axios.post(`${server.orderServer}/operate`, { operateType: 2, orderCode, returnReason, returnFiles: returnImage });
};

// 继续设计
export const continueToDesign = ({orderCode, returnReason}) => {
  return axios.post(`${server.orderServer}/operate`, { operateType: 3, orderCode, returnReason });
};

// 撤回[已退回]订单
export const cancelRebackOrder = (orderCode) => {
  return axios.post(`${server.orderServer}/operate`, { operateType: 11, orderCode });
};

// 下载文件后触发开始设计
export const startToDesign = (orderCode) => {
  return axios.post(`${server.orderServer}/operate`, { operateType: 8, orderCode });
};

/**
 * 触发生成正畸3D预览方案
 */
export const handleToCreateProgram = ({orderCode, orderFiles, upperNum, lowerNum, programmeRemark, iprInfo, additionInfo, designSoftware, softwareVersion, groupQC}) => {
  const data = {
    operateType: 4,
    orderCode,
    orderFiles,
    upperNum,
    lowerNum,
    programmeRemark,
    iprTiming: JSON.stringify(iprInfo),
    attachmentTiming: JSON.stringify(additionInfo),
    designSoftware,
    softwareVersion,
    groupQc: groupQC
  };
  return axios.post(`${server.orderServer}/operate`, data);
};

// 已完成订单再次触发生成正畸预览方案
export const saveProgramme = ({orderCode, orderFiles, upperNum, lowerNum, programmeRemark, iprInfo, additionInfo, programmeCode}) => {
  const data = {
    orderCode,
    orderFiles,
    upperNum,
    lowerNum,
    programmeRemark,
    iprTiming: JSON.stringify(iprInfo),
    attachmentTiming: JSON.stringify(additionInfo),
    programmeCode
  };
  return axios.post(`${server.orderServer}/saveProgramme`, data);
};

// 已完成订单再次提交正畸方案
export const reviseProgramme = ({orderCode, programmeCode}) => {
  const data = {
    orderCode,
    programmeCode
  };
  return axios.post(`${server.orderServer}/reviseProgramme`, data);
};

/**
 * 正畸订单[设计完成]
 */
export const submitDesignForOrtho = (orderCode) => {
  return axios.get(`${server.orderServer}/designComplete`, { params: { orderCode } });
};

/**
 * 更新正畸方案是否被查看
 * @param {*} orderCode 
 */
export const updateSchemeCheckStatus = (programmeCode) => {
  return axios.get(`${server.orderServer}/checkProgramme`, { params: { programmeCode } })
};

/**
 * 同意免单
 */
export const approveForFree = (orderCode) => {
  return axios.post(`${server.orderServer}/freeCheck`, { orderCode, isPass: true, imgS3FileIds: [], reason: '' });
};

/**
 * 不同意免单
 */
export const disapproveForFree = (data) => {
  return axios.post(`${server.orderServer}/freeCheck`, data);
};

/**
 * 未认证订单返单
 * @param {*} param0 
 */
export const rebackToClientForUnauth = ({ orderCode, returnReason, returnImage }) => {
  const data = {
    orderCode,
    returnReason,
    returnFiles:returnImage,
  };
  return axios.post(`${server.orderServer}/unauthenticatedReturn`, data);
};

// [常规问题]
export const setNormalQuestion = (orderCode) => {
  return axios.get(`${server.orderServer}/saveCommonQuestions`, { params: { orderCode } });
};

// [联合修复-保存常规问题]
export const saveUnionCommonQuestions = (data) => {
  return axios.post(`${server.orderServer}/saveUnionCommonQuestions`, data);
};

// 设置QC
export const setQC = (orderCode, userCode) => {
  const data = {
    orderCode,
    qcCode: userCode
  };
  return axios.post(`${server.orderServer}/saveGroupQc`, data);
};

// 已完成订单再次上传结果文件
// data: {orderCode, orderFiles:[]}
export const saveNewFile = (data) => {
  return axios.post(`${server.orderServer}/saveNewFile`, data);
};

/**
 * 导板订单-提交种植方案
 */
export const submitImplantsScheme = (data) => {
  return axios.post(`${server.orderServer}/submitImplantsScheme`, data);
};