<template>
  <div class="order-detail-page correct-steps">
    <div class="title">
      {{ $t('order.ortho.title.correctStep') }}
    </div>
    
    <div class="content">
      <div class="upper-box">
        <div class="content-title">
          <span>{{ $t('order.ortho.upper')  }}</span>
        </div>
        <div class="content-input">
          <el-input 
            type="number"
            @wheel.native.prevent="stopScrollFun($event)"
            v-model="upperValue" 
            :min="0" 
            :max="1000" 
            @change="(value) => handleChange(value,'upperValue')"></el-input>
          <p v-show="showError" class="error-msg">{{ $t('order.ortho.tips.correctStepNotNull') }}</p>
        </div>
      </div>

      <div class="lower-box">
        <div class="content-title">
          <span>{{ $t('order.ortho.lower') }}</span>
        </div>
        <div class="content-input">
          <el-input 
            type="number"
            @wheel.native.prevent="stopScrollFun($event)"
            v-model="lowerValue" 
            :min="0" 
            :max="1000" 
            @change="(value) => handleChange(value,'lowerValue')" ></el-input>
          <p v-show="showError" class="error-msg">{{ $t('order.ortho.tips.correctStepNotNull') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    lowerInputValue: {
      type: Number,
      default: 0,
    },
    upperInputValue: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      upperValue: 0,
      lowerValue: 0,
    }
  },
  computed: {
    showError() {
      return Number(this.upperValue) === 0 && Number(this.lowerValue) === 0;
    },
  },
  watch: {
    lowerInputValue() {
      this.lowerValue = this.lowerInputValue;
    },
    upperInputValue() {
      this.upperValue = this.upperInputValue;
    }
  },
  mounted() {
    this.initValue();
  },
  methods: {
    initValue(){
      this.upperValue = this.upperInputValue;
      this.lowerValue = this.lowerInputValue;
    },
    handleChange(value, type) {
      if(value !== '') {
        const numberValue = Math.round(Number(value));
        if(numberValue < 0) {
          this[type] = 0;
        }else if(numberValue > 1000) {
          this[type] = 1000;
        }else {
          this[type] = numberValue;
        }
      }
    },
    stopScrollFun(event) {
      event = event || window.event;
      if(event.preventDefault) {
          // Firefox
          event.preventDefault();
          event.stopPropagation();
      } else {
          // IE
          event.cancelBubble=true;
          event.returnValue = false;
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.correct-steps {

  .title {
    margin-bottom: 12px;
    color: #F7F8FA;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
  }

  .content {
    display: flex;
    width: 100%;
  }

  .content>div {
    display: flex;
    width: auto;
    margin-right: 72px;
  }

  .content-title {
    display: flex;
    align-items: center;
    padding-right: 24px;
    height: 40px;
  }

  .content-input {
    width: 320px;
    .error-msg {
      padding-top: 8px;
      color: #DC5050;
    }
  }
}
</style>

<style lang="scss">
.order-detail-page.correct-steps {
  // 隐藏右侧的箭头
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type='number'] {
    -moz-appearance: textfield;
    box-shadow: none; // 兼容firefox[火狐下会出现红色阴影]
    color: #E4E8F7;
    border-radius: 2px;
    border: 1px solid #38393D;
  }
}
</style>
