// 建议弹窗的每一项
%popUpSelectItem {
    height: 40px;
    color: $hg-secondary-fontcolor;
    font-size: 14px;
    font-weight: normal;
}
// Input
.el-input {
    .el-input__inner {
        color: $hg-primary-fontcolor;
        background-color: transparent;
        border: 1px solid $hg-border-color;
        // 输入状态
        &:focus {
            border-color: $borderFocusColor;
        }
    }

    // 禁用状态
    &.is-disabled {
        input.el-input__inner {
            background-color: $disabledhg-background-color;
            border: 1px solid $hg-border-color;
        }
    }

    .el-input__prefix{
        .el-input__icon{
            color: $hg-primary-fontcolor;
        }
    }

    // 修改input占位符
    ::-webkit-input-placeholder {
        color: $hg-secondary-fontcolor !important;
        font-weight: 400;
    }

    // 各个尺寸
    &.el-input--medium {
        height: 40px;
    }
    &.el-input--small {
        height: 32px;
    }
    &.el-input--mini {
        height: 28px;
    }

    // 复合输入框插槽部分
    &.el-input-group {
        // 前置插槽
        .el-input-group__prepend {
        }
        // 后置插槽
        .el-input-group__append {
        }
        .el-input-group__prepend,
        .el-input-group__append {
            border-color: $hg-border-color;
            background: $hg-main-black;
        }
    }
}
// el-input的文本域
.el-textarea {
    .el-textarea__inner {
        color: $hg-primary-fontcolor;
        background: $hg-main-black;
    }
}
textarea::-webkit-input-placeholder{
    color: $hg-secondary-fontcolor !important;
}

// Select
.el-select {
    .el-input {
        &.is-disabled {
            input.el-input__inner:hover {
                border-color: $hg-border-color;
            }
        }
        &.is-focus {
            input.el-input__inner {
                border-color: $borderFocusColor;
            }
        }
        .el-input__inner {
            &:focus {
                border-color: $borderFocusColor;
            }
        }
    }
    // 多选标签样式
    .el-select__tags {
        span .el-tag.el-tag--info.el-tag--small.el-tag--light {
            height: 32px;
            border-color: $hg-main-black;
            background: $hg-main-black;
            .el-select__tags-text {
                color: $hg-primary-fontcolor;
                font-size: 14px;
                line-height: 30px;
            }
            .el-tag__close.el-icon-close {
                color: $hg-secondary-fontcolor;
                background: transparent;
            }
        }
    }
}

// el-input的建议弹窗
.el-autocomplete-suggestion.el-popper[x-placement^="bottom"],
.el-autocomplete-suggestion.el-popper[x-placement^="top"] {
    .el-scrollbar {
        .el-autocomplete-suggestion__wrap.el-scrollbar__wrap {
            .el-scrollbar__view.el-autocomplete-suggestion__list {
                li {
                    @extend %popUpSelectItem;
                }
                li:hover {
                    background-color: $hg-hover-bg-color;
                }
            }
        }
    }
}
// el-select的建议弹窗
.el-select-dropdown.el-popper[x-placement^="bottom"],
.el-select-dropdown.el-popper[x-placement^="top"] {
    .el-scrollbar {
        .el-select-dropdown__wrap.el-scrollbar__wrap {
            .el-scrollbar__view.el-select-dropdown__list {
                li.el-select-dropdown__item {
                    @extend %popUpSelectItem;
                }
                li.el-select-dropdown__item.selected {
                    color: $hg-primary-fontcolor;
                }
                // 禁用选项样式
                li.el-select-dropdown__item.is-disabled {
                }
                li.el-select-dropdown__item.selected,
                li.el-select-dropdown__item:hover,
                li.el-select-dropdown__item.hover {
                    background-color: $hg-hover-bg-color;
                }
            }
        }
    }
}
.el-autocomplete-suggestion.el-popper[x-placement^="bottom"],
.el-autocomplete-suggestion.el-popper[x-placement^="top"],
.el-select-dropdown.el-popper[x-placement^="bottom"],
.el-select-dropdown.el-popper[x-placement^="top"] {
    border: 0;
    background: $hg-main-black;
    .popper__arrow {
        border-top-color: $hg-main-black;
        border-bottom-color: $hg-main-black;
        &::after {
            border-top-color: $hg-main-black;
            border-bottom-color: $hg-main-black;
        }
    }
}

// Checkout
.el-checkbox {
    .el-checkbox__input {
        .el-checkbox__inner {
            width: 14px;
            height: 14px;
            background: $hg-main-black;
            border: 1px solid $hg-border-color;
        }
        // 有焦点
        &.is-focus {
            .el-checkbox__inner {
                border-color: $hg-border-color;
            }
        }
        &:hover {
            .el-checkbox__inner {
                border: 1px solid #3054cc;
            }
        }
    }
    .el-checkbox__label {
        color: $hg-primary-fontcolor;
        font-size: 14px;
        line-height: 16px;
    }
    // 选中
    &.is-checked {
        // 选中
        .el-checkbox__input.is-checked {
            .el-checkbox__inner {
                background-color: $hg-main-blue;
                border-color: $hg-main-blue;
                &::after {
                    height: 8px;
                    border-color: #000;
                    left: 4px;
                }
            }
            // 有焦点 + 选中
            &.is-focus {
                .el-checkbox__inner {
                    border-color: $hg-main-blue;
                }
            }
            &:hover {
                .el-checkbox__inner {
                    border: 1px solid $hg-main-blue;
                }
            }
        }

        // 选中后内容
        span.el-checkbox__label {
            color: $hg-primary-fontcolor;
            font-size: 14px;
            line-height: 14px;
        }
    }
    // 禁用状态
    &.is-disabled {
        span.el-checkbox__input {
            .el-checkbox__inner {
                width: 14px;
                height: 14px;
                background: $disabledhg-background-color;
                border: 1px solid $hg-border-color;
            }
            &:hover {
                .el-checkbox__inner {
                    border: 1px solid $hg-border-color;
                }
            }
        }
    }
    // 选中 + 禁用
    &.is-checked.is-disabled {
        .el-checkbox__input.is-checked.is-disabled {
            .el-checkbox__inner {
                border-color: $hg-border-color;
                background: $disabledhg-background-color;
                // 图标
                &::after {
                    height: 8px;
                    border-color: $iconDisabledColor;
                    left: 4px;
                }
                &:hover {
                    .el-checkbox__inner {
                        border: 1px solid $hg-border-color;
                    }
                }
            }
        }
    }
}

// radio
.el-radio-group {
    height: 40px;
    padding: 0 12px;
    background: $hg-main-black;
    line-height: 40px;
    .el-radio-button {
        margin-left: 2px;
        .el-radio-button__inner {
            height: 32px;
            border-radius: 2px;
            border-color: transparent;
            padding: 0 20px;
            background: transparent;
            color: $hg-secondary-fontcolor;
            font-size: 16px;
            line-height: 32px;
            font-weight: 500;
        }
        &.is-active {
            .el-radio-button__inner{
                color: $hg-primary-fontcolor;
                background-color: $hg-main-blue;
            }
        }
        &:first-child{
            margin-left: 0;
        }
    }
}
