<template>
  <hg-card class="order-detail-tooth-info-box">
    <!-- tab切换 -->
    <div class="header">
      <div class="tab-ul">
        <div :class="['tab-li', tab.designCode === currentCode && 'is-active']" v-for="(tab, tIndex) in tabList" :key="tIndex" @click="changeTab(tab.designCode)">
          <span>{{ $getI18nText({ zh: tab.cnName, en: tab.enName }) }}</span>
        </div>
      </div>
      <div class="btn-edit-tooth" @click="openToothDrawerDailog" v-if="isEdit">
        <hg-icon icon-name="icon-edit-lab" font-size="24px"></hg-icon>
        <span>{{ $t('common.btn.editProgram') }}</span>
      </div>
    </div>
    <!-- 牙位图 -->
    <div class="data-info">
      <order-title langName="toothInfo"></order-title>
      <tooth-info 
        ref="toothInfo" 
        :orgCode='orgCode' 
        :imagePath="curData.toothImage" 
        :toothInfo="curData.toothInfo"
        :myToothInfo="curData.myToothInfo"
        :otherToothInfo="curData.otherToothInfo"
        :hasMulDesigner="curData.hasMulDesigner"
        :isResponsibleDesigner="curData.isResponsibleDesigner"
        :isEdit="isEdit"
        :toothImageBase64="curData.toothImageBase64"
        :downLoadImagePath="toothImageMap[currentCode]"
        @updateImagePath="setImagePath"></tooth-info>
    </div>

    <!-- 参数方案 -->
    <parameters class="param-info" :isEdit="isEdit" :parameterContent="curData.parameterContent" :otherDesignerType="otherDesignerType"  @openEditParam="openEditParam"></parameters>

    <tooth-drawer 
      v-if="canEditOrder"
      ref="ToothDrawer"
      :propToothDesign="sourceToothDesign" 
      :propCategoryCode="propCategoryCode"
      :orgCode="orgCode"
      :deliveryCode="deliveryCode"
      :designerTypes="designerTypes"
      :implantSystemObj="implantSystemObj"
      @closeToothDrawer="closeToothDrawer">
    </tooth-drawer>

    <edit-param v-permission="['edit']" 
      ref="handleEditParam" 
      :tabList="tabList" 
      :sourceData="sourcEditParamList" 
      @handleClose="handleCloseParamDialog"
      @onChangeCategory="onChangeCategory"></edit-param>

  </hg-card>
</template>

<script>
import { mapGetters } from 'vuex';
import OrderTitle from '../OrderTitle';
import Parameters from './Parameters';
import ToothDrawer from '@/components/ToothDrawer/Drawer';
import EditParam from '@/components/Params/EditParam';
import ToothInfo from './Info';
import { getToothInfo } from '@/public/utils/order';
import { getParamByCode } from '@/api/common';
import { copy, isEmptyResult } from '@/public/utils';
import { ROLE_CODE } from '@/public/constants';

export default {
  name: 'ToothInfoBox',
  components: { OrderTitle, ToothInfo, Parameters, ToothDrawer, EditParam },
  props: {
    orgCode: [Number, String],
    categoryList: {
      type: Array,
      default() {
        return [];
      }
    },
    createdUser: [Number, String],
    deliveryCode: Number,

    isEdit: Boolean,
    sourceMap: {
      type: Object,
      require: true,
    },
    /* orderInfo: {
      type: Object,
      default() {
        return {
          deliveryCode: 24,
          categoryList: [],
          createdUser: 0,
        }
      }
    }, */
    canEditOrder: Boolean,
    sourceToothDesign: {
      type: Array,
      default() {
        return []
      }
    },
    designerTypes: Array,
    implantSystem: String,
  },
  data() {
    return {
      currentCode: 0,
      currentParamSelectCode: 0,
      lastDesignCodeChar: '',

      toothInfoMap: {},

      paramCodeMap: {}, // 用于决定是否请求接口获取参数列表

      toothImageMap: {}, // 保存上一次的图片，否则每次切换都下载
    }
  },
  computed: {
    ...mapGetters(['designTypeTree', 'oneDesignList', 'userCode', 'roles']),
    isDesigner() {
      if (
        this.roles.some(item => item.roleCode === ROLE_CODE.DESIGNER)
      ) {
        return true;
      }
      return false
    },
    otherDesignerType() {
      if (this.designerTypes) {
        const codeList = this.designerTypes.filter(item => item.designUser !== this.userCode).map(item => {
          const designTypes = JSON.parse(item.designTypes)
          if (designTypes) {
            return designTypes.map(item => item.code)
          }
          return null
        });
        return codeList.flat()
      }
      return []
    },
    propCategoryCode() {
      const code = this.categoryList[0];
      if(code) {
        return Number(code);
      }
      return 0;
    },
    tabList() {
      const list = Object.keys(this.toothInfoMap).map(key => {
        const node = this.designTypeTree.find(item => item.designCode === Number(key)) || {};
        return node;
      });
      return list;
    },
    curData() {
      const curData = this.toothInfoMap[this.currentCode] || {}
      if (!isEmptyResult(curData) && curData.parameterContent && curData.hasMulDesigner && curData.isResponsibleDesigner) {
        const parameterArray = JSON.parse(curData.parameterContent)
        const filterParam = parameterArray.filter(item => !this.otherDesignerType.includes(item.designCode))
        curData.parameterContent = JSON.stringify(filterParam)
      }
      return curData;
    },
    // 用于给参数的对象，避免和牙位图切换时共用一个code，增加渲染
    paramData() {
      const dom = this.$refs.handleEditParam;
      if(dom && dom.isShow) {
        return this.toothInfoMap[this.currentParamSelectCode] || {};
      }else {
        return this.curData;
      }
    },
    // 减少编辑参数组件的改造，传参不变，由父组件处理
    sourcEditParamList() {
      const dom = this.$refs.handleEditParam;
      if(dom && dom.isShow) {
        const data = this.toothInfoMap[this.currentParamSelectCode] || {};
        return data.editParamList || [];
      }else {
        return this.curData.editParamList || [];
      }
    },

    implantSystemObj({ implantSystem }) {
      if (implantSystem) {
        return JSON.parse(implantSystem)
      }
      return {}
    }
  },
  watch: {
    'tabList.length': {
      immediate: true,
      handler(count) {
        if(count > 0 && this.currentCode === 0 && this.tabList[0]) {
          this.currentCode = this.tabList[0].designCode;
          this.currentParamSelectCode = this.currentCode;
        }
      }
    },
    sourceMap(map) {
      this.toothInfoMap = copy(map);
      Object.keys(map).forEach(key => {
        this.paramCodeMap[key] = map[key]?.toothDesign?.map(item => item.code).join() || '';
      });
    }
  },
  mounted() {
    this.toothInfoMap = copy(this.sourceMap);
    Object.keys(this.sourceMap).forEach(key => {
      this.paramCodeMap[key] = this.sourceMap[key]?.toothDesign?.map(item => item.code).join() || '';
    });
  },
  /* 保持组件责任独立，这里负责数据处理，父节点[完成编辑]才需要数据 */
  methods: {
    changeTab(code) {
      if(code !== this.currentCode) {
        this.currentCode = code;
      }
    },
    openEditParam() {
      this.$refs.handleEditParam.isShow = true;
    },

    /**
     * 以infoMap为准
     * @param {object} infoMap：详情展示牙位图和品类信息
     * @param {array} toothDesign: 兼容旧数据的toothDesign信息
     */
    closeToothDrawer({infoMap, totalToothDesign, imageMap, toothDesignCodes, implantForm = {}}) {
      let newToothInfoMap = {};
      let implantFormObj = {};
      if (implantForm.implantSystem) {
        implantFormObj = implantForm
      } else {
        implantFormObj = this.implantSystem
      }
      const newSelectCodeList = Object.keys(infoMap).filter(key => infoMap[key].toothDesign.length > 0);
      newSelectCodeList.forEach(key => {
        const oldData = this.toothInfoMap[key] || this.sourceMap[key] || {};
        console.log('oldData: ', oldData);
        const newData = infoMap[key];
        console.log('newData: ', newData);
        const { toothDesign: oldToothDesign, toothImage: oldImage, parameterContent: oldParamContent, toothInfo:oldInfo, editParamList: oldParamList, hasMulDesigner, isResponsibleDesigner } = oldData;
        const { toothDesign: newToothDesign, imageBase64 } = newData;
        const toothInfo = getToothInfo(newToothDesign, implantFormObj);
        const designCodeList = newToothDesign.map(item => item.code).filter(item => !this.otherDesignerType.includes(item));
        console.log('designCodeList: ', designCodeList);
        this.requestToGetParam(designCodeList, key);

        const newItem = {
          toothDesign: newToothDesign || oldToothDesign || [],
          toothImage: imageBase64 ? '' : (oldImage || ''),
          parameterContent: oldParamContent || '[]',
          toothInfo: toothInfo || oldInfo || {},
          toothImageBase64: imageBase64,
          editParamList: oldParamList || [],
          hasMulDesigner,
          isResponsibleDesigner,
          myToothInfo: [],
          otherToothInfo: [],
        };

        if (designCodeList.includes(23601)) {
          newItem.implantForm = implantForm
          this.$emit('updateToothInfo', totalToothDesign)
        }

        console.log('newItem: ', newItem);
        if (hasMulDesigner && isResponsibleDesigner) {
          const myDesignList = newItem.toothDesign.filter(item => !this.otherDesignerType.includes(item.code))
          const otherDesignList = newItem.toothDesign.filter(item => this.otherDesignerType.includes(item.code))
          newItem.myToothInfo = getToothInfo(copy(myDesignList), implantFormObj)
          newItem.otherToothInfo = getToothInfo(copy(otherDesignList), implantFormObj)
        }

        newToothInfoMap[key] = newItem;
      });
      this.toothInfoMap = newToothInfoMap;

      this.currentCode = Number(newSelectCodeList[0]); // 关闭后默认回到第一个tab
      this.currentParamSelectCode = this.currentCode; // 参数的也要同步
    },

    openToothDrawerDailog() {
      const dom = this.$refs.ToothDrawer;
      if(dom) {
        dom.isShow = true;
        const newSelectCodeList = Object.keys(this.toothInfoMap).filter(key => this.toothInfoMap[key].toothDesign.length > 0);
        dom.selectCode = Number(newSelectCodeList[0]) || this.designTypeTree[0]?.designCode; // 同步牙位图
        // dom.curToothDesign = copy(dom.toothDesignMap[dom.selectCode].toothDesign);
      }
    },

    /**
     * 获取新参数方案：和下单页不同，编辑时不能切换[设计软件]，因为需要只加载默认值 parameter是一个数组
     * @param {*} designCodeList 
     * @param {*} orderId 
     */
    requestToGetParam(designCodeList = [], categoryCode) {
      console.log('designCodeList: ', designCodeList);
      const updateCodeChar = designCodeList.join();
      console.log('this.paramCodeMap[categoryCode]: ', this.paramCodeMap[categoryCode]);
      if(updateCodeChar === this.paramCodeMap[categoryCode]) { 
        console.log('发现设计类型一样，不请求接口');
        return;
      }
      let requestList = [];
      // const { createdUser, orgCode } = this.orderInfo;
      designCodeList.forEach(designCode => {

        const designItem = this.oneDesignList.find(item => item.designCode === designCode);
        console.log('designItem: ', designItem);
        let requestItem = new Promise(() => {});
        if(designItem && designItem.hasParas) {
          requestItem = new Promise((resolve, reject) => {
            const param = {
              designCode,
              orgCode: this.orgCode,
              userCode: this.createdUser
            };
            getParamByCode(param).then(res => {
              const { data } = res;
              const { parameter, program, software } = data;
              let item = {
                designCode: designCode,
                program,
                parameter: [],
                software,
              }
              
              if(parameter) {
                parameter.some(param => {
                  if(param.software === software) {
                    item.parameter = param.data;
                    return true;
                  }
                  return false;
                });
              }

              resolve({
                code: 200,
                data: item,
              });
            }).catch(() => { 
              reject({ code: 500,data: null, }) 
            });
          });
        }else {
          requestItem = new Promise(resolve => {
            resolve({code: 200, data:[]});
          });
        }
        requestList.push(requestItem);
      });

      Promise.all(requestList).then(resList => {
        console.log('resList: ', resList);
        const paramList = resList.reduce((accList, curItem) => {
          return accList = accList.concat(curItem.data);
        },[]);
        
        this.paramCodeMap[categoryCode] = updateCodeChar;
        this.toothInfoMap[categoryCode].parameterContent = JSON.stringify(paramList);
        this.toothInfoMap[categoryCode].editParamList = paramList;

        // this.getParamForEdit(paramList, categoryCode);
      }).catch(err => {
        console.log(err);
      });

    },

    // 这里是给requestParam的
    getParamForEdit(sourceParam, categoryCode) {
      let paramList = [];
      sourceParam.forEach(param => {
        const { designCode, program, parameter, software } = param;
        const programChar = JSON.stringify(program);
        let paramItem = {};
        paramItem[software] = parameter;
        const paramChar = JSON.stringify(paramItem);

        const data = {
          designCode,
          program: programChar,
          parameter: paramChar,
          software
        };
        paramList.push(data);
        
      });
      return paramList;
    },

    handleCloseParamDialog() {
      this.tabList.forEach(item => {
        const data = this.toothInfoMap[item.designCode];
        const { parameterContent, editParamList } = data;
        const newParamChar = JSON.stringify(editParamList);
        if(parameterContent !== newParamChar) {
          this.toothInfoMap[item.designCode].parameterContent = newParamChar;
        }
      });
      this.currentParamSelectCode = this.currentCode; // 关闭后恢复一下
    },

    // 参数方案-切换一级类
    onChangeCategory(param) {
      console.log('6',param);
      this.currentParamSelectCode = param.firstTypeCode;
    },

    // 提交之前从这里整理数据并传给父节点
    getRequestParamBeforeSubmit() {
      const curCategoryCodes = Object.keys(this.toothInfoMap).filter(key => this.toothInfoMap[key].toothDesign.length > 0);
      console.log('getRequestParamBeforeSubmit-curCategoryCodes', curCategoryCodes)
      
      let requestParam = {
        totalToothDesign: [],
        totalImageMap: {},
        designCategoryCode: curCategoryCodes.join(),
        totalParamList: [],
        totalImageBase64: {},
      };
      curCategoryCodes.forEach(code => {
        const { editParamList, toothDesign, toothImageBase64, toothImage, implantForm = {} } = this.toothInfoMap[code];

        requestParam.totalParamList = requestParam.totalParamList.concat(this.getParamForEdit(editParamList));
        requestParam.totalImageMap[code] = toothImage;
        requestParam.totalToothDesign = requestParam.totalToothDesign.concat(toothDesign);
        requestParam.totalImageBase64[code] = toothImageBase64;

        if (implantForm.implantSystem) {
          requestParam.implantForm = implantForm
        }
      });

      console.log('getRequestParamBeforeSubmit-requestParam', requestParam)


      this.$emit('beforeSubmit', requestParam);
    },

    setImagePath(imagePath) {
      this.toothImageMap[this.currentCode] = imagePath;
    },

  }
}
</script>

<style lang="scss" scoped>
.order-detail-tooth-info-box {
  
}
// 上方tab页和编辑牙位图
.order-detail-tooth-info-box>.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  &>.tab-ul{
    display: flex;
    flex-direction: row;
    padding: 4px;
    border-radius: 4px;
    background: #2F3238;
    width: fit-content;

    .tab-li {
      cursor: pointer;
      padding: 4px 0;
      border-radius: 4px;
      line-height: 24px;

      &>span {
        padding: 0 16px;
        border-right: 1px solid #5C6066;
      }

      &:last-of-type {
        &>span {
          border-right: none;
        }
      }
    }

    .tab-li.is-active {
      background-color: #3760EA;
      &>span {
        border-color: #3760EA;
      }
    }
  }

  .btn-edit-tooth {
    cursor: pointer;
    display: flex;
    align-items: center;
    color: $hg-secondary-primary;

    &>span {
      text-decoration-line: underline;
    }
  }
}

.order-detail-tooth-info-box>.data-info {
  &>.tooth-info {
    margin-top: 24px;
  }
}

.order-detail-tooth-info-box>.param-info {
  margin-top: 32px;
}
</style>