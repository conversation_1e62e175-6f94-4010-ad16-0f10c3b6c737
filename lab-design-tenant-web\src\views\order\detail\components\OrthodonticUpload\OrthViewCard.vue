<template>
  <div class="other-view-card">
    <p>{{ $t('order.ortho.title.program') }}</p>
    <div class="content" @click="openOrthView">
      <span :class="contentItem.className">
        <hg-icon v-if="contentItem.iconName" :icon-name="contentItem.iconName"></hg-icon>
        {{ contentItem.content }}
      </span>
    </div>
  </div>
</template>

<script>
import { orthToolUrl } from '@/config';
import { mapGetters } from 'vuex';
import { updateSchemeCheckStatus } from '@/api/order/operate';

export default {
  name: 'OrthViewCard',
  props: {
    programCode:String,// 方案code
    orderCode: String,//订单code
    isAudit: Boolean, // 待审核
    programStatus: Number, // 方案状态 0默认 1.设计师撤回 2.审核不通过 3.客户返单
    isGenerateModel: Number, // 生成模型，1.生成 2.生成失败
    isHistory:Boolean, //历史订单
    isCheck: Boolean, // 待查看
    isResponsibleDesigner: <PERSON><PERSON><PERSON>, //负责
    isCompleteCanFile: Boolean, //是否是已完成订单设计师组长新生成的方案
    htmlTaskId: [String, Number],
    taskId: [String, Number],
  },
  data() {
    return {

    }
  },
  computed: {
    ...mapGetters(['language', 'aiFunctionsList']),

    contentItem() {
      let content = '';
      let className = '';
      let iconName = '';

      if(this.isBuilding){
        content = this.$t('order.ortho.tips.createPrograming');
        className = 'content_building';
      }else if(this.isFail) {
        content = this.$t('order.ortho.tips.createProgramFail');
        className = 'content_error-tip';
      }else if(!this.programCode) {
        content = this.$t('order.ortho.tips.noProgram');
        className = 'content_error-tip';
      }else if(this.programCode && this.isGenerateModel === 1) {
        content = this.$t('order.ortho.tips.previewProgram');
        iconName = 'icon-play-lab';
      }

      return {
        content,
        className,
        iconName,
      }
    },

    isFail() { // 4.2.6新增一个失败的值
      return this.isGenerateModel === 2;
    },
    isBuilding() { 
      // 4.2.6 只有生成成功之后才能提交方案到下一步
      return this.programCode && this.isGenerateModel === 0;
    },
    canViewOrth() {
      const orthPermission = this.aiFunctionsList.some(item => item.path === 'orthodontic-tool')
      return orthPermission
    }
   },
  methods: {
    openOrthView() {
      if(this.isBuilding || this.isFail || !this.programCode) { return; }
      if (!this.canViewOrth) {
        this.$message.error(this.$t('common.noPermission'))
        return
      }
      if(this.orderCode) {
        if(!this.isHistory && !this.isCheck && (this.isResponsibleDesigner || this.isCompleteCanFile)) { // 未查看
          updateSchemeCheckStatus(this.programCode).then(res => {
            this.$emit('init');
          }).catch(err => {});
        }
        const url = `${orthToolUrl}&orderCode=${this.orderCode}&schemeCode=${this.programCode}&lang=${this.language}&taskId=${this.taskId}&htmlTaskId=${this.htmlTaskId}`;
        window.open(url);

      }
    }
  }
}
</script>

<style lang="scss" scoped>
.other-view-card {
  margin-bottom: 24px;
  width: 100%;

  >p {
    padding-bottom: 12px;
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    color: #F7F8FA;
  }

  .content {
    cursor: pointer;
    height: 80px;
    line-height: 80px;
    border-radius: 4px;
    background: #2D2F33;
    text-align: center;

    .content_error-tip {
      color: $hg-error;
    }

    .content_building {
      color: $hg-remind;
    }
  }
}
</style>