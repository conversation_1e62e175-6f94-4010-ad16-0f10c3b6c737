export function createPromise(cb) {
  return new Promise((resolve, reject) => {
    cb(resolve, reject)
  })
}

export async function getPromiseState(promise) {
  const target = {}
  return await Promise.race([promise, target]).then(
    value => (value === target) ? 'pending' : 'fulfilled',
    () => 'rejected',
  )
}

export async function checkPromiseFulfilled(promise) {
  const state = await getPromiseState(promise)
  return state === 'fulfilled'
}

