.order-detail {
  height: 100%;

  /* Chrome, Safari, Opera */
  @-webkit-keyframes comeOut {
    from { bottom: 0; opacity: 0; }
    to { bottom: 20%; opacity: 1;}
  }

  /* Standard syntax */
  @keyframes comeOut {
    from { bottom: 0; opacity: 0; }
    to { bottom: 20%; opacity: 1;}
  }
  .result-file-tips {
    position: fixed;
    bottom: 20%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1999; // 要比load-mosk低
    padding: 8px 15px;
    background: $hg-main-blue;
    box-shadow: 0 2px 16px 0 rgba(119,119,119,0.08);
    border-radius: 16px;
    font-size: 12px;
    -webkit-animation: comeOut 2s infinite; /* Chrome, Safari, Opera */
    animation: comeOut 2s infinite;
    animation-iteration-count: 1;
    -webkit-animation-iteration-count: 1; /* Safari 和 Chrome */

    &>span:first-of-type{
      color: $hg-white;
    }

    &>span:nth-of-type(2){
      color: $hg-white;
    }

    .pointer {
      cursor: pointer;
    }

  }
}

.order-detail>.order-detail__top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;

  .top-left>span {
    padding-left: 24px;
    color: $hg-grey;
    &>span {
      color: $hg-label;
    }
  }
}

.order-detail>.content {
  height: calc(100% - 34px - 24px);
  .content-top {
    display: flex;
    margin-bottom: 24px;
    line-height: 24px;

    &>span:first-of-type {
      width: 44%;
      font-size: 16px;
      color: $hg-white;
      font-weight: bold;
    }

    .count-down {
      flex: 1;
      font-size: 16px;
    }

    /deep/ .common-question-btn {
      font-size: 14px;
      border-radius: 100px;
      line-height: 20px;
       >span {
        display: flex;
        align-items: center;
      }
    }

    .common-question-btn.is-question {
      border-color: #38393D;
      background-color: #464853;
      color: #FF5A5A;
    }
  }
}
.order-detail .content__scroll {
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100% - 48px);
  /* width: calc(100% + 24px);
  padding-right: 18px; */

  &>div {
    margin-bottom: 24px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }
  
  .fill-box>.fill-component {
    margin-bottom: 24px;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}

// 返单UI
.order-detail>.content {
  .reason-box {
    margin-top: -12px;
    background: #FF5A5A19;

    .reason-box-title {
      display: flex;
      justify-content: space-between;
      padding: 22px 24px;
      line-height: 20px;
      color: $hg-red;
      border-bottom: 1px solid $hg-border;

      .hg-icon {
        padding-right: 12px;
      }

      .reason-box-title-edit {
        cursor: pointer;
      }
    }

    .reason-box-content {
      display: flex;
      flex-direction: column;
      padding: 24px;
      color: #D7D7D9;
      line-height: 20px;
      span {
        word-break:normal; 
        display:block; 
        white-space:pre-wrap;
        word-wrap : break-word ;
        overflow: hidden ;
      }
    }
  }
}

.order-detail>.content {
  .orth-program-title>.orth-title_tip {
    margin-left: 8px;
    padding: 4px 12px;
    color: #EF9B34;
    font-size: 12px;
    border-radius: 12px;
    background: #EF9B3433;
  }
}

// 免单UI
.order-detail>.content {
  .client-need-free-box {
    display: flex;
    flex-direction: row;
    padding: 24px;
    background: #1D1D1F;
    border-radius: 4px;
    line-height: 20px;

    .left {
      padding-right: 24px;
    }

    .reason {
      flex: 1;

      &>p {
        color: #FFB22C;
      }

      .img-ul {
        display: flex;
        flex-wrap: wrap;
        
        .img-li {
          cursor: pointer;
          margin: 16px 16px 0 0;
          width: 80px;
          height: 80px;
          background: #262629;
          border-radius: 2px;

          &>img {
            padding: 4px;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .rejectFree-box{
    background: #FF5A5A19;
    .rejectfree-title{
      color: $hg-red;
    }
    .reason{
      &>p {
        color: $hg-red;
      }
    }
  }
}