<template>
  <div class="customer-details">
    <div class="title">
      <hg-button type="secondary" size="medium" @click="back">
        <hg-icon icon-name="icon-arrow-back-lab"></hg-icon>
        <span>{{ $t('common.btn.back') }}</span>
      </hg-button>
      <div class="text">
        <span class="first">{{ `${lang('rechargeHeypoint')} ` }}</span>
        <span class="first">></span>
        <span class="second">{{ ` ${lang('heypointAccountDetails')}` }}</span>
      </div>
    </div>
    <div class="details-content">
      <div class="customer-info">
        <!-- 按钮栏 -->
        <div class="edit-btn">
          <span>{{lang('basic')}}</span>
          <el-button
            v-if="!isEdit"
            type="primary"
            :disabled="businessDisabled"
            @click="edit"
            >{{ lang('edit') }}</el-button
          >
          <el-button
            v-else
            type="primary"
            class="complete-edit"
            :loading="editLoading"
            :disabled="businessDisabled"
            @click="completeEdit"
          >
            {{ lang('saveEdit') }}
          </el-button>
        </div>
        <div class="info-list">
          <div
            class="info-item"
            v-for="item in infoList"
            :key="item.name"
            :class="{
              'remark-item': item.key === 'remark',
              'discount-item': item.key === 'discount_rate',
            }"
          >
            <span>
              <span class="item-name">{{ lang(item.label) }}：</span>
              <span v-if="item.key == 'settlementType'">
                {{ item.value | settlementType }}
              </span>
              <span v-if="item.key == 'settlementCurrency'">
                {{ item.value | settlementCurrency }}
              </span>
              <span v-if="!item.isEdit && item.key != 'settlementType' && item.key != 'settlementCurrency'" :class="{ 'remark-value': item.key === 'remark' }">
                {{ item.unit ? item.value + item.unit : item.type === 'number' ? capitalize(item.value) : item.value }}
              </span>
              <span v-else-if="item.isEdit && item.key === 'creditScore'">
                <!-- 若客户当前可用信用值小于信用值，重新配置的信用值不允许小于原信用值 -->
                <!-- <el-input v-model.number="item.value" type="number" class="credit-value" :min="currentAvailableCredit"></el-input> -->
                <el-input-number
                  v-model.number="item.value"
                  controls-position="right"
                  size="mini"
                  class="credit-value"
                  :min="currentAvailableCredit"
                ></el-input-number>
              </span>
              <span v-else-if="item.isEdit && item.key === 'nextDiscountRate'">
                <!-- 若客户当前可用信用值小于信用值则不支持编辑折扣率 -->
                <!-- <el-input v-model.number="item.value" type="number" class="discount-value" :min="0" :disabled="discountDisabled"></el-input> -->
                <el-input-number
                  v-model="item.value"
                  controls-position="right"
                  size="mini"
                  class="discount-value"
                  :disabled="discountDisabled"
                  :precision="2"
                  :step="0.1"
                  :min="0"
                  :max="100"
                ></el-input-number>
                % off
              </span>
              <span
                v-else-if="item.isEdit && item.key === 'remark'"
                class="remark"
              >
                <el-input
                  v-model="item.value"
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                >
                </el-input>
              </span>
              <!-- <p v-if="item.key === 'next_discount_rate'" class="discount-tips" :class="{'is-edit': isEdit}">
                {{'注意：折扣率只能在每月1号早上9点后到2号早上6点前更改'}}
              </p> -->
            </span>
          </div>
        </div>
      </div>

      <operateLog ref="operateLog" :isEdit="isEdit">
        <template v-slot:btn>
          <el-button type="primary" class="give-btn" :disabled="businessDisabled" @click="complimentary">{{lang('giveHeypoint')}}</el-button>
          <el-button type="primary" class="recharge-btn" :disabled="businessDisabled" @click="recharge">{{lang('paidCredit')}}</el-button>
        </template>
      </operateLog>
    </div>
    <recharge-dialog :isShow.sync="rechargeVisible" :detailInfo="detailInfo" @refreshInfo="refreshInfo"></recharge-dialog>
    <complimentary-dialog :isShow.sync="complimentaryVisible" :detailInfo="detailInfo" @refreshInfo="refreshInfo"></complimentary-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getLang } from '@/public/utils';
import {
  settlementType,
  settlementCurrency,
  capitalize,
} from '@/filters/heypoint';
import { getCustomerDetails, updateCustomerDetails } from '@/api/heypoint';
import operateLog from './components/operateLog';
import rechargeDialog from './components/rechargeHeyPoint';
import complimentaryDialog from './components/complimentaryHeyPoint';

export default {
  name: 'CustomerDetails',
  components: {
    operateLog,
    rechargeDialog,
    complimentaryDialog
  },
  filters: {
    settlementType,
    settlementCurrency,
    capitalize,
  },
  data() {
    return {
      infoList: [
        {
          name: '客户名',
          label: 'customerName',
          value: null,
          key: 'orgName',
        },
        {
          name: '结算类型',
          label: 'settlementType',
          value: null,
          key: 'settlementType',
        },
        {
          name: '结算地区',
          label: 'country',
          value: null,
          key: 'settlementTimeZone',
        },
        {
          name: '结算货币',
          label: 'settlementCurrency',
          value: null,
          key: 'settlementCurrency',
        },
        
        {
          name: '充值黑豆余额',
          label: 'credit',
          value: null,
          key: 'rechargeBalance',
          type: 'number',
        },
        {
          name: '赠送黑豆余额',
          label: 'free',
          value: null,
          key: 'giftBalance',
          type: 'number',
        },
        {
          name: '信用值',
          label: 'creditValue',
          value: null,
          isEdit: false,
          key: 'creditScore',
          type: 'number',
        },
        {
          name: '可用信用值',
          label: 'availableCredit',
          value: null,
          key: 'availableCredit',
          type: 'number',
        },
        {
          name: '折扣率',
          label: 'discountRate',
          value: null,
          unit: '% off',
          key: 'discountRate',
        },
        {
          name: '业务负责人',
          label: 'salesManager',
          value: null,
          key: 'businessName',
        },
        // TODO: 暂时不需要显示业务负责人号码
        // {
        //   name: '业务负责人联系号码',
        //   label: 'salesManagerNo',
        //   value: null,
        //   key: 'contactNumber',
        // },
        // TODO: 暂时不需要显示有效日期
        // {
        //   name: '有效日期',
        //   label: 'expiredDate',
        //   value: null,
        //   key: 'effectiveDate',
        // },
        {
          name: '下月折扣率',
          label: 'nextMonthDiscountRate',
          value: null,
          isEdit: false,
          unit: '% off',
          key: 'nextDiscountRate',
        },

        {
          name: '备注',
          label: 'remark',
          value: null,
          isEdit: false,
          key: 'remark',
        },
      ],
      editKeyList: ['creditScore', 'nextDiscountRate', 'remark'],
      detailInfo: {},
      rechargeVisible: false,
      complimentaryVisible: false,
      isEdit: false,
      editLoading: false,
      currentOrgCode: null,
    };
  },
  computed: {
    ...mapGetters(['roles', 'language']),
    /**
     * 当前可用信用值
     */
    currentAvailableCredit({ detailInfo }) {
      if (detailInfo) {
        if (detailInfo.creditScore !== detailInfo.availableCredit) {
          return detailInfo.creditScore;
        } else {
          return 0;
        }
      }
      return 0;
    },
    /**
     * 是否置灰折扣率
     * 若客户当前可用信用值小于信用值则不支持编辑折扣率
     */
    discountDisabled({ detailInfo }) {
      return (
        detailInfo && detailInfo.availableCredit < detailInfo.creditScore
      );
    },

    /**
     * 业务人员禁止查看
     */
    businessDisabled({ roles }) {
      console.log('roles: ', roles);
      return  roles.length === 1 && roles[0].roleCode === 50032;
    },
  },
  created() {
    this.getCustomerDetails();
  },
  methods: {
    lang: getLang('heypoint.customer'),
    capitalize,

    /**
     * 充值黑豆
     */
    recharge() {
      this.rechargeVisible = true;
    },

    /**
     * 充值黑豆
     */
    complimentary() {
      this.complimentaryVisible = true;
    },

    /**
     * 刷新客户信息
     */
    refreshInfo() {
      this.getCustomerDetails();
      this.$refs.operateLog.searchData();
    },
    /**
     * 获取客户信息详情
     */
    async getCustomerDetails() {
      try {
        const { orgCode } = this.$route.query;
        this.currentOrgCode = Number(orgCode);
        const params = {
          orgCode: this.currentOrgCode,
        };
        const { data } = await getCustomerDetails(params);
        this.detailInfo = data;
        this.infoList.forEach((item) => {
          Object.keys(data).forEach((key) => {
            const value = data[key];
            if (key === item.key) {
              item.value = value;
            }
          });
        });
      } catch (error) {
        console.error('error: ', error);
      }
    },

    /**
     * 返回事件
     */
    back() {
      this.$router.push({
        path: '/heyPoint/customer',
        query: { ...this.$route.query },
      });
    },

    /**
     * 编辑
     */
    edit() {
      if (this.detailInfo && this.detailInfo.id === 0) {
        const creditScore = this.infoList.find(
          (item) => item.key === 'credit_score'
        );
        creditScore.value = 1000;
      }
      this.editShow(true);
    },

    /**
     * 完成编辑后的显示
     * @param isEdit 是否编辑
     */
    editShow(isEdit) {
      this.isEdit = isEdit;
      this.infoList.forEach((item) => {
        if (this.editKeyList.includes(item.key)) {
          item.isEdit = isEdit;
        }
      });
    },

    /**
     * 完成编辑
     */
    async completeEdit() {
      try {
        const isEditValue = this.infoList
          .filter((item) => this.editKeyList.includes(item.key))
          .some((item) => item.value !== this.detailInfo[item.key]);
        if (isEditValue) {
          this.editLoading = true;
          const params = {};
          this.infoList.forEach((item) => {
            if (this.editKeyList.includes(item.key)) {
              params[item.key] = item.value;
            }
          });
          params.orgCode = this.currentOrgCode;

          const { code, msg } = await updateCustomerDetails(params);
          this.editLoading = false;
          if (code === 200) {
            this.refreshInfo();
            this.editShow(false);
          } else {
            this.$message({
              type: 'error',
              message: msg,
            });
          }
        } else {
          this.editShow(false);
        }
      } catch (error) {
        console.error('error: ', error);
        const { code, message } = error
        this.editLoading = false;
        if (code === 11010032) {
          this.$message({
            message: this.$t(`http.error.${code}`, {num: message}),
            type: 'error',
            duration: 3 * 1000
          });
        } else {
          this.$message({
            message: this.$t(`http.error.${code}`),
            type: 'error',
            duration: 3 * 1000
          });
        }
      }
    },
  },
};
</script>

<style lang="scss">
.customer-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  .title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 28px;
    .hg-button {
      margin-right: 24px;
      .icon-arrow-back-lab {
        margin-right: 10px;
      }
    }
    .text {
      .first {
        color: $hg-secondary-text;
      }
      .second {
        color: $hg-label;
      }
    }
  }
  .details-content{
    display: flex;
    .customer-info {
      width: 360px;
      margin-right: 24px;
      background: $hg-main-black;
      padding: 16px 24px 24px 24px;
      position: relative;
      .info-list {
        height: calc(100vh - 260px);
        overflow-y: auto;
        overflow-x: hidden;
        .info-item {
          width: 100%;
          line-height: 24px;
          margin-bottom: 24px;
          .credit-value {
            width: 150px;
            .el-input-number__increase {
              .el-icon-arrow-up {
                bottom: 5px;
              }
            }
            .el-input-number__decrease {
              .el-icon-arrow-down {
                top: 5px;
              }
            }
          }
          .discount-value {
            width: 120px;
            .el-input-number__increase {
              .el-icon-arrow-up {
                bottom: 5px;
              }
            }
            .el-input-number__decrease {
              .el-icon-arrow-down {
                top: 5px;
              }
            }
          }
          .item-name {
            display: inline-block;
            min-width: 120px;
          }
          .remark-value {
            display: inline-block;
            max-height: 60px;
            overflow-y: auto;
          }
        }
        .info-item:first-child {
          line-height: 40px;
          padding-bottom: 20px;
          border-bottom: 1px dotted #3D4047;
        }
        .remark-item {
          width: 100%;
          // margin-right: 112px !important;
          word-break: break-word;
          .item-name {
            display: inline-block;
            min-width: 120px;
          }
          > span {
            display: flex;
            width: 100%;
            .remark {
              display: inline-block;
              width: 96%;
            }
          }
        }
        .discount-item {
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
        }
      }
      .edit-btn {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        height: 40px;
        span{
          font-size: 16px;
          font-weight: 700;
        }
        .el-button {
          position: absolute;
          right: 0;
          width: 88px;
          &.complete-edit {
            display: flex;
            justify-content: center;
          }
        }
      }
      .discount-tips {
        color: #dc5050;
        display: inline-block;
        width: 100%;
        font-size: 12px;
        &.is-edit {
          top: 28px;
        }
      }
      .return-btn {
        position: absolute;
        right: 0;
        top: 0;
        background: #fff;
        cursor: pointer;
        .icon-btn-return {
          font-size: 34px;
        }
      }
    }
  }
}
</style>