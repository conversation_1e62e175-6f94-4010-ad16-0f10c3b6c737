<template>
  <div class="hg-parameter-box">
    <slot name="set-software"></slot>
    
    <div class="hg-parameter-ul">

      <div v-if="dataList && dataList.length > 0" style="display: contents;">
        <div 
          :class="{
            'hg-parameter-li': true, 
            'on-one-line-li': item.name === 'softwareVersion' || [COMPONENT_TYPE.RADIO_CHILD_INPUT].includes(item.component),
            'is-line-li': [COMPONENT_TYPE.TEXT_IMAGE_CHECKBOX,COMPONENT_TYPE.TEXT_INPUT_CARD].includes(item.component),
            'has-child-one-line': item.component === COMPONENT_TYPE.TEXT && item.child && item.child.length
          }" 
          v-for="(item, index) in dataList" 
          :key="`${designCode}-${item.code}-${index}`">

          <div class="parameter-label" :class="{'one-line': item.component === COMPONENT_TYPE.TEXT && item.child && item.child.length}">
            <span>{{ getI18nName(item, i18nTitle, $getI18nText) }}</span> 
            <el-tooltip popper-class="params-tips" effect="light" placement="bottom" v-if="item.tip">
              <template slot="content">
                <img :src="`${PARAM_ICON_PATH}/${item.tip}.svg`" alt="">
              </template>
              <img src="@/assets/images/common/icon_supplement.png" alt="">
            </el-tooltip>
          </div>

          <div class="parameter-component">
            <hg-select v-if="item.component === COMPONENT_TYPE.SELECT"
              v-model="item.value" 
              :data="item"
              :i18nTitle="i18nTitle"></hg-select>

            <hg-radio 
              v-else-if="item.component === COMPONENT_TYPE.RADIO" 
              v-model="item.value" 
              :data="item"
              :i18nTitle="i18nTitle"></hg-radio>

            <hg-number-range 
              v-else-if="item.component === COMPONENT_TYPE.NUMBER_RANGE"
              v-model="item.value"
              :data="item"></hg-number-range>

            <hg-text-img-checkbox 
              v-else-if="item.component === COMPONENT_TYPE.TEXT_IMAGE_CHECKBOX" 
              v-model="item.value" 
              :data="item"
              :i18nTitle="i18nTitle"></hg-text-img-checkbox>

            <hg-text-input-card
              v-else-if="item.component === COMPONENT_TYPE.TEXT_INPUT_CARD" 
              :data="item"
              :i18nTitle="i18nTitle"></hg-text-input-card>

            <hg-radio-child-input
              v-else-if="item.component === COMPONENT_TYPE.RADIO_CHILD_INPUT" 
              v-model="item.value" 
              :data="item"
              :i18nTitle="i18nTitle"></hg-radio-child-input>

            <div v-else-if="item.component === COMPONENT_TYPE.TEXT && item.child && item.child.length" class="parameter-container">
              <div class="parameter-child" v-for="child in item.child" :key="child.enName">
                <div class="parameter-title">
                  <span>{{ getI18nName(child, i18nTitle, $getI18nText) }}</span> 
                  <el-tooltip popper-class="params-tips" effect="light" placement="bottom" v-if="child.tip">
                    <template slot="content">
                      <img :src="`${PARAM_ICON_PATH}/${child.tip}.svg`" alt="">
                    </template>
                    <img src="@/assets/images/common/icon_supplement.png" alt="">
                  </el-tooltip>
                </div>
                <div class="parameter-value">
                  <hg-select v-if="child.component === COMPONENT_TYPE.SELECT"
                    v-model="child.value" 
                    :data="child"
                    :i18nTitle="i18nTitle"></hg-select>
  
                  <hg-radio 
                    v-else-if="child.component === COMPONENT_TYPE.RADIO" 
                    v-model="child.value" 
                    :data="child"
                    :i18nTitle="i18nTitle"></hg-radio>
  
                  <hg-number-range 
                    v-else-if="child.component === COMPONENT_TYPE.NUMBER_RANGE"
                    v-model="child.value"
                    :data="child"></hg-number-range>
  
                  <hg-text-img-checkbox 
                    v-else-if="child.component === COMPONENT_TYPE.TEXT_IMAGE_CHECKBOX" 
                    v-model="child.value" 
                    :data="child"
                    :i18nTitle="i18nTitle"></hg-text-img-checkbox>
  
                  <hg-text-input-card
                    v-else-if="child.component === COMPONENT_TYPE.TEXT_INPUT_CARD" 
                    :data="child"
                    :i18nTitle="i18nTitle"></hg-text-input-card>
  
                  <hg-radio-child-input
                    v-else-if="child.component === COMPONENT_TYPE.RADIO_CHILD_INPUT" 
                    v-model="child.value" 
                    :data="child"
                    :i18nTitle="i18nTitle"
                    :hasLabel="true"></hg-radio-child-input>
  
                  <hg-input-number 
                    v-else 
                    v-model="child.value" 
                    :data="child" 
                    :parentItem="findParentItem(child)"></hg-input-number>
                </div>
              </div>
            </div>

            <hg-input-number 
              v-else 
              v-model="item.value" 
              :data="item" 
              :parentItem="findParentItem(item)"></hg-input-number>
              
          </div>
        </div>
      </div>

      <div v-else class="no-parameter">
        <span>{{ $t('param.noParameter') }}</span>
      </div>
      
    </div>
  </div>
</template>

<script>
import { staticResourcesUrl } from '@/config';
import { COMPONENT_TYPE, PARAM_ICON_PATH } from './utils/constant';
import HgInputNumber from './components/HgInputNumber';
import HgNumberRange from './components/HgNumberRange';
import HgTextImgCheckbox from './components/HgTextImgCheckbox';
import HgRadio from './components/HgRadio';
import HgSelect from './components/HgSelect';
import HgTextInputCard from './components/HgTextInputCard';
import HgRadioChildInput from './components/HgRadioChildInput';
import { getI18nName } from './utils';

export default {
  components: { HgInputNumber, HgRadio, HgNumberRange, HgTextImgCheckbox, HgSelect, HgTextInputCard, HgRadioChildInput },
  provide() {
    return {
      needZoom: this.needZoom,
    }
  },
  props:{
    designCode: Number,
    dataList: {
      type: Array,
      default(){
        return [];
      }
    },
    i18nTitle: {
      type: String,
      default: ''
    },
    needZoom: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      COMPONENT_TYPE,
      PARAM_ICON_PATH,
    }
  },
  methods: {
    getI18nName,
    findParentItem(item) {
      if(item.connectPid) {
        const parentItem = this.dataList.find(data => data.code === item.connectPid);
        return parentItem;
      }else {
        return null;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style/index.scss';
</style>