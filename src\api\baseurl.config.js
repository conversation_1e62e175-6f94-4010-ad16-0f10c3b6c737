let baseUrl = 'https://sit-gw.heygears.com'
const loginPath = '/lab_tenant_login' // 跳转的登录页面
const currentAppPath = '/lab_tenant_uc' // 当前用户路径  需和后台应用路径对应

switch (process.env.NODE_ENV) {
  case 'development':
    baseUrl = 'https://dev-gw.heygears.com/gw'
    // baseUrl = 'https://sit-gw.heygears.com/gw';
    // baseUrl = 'http://172.16.1.193:8150';
    break
  case 'sit':
    baseUrl = 'https://sit-gw.heygears.com/gw'
    break
  case 'uat':
    baseUrl = 'https://uat-gw.heygears.com/gw'
    break
  case 'prod':
    baseUrl = 'https://gw.heygears.cloud/gw'
    break
  case 'usprod':
    baseUrl = 'https://usgw.heygears.cloud/gw'
    break
  case 'tkyprod':
    baseUrl = 'https://tkygw.heygears.cloud/gw'
    break
  case 'parprod':
    baseUrl = 'https://pargw.heygears.cloud/gw'
    break
  case 'cnprod':
    baseUrl = 'https://cngw.heygears.cloud/gw'
    break
  default:
    baseUrl = 'https://dev-gw.heygears.com/gw'
}

export { baseUrl, loginPath, currentAppPath }
