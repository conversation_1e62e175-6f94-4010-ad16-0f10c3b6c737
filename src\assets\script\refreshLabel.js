import Vue from 'vue';

/**
 * 更新form表单的标签位置
 */
 const refreshLabel = (className) => {
	Vue.prototype.$nextTick(() => {
    let labelArr = Array.from(document.getElementsByClassName(className));
    let labelWidthArr = labelArr.map((label) => {
      return label.childNodes[0].clientWidth;
    });
    let max = Math.max(...labelWidthArr);
    labelArr.forEach(label => {
      label.childNodes[0].style.width = `${max + 1}px`;
    });
  });
}

export {
  refreshLabel
}
