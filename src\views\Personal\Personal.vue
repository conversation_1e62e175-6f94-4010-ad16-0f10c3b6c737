<template>
  <div class="Personal-box">
    <!-- <div class="back-box flex-center finger" @click="$router.back()">
      <i class="iconfont icon-back"></i>
      <div>{{ $t(personal.back) }}</div>
    </div> -->
    <div class="info-box">
      <div class="head-box">
        <div class="head-content pos-rel">
          <img v-if="personInfo.headImgurl" :src="personInfo.headImgurl" alt="picture" class="head-img" @mousemove="showChangeImg=true">
          <div v-else class="upload-operate-box">
            <input ref="uploadInput" type="file" class="uploadInput" @change="uploadImg">
            <div class="upload-btn pos-abs-center flex-start border finger" @click="beforeUpload">
              <i class="iconfont icon-out" />
              {{ $t('personal.uploadHeader') }}
            </div>
          </div>
          <div v-if="showChangeImg" class="change-upload-box" @mouseleave="showChangeImg=false">
            <input ref="uploadInput" type="file" class="uploadInput" @change="uploadImg">
            <div class="upload-btn pos-abs-center flex-start border finger" @click="beforeUpload">
              <i class="iconfont icon-out" />
              {{ $t('personal.changeHeader') }}
            </div>
          </div>
        </div>
        <p class="head-tip">
          {{ $t('personal.uploadTip') }}<br>
          {{ '1. ' + $t('personal.uploadTip1') }}<br>
          {{ '2. ' + $t('personal.uploadTip2') }}<br>
          {{ '3. ' + $t('personal.uploadTip3') }}
        </p>
      </div>
      <div class="edit-info-box custom-form">
        <el-form ref="personRuleForm" :model="personInfo" :rules="rules" :hide-required-asterisk="true">
          <el-form-item :label="$t('personal.username')" class="person-label">
            <el-input v-model="personInfo.userName" type="text" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('personal.password')" class="person-label">
            <el-input v-model="personInfo.password" type="password" :disabled="true" />
            <div class="edit-password-btn finger" @click="openChangePasswordBox"><i class="iconfont icon-edit iconfont-24" />{{ $t('personal.change') }}</div>
          </el-form-item>
          <el-form-item :label="$t('personal.name')" prop="realName" class="person-label">
            <el-input v-model="personInfo.realName" type="text" :placeholder="$t('personal.usernamePlaceholder')" :title="personInfo.realName ? '' : $t('personal.usernamePlaceholder')" />
          </el-form-item>
          <el-form-item :label="$t('personal.phone')" prop="mobile" class="person-label">
            <div class="area-code"><Select :select-options="countryListArrayComputed" :value="personInfo.mobilePrefix" :placeholder="$t('personal.areaCode')" @change="changeAreaCode" /></div>
            <el-input v-model="personInfo.mobile" type="text" :placeholder="$t('personal.phonePlaceholder')" :title="personInfo.mobile ? '' : $t('personal.phonePlaceholder')" />
          </el-form-item>
          <el-form-item :label="$t('personal.email')" prop="email" class="person-label">
            <el-input v-model="personInfo.email" type="text" :placeholder="$t('personal.emailPlaceholder')" :disabled="true" :title="personInfo.email ? '' : $t('personal.emailPlaceholder')" />
            <div
              class="edit-password-btn finger"
              @click="openChangeEmailBox"
            >
              <i class="iconfont icon-edit iconfont-24" />{{
                $t("personal.change")
              }}
            </div>
          </el-form-item>
          <!-- 时区 -->
          <el-form-item :label="$t('timezone.timezone')" prop="tzCode" class="person-label">
            <div class="input-box"><Select :select-options="timezoneList" :value="personInfo.tzCode" @change="changeTimezone" /></div>
          </el-form-item>
        </el-form>
        <div class="flex-end" style="margin-top: 40px">
          <!-- <VueButton
            v-btn-control
            width="320"
            type="primary"
            sizes="big"
            @click.native="saveUserInfoFunc"
          >
            {{ $t('personal.save') }}
          </VueButton> -->
          <el-button type="primary" style="width:320px;background-color:#3054cc" :loading="loading" @click="saveUserInfoFunc">{{ $t('personal.save') }}</el-button>
        </div>
      </div>
    </div>
    <ChangePassword
      ref="changePassword"
      :show.sync="changePasswordBool"
      :password-expired="passwordExpired"
      @submit="changePasswordFunc"
    />
    <ChangeEmail
      ref="changePassword"
      :show.sync="changeEmailBool"
      @changeSuccess="changeSuccess"
    />
  </div>
</template>

<script>
import axios from 'axios'
import ChangePassword from './ChangePassword'
import ChangeEmail from './ChangeEmail'
import Select from '@/components/func-components/Select'
import md5 from 'js-md5'
import AWS from 'aws-sdk'
import { getUserInfo, changeUserInfo, changePassword, getUploadUrl, completeUpload } from '@/api/login'
import { getAreaCodeList } from '@/api/organization'
import { mapMutations, mapState } from 'vuex'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { refreshLabel } from '@/assets/script/refreshLabel.js'
import { getStore } from '@/assets/script/utils'
import { getTimezoneList } from '@/api/common'
import { setTimezone, myTimeFormat } from '@/assets/script/formatDate.js'

export default {
  name: 'Personal',
  components: {
    ChangePassword,
    Select,
    ChangeEmail
  },
  data() {
    var checkMobile = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.PHONE_RULE.test(value)) {
          return callback(new Error(this.$t('personal.phoneErro')))
        }
      }
      callback()
    }
    var checkEmail = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.EMAIL_RULE.test(value)) {
          return callback(new Error(this.$t('personal.emailErro')))
        }
      }
      callback()
    }
    return {
      changePasswordBool: false,
      changeEmailBool: false,
      countryListArray: [], // 区号 国家列表
      rules: {
        realName: [
          { required: true, message: this.$t('personal.usernamePlaceholder') },
          { max: 50, message: this.$t('personal.realNameErro') }
        ],
        mobile: [
          { validator: checkMobile, trigger: 'blur' }
        ],
        email: [
          { required: true, message: this.$t('personal.emailPlaceholder') },
          { validator: checkEmail, trigger: 'blur' }
        ],
        tzCode: [
          { required: true, message: this.$t('timezone.timezoneErr') }
        ]
      },
      md5File: { // 上传的头像信息
        data: '',
        suffix: '',
        md5: ''
      },
      showChangeImg: false, // 鼠标移上图片显示切换头像提示
      timezoneList: [],
      loading: false,
      timezoneMap: {},
      passwordExpired: false
    }
  },
  computed: {
    ...mapState({
      personInfo: state => state.user.personInfo,
      userInfo: state => state.user.userInfo
    }),
    countryListArrayComputed() { // 根据目前的中英文状态返回相对应的中英文区号
      const countryListArrayNew = []
      this.countryListArray.forEach((item) => {
        if (this.$i18n.locale == 'zh') {
          item.label = item.countryName + ' +' + item.mobilePrefix
        } else {
          item.label = item.countryEn + ' +' + item.mobilePrefix
        }
        item.value = item.mobilePrefix
        countryListArrayNew.push(item)
      })
      return countryListArrayNew
    }
  },
  mounted() {
    Promise.allSettled([this.getTimezoneListFunc(), this.getUserinfoFunc(), this.getCountryList()])
    refreshLabel('person-label')
    this.passwordExpired = this.$route.query.passwordExpired ? this.$route.query.passwordExpired : false
    if (this.passwordExpired) {
      this.openChangePasswordBox()
    }
  },
  methods: {
    ...mapMutations({
      updatePersonInfo: 'user/updatePersonInfo',
      updateUserInfo: 'user/updateUserInfo'
    }),
    // 打开修改密码弹窗
    openChangePasswordBox(e) {
      this.changePasswordBool = true
    },
    openChangeEmailBox(e) {
      this.changeEmailBool = true
    },
    changeSuccess() {
      location.reload()
    },
    changePasswordFunc(passwordObj) {
      changePassword({
        'newPassword': md5(passwordObj.newPassword),
        'oldPassword': md5(passwordObj.oldPassword),
        'userCode': this.personInfo.userCode
      }).then((res) => {
        if (res.code == 200) {
          this.$refs.changePassword.loading = false
          this.$MessageAlert({
            text: this.$t('personal.changePWSuccessTip'),
            type: 'success'
          })
          this.changePasswordBool = false
          this.getUserinfoFunc()
        }
      }).catch(() => {
        this.$refs.changePassword.loading = false
      })
    },
    getCountryList() {
      return new Promise((resolve) => {
        getAreaCodeList({ orderBy: 0 }).then((res) => {
          if (res.code == 200) {
            this.countryListArray = res.data
          }
          resolve()
        })
      })
    },
    // 选择区号
    changeAreaCode(value) {
      // select获取到的值是value，但是显示的是label，所以将显示的值变成数字类型，就能只获取到区号，然后在区号前面加上"+"号
      this.personInfo.mobilePrefix = '+' + Number(value)
    },
    // 获取时区信息列表
    getTimezoneListFunc() {
      return new Promise((resolve) => {
        getTimezoneList().then((res) => {
          if (res.code === 200) {
            if (res.data != null && res.data.length) {
              this.timezoneList = res.data
              this.timezoneList.forEach((item) => {
                /* let utc = myTimeFormat(Math.abs(item.utc * 60 * 1000), ':')
                utc = item.utc < 0 ? `-${utc}` : `+${utc}`
                item.label = this.$t(`timezone.${item.countryCode}`, { utc: utc }) */
                const { tzNameCn, tzNameEn, tzCode, countryCode, utc, tzName } = item
                item.label = this.$i18n.locale === 'zh' ? tzNameCn : tzNameEn
                item.value = tzCode

                this.timezoneMap[tzCode] = {
                  tzCode,
                  utc,
                  tzName,
                  countryCode
                }
              })
            }
          }
          resolve()
        })
      })
    },
    // 选择时区
    changeTimezone(value) {
      this.personInfo.tzCode = value
    },
    getUserinfoFunc() {
      return new Promise((resolve) => {
        getUserInfo().then((res) => {
          if (res.code == 200) {
            const person = res.data
            const user = getStore('userInfo')
            user.avatarUrl = person.headImgurl
            if (this.timezoneMap[person.tzCode]) {
              user.timezone = this.timezoneMap[person.tzCode]
            }
            user.realName = !person.realName ? person.userName : person.realName
            this.updateUserInfo(user)

            if (!person.realName) {
              person.realName = person.userName
            }
            person.password = '123456' // 初始化的这个密码是假数据，不用显示
            person.fileId = ''
            this.updatePersonInfo(person)
          }
          resolve()
        })
      })
    },
    saveUserInfoFunc() {
      this.$refs['personRuleForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          changeUserInfo({
            'email': this.personInfo.email,
            'fileId': this.personInfo.fileId,
            'mobile': this.personInfo.mobile,
            'mobilePrefix': this.personInfo.mobilePrefix,
            'realName': this.personInfo.realName,
            'tzCode': this.personInfo.tzCode
          }).then((res) => {
            if (res.code == 200) {
              this.loading = false
              this.$MessageAlert({
                text: this.$t('personal.saveInfoSuccessTip'),
                type: 'success'
              })
              this.getUserinfoFunc()
            } else {
              this.loading = false
              throw new Error(res)
            }
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    // 上传之前，清空原来的上传内容
    beforeUpload() {
      this.$refs.uploadInput.value = ''
      this.$refs.uploadInput.click()
    },
    // 获取文件后缀名
    getSuffix(name) {
      return name
        .split('.')
        .pop()
        .toLowerCase()
    },
    // 选择图片，将图片读进内存，再拿到图片在内存里的值
    uploadImg(e) {
      const imgFileObj = e.target.files[0]
      const maxSize = COMMON_CONSTANTS.MAX_HEAD_IMG_SIZE
      const readerImg = new FileReader()
      // 判断文件是不是imgage类型
      if (!COMMON_CONSTANTS.IS_IMAGE_RULE.test(imgFileObj.type)) {
        this.$MessageAlert({
          text: this.$t('personal.uploadFormatErro'),
          type: 'warning'
        })
      } else if (imgFileObj.size > maxSize) {
        this.$MessageAlert({
          text: this.$t('personal.uploadSizeErro'),
          type: 'warning'
        })
      } else {
        readerImg.onload = () => {
          const data = readerImg.result
          // 加载图片获取图片真实宽度和高度
          const image = new Image()
          image.src = data
          setTimeout(() => {
            // console.log('6666', image.complete,image.width,image.height, imgFileObj.size);
            if (image.complete) {
              var width = image.width
              var height = image.height
              if (width > COMMON_CONSTANTS.MAX_HEAD_IMG_WIDTH && height > COMMON_CONSTANTS.MAX_HEAD_IMG_HEIGHT) {
                const readerFile = new FileReader()
                readerFile.onload = () => {
                  this.md5File.suffix = this.getSuffix(imgFileObj.name)
                  this.md5File.data = readerFile.result
                  this.md5File.md5 = AWS.util.crypto.md5(readerFile.result, 'base64')
                  this.uploadHeaderFunc()
                }
                readerFile.readAsArrayBuffer(imgFileObj)
              } else {
                this.$MessageAlert({
                  text: this.$t('personal.uploadPXErro'),
                  type: 'warning'
                })
              }
            }
          }, 30)
        }
        readerImg.readAsDataURL(imgFileObj)
      }
    },
    // 选择图片之后的操作
    uploadHeaderFunc() {
      // 获取上传预授权
      this.getUploadUrlFunc().then((data) => {
        // 将图片post到授权回来的地址
        this.postImgUrlFunc(data.url).then(() => {
          // 完成上传，调接口告诉后端，且拿到返回的图片地址和S3文件
          this.completeUploadFunc(data.urlUuid).then((data) => {
            this.personInfo.headImgurl = data.url
            this.personInfo.fileId = data.s3FileId
          })
        })
      })
    },
    getUploadUrlFunc() {
      return new Promise((resolve) => {
        getUploadUrl({
          'contentMd5': this.md5File.md5,
          'objectSuffix': this.md5File.suffix
        }).then((res) => {
          if (res.code === 200) {
            resolve(res.data)
          }
        })
      })
    },
    postImgUrlFunc(url) {
      return new Promise((resolve) => {
        axios({
          url: url,
          method: 'PUT',
          data: this.md5File.data,
          headers: {
            'Content-Type': 'multipart/form-data',
            'Content-MD5': this.md5File.md5
          }
        }).then(res => {
          resolve()
        })
      })
    },
    completeUploadFunc(urlUuid) {
      return new Promise((resolve) => {
        completeUpload(urlUuid).then((res) => {
          if (res.code === 200) {
            resolve(res.data)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.Personal-box {
  position: absolute;
  top: 0px;
  bottom: 0;
  left: 0;
  width: 100%;
  background: $hg-background-color;
  .back-box {
    position: absolute;
    top: 40px;
    left: 40px;
    color: $hg-secondary-fontcolor;
    a {
      font-size: $hg-normal-fontsize;
      line-height: 22px;
    }
    i {
      font-size: 20px;
      color: $hg-secondary-fontcolor;
      margin-right: 8px;
    }
  }
  .info-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .head-box {
      width: auto;
      margin-right: 148px;
      .head-content {
        width: 270px;
        height: 270px;
        border-radius: 50%;
        background: $hg-main-black;
        .head-img {
          width: 100%;
          height: 100%;
          z-index: 1;
          border-radius: 50%;
        }
        .uploadInput {
          display: none;
        }
        .upload-operate-box {
          width: 100%;
          height: 100%;
        }
        .change-upload-box {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          @include shade-background;
          z-index: 2;
        }
        .upload-btn {
          position: relative;
          display: inline-block;
          padding: 0 16px;
          line-height: 32px;
          color: $hg-primary-fontcolor;
          font-size: $hg-small-fontsize;
          i {
            display: inline-block;
            margin-right: 10px;
            font-size: $hg-small-fontsize;
            transform: rotate(90deg);
          }
          &:hover {
            @include hover;
          }
          &:active {
            @include active;
          }
        }
      }
      .head-tip {
        color: $hg-disable-fontcolor;
        margin-top: 40px;
        line-height: 24px;
      }
    }
    .edit-info-box {
      width: 400px;
      .input-box {
        position: relative;
        width: 344px;
      }
      .edit-password-btn {
        width: 108px;
        height: 40px;
        border: 1px solid $hg-border-color;
        border-radius: 4px;
        margin-left: 12px;
        color: $hg-primary-fontcolor;
        display: flex;
        align-items: center;
        justify-content: center;
        i {
          margin-right: 8px;
        }
        &:hover {
          color: $hg-button-hover-fontcolor;
          i {
            color: $hg-button-hover-fontcolor;
          }
        }
        &:active {
          color: $hg-button-active-fontcolor;
          i {
            color: $hg-button-active-fontcolor;
          }
        }
      }
      .area-code {
        width: 96px;
        margin-right: 12px;
      }
    }
  }
  .popup-wrapper{
    z-index: 1001;
  }
}
</style>

