<template>
  <div class="hg-text-input-card">
    <div class="hg-text-input-item" v-for="(item, index) in dataList" :key="index">
      <div class="label">
        <span>{{ getI18nName(item, null, $getI18nText) }}</span>
      </div>
      <hg-input-number v-model="item.value" :data="item"></hg-input-number>
    </div>
  </div>
</template>

<script>
import HgInputNumber from './HgInputNumber';
import { getI18nName } from '../utils';

export default {
  name: 'HgTextInputCard',
  components: { HgInputNumber },
  props: {
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          max: 3,
          min: 0,
          child: [],
        }
      }
    },
  },
  data() {
    return {
      dataList: [],
    }
  },
  mounted() {
    this.initDataList();
  },
  methods: {
    getI18nName,
    initDataList() {
      const { child } = this.data;
      this.dataList = child || [];
    },
  }
}
</script>

<style lang="scss" scoped>
.hg-text-input-card {
  display: flex;
  flex-wrap: wrap;
  padding: 24px;
  border-radius: 2px;
  background: #1D1D1F7F;
  border-top: 1px solid #38393D;
}

.hg-text-input-card>.hg-text-input-item {
  display: flex;
  align-items: center;
  width: 33.3%;

  .label,
  .hg-input-number {
    width: 50%;
  }

  .label {
    display: flex;
    padding: 0 12px;
    line-height: 20px;
    color: $hg-label;
    &>span {
      margin: auto 0;
    }
  }
}
</style>