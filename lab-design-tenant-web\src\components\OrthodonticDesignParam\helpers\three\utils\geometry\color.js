import * as THREE from 'three'
import { _color } from '../color'

export function setGeometryColor(geometry, color) {
  const colors = []

  const { r, g, b } = _color.set(color)
  const { attributes } = geometry
  const { count } = attributes.position

  for (let i = 0; i < count; i++) {
    colors.push(r, g, b)
  }

  geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3))
}

export function setGeometryColorByVertexIndexs(geometry, vertexIndexs, color) {
  const { attributes } = geometry
  const colorAttributes = attributes.color

  const { r, g, b } = _color.set(color)

  for (const vertexIndex of vertexIndexs) {
    colorAttributes.setXYZ(vertexIndex, r, g, b)
  }

  colorAttributes.needsUpdate = true
}
