<template>
  <Popup :show="show" :popup-title="popupTitle" :is-use-ele="true" :loading="loading" @cancel="cancel" @submit="submitForm('editUserRuleForm')">
    <div slot="popupContent" class="edit-user-box custom-form">
      <el-form ref="editUserRuleForm" :model="editUserObj" :rules="rules">
        <el-form-item :label="$t('org.userId')" class="edit-user-label">
          <el-input v-model="editUserObj.userCode" type="text" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('org.name')" prop="realName" class="edit-user-label">
          <el-input v-model="editUserObj.realName" type="text" :placeholder="$t('org.usernamePlaceholder')" />
        </el-form-item>
        <el-form-item :label="$t('org.email')" prop="email" class="edit-user-label">
          <el-input v-model="editUserObj.email" type="text" :placeholder="$t('org.emailPlaceholder')" :disabled="true" />
        </el-form-item>
        <!-- 时区 -->
        <el-form-item :label="$t('timezone.timezone')" prop="tzCode" class="edit-user-label">
          <div class="input-box"><Select :select-options="timezoneList" :value="editUserObj.tzCode" @change="changeTimezone" /></div>
        </el-form-item>
        <!-- 区域 -->
        <!-- <el-form-item :label="$t('customer.area')" prop="areaCode" class="edit-user-label">
          <div class="input-box">
            <el-cascader style="width: 100%;" v-model="editUserObj.areaCode" :props="{label: 'label', value: 'areaCode'}" :options="areaList" @change="changeArea"></el-cascader>
          </div>
        </el-form-item> -->
        <el-form-item :label="$t('org.department')" prop="selectedDept" class="edit-user-label">
          <div class="input-box"><VueCascader :value="editUserObj.selectedDept" :options="deptArr" :props-obj="propsObj" @change="changeDept" /></div>
        </el-form-item>
        <el-form-item :label="$t('org.role')" prop="selectedRole" class="edit-user-label">
          <div class="input-box"><Select :placeholder="$t('org.rolePlaceholder')" :select-options="updateRoleArr" :value="editUserObj.selectedRole" :is-multiple="true" @change="changeRole" /></div>
        </el-form-item>
        <el-form-item :label="$t('org.phone')" prop="mobile" class="edit-user-label">
          <div class="area-code"><Select :select-options="countryListArrayComputed" :value="editUserObj.mobilePrefix" :disabled="isDisabled" :placeholder="$t('org.areaCode')" @change="changeAreaCode" /></div>
          <el-input v-model="editUserObj.mobile" type="text" :disabled="isDisabled" :placeholder="$t('org.phonePlaceholder')" :title="editUserObj.mobile ? '' : $t('org.phonePlaceholder')" />
        </el-form-item>
      </el-form>
    </div>
  </Popup>
</template>

<script>
import Popup from '@/components/func-components/Popup'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import Select from '@/components/func-components/Select'
import { refreshLabel } from '@/assets/script/refreshLabel.js'
import VueCascader from '@/components/func-components/VueCascader'
import { getTimezoneList, getAreaList } from '@/api/common'
import { myTimeFormat } from '@/assets/script/formatDate.js'

export default {
  name: 'EditOrgUser',
  components: {
    Popup,
    Select,
    VueCascader
  },
  props: {
    popupTitle: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    userObj: {
      type: Object,
      default: {}
    },
    deptArr: {
      type: Array,
      default: []
    },
    roleArr: {
      type: Array,
      default: []
    },
    areaCodeArr: {
      type: Array,
      default: []
    },
    fromCustomer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    var checkMobile = (rule, value, callback) => {
      if(!value){
        return callback(new Error(this.$t('personal.phonenull')))
      }
      if (value) {
        if (!COMMON_CONSTANTS.PHONE_RULE.test(value)) {
          return callback(new Error(this.$t('org.phoneErro')))
        }
      }
      callback()
    }
    var checkEmail = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.EMAIL_RULE.test(value)) {
          return callback(new Error(this.$t('org.emailErro')))
        }
      }
      callback()
    }
    var getChildrenOrg = (node) => {
      return new Promise((resolve) => {
        this.$parent.getOrgListFunc(node.value).then((nodes) => {
          resolve(nodes)
        })
      })
    }
    return {
      editUserObj: {},
      rules: {
        realName: [
          { required: true, message: this.$t('org.usernamePlaceholder') },
          { max: 50, message: this.$t('org.realNameErro') }
        ],
        email: [
          { required: true, message: this.$t('org.emailPlaceholder') },
          { validator: checkEmail, trigger: 'blur' }
        ],
        tzCode: [
          { required: true, message: this.$t('timezone.timezoneErr') }
        ],
        areaCode: [
          { required: true, message: this.$t('customer.selectArea') }
        ],
        selectedRole: [
          { required: true, message: this.$t('org.rolePlaceholder') }
        ],
        selectedDept: [
          { required: true, message: this.$t('org.deptPlaceholder') }
        ],
        mobile: []
      },
      propsObj: {
        expandTrigger: 'hover',
        value: 'orgCode',
        label: 'orgName',
        children: 'sonList',
        leaf: 'isLeaf',
        emitPath: false,
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          if (!node.isLeaf) {
            getChildrenOrg(node).then((data) => {
              node.children = []
              node.children.concat(data)
              // 通过调用resolve将子节点数据返回，通知组件数据加载完成
              resolve(data)
            })
          } else {
            resolve()
          }
        }
      },
      timezoneList: [],
      areaList: [],
      updateRoleArr: [],
      loading: false,
      isDisabled: false
    }
  },
  computed: {
    countryListArrayComputed() { // 根据目前的中英文状态返回相对应的中英文区号
      const countryListArrayNew = []
      this.areaCodeArr.forEach((item) => {
        if (this.$i18n.locale == 'zh') {
          item.label = item.countryName + ' +' + item.mobilePrefix
        } else {
          item.label = item.countryEn + ' +' + item.mobilePrefix
        }
        item.value = item.mobilePrefix
        countryListArrayNew.push(item)
      })
      return countryListArrayNew
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.updateRoleArr = this.roleArr;
        refreshLabel('edit-user-label')
        this.editUserObj = JSON.parse(JSON.stringify(this.userObj));
        if(this.editUserObj.mobile || this.editUserObj.mobile === 0){
          this.isDisabled = true;
        } else {
          this.isDisabled = false;
        }
        !this.editUserObj.mobilePrefix ? this.editUserObj.mobilePrefix = '+86' : '' // 如果没有区号则默认选中中国的区号
        // 如果选中的部门不是一级组织，则不能选择管理员角色(没有管理员这个角色了，已经废弃)
        // this.updateRoleList(this.editUserObj.orgInfo[0])

        this.getTimezoneListFunc();
      } else {
        this.resetForm('editUserRuleForm')
      }
    },
    editUserObj: {
      handler(newVal){
        console.log(newVal.selectedRole, 6666)
        if(newVal.selectedRole.includes(50032)){
          console.log(1111)
          this.$set(this.rules, 'mobile', [{ required: true, validator: this.checkMobile, trigger: 'blur' }]);
        } else {
          console.log(2222)
          this.$set(this.rules, 'mobile', []);
          this.$nextTick(() => {
            if(this.$refs && this.$refs.editUserRuleForm){
              this.$refs.editUserRuleForm.clearValidate('mobile');
            }
          })
        }
      },
      deep: true,
    }
  },
  mounted() {
  },
  methods: {
    checkMobile(rule, value, callback){
      if(!value){
        return callback(new Error(this.$t('personal.phonenull')))
      }
      if (value) {
        if (!COMMON_CONSTANTS.PHONE_RULE.test(value)) {
          return callback(new Error(this.$t('org.phoneErro')))
        }
      }
      callback()
    },
    // 根据选中的部门更新角色列表，只有一级组织才能选择管理员角色
    updateRoleList(curOrg) {
      if (this.updateRoleArr.length) {
        const admin = this.updateRoleArr.filter(item => item.value === 50015)
        if (curOrg.parentCode && admin.length) {
          this.updateRoleArr = this.updateRoleArr.filter(item => item.value !== 50015)
          // 选择了非一级组织作为部门，则去掉已选中的管理员角色
          this.editUserObj.selectedRole.includes(50015) ? this.editUserObj.selectedRole.splice(this.editUserObj.selectedRole.findIndex(item => item === 50015), 1) : ''
        } else if (!curOrg.parentCode && !admin.length && this.fromCustomer) {
          this.updateRoleArr.unshift({ label: this.$t(50015), value: 50015 })
        }
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$emit('submit', this.editUserObj)
        } else {
          this.loading = false
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    cancel() {
      this.loading = false
      this.$emit('update:show', false)
    },
    // 获取时区信息列表
    getTimezoneListFunc() {
      getTimezoneList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.timezoneList = res.data
            this.timezoneList.forEach((item) => {
              /* let utc = myTimeFormat(Math.abs(item.utc * 60 * 1000), ':')
              utc = item.utc < 0 ? `-${utc}` : `+${utc}`
              item.label = this.$t(`timezone.${item.countryCode}`, { utc: utc }) */
              item.label = this.$i18n.locale === 'zh' ? item.tzNameCn : item.tzNameEn;
              item.value = item.tzCode
            })
          }
        }
      })
    },
    // 获取区域列表
    async getAreaList(){
      const loop = (arr) => {
        arr.forEach((item) => {
          item.label =  this.$i18n.locale === 'zh' ? item.nameCn : item.name;
          if(item.children){
            loop(item.children)
          }
        })
        return arr
      }
      const { code, data } = await getAreaList();
      if(code == 200){
        this.areaList = loop(data);
        console.log(this.areaList)
      }
    },
    // 选择时区
    changeTimezone(value) {
      this.editUserObj.tzCode = value
    },
    changeArea(value){
      this.editUserObj.areaCode = value
    },
    // 选择部门
    changeDept(value, nodes) {
      this.editUserObj.selectedDept = value
      this.updateRoleList(nodes[0].data)
    },
    // 选择角色
    changeRole(value) {
      this.editUserObj.selectedRole = value
    },
    // 选择区号
    changeAreaCode(value) {
      // select获取到的值是value，但是显示的是label，所以将显示的值变成数字类型，就能只获取到区号，然后在区号前面加上"+"号
      this.editUserObj.mobilePrefix = '+' + Number(value)
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-user-box {
  .input-box {
    width: 320px;
  }
  .area-code {
    width: 96px;
    margin-right: 12px;
  }
}
</style>
