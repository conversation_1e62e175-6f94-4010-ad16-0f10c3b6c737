function check(list, cb, leftIndex, rightIndex) {
  const left = list[leftIndex]
  const right = list[rightIndex]
  return cb(left, right, leftIndex, rightIndex)
}

// 排列遍历
export function traverseArrayByPermutation(list, cb) {
  let index = 0
  const length = list.length

  while (index < length) {
    for (let i = 0; i < length; i++) {
      if (i === index) {
        continue
      }

      const left = list[index]
      const right = list[i]
      const result = cb(left, right)
      if (!result) {
        return false
      }
    }

    index++
  }
}

// 组合遍历
export function traverseArrayByCombination(list, cb) {
  const arr = list.slice()
  let length = arr.length

  let leftIndex = 0

  while (length > 1) {
    const left = arr[0]
    for (let i = 1; i < length; i++) {
      const right = arr[i]
      const rightIndex = leftIndex + i
      const result = cb(left, right, leftIndex, rightIndex)
      if (!result) {
        return false
      }
    }
    arr.shift()
    length--
    leftIndex++
  }

  return true
}

// 相邻遍历
export function traverseArrayByNear(list, cb, closed) {
  const length = list.length - 1

  for (let i = 0; i < length; i++) {
    const leftIndex = i
    const rightIndex = i + 1

    const result = check(list, cb, leftIndex, rightIndex)
    if (!result) {
      return false
    }
  }

  if (closed) {
    const leftIndex = length
    const rightIndex = 0

    const result = check(list, cb, leftIndex, rightIndex)
    if (!result) {
      return false
    }
  }

  return true
}
