import { createMatrix4 } from '../matrix4'

import {
  traverseTreeChildren,
} from '@/components/OrthodonticDesignParam/helpers/utils/index'

export function setGroupCentre(object, position, needKeepPosition) {
  if (!object.isGroup) {
    return
  }

  const matrix = createMatrix4(position)
  const matrixInvert = matrix.clone().invert()
  const geometryMatrix = matrixInvert.multiply(matrixInvert)

  traverseTreeChildren(object, (child) => {
    if (object === child) {
      if (needKeepPosition) {
        object.applyMatrix4(matrix)
      }
      return true
    }

    if (child.geometry) {
      child.applyMatrix4(matrix)
      child.geometry.applyMatrix4(geometryMatrix)
    }

    return true
  })
}
