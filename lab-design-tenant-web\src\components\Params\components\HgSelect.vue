<template>
  <div class="hg-param-select">
    <el-select v-model="selectValue" :placeholder="$t('component.tip.select')" @change="handleChange" :disabled="eventDisable">
      <el-option
        v-for="(item, index) in selectList" 
        :key="index" 
        :label="getI18nName(item, i18nTitle, $getI18nText, false)"
        :value="item.name ? item.name : item">
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getI18nName } from '../utils';

export default {
  model: {
    prop: 'value',
    event: 'update',
  },
  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          max: 3,
          min: 0,
          ranges: []
        }
      }
    },
    i18nTitle: { // i18n需要拼接成i18n国际化文本，数据库就不存太长
      type: String,
      default: '',
    },
    eventDisable: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    }
  },
  data(){
    return {
      selectValue: null,
    }
  },
  computed: {
    selectList() {
      const { ranges } = this.data;
      if(typeof ranges === 'string') {
        const selectList = JSON.parse(ranges);
        return selectList;
      } else if(ranges.constructor === Array) {
        return ranges;
      }
      return [];
    }
  },
  watch: {
    value(value) {
      if(value !== this.selectValue) {
        this.selectValue = value;
      }
    }
  },
  mounted() {
    this.selectValue = this.value;
  },
  methods: {
    getI18nName,
    handleChange(value) {
      this.$emit('update',value);
    }
  }
}
</script>

<style lang="scss" scoped>
.hg-param-select {
  max-width: 194px;
  /deep/.el-input__inner {
    height: 32px;
  }
  /deep/.el-input .el-select__caret {
    line-height: 32px;
  }
}
</style>