import Vue from 'vue'
import Vuex from 'vuex'
import user from './modules/user'
import customer from './modules/customer'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    user,
    customer
  },
  state: {
    activeMenus: [],
    permission: []
  },
  mutations: {
    ACTIVE_MENUS: (state, data) => {
      state.activeMenus = data
    },
    PERMISSION: (state, data) => {
      state.permission = data
    }
  }
})

export default store
