import { getOrthList } from '@/api/order';
import { handleToCreateProgram, saveProgramme } from '@/api/order/operate';
import { ORDER_TYPES, FILE_TYPES } from '@/public/constants';
import { parseJson } from '@/public/utils';

export default {
  data() {
    return {
      orthodonticInfo: {
        originPateintImageList: [],
        designRemarkContent: '',
        originFileName: '',
        fileList: [],
        historyList: [],
        upperInputValue: 0,
        lowerInputValue: 0,
        isAudit: 0, // 审核中 默认0
        programCode: '', // 正畸方案编码
        isNotPassOrReturn: false, // OQC审核不通过或客户返单
        isCheck: false, //  设计师是否已查看方案
        htmlTaskId: 0,
        taskId: 0,
        orthoDesignParam: null,
        isCompleted: 0,

        oldFiles: [], // 保存旧的文件
      }
    }
  },
  computed: {
    // 是否隐藏正畸上传模块
    hiddenOrthUploadBox() {
      if(this.orderStatus == ORDER_TYPES.DESIGNING) { // 设计中，设计师
        return !this.isResponsibleDesigner;
      }else if(this.orderStatus === ORDER_TYPES.PENDING_REVIEW) { // 待审核 OQC
        return !this.isResponsibleExamine; 
      }
      return true;
    }
  },
  methods: {
    /**
     * 正畸需要另外调接口获取方案文件列表
     */
    handleOrthFileAndInfo(sourceList) {
      if(sourceList && sourceList.length > 0) {
        const originFile = sourceList.find(item => item.fileType === FILE_TYPES.ORIGIN_FILE);
        if(originFile) {
          this.file.originFile = originFile; // 原始文件显示
          const tempName = originFile.fileName.split('.');
          tempName.pop();
          this.orthodonticInfo.originFileName = tempName.join('.');
        }

        // 设计师 设计 || 负责OQC 审核 才加载患者图片
        if((this.orderStatus === ORDER_TYPES.DESIGNING && this.isResponsibleDesigner) || 
         (this.orderStatus === ORDER_TYPES.PENDING_REVIEW && this.isResponsibleExamine) ) {
          
          const originPateintImageList = sourceList.filter(item => item.fileType === FILE_TYPES.ORTH_ORIGIN_PIC);
          this.orthodonticInfo.originPateintImageList = originPateintImageList;
        }
      }
      
      getOrthList(this.orderCode).then(res => {
        let schemeList = res.data || [];
        schemeList.map(item => {
          let orderFiles = item.orderFiles || [];
          orderFiles = orderFiles.filter(item => [4,5,22,23,24,13].includes(item.fileType));// 设计文件、设计STL 患者照片
          item.orderFiles = orderFiles;
          return item;
        });

        schemeList.sort((beforeItem, afterItem) => { // 时间排序 
          return afterItem.createdTime - beforeItem.createdTime;
        });

        if(this.orderStatus === ORDER_TYPES.DESIGNING && !this.isResponsibleDesigner) { // 设计中不显示方案了
          schemeList = [];
        }

        const currentOrthSchemeIndex = schemeList.findIndex(item => {
          if(this.orderStatus === ORDER_TYPES.PENDING_REVIEW && this.isResponsibleExamine) { // 待审核 && 负责OQC
            return !item.isAudit; // 未审核
          }else if(this.orderStatus === ORDER_TYPES.DESIGNING && this.isResponsibleDesigner) { // 设计中-需要回填的情况：设计师主动撤回 || 审核不通过
            // 20230320 4.2.6 设计师点击[生成3D方案]会保存文件，但订单状态不发生变化，因此这里筛选，需要 增加currentStatus为0的状态 
            return [0,1,2].includes(item.currentState);
          } else if([[8, 11].includes(this.orderStatus)] && this.roles.map((item) => { return item.roleCode}).includes(50031) ){
            // 订单已完成并且是设计师组长在重新上传文件
            return item.isCompleted && !item.isAudit; // 是已完成订单新建的订单并且在待审核状态
          }
          return false;
        });
        console.log('currentOrthSchemeIndex:', currentOrthSchemeIndex)
        if(currentOrthSchemeIndex > -1) {
          const { orderFiles, remark, upperNum, lowerNum, isAudit, programmeCode, currentState, isCompleted,
            isGenerateModel, createdTime, isCheck, htmlTaskId, taskId, iprTiming, attachmentTiming } = schemeList[currentOrthSchemeIndex];
          this.orthodonticInfo.isAudit = isAudit;
          this.orthodonticInfo.programStatus = currentState;
          this.orthodonticInfo.programGenerateModel = isGenerateModel;
          this.orthodonticInfo.fileList = orderFiles;
          this.orthodonticInfo.upperInputValue = upperNum;
          this.orthodonticInfo.lowerInputValue = lowerNum;
          this.orthodonticInfo.designRemarkContent = remark;
          this.orthodonticInfo.programCode = programmeCode;
          this.orthodonticInfo.programCreatedTime = createdTime;
          this.orthodonticInfo.isCheck = Boolean(isCheck);
          this.orthodonticInfo.taskId = taskId;
          this.orthodonticInfo.htmlTaskId = htmlTaskId;
          this.orthodonticInfo.isCompleted = isCompleted;
          this.orthodonticInfo.orthoDesignParam = {
            iprInfo: parseJson(iprTiming),
            additionInfo: parseJson(attachmentTiming),
          };  

          schemeList.splice(currentOrthSchemeIndex, 1); //移除显示项
        }else {
          this.orthodonticInfo.orthoDesignParam = {};
        }

        this.orthodonticInfo.historyList = schemeList;
        

      }).catch(err => {
        console.log('历史方案获取失败',err);
      });
    },

    clearOrthInfo() {
      this.orthodonticInfo = {
        originPateintImageList: [],
        designRemarkContent: '',
        originFileName: '',
        fileList: [],
        historyList: [],
        upperInputValue: 0,
        lowerInputValue: 0,
        isAudit: 0, // 审核中 默认0
        programCode: '', // 正畸方案编码
        isNotPassOrReturn: false, // OQC审核不通过或客户返单
        programGenerateModel: 0,
        isCheck: false,
        orthoDesignParam: null,
        isCompleted: 0,
      }
    },
    
    // 保存方案数据
    saveProgramInfo(callback, orthoDesignParam) {
      if(!this.verifyOrthoInfo('createProgramBtn')) { 
        callback && callback();
        return;
      }

      // 设计软件，软件版本，组内QC转必填 20240327
      if(!this.baseInfo.designSoftware){
        this.$hgOperateWarning(this.$t('order.detail.tips.selectsoftware'));
        callback && callback();
        return
      }
      if(!this.baseInfo.softwareVersion){
        callback && callback();
        this.$hgOperateWarning(this.$t('order.detail.tips.selectversion'));
        return
      }
      // 本版本暂未开放
      // if(!this.baseInfo.groupQC){
      //   this.$hgOperateWarning('请选择组内QC');
      //   return
      // }

      let requestFileList = [];
      const compList = this.uploadCompList;
      compList.forEach(item => {
        const fileList = item.fileList;
        fileList.forEach(file => {
          const { fileName, filePath, fileSize, fileType } = file;
          requestFileList.push({ fileName, filePath, fileSize, fileType });
        });
      });

      let param = {
        orderCode: this.orderCode, 
        orderFiles: requestFileList, 
        designSoftware: this.baseInfo.designSoftware,
        softwareVersion: this.baseInfo.softwareVersion,
        groupQC: this.baseInfo.groupQC,
      };
      
      const orthoDom = this.$refs.orthoInput;
      if(orthoDom) {
        param.upperNum = orthoDom.upperValue;
        param.lowerNum = orthoDom.lowerValue;
        param.programmeRemark = this.orthodonticInfo.designRemarkContent || '';
      }

      const orthoParamDom = this.$refs.orthoDesignParamDom;
      if(orthoParamDom) {
        orthoParamDom.onSave();
        const orthoDesignParam = this.orthodonticInfo.orthoDesignParam; 
        param = { ...param, ...orthoDesignParam };
      }

      console.log('到此为止', param);
      handleToCreateProgram(param).then(res => {
        this.init();
      }).catch(err => {
        const { code, message } = err;
        if(code && message) {
          this.$hgOperateFail(message);
        }
      }).finally(() => {
        callback && callback();
      })
    },

    // 已完成订单
    saveProgramInfoInComplete(callback, orthoDesignParam) {
      if(!this.verifyOrthoInfo('createProgramBtn')) { 
        callback && callback();
        return;
      }

      let requestFileList = [];
      const compList = this.uploadCompList;
      compList.forEach(item => {
        const fileList = item.fileList;
        fileList.forEach(file => {
          const { fileName, filePath, fileSize, fileType } = file;
          requestFileList.push({ fileName, filePath, fileSize, fileType });
        });
      });

      let param = {
        orderCode: this.orderCode, 
        orderFiles: requestFileList, 
        programmeCode: this.orthodonticInfo.programCode ? this.orthodonticInfo.programCode : 0,
      };
      
      const orthoDom = this.$refs.orthoInput;
      if(orthoDom) {
        param.upperNum = orthoDom.upperValue;
        param.lowerNum = orthoDom.lowerValue;
        param.programmeRemark = this.orthodonticInfo.designRemarkContent || '';
      }

      const orthoParamDom = this.$refs.orthoDesignParamDom;
      if(orthoParamDom) {
        orthoParamDom.onSave();
        const orthoDesignParam = this.orthodonticInfo.orthoDesignParam; 
        param = { ...param, ...orthoDesignParam };
      }

      console.log('到此为止', param);
      saveProgramme(param).then(res => {
        this.init();
      }).catch(err => {
        const { code, message } = err;
        if(code && message) {
          this.$hgOperateFail(message);
        }
      }).finally(() => {
        callback && callback();
      })
    },
  },
}