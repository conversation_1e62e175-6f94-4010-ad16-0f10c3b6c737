import Vue from 'vue';
import VueRouter from 'vue-router';
import Layout from '@/layout';
import { initUserInfo, loadLocalMenu } from './identity';
import { IS_LOCAL_MODEL } from '@/public/constants/setting';
import { ROUTE_NAME, ROUTE_PATH } from '@/public/constants';
import store from '@/store';

Vue.use(VueRouter);

// 菜单-路由配置map
const RouteMap = new Map([
  [ROUTE_PATH.ORDER_DETAIL, { name: ROUTE_NAME.ORDER_DETAIL, component: '/order/detail/index.vue' }],
  [ROUTE_PATH.ORDER_LIST, { name: ROUTE_NAME.ORDER_LIST, component: '/order/list/index.vue' }],
  [ROUTE_PATH.MANAGE_USER, { name: ROUTE_NAME.MANAGE_USER, component: '/configuration/user/index.vue' }],
  [ROUTE_PATH.HEYPOINT_CUSTOMER, { name: ROUTE_NAME.HEYPOINT_CUSTOMER, component: '/heyPoint/customer/index.vue' }],
  [ROUTE_PATH.HEYPOINT_CUSTOMER_INFO, { name: ROUTE_NAME.HEYPOINT_CUSTOMER_INFO, component: '/heyPoint/customer/detail.vue' }],
  [ROUTE_PATH.HEYPOINT_SETTING, { name: ROUTE_NAME.HEYPOINT_SETTING, component: '/heyPoint/setting/index.vue' }],
  [ROUTE_PATH.HEYPOINT_LOG, { name: ROUTE_NAME.HEYPOINT_LOG, component: '/heyPoint/log/index.vue' }],
  [ROUTE_PATH.BILL, { name: ROUTE_NAME.BILL, component: '/billManager/index.vue' }],
  [ROUTE_PATH.BILL_DETAIL, { name: ROUTE_NAME.BILL_DETAIL, component: '/billManager/detail.vue' }],
  [ROUTE_PATH.DATA_BOARD, { name: ROUTE_NAME.DATA_BOARD, component: '/databoard/index.vue' }],
  [ROUTE_PATH.ORDER_UNVERIFIED, { name: ROUTE_NAME.ORDER_UNVERIFIED, component: '/order/unverified/index.vue' }],

  [ROUTE_PATH.POINTS_ALLOCATION, { name: ROUTE_NAME.POINTS_ALLOCATION, component: '/configuration/pointsAllocation/index.vue' }],
  [ROUTE_PATH.DESIGNER_POINTS, { name: ROUTE_NAME.DESIGNER_POINTS, component: '/designerPoints/index.vue' }],
  [ROUTE_PATH.MY_DESIGN_POINTS, { name: ROUTE_NAME.MY_DESIGN_POINTS, component: '/myDesignPoints/index.vue' }],
])

initUserInfo();

let hasRoleBoard = false; // 角色看板
// 加载菜单
function loadMenu() {
  const menuList = store.getters.menuList || [];
  let children = [];
  menuList.forEach((menu) => {
    const { path } = menu;
    const routeItem = RouteMap.get(path);
    if (path && routeItem) {
      const item = {
        path: path,
        name: routeItem.name,
        component: (resolve) => require([`@/views${routeItem.component}`], resolve),
      };
      children.push(item);

      if(path === ROUTE_PATH.DATA_BOARD) {
        hasRoleBoard = true;
      }
    }
  });
  return children;
}

const routerChildren = IS_LOCAL_MODEL ? loadLocalMenu() : loadMenu();

const heyPointItem = routerChildren.find(item => item.path.includes('/heyPoint'));

const defalutRouter = [
  {
    path: '/',
    name: 'Layout',
    redirect: hasRoleBoard ? ROUTE_PATH.DATA_BOARD : ROUTE_PATH.ORDER_LIST,
    hidden: true,
    component: Layout,
    children: [{
      path: '/heyPoint',
      redirect: heyPointItem? heyPointItem.path : '/heyPoint',
      hidden: true,
    },...routerChildren],
  },
  {
    path: '/404',
    name: '404',
    meta: {
      reload: true,
    },
    component: () => import('@/views/404.vue'),
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true,
  },
];

// 开发前期 先用这个
/* defalutRouter[0].children = [
  {
    path: '/order',
    name: 'OrderList',
    component: () => import('../views/order/list/index.vue'),
  },{
    path: '/order/detail',
    name: 'OrderDetail',
    component: () => import('../views/order/detail/index.vue'),
  },{
    path: '/user',
    name: 'ManageUser',
    component: () => import('../views/user/index.vue'),
  },{
    path: '/temp',
    name: 'temp',
    component: () => import('../views/temp/index.vue'),
  },
  {
    path: '/heyPoint',
    name: 'HeyPoint',
    redirect: '/heyPoint/customer'
  },{
    path: '/heyPoint/customer',
    name: 'Customer',
    component: () => import('../views/heyPoint/customer/index.vue'),
  },
  {
    path: '/heyPoint/customer/detail',
    name: 'CustomerInfo',
    component: () => import('../views/heyPoint/customer/detail.vue'),
  },
  {
    path: '/heyPoint/setting',
    name: 'BalanceSetting',
    component: () => import('../views/heyPoint/setting/index.vue'),
  },
  {
    path: '/heyPoint/log',
    name: 'OperateLobg',
    component: () => import('../views/heyPoint/log/index.vue'),
  },
  {
    path: '/billManager',
    name: 'BillManager',
    component: () => import('../views/billManager/index.vue'),
  },
  {
    path: '/billManager/detail',
    name: 'BillDetails',
    component: () => import('../views/billManager/detail.vue'),
  },
] */

// https://blog.csdn.net/weixin_43840202/article/details/108086354
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject);
  }
  return originalPush.call(this, location).catch((err) => err);
};

export default new VueRouter({
  routes: defalutRouter,
});
