<template>
  <div class="hg-radio-child-input">
    <el-radio-group 
      v-model="selectValue" 
      @change="handleChange">
      <el-radio 
        v-for="(item, index) in data.child" 
        :key="index" 
        :label="typeof item === 'string' ? item : item.name" 
        :class="{'has-child': item.child && item.child.length > 0}">
        <span>{{ getI18nName(item, i18nTitle, $getI18nText) }}</span>

        <div class="child-input" v-if="item.child && item.child.length > 0">
          <div class="child-item" v-for="(child, cIndex) in item.child" :key="cIndex+'-child-input'">
            <div v-if="hasLabel" class="child-label">
              {{ getI18nName(child, i18nTitle, $getI18nText) }}
            </div>
            <hg-select v-if="child.component === COMPONENT_TYPE.SELECT"
                v-model="child.value" 
                :data="child"
                :i18nTitle="i18nTitle"></hg-select>
            <hg-input-number
              v-else
              v-model="child.value" 
              :data="child"
              :disabled="item.name !== selectValue"></hg-input-number>
          </div>
          
        </div>

      </el-radio>
    </el-radio-group>
  </div>
</template>

<script>
import { getI18nName } from '../utils';
import HgInputNumber from './HgInputNumber';
import HgSelect from './HgSelect';
import { COMPONENT_TYPE } from '../utils/constant';

export default {
  components: { HgInputNumber, HgSelect },
  inject: ['getTupdateTimes'],
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          child: [],
        }
      }
    },
    i18nTitle: { // i18n需要拼接成i18n国际化文本，数据库就不存太长
      type: String,
      default: '',
    },
    hasLabel: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      selectValue: null,
      lastSelectValue: null,
      oldSelectChildValue: {},
      COMPONENT_TYPE
    }
  },
  computed: {
    updateTimes() { // 保存更新的次数
      return this.getTupdateTimes ? this.getTupdateTimes() : 0;
    },
  },
  watch: {
    value(newValue) {
      if(newValue !== this.selectValue) {
        this.selectValue = newValue;
      }
    },
    updateTimes(value) {
      if(value> 0) {
        this.initData(this.data);
      }
    },
  },
  mounted() {
    this.selectValue = this.value;
    this.lastSelectValue = this.value;
    this.initData();
  },
  methods: {
    getI18nName,
    handleChange(value) {
      this.resetChildValue(this.lastSelectValue);
      this.$emit('update',value);
      this.lastSelectValue = value;
    },
    // 初始化子集-值
    initData() {
      const { child } = this.data;
      if(child) {
        child.forEach(item => {
          const { child, name } = item;
          if(child && child.length > 0) {
            let childValueObject = {};
            child.forEach(cItem => {
              const { name, value } = cItem;
              childValueObject[name] = value;
            });
            this.oldSelectChildValue[name] = childValueObject;
          }
        });
      }
    },
    // 切换父节点之后，要重置上一个选择的值
    resetChildValue(oldSelect) {
      const oldChildValue = this.oldSelectChildValue[oldSelect];
      const lastSelectItem = this.data.child.find(item => item.name === oldSelect);
      if(lastSelectItem) {
        const { child } = lastSelectItem;
        if(child && child.length > 0) {
          child.forEach(cItem => {
            const oldValue = oldChildValue[cItem.name];
            cItem.value = oldValue;
          });
        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.hg-radio-child-input {

  .el-radio-group {
    display: flex;
    align-items: center;
    width: 100%;

    .el-radio {
      margin-right: 80px;
    }
  }

  .has-child  {
    display: flex;
    align-items: center;

    /deep/.el-radio__label {
      display: flex;
      align-items: center;
    }

    .child-input {
      display: flex;
      padding-left: 32px;
      .child-item {
        display: flex;
        align-items: center;
        margin-right: 40px;
        .child-label {
          width: 30%;
          margin-right: 40px;
        }
      }
    }
  }
}
</style>