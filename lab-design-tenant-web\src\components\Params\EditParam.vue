<template>
  <el-drawer
    class="edit-param-dialog"
    :visible.sync="isShow"
    center
    size="75%"
    :show-close="false"
    :append-to-body="true"
    :destroy-on-close="true"
    :modal-append-to-body="false">
    <template slot="title">
      <span class="title">{{ $t('param.title.setting') }}</span>
    </template>
    <div class="category-tab" v-if="showCategoryTab">
      <div class="tab-ul">
        <div 
          v-for="(tabItem, tIndex) in tabList" 
          :key="tIndex" 
          :class="['tab-li', selectCategoryCode === tabItem.designCode && 'is-active']"  
          @click="onSelectCategory(tabItem.designCode)">
          <p>{{ $t(`apiCommon.${tabItem.designCode}`) }}</p>
        </div>
      </div>
    </div>
    <div class="parameter-box" v-if="sourceData.length > 0">
      <select-tab v-model="selectCode" :tabList="headerList"></select-tab>
      
      <div class="scroll-box" v-if="selectData">
        <div class="parameter-content">
          <!-- 方案 -->
          <hg-card class="program-card">
            <el-collapse accordion value="program">
              <el-collapse-item name="program" :title="$t('param.title.program')">
                <hg-program :i18nTitle="`${I18N_TITLE[selectCode]}.program`" :dataList="selectData.program"></hg-program>
              </el-collapse-item>
            </el-collapse>
          </hg-card>

          <!-- 参数：需要选择设计软件 -->
          <hg-card class="parameter-card" v-if="needSelectSoftware">
            <el-collapse accordion value="parameter">
              <el-collapse-item name="parameter" :title="$t('param.title.parameter')">

                <div class="param-software">
                  <span>{{ $t('param.version') }}</span>
                  <el-select class="parameter-component" v-model="selectData.software" :placeholder="$t('component.tip.select')">
                    <el-option
                      v-for="item in softwareList" 
                      :key="item.code" 
                      :label="$t(item.label)"
                      :value="item.code">
                    </el-option>
                  </el-select>
                </div>
                <!-- 3shape -->
                <hg-parameter 
                  v-show="selectData.software === SOFTWARE.THREE_SHAPE" 
                  :designCode="selectCode"
                  :i18nTitle="`${I18N_TITLE[selectCode]}.param`" 
                  :dataList="selectData.parameter[SOFTWARE.THREE_SHAPE]"></hg-parameter>
                <!-- exo cad -->
                <hg-parameter 
                  v-show="selectData.software === SOFTWARE.EXO_CAD" 
                  :designCode="selectCode"
                  :i18nTitle="`${I18N_TITLE[selectCode]}.param`" 
                  :dataList="selectData.parameter[SOFTWARE.EXO_CAD]"></hg-parameter>
                <!-- rios design studio -->
                <hg-parameter 
                  v-show="selectData.software === SOFTWARE.RIOS_DESIGN" 
                  :designCode="selectCode"
                  :i18nTitle="`${I18N_TITLE[selectCode]}.param`" 
                  :dataList="selectData.parameter[SOFTWARE.RIOS_DESIGN]"></hg-parameter>

              </el-collapse-item>
            </el-collapse>
          </hg-card>

          <!-- 参数：不能选择设计软件 -->
          <hg-card class="parameter-card" v-else>
            <el-collapse accordion value="parameter">
              <el-collapse-item name="parameter" :title="$t('param.title.parameter')">
                <hg-parameter :designCode="selectCode" :i18nTitle="`${I18N_TITLE[selectCode]}.param`" :dataList="selectData.parameter">
                  <div class="software-content" slot="set-software">
                    <span>{{ $t('param.version') }}</span>
                    <span>{{ $t(SOFTWARE_I18N[selectData.software]) }}</span>
                  </div>
                </hg-parameter>
              </el-collapse-item>
            </el-collapse>
          </hg-card>

        </div>
      </div>
    </div>

    <div class="no-data" v-else>
      {{ $t('common.noData') }}
    </div>

  </el-drawer>
  
</template>

<script>
import { mapGetters } from 'vuex';
import { SOFTWARE, I18N_TITLE, SOFTWARE_I18N } from './utils/constant';
import SelectTab from './components/SelectTab/index.vue';
import HgParameter from './HgParameter';
import HgProgram from './HgProgram';

export default {
  name: 'EditParam',
  provide: {
    getTupdateTimes: () => 0,
  },
  components: { SelectTab, HgParameter, HgProgram },
  props: {
    /**
     * 主要不同体现在parameter对象，一个为数组，一个为Object对象
     * 修订格式：[{ designCode: 2001, program: [], parameter: [], software: 1 }]
     * 新增格式：[{ designCode: 2001, program: [], parameter: {1:[], 2: [], 3: []}, software: 1 }]
     */
    sourceData:{
      type: Array,
      default() {
        return [];
      }
    },
    needSelectSoftware: {
      type: Boolean,
      default: false,
    },
    showCategoryTab: {
      type: Boolean,
      default: true
    },
    tabList: Array,
    orderId: String
  },
  computed: {
    ...mapGetters(['oneDesignList']),
    threeDesignList() {
      return this.oneDesignList.filter(item => item.level === 3);
    },
    headerList() {
      const postList = [21304, 21305, 21306, 21307];
      let dataList = [];
      this.sourceData.forEach(item => {
        const data = this.threeDesignList.find(three => three.designCode === item.designCode);
        if(data) {
          // 如果选择的三级设计类型包含一个或多个桩核，则名称改为桩核
          if (postList.includes(data.designCode)) {
            data.cnName = '桩核'
            data.enName = 'Post & Core'
          }
          dataList.push(data);
        }
      });
      return dataList;
    },
    selectData() {
      const data = this.sourceData.find(item => item.designCode === this.selectCode);
      return data;
    }
  },
  data() {
    return {
      I18N_TITLE,
      SOFTWARE,
      SOFTWARE_I18N,

      isShow: false,
      selectCode: 0,
      selectCategoryCode: 0,

      softwareList: [
        {
          code: SOFTWARE.THREE_SHAPE,
          label: 'param.software.shape'
        },{
          code: SOFTWARE.EXO_CAD,
          label: 'param.software.exoCad'
        },{
          code: SOFTWARE.RIOS_DESIGN,
          label: 'param.software.riosDesign'
        }
      ],
    }
  },
  watch: {
    isShow(value){
      if(value) {
        this.selectCode = this.sourceData.length > 0 ? this.sourceData[0].designCode : 0;
        this.selectCategoryCode = this.tabList.length > 0 ? this.tabList[0].designCode : 0;
      }else {
        this.selectCode = 0;
        this.selectCategoryCode = 0;
        this.$emit('handleClose', { orderId: this.orderId });
      }
    },
    selectCategoryCode(code) {
      this.selectCode = this.sourceData.length > 0 ? this.sourceData[0].designCode : 0;
    }
  },
  methods: {
    onSelectCategory(code) {
      if(code !== this.selectCategoryCode) {
        this.$emit('onChangeCategory', { firstTypeCode: code, orderId: this.orderId });
        this.selectCategoryCode = code;
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.edit-param-dialog {
  .title {
    color: $hg-label;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
  }

  /deep/.el-drawer__body {
    margin: 16px;
  }

  .parameter-box {
    height: calc(100% - 24px);

    .parameter-select-tab {
      margin-bottom: 16px;
    }

    .software-content {
      margin-bottom: 24px;
      width: 100%;
      line-height: 32px;
      span {
        color: $hg-label;
        font-weight: bold;

        &:first-of-type {
          margin-right: 15%;
        }
      }
    }
  }

  .parameter-box>.scroll-box {
    width: 100%;
    height: 100%;
    overflow: hidden;

    .parameter-content {
      overflow-x: hidden;
      overflow-y: auto;
      height: calc(100% - 56px - 48px - 16px);

      .hg-card {
        background: $hg-hover;
        &:first-of-type {
          margin-bottom: 24px;
        }
      }
    }

    .param-software {
      padding-top: 24px;

      &>span {
        padding-right: 64px;
        color: $hg-label;
      }
    }

    .parameter-card .hg-parameter-box{
      padding-top: 24px;
    }
    
    .hg-parameter-box /deep/.hg-parameter-ul {
      margin-bottom: 24px;
      padding: 24px 24px 0 24px;
      border-radius: 4px;
      border: 1px solid $hg-border;
    }
  }

  .no-data {
    color: $hg-label;
    text-align: center;
    padding-top: 64px;
  }

}

.edit-param-dialog {
  .category-tab {
    margin-top: 16px;

    .tab-ul {
      display: flex;
      color: #E1E8FF;
      font-size: 16px;
      line-height: 24px;

      .tab-li {
        cursor: pointer;
        padding: 8px;

        &>p {
          padding: 4px 16px;
          font-weight: bold;
          border-radius: 4px;
          &:hover {
            background-color: #3760EA;
          }
        }
        
      }

      .tab-li.is-active {
        border-radius: 4px 4px 0px 0px;
        background: #27292E;
        &>p {
          background-color: #3760EA;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.edit-param-dialog {
  .el-drawer {
    background: $hg-main-black;
  }

  .el-drawer__header {
    margin-bottom: 0;
    padding: 0 24px;
    height: 60px;
    background: $hg-hover;
    border-bottom: 1px solid $hg-border;
  }

  .el-collapse-item__header {
    height: 20px;
    color: $hg-secondary-primary;
    font-size: 16px;
    font-weight: bold;
    
  }

  .el-drawer__body {
    overflow: hidden;
  }

  .el-collapse-item__wrap {
    margin-top: 24px;
    border-top: 1px dashed $hg-border;
  }

  .el-collapse-item__arrow {
    color: $hg-label;
    font-size: 14px;
  }

  .hg-program-box {
    .program-content {
      .children-card {
        border-radius: 4px;
        border: 1px solid $hg-border;
      }

      .child-select-card {
        border-radius: 4px;
        border: 1px solid $hg-border;
      }
    }
  }

  .hg-parameter-box {
    .hg-parameter-li {
      width: 50%;
    }
    .has-child-one-line {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .one-line {
        width: 100%;
        line-height: 20px;
        margin-bottom: 12px;
        span {
          font-weight: 700;
        }
      }
      .parameter-component {
        width: 100%;
      }
      .parameter-container {
        .parameter-child {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          .parameter-title {
            color: #E4E8F7;
            width: 16.65%;
            padding: 0 12px;
          }
        }
      }
    }
  }

  .hg-parameter-box .hg-parameter-li.on-one-line-li {
    width: 100%;
    .parameter-label {
      width: 25%;
    }
  }
}
</style>