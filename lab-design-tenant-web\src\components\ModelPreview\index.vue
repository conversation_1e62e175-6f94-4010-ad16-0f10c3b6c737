
<template>
  <div class="out-pre-model">
    <div
      v-show="!isError"
      v-loading="isLoading"
      element-loading-spinner="loading-icon el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0)"
      class="pre-model-random"
      :id="domId"
    ></div>

    <div v-if="isError" class="model-error">
      <i class="el-icon-close error-icon"></i>
      {{ errorText }}
    </div>
  </div>
</template>
<script>
import bg from '@/assets/images/modelPreview/texture.jpg';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
// import { TrackballControls } from 'three/examples/jsm/controls/TrackballControls';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { getFileUrlByS3ID } from '@/public/utils/file';
import { ArcballControls } from '@/public/utils/arcballControls';

export const newRamdomId = () => {
  return Number(Math.random().toString().substr(3) + Date.now()).toString(36);
};

export default {
  name: 'model-preview',
  components: {},
  data() {
    return {
      isLoading: false,
      animate_stop: false,
      domId: '',
      modelUrl: '',
      isError: false,
      isLoaded: false,

      scene: null,
      camera: null,
      domInfo: {
        domW: 100,
        domH: 200,
        canvasdom: null,
      },

      controls: null,
      light: null,
      mesh: null,
      isDestroy: false,
      isRotate: false,
      totalRadius: 0,
      group: null,
      clock: null,
    };
  },
  props: {
    //   错误提示文字
    errorText: {
      type: String,
      default: 'error',
    },
    //   模型预览容器的背景
    bg: {
      type: String,
      default: '#1D1D1F',
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    /* fileList: {
      handler(newVal, oldVal)  {
        console.log('newVal: ', newVal, oldVal);
        if (newVal && newVal.length && (!oldVal || oldVal.length !== newVal.length)) {
          this.getModelUrl();
        }
      },
      immediate: true
    } */
  },
  created() {
    // 创建随机id
    this.newId();
    this.newLight();
    console.log('cre',1111)
  },
  mounted() {
    try {
      this.getDomInfo();
      this.initFlow();
      // 根据路径得到s3里面的模型文件
      this.getModelUrl();
    } catch (error) {
      console.error('error: ', error);
    }
  },
  beforeDestroy() {
    if (this.mesh) {
      this.mesh.geometry.dispose();
      this.mesh.material.dispose();
    }
    this.scene = null;
    this.renderer = null;
    this.camera = null;
    if (this.animate_stop) {
      cancelAnimationFrame(this.animate_stop);
    }
    this.isDestroy = true;
  },
  methods: {
    //   生成随机id
    newId() {
      this.domId = newRamdomId();
    },
    /**
     * 隐藏模型
     * @param filePath 模型名称
     */
    toggleModelVisible(filePath, fileName) {
      // 遍历查找对象的子对象，返回name对应的对象（name是可以重名的，返回第一个）
      if (filePath) {
        const nameNode = this.scene.getObjectByName(filePath + fileName);
        if (nameNode) {
          nameNode.visible = !nameNode.visible;
          this.$emit('updateModeVisibleStatus', {
            filePath,
            visible: nameNode.visible,
            fileName: fileName,
          });
        }
      }
    },

    /**
     * 重置模型的显示状态
     */
    resetVisibleStatus() {
      const self = this;
      this.scene.traverse(function (obj) {
        if (obj.type === 'Mesh') {
          obj.visible = true;
          self.controls.reset();
        }
      });
    },

    /**
     * 获取模型文件地址
     */
    async getModelUrl() {
      try {
        const results = await Promise.all(
          this.fileList.map(async (item) => {
            const { viewUrl, filePath, fileName, orgCode } = item;
            if(viewUrl) { // 已经获取下载地址则不再请求
              return {
                url: viewUrl,
                filePath: filePath,
                fileName: fileName,
              }
            }else {
              // 等待异步操作完成，返回执行结果
              const url = await getFileUrlByS3ID({s3FileId: filePath, orgCode: orgCode});
              item.viewUrl = url;
              return {
                url,
                filePath: filePath,
                fileName: fileName,
              };
            }
          })
        );
        this.$nextTick(() => {
          let manager = this.initManager();
          let loader = new STLLoader(manager);

          this.group = new THREE.Group();

          results.forEach((item) => {
            // 几何体对象Geometry
            let handler = (geometry) => {
              if (this.isDestroy) {
                return false;
              }
              this.newMesh(geometry, item.filePath + item.fileName);
              this.group.add(this.mesh);

              const box3 = new THREE.Box3();
              // 计算层级模型group的包围盒
              // 模型group是加载一个三维模型返回的对象，包含多个网格模型
              box3.expandByObject(this.group);
              // 计算一个层级模型对应包围盒的几何体中心在世界坐标中的位置
              const center = new THREE.Vector3();
              box3.getCenter(center);

              // 重新设置模型的位置，使之居中。
              this.group.position.x = this.group.position.x - center.x;
              this.group.position.y = this.group.position.y - center.y;
              this.group.position.z = this.group.position.z - center.z;
            };
            this.scene.add(this.group);
            loader.load(item.url, handler);
          });
        });
      } catch (error) {
        console.error('error: ', error);
      }
    },

    /**
     * 初始化
     */
    initFlow() {
      this.initScene();
      this.scene.add(this.light);
      this.initRenderer();
      this.bingMouseCtrl();
      this.animate();
    },

    /**
     * 其功能是处理并跟踪已加载和待处理的数据。
     */
    initManager() {
      let manager = new THREE.LoadingManager();
      manager.onStart = (item, loaded, total) => {
        this.isLoading = true;
      };
      manager.onLoad = () => {
        this.isLoading = false;
      };
      manager.onProgress = (item, loaded, total) => {};
      manager.onError = (url) => {
        console.error('error');
        this.isError = true;
        this.$emit('error');
      };
      return manager;
    },

    /**
     * 获取节点信息
     */
    getDomInfo() {
      (this.domInfo.domW = document.getElementById(this.domId).clientWidth),
        (this.domInfo.domH = document.getElementById(this.domId).clientHeight);
      this.domInfo.canvasdom = document.getElementById(this.domId);
    },

    /**
     * 构建光源
     */
    newLight() {
      this.light = new THREE.DirectionalLight(0xe1dbc1, 1);
      let light1 = this.light;
      light1.position.set(200, 200, 200).normalize();
      //light1.castShadow = true;
      light1.shadow.camera.near = 2;
      light1.shadow.camera.far = 1000;
      light1.shadow.camera.left = -600;
      light1.shadow.camera.right = 1000;
      light1.shadow.camera.top = 600;
      light1.shadow.camera.bottom = -600;
      light1.shadow.mapSize.height = 10;
      light1.shadow.mapSize.width = 10;
      light1.distance = 1000;
    },

    /**
     * 环境光会均匀的照亮场景中的所有物体。但是环境光不能用来投射阴影，因为它没有方向。
     */
    newAmbientLight() {
      const ambientLight = new THREE.AmbientLight(0xe1dbc1);
      this.scene.add(ambientLight);
    },

    /**
     * 构建材料
     * @param geometry 几何体对象Geometry
     * @param filePath s3文件id
     */
    newMesh(geometry, filePath) {
      // let meshMaterial = new THREE.MeshPhongMaterial({
      //   emissive: 0x363636,
      //   transparent: true
      // });
      // TextureLoader创建一个纹理加载器对象，可以加载图片作为几何体纹理
      // 加载texture的一个类。 内部使用ImageLoader来加载文件
      const textureloader = new THREE.TextureLoader();
      let platformMatcap = textureloader.load(bg, (groundTexture) => {
        // 如果设置, 那么它期望所有纹理和颜色需要乘以gamma输出。
        groundTexture.encoding = THREE.sRGBEncoding;
      });
      // MeshMatcapMaterial 由一个材质捕捉（MatCap，或光照球（Lit Sphere））纹理所定义，其编码了材质的颜色与明暗。
      const meshMaterial = new THREE.MeshMatcapMaterial({
        // 材质的颜色(Color)，默认值为白色 (0xffffff)。
        color: 0xffffff,
        transparent: true, //设置材质是否透明
        // side: FrontSide, //定义渲染哪一面
        // 定义材质是否使用平面着色进行渲染。默认值为false。
        flatShading: false,
        // 在0.0 - 1.0的范围内的浮点数，表明材质的透明度。值0.0表示完全透明，1.0表示完全不透明。
        opacity: 1,
        // matcap贴图
        matcap: platformMatcap,
      });
      //绑定盒子模型
      geometry.computeBoundingBox();
      // 计算当前几何体的的边界球形，该操作会更新已有 [param:.boundingSphere]。
      // 边界球形不会默认计算，需要调用该接口指定计算边界球形，否则保持默认值 null。
      geometry.computeBoundingSphere();
      // meshMaterial.shading =THREE.SmoothShading;
      meshMaterial.side = THREE.DoubleSide;
      this.mesh = new THREE.Mesh(geometry, meshMaterial);
      let mesh = this.mesh;
      this.totalRadius += geometry.boundingSphere.radius;

      mesh.receiveShadow = true; //允许物体投射阴影
      mesh.castShadow = true; //允许某物体接收阴影
      mesh.name = filePath;
      // 根据边界矩形将几何体居中。
      if (this.fileList.length === 1) {
        mesh.geometry.center();
      } else {
        const width = this.totalRadius;
        mesh.position.set(width, 0, 0);
        mesh.scale.set(0.5, 0.5, 0.5);
      }

      // mesh.rotation.set(Math.PI / 2, -Math.PI, 0);

      // 立方体网格模型整体缩小0.5倍，相当于xyz三个方向分别缩小0.5倍
      //   mesh.scale.set(.2, .2, .2);
      // 旋转角度
      // mesh.rotation.x = 1.9
      // mesh.rotation.y = -1.5
      // mesh.rotation.z = -1.5
    },

    /**
     * 初始化坐标格
     */
    initGrid() {
      // 坐标格辅助对象. 坐标格实际上是2维线数组.
      // let gridHelper = new THREE.GridHelper(4000, 140, '#ddd', '#ddd')
      // gridHelper.rotation.z = -1.5
      let gridHelper = new THREE.GridHelper(160, 10, '#ddd', '#ddd');
      gridHelper.rotation.x = Math.PI / 2;
      this.scene.add(gridHelper);
    },

    /**
     * 初始化场景并且把相机添加到场景
     */
    initScene() {
      let domW = this.domInfo.domW;
      let domH = this.domInfo.domH;
      let radius = 10;
      this.scene = new THREE.Scene();
      this.camera = new THREE.PerspectiveCamera(
        30,
        domW / domH,
        1,
        radius * 100
      ); //透视投影
      // camera = new THREE.OrthographicCamera(-domW/10, domW/10, domH/8, -domH/8, -500,1000);//正交头影
      //camera.position.z = radius * 10;
      //camera.position.y = radius * 10;
      // camera.position.z = 0;
      // camera.position.y = 0;
      this.camera.position.set(0, 0, 300);
      this.camera.lookAt(new THREE.Vector3(0, 0, 0));
      // camera.lookAt(0, 0, 0);
      this.camera.lookAt(this.scene.position);
      this.scene.add(this.camera);

      // 用于模拟方向的3维箭头对象.
      // var dir = new THREE.Vector3( 1, 2, 0 );
      // //normalize the direction vector (convert to vector of length 1)
      // dir.normalize();
      // var origin = new THREE.Vector3( 0, 0, 0 );
      // var length = 50;
      // var hex = 0xffff00;
      // var arrowHelper = new THREE.ArrowHelper( dir, origin, length, hex );
      // this.scene.add( arrowHelper );

      // 增加x,y,z轴辅助线 红色代表 X 轴. 绿色代表 Y 轴. 蓝色代表 Z 轴.
      // var axesHelper = new THREE.AxesHelper( 150 );
      // this.scene.add( axesHelper );

      // 模拟3维包围盒 Box3 的辅助对象.
      // var box = new THREE.Box3();
      // box.setFromCenterAndSize( new THREE.Vector3( 0, 0, 0 ), new THREE.Vector3( 50, 50, 50 ) );
      // var helper = new THREE.Box3Helper( box, 0xffff00 );
      // this.scene.add( helper );

      // 初始化坐标格
      // this.initGrid()

      this.newAmbientLight();
    },

    /**
     * 初始化渲染器
     */
    initRenderer() {
      //设置渲染器
      this.renderer = new THREE.WebGLRenderer({
        antialias: true, //是否开启反锯齿
        alpha: true, // canvas是否包含alpha (透明度)。默认为 false
        precision: 'highp', //着色精度选择
        premultipliedAlpha: false, // renderer是否假设颜色有 premultiplied alpha. 默认为true,表示是否可以设置像素深度（用来度量图像的分辨率）
        preserveDrawingBuffer: true, //绘画缓存 是否保留缓存到手动清除或被覆盖。 默认false.
        stencil: false, // 绘图缓存是否有一个至少8位的模板缓存(stencil buffer)。默认为true
        maxLights: 1, // 最大灯光数，我们的场景中最多能够添加多少个灯光
        // stencil: false // false/true 表示是否使用模板字体或图案
      });
      const renderer = this.renderer;
      // renderer.shadowMapEnabled = true;
      renderer.shadowMapSoft = true; //柔和阴影
      // renderer.setFaceCulling(THREE.CullFaceNone); //剔除前面,剔除模式，
      // renderer.gammaInput = true // 如果设置，那么所有的纹理和颜色都会预乘gamma。 默认值是false. 最近版本已废弃
      // renderer.gammaOutput = true // 如果设置, 那么它期望所有纹理和颜色需要乘以gamma输出。 默认值false. 最近版本已废弃
      // 如果设置,那么所有的纹理和颜色都会预乘gamma 新版本写法
      renderer.outputEncoding = THREE.sRGBEncoding;
      renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比。通常用于避免HiDPI设备上绘图模糊
      renderer.setSize(this.domInfo.domW, this.domInfo.domH); // 将输出canvas的大小调整为(width, height)并考虑设备像素比，且将视口从(0, 0)开始调整到适合大小 将updateStyle设置为false以阻止对canvas的样式做任何改变。

      renderer.setClearColor(this.bg); //设置背景颜色 设置颜色及其透明度
      this.domInfo.canvasdom.appendChild(renderer.domElement);
      // this.addOrbitControls();
      // this.clock = new THREE.Clock();
      // this.controls = new OrbitControls(this.camera, renderer.domElement);
      // this.controls.minDistance = 100;
      // this.controls.maxDistance = 1000;
      // 将其设为true，以自动围绕目标旋转。请注意，如果它被启用，你必须在你的动画循环里调用.update()。
      // this.controls.autoRotate = true;
      // this.bingMouseCtrl()
    },

    /**
     * 新增轨道控制器
     */
    addOrbitControls() {
      // 该对象用于跟踪时间。
      this.clock = new THREE.Clock();
      // 将要被控制的相机.该相机不允许是其他任何对象的子级，除非该对象是场景自身。
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    },

    // 绑定鼠标的一些操作
    bingMouseCtrl() {
      this.controls = new ArcballControls(this.camera, this.renderer.domElement, this.scene);
      this.controls.rotateSpeed = 6.0;
      this.controls.target.set(0, 0, 0);
      this.controls.enableDamping = true;
      this.controls.dampingFactor = 10;
      this.controls.enablePan = true;
      this.controls.enableAnimations = true;
      this.controls.wMax = 5;
      this.controls.setGizmosVisible(false);
    },

    /**
     * 渲染
     */
    renderObj() {
      if (!this.renderer.autoClear) this.renderer.clear();
      //光源随相机的移动而移动
      this.light.position.set(
        this.camera.position.x,
        this.camera.position.y,
        this.camera.position.z
      );

      this.renderer.render(this.scene, this.camera);
    },

    /**
     * 动画
     */
    animate() {
      if (this.animate_stop) {
        cancelAnimationFrame(this.animate_stop);
      }
      this.animate_stop = requestAnimationFrame(this.animate);
      if (this.mesh && this.isRotate) {
        this.mesh.rotation.y += 0.014;
        this.mesh.rotation.x -= 0.01;
      }

      this.renderObj();
    },
  },
};
</script>
<style lang="scss" scoped>
.out-pre-model {
  overflow: hidden;
  border-radius: 4px;
  .pre-model-random {
    height: 320px;
    border-radius: 4px;
  }

  .model-error {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 320px;
    font-size: 12px;
    color: #dc5050;
    line-height: 18px;
    .error-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 3px;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      margin-right: 8px;
      background: #dc5050;
      color: #fff;
      font-weight: bold;
    }
  }
}
</style>
<style lang="scss">
.out-pre-model {
  .loading-icon {
    content: '';
    color: transparent;
    width: 32px;
    height: 32px;
    background: url('~@/assets/images/modelPreview/icon_loading.png') center
      center no-repeat;
    background-size: 32px 32px;

    animation: rotateCircle 1s infinite;
    animation-timing-function: linear;
  }
  @keyframes rotateCircle {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
</style>