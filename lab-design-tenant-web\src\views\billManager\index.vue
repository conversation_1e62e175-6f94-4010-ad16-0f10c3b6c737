<template>
  <div class="bill-list">
    <filterComponent
      ref="filter"
      :conditions="conditions"
      @searchData="searchData"
    >
      <div class="bill-down-btn">
        <hg-button
          type="primary"
          @click="exportBill"
          style="margin-right: 10px"
          >{{ lang("exportBill") }}</hg-button
        >
        <hg-button
          type="primary"
          :loading="pushSaleOrderLoading"
          :disabled="billselectSelection.length == 0"
          v-permission="['pushSaleOrder']"
          @click="pushSaleOrder"
          style="margin-right: 10px"
          >{{ lang("pushSaleOrder") }}</hg-button
        >
        <hg-button
          type="primary"
          :disabled="billselectSelection.length == 0"
          @click="batchDownLoadBill"
          >{{ lang("multiDownload") }}</hg-button
        >
      </div>
    </filterComponent>
    <hg-table
      class="bill-table"
      maxHeight="100%"
      :data="tableInfo.data"
      :header-data="tableInfo.headerData"
      :loading="tableInfo.loading"
      :needSelect="true"
      @update-selected-rows="selectBillTable"
      v-bind="$attrs"
    >
      <!-- 账单状态 -->
      <template #status="{ row }">
        <span style="color: #fbaa0e" v-if="row.status == 2">{{
          lang("leftDrawer.draft")
        }}</span>
        <span style="color: #33c9ff" v-if="row.status == 1">{{
          lang("leftDrawer.unconfirmed")
        }}</span>
        <span style="color: #eb6f70" v-if="row.status == 3">{{
          lang("leftDrawer.chanllenged")
        }}</span>
        <span style="color: #f3f5f7" v-if="row.status == 0">{{
          lang("leftDrawer.confirm")
        }}</span>
      </template>

      <!-- 币种 -->
      <template #settlementCurrency="{ row }">
        <span v-if="row.settlementCurrency == 0">{{
          lang("leftDrawer.usd")
        }}</span>
        <span v-if="row.settlementCurrency == 1">{{
          lang("leftDrawer.cny")
        }}</span>
        <span v-if="row.settlementCurrency == 2">{{
          lang("leftDrawer.eur")
        }}</span>
        <span v-if="row.settlementCurrency == 3">{{
          lang("leftDrawer.jpy")
        }}</span>
        <span v-if="row.settlementCurrency == 4">{{
          lang("leftDrawer.aud")
        }}</span>
        <hg-icon
          v-if="row.isChangeCurrency"
          style="color: #fbaa0e"
          icon-name="icon-CurrencyExchange"
        ></hg-icon>
      </template>
      <!-- 应收金额 -->
      <template #amountPayable="{ row }">
        <span style="color: #1cce83">{{ row.amountPayable | capitalize }}</span>
        <span style="margin: 0px 5px">|</span>
        <span style="color: #e18b11">{{
          row.amountPayableTax | capitalize
        }}</span>
      </template>
      <!-- 赠送抵扣 -->
      <template #giftPoint="{ row }">
        <span>{{ row.giftPoint | capitalize }}</span>
      </template>
      <!-- 免单金额 -->
      <template #freePoint="{ row }">
        <span>{{ row.freePoint | capitalize }}</span>
      </template>

      <!-- 订单总额 -->
      <template #totalPoint="{ row }">
        <span>{{ row.totalPoint | capitalize }}</span>
      </template>
      <!-- 账单详情 -->
      <template #details="{ row }">
        <img
          class="details-icon"
          src="~@/assets/images/bill/icon_bill_details.svg"
          alt=""
          @click="toDetailPage(row)"
        />
      </template>
      <template #pushCRM="{ row }">
        <span v-if="row.pushStatus == 0">{{ lang("leftDrawer.nosent") }}</span>
        <span
          v-if="row.pushStatus == 1"
          style="color: #1cce83"
          @click="openOrderNameDialog(row)"
          >{{ lang("leftDrawer.sent") }}</span
        >
        <span
          v-if="row.pushStatus == 2"
          class="error-row"
          @click="openErrorDialog(row)"
          >{{ lang("leftDrawer.faile") }}</span
        >
        <span v-if="row.pushStatus == 3" style="color: #fbaa0e">{{
          lang("leftDrawer.deleteError")
        }}</span>
      </template>

      <template #pushUpdateCRM="{ row }">
        <span v-if="row.pushCrmUpdateStatus == 0">{{
          lang("leftDrawer.nosent")
        }}</span>
        <span v-if="row.pushCrmUpdateStatus == 1" style="color: #1cce83">{{
          lang("leftDrawer.sent")
        }}</span>
        <span v-if="row.pushCrmUpdateStatus == 2" class="error-row">{{
          lang("leftDrawer.faile")
        }}</span>
        <!-- <span v-if="row.pushCrmUpdateStatus == 3" style="color:#FBAA0E;">{{lang('leftDrawer.deleteError')}}</span> -->
      </template>
    </hg-table>
    <billdrawer
      :drawer.sync="drawer"
      :detailRow="detailRow"
      @submitSuccess="submitSuccess"
    ></billdrawer>
    <pagination
      class="table-footer"
      :total="tableInfo.total"
      :initPageIndex="tableInfo.page"
      :initPageSize="tableInfo.pageSize"
      :disabled="tableInfo.loading"
      @onSearch="searchData"
    ></pagination>
    <el-dialog
      :title="lang('leftDrawer.errordialog')"
      :visible.sync="pushErrorVisible"
      width="600px"
      custom-class="error-dialog"
    >
      <div class="error-list">
        <p class="error-icon"><span class="el-icon-circle-close"></span></p>
        <div class="content">
          <p class="error-tips" v-for="(item, idx) in errorArr" :key="idx">
            {{ item }}
          </p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="pushErrorVisible = false">{{
          lang("leftDrawer.know")
        }}</el-button>
      </span>
    </el-dialog>

    <el-dialog
      :title="lang('leftDrawer.sent')"
      :visible.sync="pushSuccessVisible"
      width="600px"
      custom-class="error-dialog"
    >
      <div class="error-list">
        <div class="content">
          <p class="error-tips">{{ orderName }}</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="pushSuccessVisible = false">{{
          lang("leftDrawer.know")
        }}</el-button>
      </span>
    </el-dialog>

    <!-- 批量下推失败 -->
    <el-dialog
      :title="lang('leftDrawer.reminding')"
      :visible.sync="pushSaleNoReceiverVisible"
      width="600px"
      class="error-custom-table"
    >
      <div>
        <p
          style="
            color: #fe9b0e;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
          "
        >
          {{ lang("leftDrawer.crmNoContactError") }}
        </p>
        <div class="error-custom-table">
          <div class="table-header">
            <div class="table-cell">{{ lang("info.number") }}</div>
            <div class="table-cell">{{ lang("customerName") }}</div>
            <div class="table-cell">{{ lang("customerSn") }}</div>
          </div>
          <div class="table-body">
            <div
              v-for="(item, index) in noReceiverErrorArr"
              :key="index"
              class="table-row"
            >
              <div class="table-cell">{{ index + 1 }}</div>
              <div class="table-cell">{{ item.orgName }}</div>
              <div class="table-cell">{{ item.orgSn }}</div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="pushSaleNoReceiverVisible = false">{{
          lang("leftDrawer.know")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getLang } from "@/public/utils";
import filterComponent from "./components/filter.vue";
import hgTable from "@/components/HgTable";
import pagination from "@/components/Pagination";
import {
  getBillManagerList,
  searchManualBill,
  getPushCrmErrorInfo,
  exportManualBill,
  batchPushCrm,
  getManualBillById,
} from "@/api/heypoint";
import { newbillStatus, capitalize } from "@/filters/heypoint";
import billdrawer from "./components/billdrawer.vue";
import { getBatchDownloadUrl, getDownloadUrl } from "@/api/order";
import {
  directDown,
  createIFrameDownLoad,
  createIFrameDownLoad2,
} from "@/public/utils/file";
import { mapGetters } from "vuex";
export default {
  name: "HeyPointCustomer",
  components: {
    filterComponent,
    hgTable,
    pagination,
    billdrawer,
  },
  filters: {
    newbillStatus,
    capitalize,
  },
  data() {
    return {
      tableInfo: {
        total: 0,
        pageSize: 20,
        page: 1,
        loading: false,
        sortType: "",
        orderByColumn: "",
        data: [],
        downBtnloading: false,
        headerData: [
          {
            prop: "orgName",
            getLabel: () => {
              return this.lang("customerName");
            },
          },
          {
            prop: "orgSn",
            getLabel: () => {
              return this.$t("heypoint.customer.customerNo");
            },
          },
          {
            prop: "billDate",
            getLabel: () => {
              return this.lang("billDate");
            },
          },
          {
            prop: "settlementCurrency",
            width: "120px",
            getLabel: () => {
              return this.lang("currency");
            },
          },
          {
            prop: "totalPoint",
            getLabel: () => {
              return this.lang("total");
            },
          },
          {
            prop: "giftPoint",
            getLabel: () => {
              return this.lang("deduction");
            },
          },
          {
            prop: "freePoint",
            getLabel: () => {
              return this.lang("freeorder");
            },
          },
          {
            prop: "amountPayable",
            getLabel: () => {
              return this.lang("payable");
            },
          },
          {
            prop: "status",
            getLabel: () => {
              return this.lang("billStatus");
            },
          },
          {
            prop: "pushCRM",
            getLabel: () => {
              return this.lang("leftDrawer.sendcrm");
            },
          },
          {
            prop: "pushUpdateCRM",
            getLabel: () => {
              return this.lang("leftDrawer.pushUpdateCRM");
            },
          },
          {
            prop: "details",
            getLabel: () => {
              return this.lang("billdetails");
            },
          },
        ],
      },
      pushSaleOrderLoading: false,
      conditions: {
        orgName: "",
        status: "",
        billType: 2,
        dayTime: [],
        monthTime: [],
        weekTime: [],
        currency: "", //币种
        notZero: "", // 隐藏零元账单
        pushStatus: "", //推送状态
        updateOrderStatus: "",
        isChangeCurrency: "", //仅币种变更账单
      },
      drawer: false,
      detailRow: null,
      billselectSelection: [],
      pushErrorVisible: false,
      pushSuccessVisible: false,
      pushSaleNoReceiverVisible: false,
      errorArr: [],
      orderName: "",
      noReceiverErrorArr: [
        {
          orgCode: 1111,
          orgName: "12321",
          orgSn: "sdfa",
        },
      ],
    };
  },
  watch: {
    drawer(newValue, oldValue) {
      if (!newValue) {
        this.getList();
      }
    },
  },
  computed: {
    ...mapGetters(["language"]),
  },
  created() {
    this.setTime();
    if (this.$route.query && this.$route.query.billNo) {
      this.updateCondition();
    }
    this.$nextTick(() => {
      if (this.$refs.filter) {
        this.$refs.filter.typeChange(this.conditions.billType);
      }
    });
  },
  methods: {
    lang: getLang("bill"),

    /**
     * 更新过滤项参数
     */
    updateCondition() {
      const query = this.$route.query;
      console.log("query: ", query);
      const keys = ["billType", "billStatus"];
      const dateKeys = ["dayTime", "monthTime", "weekTime"];
      const tableKeys = ["page", "pageSize"];
      const newCondition = {};
      Object.keys(query).forEach((item) => {
        const value = query[item];
        if (value) {
          if (keys.includes(item)) {
            newCondition[item] = Number(value);
          } else if (dateKeys.includes(item) && value.length) {
            newCondition[item] = [
              new Date(Number(value[0])),
              new Date(Number(value[1])),
            ];
          } else if (tableKeys.includes(item)) {
            this.tableInfo[item] = Number(value);
          } else {
            newCondition[item] = value;
          }
        }
      });
      this.conditions = { ...this.conditions, ...newCondition };
      console.log("this.conditions: ", this.conditions);
    },

    /**
     * 跳转至详情页
     * @param row 当前行信息
     */
    async toDetailPage(row) {
      this.detailRow = row;
      this.drawer = true;
    },

    /**
     * 验证搜索条件
     */
    validateConditions() {
      console.log("this.conditions: ", this.conditions);
      // 月账单
      const date = this.conditions.monthTime;
      if (!date || !date.length) {
        const msg = this.lang("selectTimeTips");
        this.$message.error(msg);
        return false;
      }
      if (date[0] > date[1]) {
        const msg = this.lang("timeLimitTip");
        this.$message.error(msg);
        return false;
      } else {
        if (date[1] - date[0] >= 365 * 24 * 60 * 60 * 1000) {
          const msg = this.lang("monthBillDateTips");
          this.$message.error(msg);
          return false;
        }
        this.conditions.monthTime[1] = new Date(
          date[1].getFullYear(),
          date[1].getMonth() + 1,
          0,
          23,
          59,
          59
        );
      }
      return true;
    },

    /**
     * 设置默认时间
     */
    setTime() {
      // 获取当月第一天和最后一天
      const year = new Date().getFullYear();
      const month = new Date().getMonth();

      const startMonth = new Date(year, month - 6, 1);
      const endMonth = new Date(year, month, 0, 23, 59, 59);
      this.conditions.monthTime = [startMonth, endMonth];
    },
    // 获取该天距离上周日多少天
    getDateWeek() {
      let nowTime = new Date();
      let day = nowTime.getDay();
      let deltaDay;
      if (day == 0) {
        deltaDay = 6 + 1;
      } else {
        deltaDay = day - 1 + 1;
      }
      return deltaDay;
    },
    /**
     * 搜索
     * @param type 类型
     * @param conditions 搜索条件
     */
    searchData(type, conditions) {
      if (type === "filter") {
        this.tableInfo.page = 1;
      } else if (type === "page") {
        this.tableInfo.page = conditions.pageIndex;
        this.tableInfo.pageSize = conditions.pageSize;
      }
      this.getList();
    },

    /**
     * 获取列表
     */
    async getList() {
      try {
        if (!this.validateConditions()) {
          return false;
        }

        this.tableInfo.loading = true;
        const params = {
          orgName: this.conditions.orgName,
          status: this.conditions.status,
          pageSize: this.tableInfo.pageSize,
          pageNum: this.tableInfo.page,
          startTime: null,
          endTime: null,
          currency: this.conditions.currency,
          notZero: this.conditions.notZero == "1" ? true : false,
          pushStatus: this.conditions.pushStatus,
          updateOrderStatus: this.conditions.updateOrderStatus,
          changeCurrency:
            this.conditions.isChangeCurrency == "1" ? true : false,
        };

        params.startTime =
          new Date(this.conditions.monthTime[0]).getTime() / 1000;
        params.endTime =
          new Date(this.conditions.monthTime[1]).getTime() / 1000;
        const { data } = await searchManualBill(params);
        this.tableInfo.total = data.total;
        // data.records.forEach(record => {
        //   // 为每个对象添加新的属性
        //   record.pushCrmUpdateStatus = 0; // 你可以根据需要设置默认值
        // });
        this.tableInfo.data = data.records;
        this.billselectSelection = [];
        console.log("data: ", data);
      } catch (error) {
        console.log("error: ", error);
      } finally {
        this.tableInfo.loading = false;
      }
    },
    // 提交成功
    submitSuccess(type, row) {
      if (type == "error") {
        this.getPushCrmErrorInfo(row);
        return;
      }
      // this.getList()
    },
    // 批量选择订单
    selectBillTable(length, selection) {
      this.billselectSelection = selection;
    },
    // 批量下载
    batchDownLoadBill() {
      this.downBtnloading = true;
      let data = [];
      this.billselectSelection.forEach((item) => {
        let obj = {
          s3FileId: item.manualS3Id,
          filename: `${item.orgName}_【${item.billDate}】`,
        };
        data.push(obj);
      });
      getBatchDownloadUrl(data).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item, index) => {
            setTimeout(() => {
              createIFrameDownLoad2(
                item.url,
                (index + 1) * 4000,
                res.data.length,
                index
              );
            }, index * 100);
          });
          setTimeout(() => {
            this.downBtnloading = false;
          }, 3000 * res.data.length - 1);
        }
      });
    },
    async pushSaleOrder() {
      let params = [];
      this.billselectSelection.forEach((item) => {
        params.push(item.id);
      });
      try {
        this.pushSaleOrderLoading = true;
        const { data } = await batchPushCrm(params);
        this.pushSaleOrderLoading = false;
        this.getList();
        if (data && data.length > 0) {
          this.noReceiverErrorArr = data;
          this.pushSaleNoReceiverVisible = true;
        }
      } catch (error) {
        this.pushSaleOrderLoading = false;
        if (error.code == '11010038') {
          let str =
            this.language == 'zh'
              ? JSON.parse(error.message).zh
              : JSON.parse(error.message).en;
          this.$confirm(str, this.lang("leftDrawer.reminding"), {
            confirmButtonText: this.$t("common.btn.confirm"),
            cancelButtonText: this.$t("common.btn.cancel"),
            type: "warning",
          })
            .then(() => {
              this.splitLoading = false;
            })
            .catch(() => {
              this.splitLoading = false;
            });
        }
      }
    },

    async exportBill() {
      const params = {
        orgName: this.conditions.orgName,
        status: this.conditions.status,
        pageSize: this.tableInfo.pageSize,
        pageNum: this.tableInfo.page,
        startTime: null,
        endTime: null,
        currency: this.conditions.currency,
        notZero: this.conditions.notZero == "1" ? true : false,
        pushStatus: this.conditions.pushStatus,
        changeCurrency: this.conditions.isChangeCurrency == "1" ? true : false,
      };
      params.startTime =
        new Date(this.conditions.monthTime[0]).getTime() / 1000;
      params.endTime = new Date(this.conditions.monthTime[1]).getTime() / 1000;
      const { data } = await exportManualBill(params);
      // var data = '6be72da5cfe54e3c83000a96684f9c34';
      let obj = {
        s3FileId: data,
        filename: "账单列表",
      };
      getDownloadUrl(obj).then((res) => {
        if (res.code == 200) {
          createIFrameDownLoad(res.data.url, 2 * 100);
        }
      });
    },
    // 推送失败弹窗
    openErrorDialog(row) {
      this.getPushCrmErrorInfo(row);
    },
    openOrderNameDialog(row) {
      this.pushSuccessVisible = true;
      if (this.language == "zh") {
        this.orderName = "销售订单号：" + row.crmOrderNo;
      } else {
        this.orderName = "Order：" + row.crmOrderNo;
      }
    },
    // 获取失败原因
    getPushCrmErrorInfo(row) {
      let data = {
        billNo: row.billNo,
        orgCode: row.orgCode,
      };
      getPushCrmErrorInfo(data).then((res) => {
        if (res.code == 200) {
          this.pushErrorVisible = true;
          if (this.language == "zh") {
            this.errorArr = res.data.zh;
          } else {
            this.errorArr = res.data.en;
          }
        }
      });
    },
  },
};
</script>

<style lang="scss">
.bill-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  .bill-table {
    flex: 1;
    max-height: calc(100% - 58px - 60px- 35px);
    .org-name {
      cursor: pointer;
      color: #4477fb;
    }
    .details-icon {
      width: 24px;
      height: 24px;
      // cursor: pointer;
    }
    .error-row {
      cursor: pointer;
      color: #eb6f70;
      text-decoration: underline;
    }
  }
  .table-footer {
    border-top: 1px solid $hg-border;
  }
}
.error-dialog {
  .error-list {
    display: flex;
    .error-icon {
      margin-right: 16px;
      color: #eb6f70;
    }
    .content {
      display: flex;
      flex-direction: column;
      .error-tips {
        color: #fff;
        line-height: 20px;
      }
    }
  }
}

.error-custom-table {
  color: white; /* 设置文本颜色为白色 */
  border-collapse: collapse; /* 合并边框 */
  width: 100%; /* 设置宽度 */
  margin-top: 20px;
}

.table-header {
  display: flex; /* 使用 flexbox 布局 */
  background-color: #333; /* 表头背景颜色 */
}

.table-body {
  display: flex;
  flex-direction: column; /* 垂直排列行 */
}

.table-row {
  display: flex; /* 使用 flexbox 布局 */
  padding: 10px; /* 行内边距 */
  border-bottom: 1px solid #444; /* 行底部边框 */
}

.table-cell {
  flex: 1; /* 每个单元格均分宽度 */
  padding: 10px; /* 单元格内边距 */
}

// .table-row:hover {
//   background-color: #444; /* 鼠标悬停时的背景颜色 */
// }
</style>