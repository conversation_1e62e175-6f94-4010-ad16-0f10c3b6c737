<template>
  <div class="home">
    <div class="content">
      <router-view></router-view>
    </div>
  </div>
</template>
<script>
// import DeviceList from "../DeviceList/DeviceList.vue";
// DeviceList,
export default {
  name: "Home",
  components: {
  },
  mounted() {
  },
  methods: {
  },
  data() {
    return {
      navList: [
        {}
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.home {
  width: 100%;
  height: 100%;
  .content {
    width: 100%;
    height: 100%;
  }
}
</style>
