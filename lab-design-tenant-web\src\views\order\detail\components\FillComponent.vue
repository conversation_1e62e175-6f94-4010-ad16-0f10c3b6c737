<template>
  <hg-card class="fill-component">
    <div class="header">
      <span class="title">{{ titleName }}<span> *</span></span>
      <div v-if="canDelete" class="btn">
        <span class="delete" @click.stop="deleteSelectComp"><hg-icon icon-name="icon-delete-lab"></hg-icon> {{ $t('common.btn.delete') }}</span>
        <span class="add" @click.stop="addSelectComp"><hg-icon icon-name="icon-add-lab"></hg-icon>{{ $t('common.btn.add') }}</span>
      </div>
    </div>

    <div class="select-box">
      <div :class="['select-item']" v-for="item in selectCompList" :key="item.key">
        <div class="select-item-left">
          <el-checkbox v-if="canDelete" v-model="item.isSelect"></el-checkbox>
          <span>{{ $t('order.detail.tips.selectRPD') }}</span>
        </div>
        <div class="select-item-right">
          <el-select
            :placeholder="$t('order.detail.tips.pleaseSelectRPD')"
            popper-class="select-item-select"
            v-model="item.value">

            <el-option
              v-for="(item, itemIndex) in selectDataList"
              :key="itemIndex"
              :label="item.name"
              :value="item.designCode">
              <!-- <el-tooltip v-if="item.iconUrl" popper-class="select-item-tips" effect="light" placement="left" :visible-arrow="false">
                <template slot="content">
                  <img :src="'~@/assets/images/order/'+item.iconUrl" alt="">
                </template>
                <span>{{ item.name }}</span>
              </el-tooltip> -->
              <span>{{ item.name }}</span>

            </el-option>
          </el-select>
          <span class="error-msg" v-show="!item.value">{{ $t('order.detail.tips.selectRPDNotEmpty') }}</span>
        </div>  
      </div>
    </div>

  </hg-card>
</template>

<script>
import { RPD_PARENT_CODE, DENTURE_PARENT_CODES, ORDER_TYPES } from '@/public/constants';
import { getRpdSelectList } from '@/api/common';
import { mapGetters } from 'vuex';
import { copy } from '@/public/utils';

export default {
  name: 'FillComponent',
  props: {
    canDelete: Boolean,
    designCode: Number,
    parentCode: Number,
    initSelectCompList: { // 如果是审核或者重新设计，有回填内容，则直接用这个
      type: Array,
      default(){
        return []
      }
    },
    initSelectCounts: {
      type: Number,
      default: 1,
    },
    orderStatus: Number,
  },
  data() {
    return {
      selectDataList: [],
      selectCompList: [],
    }
  },
  computed: {
    ...mapGetters(['oneDesignList', 'language']),
    lastKey() {
      return this.selectCompList[this.selectCompList.length - 1].key;
    },
    titleName() {
      if(this.parentCode === RPD_PARENT_CODE) {
        return this.orderStatus === ORDER_TYPES.DESIGNING ? this.$t('order.detail.title.selectYourRPD') : this.$t('order.detail.title.rpdTitle');
      }else if(DENTURE_PARENT_CODES.includes(this.parentCode)) {
        const item = this.oneDesignList.find(item => item.designCode === this.designCode);
        if(item) {
          const name = this.language === 'zh' ? item.cnName : item.enName;
          return this.orderStatus === ORDER_TYPES.DESIGNING ? this.$t('order.detail.title.selectYourType',[name]) : this.$t('order.detail.title.typeTitle',[name]);
        }
      }
      return '';
    }
  },
  mounted() {
    this.initSelectList();
    if(this.initSelectCompList.length > 0) {
      this.selectCompList = copy(this.initSelectCompList);
    }else {
      for(let i =0; i<this.initSelectCounts; i ++) {
        const item = {
          key: i,
          value: '',
          isSelect: false,
        };
        this.selectCompList.push(item);
      }
    }
    
  },
  methods: {

    initSelectList() {
      // 这里需要判断是哪个类型，请求获取对应的列表
      getRpdSelectList().then(res => {
        this.selectDataList = res.data;
      }).catch(err => {
        console.log('err',err);
        this.$hgOperateFail(this.$t('order.detail.tips.loadSelectListFail'));
      });
    },

    // 删除Select组件
    deleteSelectComp() {
      const selectCounts = this.selectCompList.filter(item => item.isSelect).length;
      const showCompCount = this.selectCompList.length;

      if(selectCounts === showCompCount) {
        this.$hgOperateWarning(this.$t('order.detail.tips.cannotClearAll'));
        return;
      }

      this.selectCompList = this.selectCompList.filter(item => !item.isSelect);

    },

    // 添加Select组件
    addSelectComp() {
      
      if(this.selectCompList.length === 4) {
        this.$hgOperateWarning(this.$t('order.detail.tips.limitFourComp'));
        return;
      }

      this.selectCompList.push({key: this.lastKey + 1, value: '',isSelect: false,})
    },
  }
}
</script>

<style lang="scss" scoped>
.fill-component {
  .header {
    display: flex;
    justify-content: space-between;
    padding-bottom: 12px;
    border-bottom: 1px dashed #38393D;

    .title {
      font-size: 16px;
      line-height: 24px;
      font-weight: bold;
      color: $hg-secondary-primary;

      &>span {
        color: $hg-error;
      }
    }

    .btn {
      display: flex;
    }

    .btn>span {
      cursor: pointer;
      display: flex;
      padding: 4px 12px;
      margin-left: 24px;
      border-radius: 2px;
      font-size: 12px;
      line-height: 24px;
      &>.hg-icon {
        margin-right: 8px;
        font-size: 24px;
      }
    }

    .btn>.delete {
      border: 1px solid $hg-border;
    }

    .btn>.add {
      color: $hg-secondary-primary;
      border: 1px solid $hg-secondary-primary;
    }
  }

  .select-box {
    display: flex;
    flex-wrap: wrap;
    background: $hg-main-black;
  }

  .select-box>.select-item {
    display: flex;
    flex-wrap: wrap;
    margin-top: 24px;
    width: 50%;
    
    @media screen and (min-width: 1441px){
      width: 25%;
    }


    .select-item-left {
      margin-top: 12px;
      .el-checkbox {
        margin-right: 18px;
      }
      &>span {
        margin-right: 18px;
      }
    }

    .select-item-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-right: 24px;

      .error-msg {
        margin-top: 12px;
        color: $hg-error;
      }
    }
  }
}
</style>