import { leftUpperN<PERSON>ber, rightUpperNumber, leftLowerN<PERSON>ber, rightLowerNumber, upperNumbers, lowerNumbers, toothNumbers } from './constant';
export default {
  methods: {
    /**
     * 半口支架
     * @param {*} threeLevelDesignItem 
     * @param {*} toothNameList 
     */
    check1(threeLevelDesignItem, toothNameList) {
      const { designCode, parentCode, enName, cnName } = threeLevelDesignItem;
      const isUpper = toothNameList.some(num => upperNumbers.includes(num));
      const isLower = toothNameList.some(num => lowerNumbers.includes(num));
      const default_up_tooth = 11;
      const default_low_tooth = 41;
      // 判断列表是否有这种类型
      let targetDesignItem = this.toothDesign.find(designItem => designItem.code === designCode);

      let localDesignItem = this.toothDesign.find(designItem => designItem.code === 22102);

      if (!targetDesignItem) {
        targetDesignItem = {
          sortTimeStamp: Date.now(),
          code: designCode,
          enName: enName,
          pidCode: parentCode,
          pidEnName: '',
          pidZhName: '',
          zhName: cnName,
          tooth: [],
        };
        this.toothDesign.unshift(targetDesignItem);
      }

      if (isUpper && !targetDesignItem.tooth.some(num => upperNumbers.includes(num))) {
        if (localDesignItem && localDesignItem.tooth.some(num => upperNumbers.includes(num))) {
          //console.log('test');
        } else {
          targetDesignItem.tooth.push(default_up_tooth);
        }
      }

      if (isLower && !targetDesignItem.tooth.some(num => lowerNumbers.includes(num))) {
        if (localDesignItem && localDesignItem.tooth.some(num => lowerNumbers.includes(num))) {
          //console.log('test');
        } else {
          targetDesignItem.tooth.push(default_low_tooth);
        }
      }
    },
    /**
     * 1/4支架
     * @param {*} threeLevelDesignItem 
     * @param {*} toothNameList 
     */
    check3(threeLevelDesignItem, toothNameList) {
      const { designCode, parentCode, enName, cnName } = threeLevelDesignItem;
      const default_left_up_tooth = 11,
        default_right_up_tooth = 21,
        default_right_low_tooth = 31,
        default_left_low_tooth = 41;

      // 判断列表是否有这种类型
      let targetDesignItem = this.toothDesign.find(designItem => designItem.code === designCode);
      let localDesignItem = this.toothDesign.find(designItem => designItem.code === 22101);

      if (!targetDesignItem) {
        targetDesignItem = {
          sortTimeStamp: Date.now(),
          code: designCode,
          enName: enName,
          pidCode: parentCode,
          pidEnName: '',
          pidZhName: '',
          zhName: cnName,
          tooth: [],
        };
        this.toothDesign.unshift(targetDesignItem);
      }
      //左上
      const isLeftUpper = toothNameList.some(num => leftUpperNumber.includes(num));
      if (isLeftUpper && !targetDesignItem.tooth.some(num => leftUpperNumber.includes(num))) {
        if (localDesignItem && localDesignItem.tooth.some(num => upperNumbers.includes(num))) {
          //console.log('test');
        } else {
          targetDesignItem.tooth.push(default_left_up_tooth);
        }
      }
      //右上
      const isRightUpper = toothNameList.some(num => rightUpperNumber.includes(num));
      if (isRightUpper && !targetDesignItem.tooth.some(num => rightUpperNumber.includes(num))) {
        if (localDesignItem && localDesignItem.tooth.some(num => upperNumbers.includes(num))) {
          //console.log('test');
        } else {
          targetDesignItem.tooth.push(default_right_up_tooth);
        }
      }
      //左下 
      const isLeftLower = toothNameList.some(num => leftLowerNumber.includes(num));
      if (isLeftLower && !targetDesignItem.tooth.some(num => leftLowerNumber.includes(num))) {
        if (localDesignItem && localDesignItem.tooth.some(num => lowerNumbers.includes(num))) {
          //console.log('test');
        } else {
          targetDesignItem.tooth.push(default_left_low_tooth);
        }
      }

      //右下  
      const isRightLower = toothNameList.some(num => rightLowerNumber.includes(num));
      if (isRightLower && !targetDesignItem.tooth.some(num => rightLowerNumber.includes(num))) {
        if (localDesignItem && localDesignItem.tooth.some(num => lowerNumbers.includes(num))) {
          //console.log('test');
        } else {
          targetDesignItem.tooth.push(default_right_low_tooth);
        }
      }
    },
    /**
     * 托盘等上下颚类型
     * @param {*} threeLevelDesignItem 
     * @param {*} toothNameList 
     */
    check11(threeLevelDesignItem, toothNameList) {
      
      const { designCode, parentCode, enName, cnName } = threeLevelDesignItem;
      const isUpper = toothNameList.some(num => upperNumbers.includes(num));
      const isLower = toothNameList.some(num => lowerNumbers.includes(num));
      const default_up_tooth = 11;
      const default_low_tooth = 41;
      // 判断列表是否有这种类型
      let targetDesignItem = this.toothDesign.find(designItem => designItem.code === designCode);
      this.handleModelReplace(designCode, toothNameList);
      if (!targetDesignItem) {
        targetDesignItem = {
          sortTimeStamp: Date.now(),
          code: designCode,
          enName: enName,
          pidCode: parentCode,
          pidEnName: '',
          pidZhName: '',
          zhName: cnName,
          tooth: [],
        };
        this.toothDesign.unshift(targetDesignItem);
      }

      if (isUpper && !targetDesignItem.tooth.some(num => upperNumbers.includes(num))) {
        targetDesignItem.tooth.push(default_up_tooth);
      }

      if (isLower && !targetDesignItem.tooth.some(num => lowerNumbers.includes(num))) {
        targetDesignItem.tooth.push(default_low_tooth);
      }
    },
    //选了其他类型，禁选正畸
    check15(threeLevelDesignItem,toothNameList){
      const othroCode = 24102; //正畸code
      const { designCode} = threeLevelDesignItem;
      const otherDesign = this.toothDesign.filter(ele => ele.code !== othroCode) //已做类型筛选出 除了正畸类型
      if(designCode === othroCode && otherDesign.length > 0) {  //正畸和其他类型互斥
        this.$message.error(this.$t('order.add.tooth.rule.tip13'))
        return true
      }
    },
    // 选了正畸 ，禁选其他
    check16(threeLevelDesignItem){
      const othroCode = 24102; //正畸code
      const { designCode} = threeLevelDesignItem;  //当前选择的类型
      console.log('check16-designCode: ', designCode);
      const othroDesign = this.toothDesign.find(ele => ele.code === othroCode) //已做类型是否存在正畸
      console.log('check16-othroDesign: ', othroDesign);
     if(designCode !==othroCode &&  othroDesign) { 
      this.$message.error(this.$t('order.add.tooth.rule.tip13'))
       return true
     }
    },
    
    // 选了夜磨牙垫，颌垫，分牙服务禁选正畸带环
    check17(threeLevelDesignItem, toothNameList) {
      console.log('17-threeLevelDesignItem: ', threeLevelDesignItem);
      console.log('17-toothNameList: ', toothNameList);
      const orthoHandCode = 24406; // 正畸带环
      const splitCodes = [24301, 24302, 24403]; // 夜磨牙垫，颌垫，分牙服务
      const { designCode} = threeLevelDesignItem;  //当前选择的类型
      if (designCode === orthoHandCode) {
        const hasSplit = this.toothDesign.some(ele => splitCodes.includes(ele.code));
        if (hasSplit) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      } else if (splitCodes.includes(designCode)) {
        const hasOrthoHand = this.toothDesign.some(ele => ele.code === orthoHandCode)
        if (hasOrthoHand) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      }
    },

    // 选了放射导板禁选除其他的设计类型
    check18(threeLevelDesignItem, toothNameList) {
      const radiographicCode = 23403; // 放射导板
      const otherCode = [23403, 23402]; // 其他
      const { designCode} = threeLevelDesignItem;  //当前选择的类型
      if (designCode === radiographicCode) {
        const hasLimit= this.toothDesign.some(ele => !otherCode.includes(ele.code));
        if (hasLimit) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      } else {
        const hasRadiographic = this.toothDesign.some(ele => ele.code === radiographicCode)
        if (hasRadiographic && !otherCode.includes(designCode)) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      }
    },

    // 马泷桥跟解剖型桥架-牙龈回切互斥
    check19(threeLevelDesignItem, toothNameList) {
      // 马泷桥23103, // 单孔牙支持式导板23601
      const fixMutualType = [23103, 23601]
      // 解剖型桥架-牙龈回切23106, 23404杆卡
      const fixArr = [23106, 23404]

      const isUpper = toothNameList.some(num => upperNumbers.includes(num));
      console.log('check19-isUpper: ', isUpper)
      const isLower = toothNameList.some(num => lowerNumbers.includes(num));

      if (fixMutualType.includes(threeLevelDesignItem.designCode)) {
        const fixItem = this.toothDesign.find(item => fixArr.includes(item.code))
        const isUpperFix = fixItem && fixItem.tooth.some(num => upperNumbers.includes(num));
        const isLowerFix = fixItem && fixItem.tooth.some(num => lowerNumbers.includes(num));
        const isSameSide = (isUpper === isUpperFix) || (isLower === isLowerFix)
        if (fixItem && isSameSide) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }

      } else if (fixArr.includes(threeLevelDesignItem.designCode)) {
        const fixItem = this.toothDesign.find(item => fixMutualType.includes(item.code))
        console.log('check19-fixItem: ', fixItem)

        const isUpperFix = fixItem && fixItem.tooth.some(num => upperNumbers.includes(num));
        console.log('check19-isUpperFix: ', isUpperFix)

        const isLowerFix = fixItem && fixItem.tooth.some(num => lowerNumbers.includes(num));
        const isSameSide = (isUpper === isUpperFix) || (isLower === isLowerFix)
        if (fixItem && isSameSide) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      }
    },

    // 马里兰桥21103，超薄临时牙桥21111 与 接触模21404, 代型基牙21403, 分割代型牙模21401, 固定个性化托盘21303 互斥
    check20(threeLevelDesignItem, toothNameList) {
      // 马里兰桥21103，超薄临时牙桥21111
      const fixMutualType = [21103, 21111]
      // 接触模21404, 代型基牙21403, 分割代型牙模21401, 固定个性化托盘21303
      const fixArr = [21401, 21403, 21404, 21303]
      if (fixMutualType.includes(threeLevelDesignItem.designCode)) {
        if (this.toothDesign.find(item => fixArr.includes(item.code))) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }

      } else if (fixArr.includes(threeLevelDesignItem.designCode)) {
        if (this.toothDesign.find(item => fixMutualType.includes(item.code))) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      }
    },

    // 马里兰桥21103，超薄临时牙桥21111，马泷桥23103，杆卡23404跟桥体互斥
    check21(threeLevelDesignItem, toothNameList) {
      const currentIsUpper = toothNameList.some(num => upperNumbers.includes(num));
      const currentIsLower = toothNameList.some(num => lowerNumbers.includes(num));
      const connectCodeList = [21501,23501]
      // 马里兰桥21103，超薄临时牙桥21111，马泷桥23103，杆卡23404跟桥体互斥
      const mutualType = [21103, 21111, 23103, 23404]
      console.log('check21-threeLevelDesignItem: ', threeLevelDesignItem);
      const isConnect = this.toothDesign.find(item => connectCodeList.includes(item.code) && toothNameList.some(toothItem => item.tooth.includes(toothItem)))
      const isMutual = mutualType.includes(threeLevelDesignItem.designCode)
      if (isMutual && isConnect) {
        this.$message.error(this.$t('order.add.tooth.rule.tip13'))
        console.log('isConnect: ', isConnect)
        console.log('isMutual: ', isMutual)
        return true
      } else if (connectCodeList.includes(threeLevelDesignItem.designCode)) {
        const mutaulItem = this.toothDesign.find(item => mutualType.includes(item.code))
        const isUpperFix = mutaulItem && mutaulItem.tooth.some(num => upperNumbers.includes(num));
        const isLowerFix = mutaulItem && mutaulItem.tooth.some(num => lowerNumbers.includes(num));
        const isSameSide = (currentIsUpper === isUpperFix) || (currentIsLower === isLowerFix)
        if (mutaulItem && isSameSide) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      }
    },

    // 马泷桥23103，杆卡23404 跟 种植托盘23401, 螺丝固位冠23203, 个性化基台23201互斥
    check22(threeLevelDesignItem, toothNameList) {
      // 杆卡23404，马泷桥23103
      const fixMutualType = [23404, 23103]
      // 种植托盘23401, 螺丝固位冠23203, 个性化基台23201, 单孔牙支持式导板23601
      const fixArr = [23401, 23203, 23201, 23601]
      // const fixArr = [23401]
      const currentIsUpper = toothNameList.some(num => upperNumbers.includes(num));
      const currentIsLower = toothNameList.some(num => lowerNumbers.includes(num));

      if (fixMutualType.includes(threeLevelDesignItem.designCode)) {
        if (threeLevelDesignItem.designCode === 23404) {
          const fixItem = this.toothDesign.find(item => fixArr.includes(item.code))
          const isUpperFix = fixItem && fixItem.tooth.some(num => upperNumbers.includes(num));
          const isLowerFix = fixItem && fixItem.tooth.some(num => lowerNumbers.includes(num));
          const isSameSide = (currentIsUpper === isUpperFix) || (currentIsLower === isLowerFix)
          if (fixItem && isSameSide) {
            this.$message.error(this.$t('order.add.tooth.rule.tip13'))
            return true
          }
        } else if (threeLevelDesignItem.designCode === 23103) {
          const fixItem = this.toothDesign.find(item => [23401].includes(item.code))
          const isUpperFix = fixItem && fixItem.tooth.some(num => upperNumbers.includes(num));
          const isLowerFix = fixItem && fixItem.tooth.some(num => lowerNumbers.includes(num));
          const isSameSide = (currentIsUpper === isUpperFix) || (currentIsLower === isLowerFix)
          if (fixItem && isSameSide) {
            this.$message.error(this.$t('order.add.tooth.rule.tip13'))
            return true
          }
        }

      } else if (fixArr.includes(threeLevelDesignItem.designCode)) {
        console.log('this.toothDesign', this.toothDesign)
        if (threeLevelDesignItem.designCode === 23401) {
          const fixItem = this.toothDesign.find(item => fixMutualType.includes(item.code))
          const isUpperFix = fixItem && fixItem.tooth.some(num => upperNumbers.includes(num));
          const isLowerFix = fixItem && fixItem.tooth.some(num => lowerNumbers.includes(num));
          const isSameSide = (currentIsUpper === isUpperFix) || (currentIsLower === isLowerFix)
  
          if (fixItem && isSameSide) {
            this.$message.error(this.$t('order.add.tooth.rule.tip13'))
            return true
          }
        } else {
          const fixItem = this.toothDesign.find(item => [23404].includes(item.code))
          const isUpperFix = fixItem && fixItem.tooth.some(num => upperNumbers.includes(num));
          const isLowerFix = fixItem && fixItem.tooth.some(num => lowerNumbers.includes(num));
          const isSameSide = (currentIsUpper === isUpperFix) || (currentIsLower === isLowerFix)
          if (fixItem && isSameSide) {
            this.$message.error(this.$t('order.add.tooth.rule.tip13'))
            return true
          }
        }
      }
    },

    // 基台定位器23204，跟杆卡23404，放射导板23403，解剖型桥架-牙龈回切23106，种植托盘23401互斥
    check23(threeLevelDesignItem, toothNameList) {
      // 基台定位器23204
      const fixMutualType = [23204]
      // 种植托盘23401, 螺丝固位冠23203, 个性化基台23201
      const fixArr = [23404, 23106, 23401]
      // const fixArr = [23401]
      const radiographicGuide = [23403]
      const currentIsUpper = toothNameList.some(num => upperNumbers.includes(num));
      const currentIsLower = toothNameList.some(num => lowerNumbers.includes(num));

      if (fixMutualType.includes(threeLevelDesignItem.designCode)) {
        //放射导板特殊处理，不管是否同侧，都互斥
        const anyRadiographicGuide =this.toothDesign.some(item => radiographicGuide.includes(item.code));
        const fixItem = this.toothDesign.find(item => fixArr.includes(item.code))
        const isUpperFix = fixItem && fixItem.tooth.some(num => upperNumbers.includes(num));
        const isLowerFix = fixItem && fixItem.tooth.some(num => lowerNumbers.includes(num));
        const isSameSide = (currentIsUpper === isUpperFix) || (currentIsLower === isLowerFix)
        if ((fixItem && isSameSide) || anyRadiographicGuide) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      } else if (fixArr.includes(threeLevelDesignItem.designCode)) {
        const fixItem = this.toothDesign.find(item => fixMutualType.includes(item.code))
        const isUpperFix = fixItem && fixItem.tooth.some(num => upperNumbers.includes(num));
        const isLowerFix = fixItem && fixItem.tooth.some(num => lowerNumbers.includes(num));
        const isSameSide = (currentIsUpper === isUpperFix) || (currentIsLower === isLowerFix)
        if (fixItem && isSameSide) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      }
    },

    // 基台定位器23204，跟连接体23501互斥，目前仅和种植修复的连接体互斥。
    check24(threeLevelDesignItem, toothNameList) {
      // 杆卡23404，马泷桥23103
      const fixMutualType = [23204]
      // 连接体23501（种植修复）
      const connectCodeList = [23501]

      if (fixMutualType.includes(threeLevelDesignItem.designCode)) {
        const isConnect = this.toothDesign.find(item => connectCodeList.includes(item.code) && toothNameList.some(toothItem => item.tooth.includes(toothItem)))
        if (isConnect) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      } else if (connectCodeList.includes(threeLevelDesignItem.designCode)) {
        const isPositionGuide = this.toothDesign.find(item => fixMutualType.includes(item.code) && toothNameList.some(toothItem => item.tooth.includes(toothItem)))
        if (isPositionGuide) {
          this.$message.error(this.$t('order.add.tooth.rule.tip13'))
          return true
        }
      }
    },

    // 处理 模型相关类型
    check2(threeLevelDesignItem) {
      
      const default_tooth = [41, 11];
      const { designCode, parentCode, enName, cnName } = threeLevelDesignItem;
      // 判断列表是否有这种类型
      const hasThisType = this.toothDesign.some(ele => {
        return ele.code == designCode;
      });

      if (hasThisType) {
        return true;
      }

      this.handleModelReplace(threeLevelDesignItem.designCode);

      this.toothDesign.unshift({
        sortTimeStamp: Date.now(),
        code: designCode,
        enName: enName,
        pidCode: parentCode,
        pidEnName: '',
        pidZhName: '',
        zhName: cnName,
        tooth: default_tooth,
      });

      // return true;
    },

    // 判断是否选中牙齿
    check4(toothNameList) {
      const len = toothNameList.length;
      if (!len) {
        const msg = this.$t('order.add.tooth.no.selectTooth');
        this.$message.error(msg);
        return true;
      }
    },
   
    //全口义齿
    check5(threeLevelDesignItem,toothNameList) {
      const len = toothNameList.length;
      if (len < 7) {
        const msg = this.$t('order.add.tooth.rule.tip15');
        this.$message.error(msg);
        return true;
      }
  

      const FullMouthCode = [22201, 22202, 22203, 22204]; //全口义齿 code

      const filterFullMouthCode = FullMouthCode.filter(code => code !== threeLevelDesignItem.designCode);
      const filterToothDesign = this.toothDesign.filter(designItem => filterFullMouthCode.includes(designItem.code)) //是否已做其他全口类型                          
      if(!filterToothDesign) return false
 
      let isHaveUpper =  filterToothDesign.some(designItem => designItem.tooth.some(num=>upperNumbers.includes(num)))
      let isHaveUpper2 =  toothNameList.some(num=>upperNumbers.includes(num))

      let isHaveLower =  filterToothDesign.some(designItem => designItem.tooth.some(num=>lowerNumbers.includes(num)))
      let isHaveLower2 =  toothNameList.some(num=>lowerNumbers.includes(num))

      if((isHaveUpper && isHaveUpper2) || (isHaveLower && isHaveLower2)) {
        const msg = this.$t('order.add.tooth.rule.tip14');
        this.$message.error(msg);
        return true
      }
      
    },
    //局部义齿
    check55(toothNameList) {
      const len = toothNameList.length;
      if (len >= 7) {
        const msg = this.$t('order.add.tooth.rule.tip16');
        this.$message.error(msg);
        return true;
      }

      const FullMouthCode = [22201, 22202, 22203, 22204]; //全口义齿 code
      const FullMouthToothDesign = this.toothDesign.filter(designItem => FullMouthCode.includes(designItem.code)) //是否已做其他全口类型
      let isHave =  FullMouthToothDesign.some(designItem => designItem.tooth.some(num=>toothNameList.includes(num)))
      if(isHave) {
        const msg = this.$t('order.add.tooth.rule.tip17');
        this.$message.error(msg);
        return true;
      }


    },
    //解剖型桥架
    check66(threeLevelDesignItem,toothNameList){
      // const map ={
      //   23104:23105,
      //   23105:23104
      // };
      // const ExcludedCode = map[threeLevelDesignItem.designCode]
      
      // const targetToothDesignItem = this.toothDesign.find(Item => Item.code === ExcludedCode)
      // if(!targetToothDesignItem) return false;//没有互斥的

      // // 已做的类型存在上下颌
      // let isHaveUpper =  targetToothDesignItem.tooth.some(number=>upperNumbers.includes(number)) 
      // let isHaveLower =  targetToothDesignItem.tooth.some(number=>lowerNumbers.includes(number)) 

      // //选中的牙齿也存在上下颌
      // let isHaveUpper2 =  toothNameList.some(number=>upperNumbers.includes(number)) 
      // let isHaveLower2 =  toothNameList.some(number=>lowerNumbers.includes(number)) 
      // if((isHaveUpper && isHaveUpper2) || (isHaveLower && isHaveLower2)) {
      //   // const msg = this.$t('同颌侧种植架桥有蜡型和无蜡型不能同时存在');
      //   this.$message.error(msg);
      //   return true
      // }

    },
    // 单独处理桥体  将部份设计类型塞进桥体
    check6(threeLevelDesignItem, toothNameList) {
      // 获取做了桥的牙号
      const bridgeTooth = this.toothDesign
        .filter(ele => {
          return [21501,23501].includes(Number(ele.code));
        })
        .map(ele => {
          return ele.tooth;
        })
        .reduce((a, b) => a.concat(b), []);

      // 选择的牙齿是否做了牙桥
      const isMakeBridge = toothNameList.some(ele => {
        return bridgeTooth.includes(ele);
      });

      // 选择的牙齿是否在上下排中 false不在
      // 在上排牙齿
      let upStatus = toothNameList.some(tooth => {
        return upperNumbers.includes(tooth);
      });

      // 在下排牙齿
      let downStatus = toothNameList.some(tooth => {
        return lowerNumbers.includes(tooth);
      });

      // 判断牙齿是否连续
      let toothIndexArr = [];
      toothNameList.forEach(ele => {
        const index = toothNumbers.indexOf(ele);
        toothIndexArr.push(index);
      });
      toothIndexArr.sort((a, b) => {
        return a - b;
      });
      let tempIndexArr = [];
      // 取第一个和最后一个的值，然后从最小累加到最大 如1-4得到[1,2,3,4]
      for (let i = toothIndexArr[0]; i <= toothIndexArr.slice(-1); i++) {
        tempIndexArr.push(i);
      }

      // 模型和桥类型
      const noCrowntype = [21401, 21402, 21403, 21404, 21501, 23501, 23106];
      // 模型 桥 缺失位类型
      //  含有冠类型的牙号(包括缺失位)
      let crownTooths = [];
      // 冠类型牙号，不包含缺失位
      this.toothDesign.forEach(ele => {
        if (!noCrowntype.includes(ele.code)) {
          crownTooths = [...crownTooths, ...ele.tooth];
        }
      });

      // 有冠类型 缺失位没有排除
      const hasCrown = toothNameList.some(tooth => {
        return crownTooths.includes(tooth);
      });

      // 如果选择的牙齿做过桥就提示
      if (isMakeBridge) {
        const msg = this.$t('order.add.tooth.rule.tip8');
        this.$message.error(msg);
        toothNameList.splice(0);
        return true;
      }

      //  选择的牙齿颗数小于2
      if (toothNameList.length < 2) {
        const msg = this.$t('order.add.tooth.rule.tip9');
        this.$message.error(msg);
        return true;
      }

      // 如果选择的牙齿跨上下颌就提示
      if (upStatus && downStatus) {
        const msg = this.$t('order.add.tooth.rule.tip11');
        this.$message.error(msg);
        return true;
      }

      // 选择的牙齿不连续
      // if (tempIndexArr.toString() != toothIndexArr.toString()) {
      //     const msg = this.$t('order.add.tooth.rule.tip10')
      //     this.$message.error(msg)
      //     return true
      // }

      // 选择的牙没有冠
      if (!hasCrown) {
        const msg = this.$t('order.add.tooth.rule.tip12');
        this.$message.error(msg);
        return true;
      }
      
      // 种植的桥体：新增的规则
      if(threeLevelDesignItem.designCode === 23501) {
        // 提取选中牙齿中包含的设计类型
        let currentBridgeDesignList = this.toothDesign.filter(item => {
          let hasCurrentBridge = false;
          const { code, tooth:toothList } = item;
          if(![21501,23501, 23106].includes(code)) { // 牙龈回切不算入，因为它和桥体互斥
            hasCurrentBridge = toothList.some(tooth => toothNameList.includes(tooth));
          }
          return hasCurrentBridge;
        });

        let currentBridgeToothList = currentBridgeDesignList.reduce((curList, item) => {
          const { tooth } = item;
          const curTooth = tooth.filter(tooth => toothNameList.includes(tooth));
          curList = curList.concat(curTooth);
          return curList;
        },[]);
        currentBridgeToothList = Array.from(new Set(currentBridgeToothList)); // 去重
        //console.log('currentBridgeDesignList', currentBridgeDesignList, currentBridgeToothList);
        // 1.选中的牙齿中，仅有一颗牙齿做了类型，不能连接成桥
        if(currentBridgeToothList.length === 1) {
          const msg = this.$t('order.add.tooth.rule.tip19');
          this.$message.error(msg);
          return true;
        }

        // 2.选中的牙齿中，包含基台，但仅有基台，不能连接成桥，必须含有螺丝固位冠
        const hasAbutment = currentBridgeDesignList.some(item => item.code === 23201);
        const hasScrewCrown = currentBridgeDesignList.some(item => item.code === 23203);
        if(hasAbutment && !hasScrewCrown) {
          const msg = this.$t('order.add.tooth.rule.tip20');
          this.$message.error(msg);
          return true;
        }
      }

      this.handleModelReplace(threeLevelDesignItem.designCode, toothNameList);
      //console.log('in 6');
      this.toothDesign.unshift({
        sortTimeStamp: Date.now(),
        code: threeLevelDesignItem.designCode,
        enName: threeLevelDesignItem.enName,
        pidCode: threeLevelDesignItem.parentCode,
        pidEnName: '',
        pidZhName: '',
        zhName: threeLevelDesignItem.cnName,
        tooth: toothNameList,
      });
      return true;
    },
    /**
     * 原来是用于限制选基牙之前，一定要选分割代型牙模，但4.3.28去掉
     * 这里更改为基牙的选中规则：基牙和所有类型共存
     */
    check7(threeLevelDesignItem, toothNameList){
      const { designCode, enName, cnName, parentCode } = threeLevelDesignItem;

      const curItem = this.toothDesign.find(item => item.code === designCode);
      if(curItem) {
        const newToothList = toothNameList.filter(tooth => !curItem.tooth.includes(tooth));
        curItem.tooth = curItem.tooth.concat(newToothList);
      }else {
        this.toothDesign.unshift({
          sortTimeStamp: Date.now(),
          code: designCode,
          enName: enName,
          pidCode: parentCode,
          pidEnName: '',
          pidZhName: '',
          zhName: cnName,
          tooth: toothNameList,
        });
      }
      /* if(!hasThisType(21402)) {
        this.$message.error(this.$t('order.add.tooth.rule.tip18'))
        return true
      } */
    },
    // 通用类型处理
    check8(threeLevelDesignItem, toothNameList) {
      console.log('threeLevelDesignItem: ', threeLevelDesignItem);
      console.log('toothNameList: ', toothNameList);
      console.log('this.toothDesign', this.toothDesign);
      const handle = toothCode => {
        // 判断toothDesign里面有没有对应类型的
        let currentDesignItem = this.toothDesign.find(ele => ele.code == threeLevelDesignItem.designCode);
       
        // 牙号对应的类型
        let toothType = [];
        this.toothDesign.forEach(ele => {
          if (ele.tooth.indexOf(toothCode) > -1) {
            // 牙号对应的类型数组
            toothType.push(ele.code);
          }
        });

        this.handleModelReplace(threeLevelDesignItem.designCode, toothNameList); // 新增之前，模型互斥的数据处理
        // 新增 如果orderlist里面没有当前选择的类型 并且当前选择的牙也没有类型
        if (!currentDesignItem && toothType.length == 0) {
          this.toothDesign.unshift({
            tooth: [toothCode],
            sortTimeStamp: Date.now(),
            code: threeLevelDesignItem.designCode,
            enName: threeLevelDesignItem.enName,
            pidCode: threeLevelDesignItem.parentCode,
            pidEnName: '',
            pidZhName: '',
            zhName: threeLevelDesignItem.cnName,
            jawType: upperNumbers.includes(toothCode) ? 'upper' : 'lower'
          });
          return false;
        }

        // 如果toothDesign存在有当前选择的类型并且当前选择的牙没有任何类型
        if (currentDesignItem && toothType.length == 0) {
          this.toothDesign.forEach(ele => {
            if (ele.code == threeLevelDesignItem.designCode) {
              ele.tooth.push(toothCode);
            }
          });
          return false;
        } 

        // 特殊类型code
        const specialArr = [21501,21401, 21402, 23501, 21403, 21404];
        // 设计类型共存的对象
        // 21101:全冠，21106:牙冠缺失位, 21102:临时冠,21107:临时冠缺失位,21201:内冠，21202: 解剖型内冠, 21203:内冠缺失位, 21202:解剖型内冠, 21204: 解剖型内冠缺失位, 21205: 双层冠
        // 21301:桩核, 21302:套筒冠 21303:固定个性化托盘, 21304:标准桩核,21305:解剖桩核,21306:桩核一体全冠,21307:桩核一体内冠
        const designTypeCoexisting = {
          21101: [21301, 21302, 21304, 21305],
          21102: [21301, 21304, 21305, 21306, 21307, 21111],
          21201: [21301, 21304, 21103],
          21202: [21301, 21304, 21103],
          21203: [21103],
          21204: [21103],
          21205: [21301, 21302, 21304, 21305, 21103],
          21301: [21101, 21102, 21201, 21202, 21302, 21205],
          21302: [21101, 21205, 21301, 21304, 21305, 21306, 21307],
          21304: [21101, 21102, 21201, 21202, 21302, 21205],
          21305: [21101, 21102, 21302, 21205],
          21306: [21102, 21302],
          21307: [21102, 21302],
          23201: [23203, 23104, 23105, 23204, 23601],
          23203: [23201, 23104, 23105, 23601],
          23104: [23201, 23203],
          23105: [23201, 23203],

          21103: [21201, 21202, 21203, 21204, 21205],
          21111: [21102],
          23204: [23201, 23601],
          23601: [23201, 23203, 23204],
        };
        this.toothDesign.forEach(ele => {
          if (specialArr.includes(threeLevelDesignItem.designCode)) {
            return true;
          }
          // 找到toothDesign里面的类型 跟牙号所拥有的类型对比
          // 如果牙号拥有的类型在orderlist里面，就删除掉orderlist里面类型的牙号
          //    如果是特殊类型就返回

          //  剔除掉特殊类型之后
          let tempToothType = toothType.filter(ele => {
            return !specialArr.includes(ele);
          });
          
          // 当前牙位是否为共存设计类型
          const currentToothType = ele.tooth.includes(toothCode) ? ele.code : null;
          const isToothCoexist = designTypeCoexisting[currentToothType];
          // 当前选择的三级设计类型是否为共存设计类型
          // const isCoexist = designTypeCoexisting[threeLevelDesignItem.designCode];
          const isCoexist = isToothCoexist && isToothCoexist.includes(threeLevelDesignItem.designCode);
          const flag = isToothCoexist && isCoexist;
          // 不是特殊类型就把旧类型删除
          if (tempToothType.indexOf(ele.code) > -1 && !flag) {
            let tempTypeArr = ele.tooth;
            let index = tempTypeArr.indexOf(toothCode);
            if (index > -1) {
              ele.tooth.splice(index, 1);
            }
          }
        });

        // // 如果toothDesign里面没有这个类型就新增一个
        if (!currentDesignItem) {
          //console.log('in 8-2');
          this.toothDesign.unshift({
            tooth: [],
            sortTimeStamp: Date.now(),
            code: threeLevelDesignItem.designCode,
            enName: threeLevelDesignItem.enName,
            pidCode: threeLevelDesignItem.parentCode,
            pidEnName: '',
            pidZhName: '',
            zhName: threeLevelDesignItem.cnName,
            jawType: upperNumbers.includes(toothCode) ? 'upper' : 'lower'
          });
        } 

        this.toothDesign.forEach(ele => {
          if (ele.code == threeLevelDesignItem.designCode && !ele.tooth.includes(toothCode)) {
            ele.tooth.push(toothCode);
          }
            
        });
      };
      // 正常的逻辑 需要选牙齿
      for (let i = 0; i < toothNameList.length; i++) {
        let toothCode = toothNameList[i];
        handle(toothCode);
      }
    },
    // 杂项其他特殊处理
    checkOther(threeLevelDesignItem, toothNameList){
      // 如果选择了杂项的其他（可以不选牙齿，跟所有品类都不冲突）
      // 判断toothDesign里面有没有对应类型的
      let currentDesignItem = this.toothDesign.find(ele => ele.code == threeLevelDesignItem.designCode);
      if (!currentDesignItem) {
        this.toothDesign.push({
          tooth: [49], // 杂项其他固定传值49
          sortTimeStamp: Date.now(),
          code: threeLevelDesignItem.designCode,
          enName: threeLevelDesignItem.enName,
          pidCode: threeLevelDesignItem.parentCode,
          pidEnName: '',
          pidZhName: '',
          zhName: threeLevelDesignItem.cnName,
        });
      }
    },

    /**
     * 互斥处理-直接替换-这里是半颌、整颌之间的互斥，单个在check8
     * exclusionModel 整颌替换
     * exclusionHalf 半颌替换
     * @param {*} designCode 当前操作的类型
     * @param {Array} curToothList 当前操作的牙位列表
     */
    handleModelReplace(designCode, curToothList = []) {
      // 处理1
      const exclusionModel = {
        21402: [21401],
        21401: [21402], 
        21108: [21303],
        21303: [21108],   // 诊断蜡型和固定托盘互斥
      };
      const exclusionCodes = exclusionModel[designCode];
      if(exclusionCodes) {
        exclusionCodes.forEach(code => {
          const delIndex = this.toothDesign.findIndex(item => item.code === code);
          if(delIndex > -1) {
            this.toothDesign.splice(delIndex, 1);
          }
        });
        return;
      }

      // 处理2
      // 目前是解剖型桥架三者半颌互斥
      const exclusionHalf = {
        23106: [23104, 23105, 23501, 23103],
        23104: [23105, 23106],
        23105: [23104, 23106],
        23501: [23106],
        23404: [23103]
      };
      let exclusionHalfCodes = exclusionHalf[designCode];
      // //console.log('exclusionHalfCodes', exclusionHalfCodes);
      if(exclusionHalfCodes) {
        const hasUpper = curToothList.some(tooth => upperNumbers.includes(tooth));
        const hasLower = curToothList.some(tooth => lowerNumbers.includes(tooth));

        const tempToothDesign = JSON.parse(JSON.stringify(this.toothDesign)); //
        tempToothDesign.forEach(item => {
          const { code: designCode, tooth: exclusionToothList } = item;
          let newToothList = [];
          if(exclusionHalfCodes.includes(designCode)) {

            if(hasUpper && !hasLower) { // 单边-上
              newToothList = exclusionToothList.filter(tooth => lowerNumbers.includes(tooth));
            }else if(!hasUpper && hasLower) { // 单边-下
              newToothList = exclusionToothList.filter(tooth => upperNumbers.includes(tooth));
            }

            // 结论：牙位全清空则删除，否则保留对颌的牙位
            const delIndex = this.toothDesign.findIndex(item => item.code === designCode);
            if(newToothList.length === 0) {
              this.toothDesign.splice(delIndex, 1);
            }else {
              this.toothDesign[delIndex].tooth = newToothList;
            }
          }
        });
        return;
      }
      
      
    },

  },
};
