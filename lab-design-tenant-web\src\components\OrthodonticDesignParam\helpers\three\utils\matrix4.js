
import * as THREE from 'three'
import { toFixed } from '@/components/OrthodonticDesignParam/helpers/utils/index'
import { _quaternion } from './quaternion'

export const _matrix4 = new THREE.Matrix4()

export function createMatrix4() {
  const matrix = new THREE.Matrix4()

  if (!arguments.length) {
    return matrix
  }

  const translate = arguments[0] || new THREE.Vector3(0, 0, 0)
  const rotate = arguments[1] || new THREE.Euler(0, 0, 0)
  const scale = arguments[2] || new THREE.Vector3(1, 1, 1)

  if (rotate.isEuler) {
    const euler = rotate
    const quaternion = _quaternion
    quaternion.setFromEuler(euler)
    matrix.compose(translate, quaternion, scale)
  } else if (rotate.isQuaternion) {
    const quaternion = rotate
    matrix.compose(translate, quaternion, scale)
  }

  return matrix
}

export function getMultiplyMatrix4(originMatrix, resultMatrix) {
  return originMatrix.clone().invert().multiply(resultMatrix)
}

export function getPremultiplyMatrix4(originMatrix, resultMatrix) {
  return resultMatrix.clone().multiply(_matrix4.copy(originMatrix).invert())
}

export function toFixedMatrix4(matrix, precision) {
  const elements = matrix.elements
  for (let i = 0; i < elements.length; i++) {
    elements[i] = toFixed(elements[i], precision)
  }
  return matrix
}

export function composeMatrix4(matrix) {
  const translate = new THREE.Vector3()
  const quaternion = new THREE.Quaternion()
  const scale = new THREE.Vector3()
  const euler = new THREE.Euler()

  matrix.decompose(translate, quaternion, scale)

  euler.setFromQuaternion(quaternion)

  return {
    translate,
    quaternion,
    euler,
    scale
  }
}

export function getRotateRroundApplyMatrix(position, rotationPoint, rotationAxis, rotationAngle) {
  const quaternion = _quaternion
  quaternion.setFromAxisAngle(rotationAxis, rotationAngle)
  const rotationMatrix = createMatrix4(null, quaternion, null)

  const originPosition = position.clone()
  position.sub(rotationPoint)
  position.applyQuaternion(quaternion)
  position.add(rotationPoint)

  const originPositionMatrix = createMatrix4(originPosition)
  const resultPositionMatrix = createMatrix4(position)
  const revolutionMatrix = getPremultiplyMatrix4(originPositionMatrix, resultPositionMatrix)

  const applyMatrix = rotationMatrix.premultiply(revolutionMatrix)
  return applyMatrix
}
