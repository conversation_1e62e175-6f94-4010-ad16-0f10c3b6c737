/**
 * 左上
 */
export const leftUpperNumber = [18, 17, 16, 15, 14, 13, 12, 11];
/**
 * 右上
 */
export const rightUpperNumber = [21, 22, 23, 24, 25, 26, 27, 28];

/**
 * 左下
 */
export const rightLowerNumber = [38, 37, 36, 35, 34, 33, 32, 31];

/**
 * 右下
 */
export const leftLowerNumber = [41, 42, 43, 44, 45, 46, 47, 48];

export const upperNumbers = [18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28];

export const lowerNumbers = [38, 37, 36, 35, 34, 33, 32, 31, 41, 42, 43, 44, 45, 46, 47, 48];

export const toothNumbers = [18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28, 38, 37, 36, 35, 34, 33, 32, 31, 41, 42, 43, 44, 45, 46, 47, 48];

export const topBoxNumber = [13, 12, 11, 21, 22, 23]
export const bottomBoxNumber = [33, 32, 31, 41, 42, 43]
export const iprtopBoxNumber = [13, 12, 11, 21, 22]
export const iprbottomBoxNumber = [32, 31, 41, 42, 43]

export const colorList = [0xff0004, 0xfe4800, 0xfe9500, 0xfde000, 0xcdfc00, 0x35fa00, 0x00f660, 0x00f1ef, 0x005feb, 0x2d00e3, 0xaf00db, 0xd4007f];
export const orgincolorList = ['#ff0004', '#fe4800', '#fe9500', '#fde000', '#cdfc00', '#35fa00', '#00f660', '#00f1ef', '#005feb', '#2d00e3', '#af00db', '#d4007f'];
export const cardposition = {
  // 上颌
  18: {x:61.2648468017578, y: 96.34035110473633, z: 16.357550144195557}, 
  17: {x:61.2648468017578, y: 112.34035110473633, z: 16.357550144195557}, 
  16: {x:61.2648468017578, y: 129.34035110473633, z: 16.357550144195557}, 
  15: {x:61.2648468017578, y: 146.34035110473633, z: 16.357550144195557}, 
  14: {x:61.2648468017578, y: 163.34035110473633, z: 16.357550144195557}, 

  13: {x:82.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  12: {x:117.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  11: {x:152.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  21: {x:187.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  22: {x:222.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  23: {x:257.2648468017578, y: 167.34035110473633, z: 16.357550144195557},

  24: {x:279.2648468017578, y: 163.34035110473633, z: 16.357550144195557}, 
  25: {x:279.2648468017578, y: 146.34035110473633, z: 16.357550144195557},
  26: {x:279.2648468017578, y: 129.34035110473633, z: 16.357550144195557}, 
  27: {x:279.2648468017578, y: 112.34035110473633, z: 16.357550144195557}, 
  28: {x:279.2648468017578, y: 96.34035110473633, z: 16.357550144195557}, 
  // 下颌
  38: {x:279.2648468017578, y: 91.34035110473633, z: 16.357550144195557}, 
  37: {x:279.2648468017578, y: 74.34035110473633, z: 16.357550144195557}, 
  36: {x:279.2648468017578, y: 57.34035110473633, z: 16.357550144195557}, 
  35: {x:279.2648468017578, y: 40.34035110473633, z: 16.357550144195557}, 
  34: {x:279.2648468017578, y: 23.34035110473633, z: 16.357550144195557}, 

  33: {x:257.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 
  32: {x:222.2648468017578, y: 20.34035110473633, z: 16.357550144195557},
  31: {x:187.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 
  41: {x:152.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 
  42: {x:117.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 
  43: {x:82.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 

  44: {x:61.2648468017578, y: 23.34035110473633, z: 16.357550144195557}, 
  45: {x:61.2648468017578, y: 40.34035110473633, z: 16.357550144195557}, 
  46: {x:61.2648468017578, y: 57.34035110473633, z: 16.357550144195557}, 
  47: {x:61.2648468017578, y: 74.34035110473633, z: 16.357550144195557}, 
  48: {x:61.2648468017578, y: 91.34035110473633, z: 16.357550144195557}
}

export const cardpositionEn = {
  // 上颌
  18: {x:67.2648468017578, y: 96.34035110473633, z: 16.357550144195557}, 
  17: {x:67.2648468017578, y: 112.34035110473633, z: 16.357550144195557}, 
  16: {x:67.2648468017578, y: 129.34035110473633, z: 16.357550144195557}, 
  15: {x:67.2648468017578, y: 146.34035110473633, z: 16.357550144195557}, 
  14: {x:67.2648468017578, y: 163.34035110473633, z: 16.357550144195557}, 

  13: {x:86.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  12: {x:120.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  11: {x:153.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  21: {x:187.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  22: {x:220.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  23: {x:253.2648468017578, y: 167.34035110473633, z: 16.357550144195557},

  24: {x:273.2648468017578, y: 163.34035110473633, z: 16.357550144195557}, 
  25: {x:273.2648468017578, y: 146.34035110473633, z: 16.357550144195557},
  26: {x:273.2648468017578, y: 129.34035110473633, z: 16.357550144195557}, 
  27: {x:273.2648468017578, y: 112.34035110473633, z: 16.357550144195557}, 
  28: {x:273.2648468017578, y: 96.34035110473633, z: 16.357550144195557}, 
  // 下颌
  38: {x:273.2648468017578, y: 91.34035110473633, z: 16.357550144195557}, 
  37: {x:273.2648468017578, y: 74.34035110473633, z: 16.357550144195557}, 
  36: {x:273.2648468017578, y: 57.34035110473633, z: 16.357550144195557}, 
  35: {x:273.2648468017578, y: 40.34035110473633, z: 16.357550144195557}, 
  34: {x:273.2648468017578, y: 23.34035110473633, z: 16.357550144195557}, 

  33: {x:253.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 
  32: {x:220.2648468017578, y: 22.34035110473633, z: 16.357550144195557},
  31: {x:187.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 
  41: {x:153.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 
  42: {x:120.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 
  43: {x:86.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 

  44: {x:67.2648468017578, y: 23.34035110473633, z: 16.357550144195557}, 
  45: {x:67.2648468017578, y: 40.34035110473633, z: 16.357550144195557}, 
  46: {x:67.2648468017578, y: 57.34035110473633, z: 16.357550144195557}, 
  47: {x:67.2648468017578, y: 74.34035110473633, z: 16.357550144195557}, 
  48: {x:67.2648468017578, y: 91.34035110473633, z: 16.357550144195557}
}

export const iprcardposition = {
  // 上颌
  18: {x:63.2648468017578, y: 97.34035110473633, z: 16.357550144195557}, 
  17: {x:63.2648468017578, y: 114.34035110473633, z: 16.357550144195557}, 
  16: {x:63.2648468017578, y: 131.34035110473633, z: 16.357550144195557}, 
  15: {x:63.2648468017578, y: 148.34035110473633, z: 16.357550144195557}, 
  14: {x:63.2648468017578, y: 165.34035110473633, z: 16.357550144195557}, 

  13: {x:87.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  12: {x:128.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  11: {x:169.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  21: {x:210.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  22: {x:251.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 

  23: {x:276.2648468017578, y: 165.34035110473633, z: 16.357550144195557}, 
  24: {x:276.2648468017578, y: 148.34035110473633, z: 16.357550144195557},
  25: {x:276.2648468017578, y: 131.34035110473633, z: 16.357550144195557}, 
  26: {x:276.2648468017578, y: 114.34035110473633, z: 16.357550144195557}, 
  27: {x:276.2648468017578, y: 97.34035110473633, z: 16.357550144195557}, 
  28: {x:276.2648468017578, y: 78.34035110473633, z: 16.357550144195557},
  // 下颌
  38: {x:276.2648468017578, y: 91.34035110473633, z: 16.357550144195557}, 
  37: {x:276.2648468017578, y: 91.34035110473633, z: 16.357550144195557}, 
  36: {x:276.2648468017578, y: 74.34035110473633, z: 16.357550144195557}, 
  35: {x:276.2648468017578, y: 57.34035110473633, z: 16.357550144195557}, 
  34: {x:276.2648468017578, y: 40.34035110473633, z: 16.357550144195557}, 
  33: {x:276.2648468017578, y: 23.34035110473633, z: 16.357550144195557}, 

  32: {x:251.2648468017578, y: 20.34035110473633, z: 16.357550144195557},
  31: {x:210.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 
  41: {x:169.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 
  42: {x:128.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 
  43: {x:87.2648468017578, y: 20.34035110473633, z: 16.357550144195557}, 

  44: {x:63.2648468017578, y: 23.34035110473633, z: 16.357550144195557}, 
  45: {x:63.2648468017578, y: 40.34035110473633, z: 16.357550144195557}, 
  46: {x:63.2648468017578, y: 57.34035110473633, z: 16.357550144195557}, 
  47: {x:63.2648468017578, y: 74.34035110473633, z: 16.357550144195557}, 
  48: {x:63.2648468017578, y: 91.34035110473633, z: 16.357550144195557}
}

export const iprcardpositionEn = {
  // 上颌
  18: {x:81.2648468017578, y: 97.34035110473633, z: 16.357550144195557}, 
  17: {x:81.2648468017578, y: 114.34035110473633, z: 16.357550144195557}, 
  16: {x:81.2648468017578, y: 131.34035110473633, z: 16.357550144195557}, 
  15: {x:81.2648468017578, y: 148.34035110473633, z: 16.357550144195557}, 
  14: {x:81.2648468017578, y: 165.34035110473633, z: 16.357550144195557}, 

  13: {x:105.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  12: {x:137.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  11: {x:169.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  21: {x:201.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 
  22: {x:233.2648468017578, y: 167.34035110473633, z: 16.357550144195557}, 

  23: {x:258.2648468017578, y: 165.34035110473633, z: 16.357550144195557}, 
  24: {x:258.2648468017578, y: 148.34035110473633, z: 16.357550144195557},
  25: {x:258.2648468017578, y: 131.34035110473633, z: 16.357550144195557}, 
  26: {x:258.2648468017578, y: 114.34035110473633, z: 16.357550144195557}, 
  27: {x:258.2648468017578, y: 97.34035110473633, z: 16.357550144195557}, 
  28: {x:258.2648468017578, y: 78.34035110473633, z: 16.357550144195557},
  // 下颌
  38: {x:258.2648468017578, y: 91.34035110473633, z: 16.357550144195557}, 
  37: {x:258.2648468017578, y: 91.34035110473633, z: 16.357550144195557}, 
  36: {x:258.2648468017578, y: 74.34035110473633, z: 16.357550144195557}, 
  35: {x:258.2648468017578, y: 57.34035110473633, z: 16.357550144195557}, 
  34: {x:258.2648468017578, y: 40.34035110473633, z: 16.357550144195557}, 
  33: {x:258.2648468017578, y: 23.34035110473633, z: 16.357550144195557}, 

  32: {x:233.2648468017578, y: 22.34035110473633, z: 16.357550144195557},
  31: {x:201.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 
  41: {x:169.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 
  42: {x:137.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 
  43: {x:105.2648468017578, y: 22.34035110473633, z: 16.357550144195557}, 

  44: {x:81.2648468017578, y: 23.34035110473633, z: 16.357550144195557}, 
  45: {x:81.2648468017578, y: 40.34035110473633, z: 16.357550144195557}, 
  46: {x:81.2648468017578, y: 57.34035110473633, z: 16.357550144195557}, 
  47: {x:81.2648468017578, y: 74.34035110473633, z: 16.357550144195557}, 
  48: {x:81.2648468017578, y: 91.34035110473633, z: 16.357550144195557}
}

export const fileCardcenter = {
  // 上颌
  18: [1, 0], 
  17: [1, 0], 
  16: [1, 0], 
  15: [1, 0], 
  14: [1, 0], 

  13: [0.5, 0], 
  12: [0.5, 0], 
  11: [0.5, 0], 
  21: [0.5, 0], 
  22: [0.5, 0], 
  23: [0.5, 0],

  24: [0, 0], 
  25: [0, 0],
  26: [0, 0], 
  27: [0, 0], 
  28: [0, 0], 
  // 下颌
  38: [0, 1], 
  37: [0, 1], 
  36: [0, 1], 
  35: [0, 1], 
  34: [0, 1], 

  33: [0.5, 1], 
  32: [0.5, 1],
  31: [0.5, 1], 
  41: [0.5, 1], 
  42: [0.5, 1], 
  43: [0.5, 1], 

  44: [1, 1], 
  45: [1, 1], 
  46: [1, 1], 
  47: [1, 1], 
  48: [1, 1]
}

export const iprfileCardcenter = {
  // 上颌
  18: [1, 0], 
  17: [1, 0], 
  16: [1, 0], 
  15: [1, 0], 
  14: [1, 0], 

  13: [0.5, 0], 
  12: [0.5, 0], 
  11: [0.5, 0], 
  21: [0.5, 0], 
  22: [0.5, 0], 

  23: [0, 0],
  24: [0, 0], 
  25: [0, 0],
  26: [0, 0], 
  27: [0, 0], 
  28: [0, 0], 
  // 下颌
  38: [0, 1], 
  37: [0, 1], 
  36: [0, 1], 
  35: [0, 1], 
  34: [0, 1], 
  33: [0, 1],
 
  32: [0.5, 1],
  31: [0.5, 1], 
  41: [0.5, 1], 
  42: [0.5, 1], 
  43: [0.5, 1], 

  44: [1, 1], 
  45: [1, 1], 
  46: [1, 1], 
  47: [1, 1], 
  48: [1, 1]
}

export const TYPE_CODE = {

  //种植修复
  IMPLANT_ABUTMENT_TITLE: 23200,
  IMPLANT_CUSTOM_ABUTMENT: 23201,
  IMPLANT_SCREW_RETAINED_CROWN: 23203,
  IMPLANT_BRIDGE_TITLE: 23500,
  IMPLANT_BRIDGE: 23501,
  IMPLANT_MODEL_TITLE: 23300,
  IMPLANT_MODEL: 23301,
  IMPLANT_OTHER_TITLE: 23400,
  IMPLANT_TRAY: 23401,
};
