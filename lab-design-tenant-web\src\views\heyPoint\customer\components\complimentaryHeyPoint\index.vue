<template>
  <el-dialog
    custom-class="complimentary-dialog"
    append-to-body
    top="8vh"
    width="640px"
    :title="lang('giveHeypoint')"
    :visible.sync="isShow"
    :close-on-click-modal="false"
    @close="onClose('complimentaryForm')">
    <el-form
      ref="complimentaryForm"
      :model="complimentaryForm"
      :rules="rules"
      :label-position="'left'"
      :label-width="language == 'zh' ? '180px' : '240px'"
      class="complimentary-form">
      <el-form-item :label="$t('heypoint.customer.customerName') + '：'" prop="userName">
        {{detailInfo.orgName}}
      </el-form-item>
      <el-form-item :label="lang('settlementType') + '：'" prop="settlementType">
        {{detailInfo.settlementType | settlementType}}
      </el-form-item>
      <el-form-item :label="lang('nowfreebalance') + '：'" prop="balance">
        {{detailInfo.giftBalance  | capitalize}}
      </el-form-item>
      <el-form-item :label="lang('nowAmount') + '：'" prop="complimentaryNum">
        <el-input-number v-model="complimentaryForm.complimentaryNum" :precision="2" :step="1"></el-input-number>
      </el-form-item>
      <el-form-item :label="lang('expiredday') + '：'" prop="effectiveDate">
        <el-date-picker
          class="effective-date"
          v-model="complimentaryForm.effectiveDate"
          align="right"
          type="date"
          :placeholder="lang('expiredday')"
          :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="lang('remark') + '：'" prop="remark" class="remark-item">
        <el-input
          v-model="complimentaryForm.remark"
          type="textarea"
          maxlength="500"
          :placeholder="lang('reson')"
          show-word-limit>
        </el-input>
      </el-form-item>
    </el-form>

    <div class="tips">{{lang('information')}}</div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose('complimentaryForm')">{{lang('close')}}</el-button>
      <el-button type="primary" :loading="complimentaryLoading" @click="confirm('complimentaryForm')">{{lang('submit')}}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { rechargeOrGift } from '@/api/heypoint';
import { getLang } from '@/public/utils';
import {
  settlementType,
  capitalize,
} from '@/filters/heypoint';
import { mapGetters } from 'vuex';
export default {
  name: 'ComplimentaryDialog',
  props: {
    isShow: {
      default: false
    },
    detailInfo: {
      default: () => {}
    }
  },
  filters: {
    settlementType,
    capitalize,
  },
  data() {
    return {
      complimentaryLoading: false,
      complimentaryForm: {
        complimentaryNum: null,
        remark: null,
        effectiveDate: null
      },
      rules: {
        complimentaryNum: [
          { required: true, message: this.lang('refill'), trigger: 'blur'},
          { type: 'number', message: this.lang('smaller'), min: 1, trigger: 'blur'}
        ],
        effectiveDate: [
          { required: true, message: this.lang('expiration'), trigger: 'change'}
        ]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
        shortcuts: [{
          text: this.lang('nextday'),
          onClick(picker) {
            const date = new Date();
            date.setDate(date.getDate() + 1);
            date.setHours(23);
            date.setMinutes(59);
            date.setSeconds(59);
            picker.$emit('pick', date);
          }
        }, {
          text: this.lang('week'),
          onClick(picker) {
            const date = new Date();
            date.setDate(date.getDate() + 7);
            date.setHours(23);
            date.setMinutes(59);
            date.setSeconds(59);
            picker.$emit('pick', date);
          }
        }, {
          text: this.lang('days'),
          onClick(picker) {
            const date = new Date();
            date.setDate(date.getDate() + 30);
            date.setHours(23);
            date.setMinutes(59);
            date.setSeconds(59);
            picker.$emit('pick', date);
          }
        }]
      },
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  methods: {
    lang: getLang('heypoint.customer.operate'),
    /**
     * 关闭弹窗
     * @param formName 表单名称
     */
    onClose(formName) {
      this.$refs[formName].resetFields();
      this.$emit('update:isShow', false);
      this.$emit('refreshInfo');
    },

    /**
     * 提交充值
     */
    confirm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            this.complimentaryLoading = true;
            const { orgCode } = this.$route.query;
            const date = new Date(this.complimentaryForm.effectiveDate);
            date.setHours(23);
            date.setMinutes(59);
            date.setSeconds(59);
            const params = {
              effectiveDate: parseInt(new Date(date).getTime() / 1000),
              operationType: 2,
              orgCode: Number(orgCode),
              num: this.complimentaryForm.complimentaryNum,
              remark: this.complimentaryForm.remark,
            };
            const { code, msg } = await rechargeOrGift(params);
            if (code === 200) {
              this.onClose('complimentaryForm');
            } else {
              this.$message({
                type: 'error',
                message: msg
              });
            }
          } catch (error) {
            console.error('error: ', error);
          } finally {
            this.complimentaryLoading = false;
          }
        }
      })
    },
    //结算类型
    settlementType(param) {
      if (param === undefined) return
      var monthly = this.lang('monthly');
      var prepaid = this.lang('prepaidMonth');
      const typeMap = {
        0: monthly,
        1: prepaid
      }
      return typeMap[param]
    },
  }
}
</script>

<style lang="scss">
.complimentary-dialog {
  .el-dialog__body {
    padding: 40px 64px 0;
    .complimentary-form {
      color: $hg-label;
      .el-form-item {
        .el-form-item__content {
          width: 320px;
          .el-input-number {
            width: 320px;
            .el-input-number__decrease, .el-input-number__increase {
              height: 37px;
            }
            .el-input {
              .el-input__inner {
                height: 39px;
              }
            }
          }
          .el-date-editor {
            width: 320px;
          }
        }
        
        &.remark-item {
          .el-form-item__content {
            width: 320px;
            .el-textarea__inner {
              width: 320px;
              height: 90px;
            }
            .el-input__count {
              height: 20px;
              line-height: 20px;
            }
          }
        }
        &.is-error {
          margin-bottom: 22px;
        }
        .effective-date {
          .el-input__prefix {
            margin-left: 5px;
            left: 0;
          }
        }
      }
    }
    .tips {
      width: 640px;
      word-break: break-word;
      color: #DC5050;
      margin: 10px 0 14px 0;
    }
  }
  .el-dialog__footer {
    text-align: right;
    .dialog-footer {
      .el-button {
        width: 112px;
        height: 40px;
        padding: 0;
      }
    }
  }
}
</style>
