import * as THREE from 'three'
import { _triangle } from '../triangle'

export function getThreeVector3MidPoint(v1, v2, v3) {
  const midpoint = new THREE.Vector3()
  const triangle = _triangle
  triangle.set(v1, v2, v3)
  triangle.getMidpoint(midpoint)

  return midpoint
}

export function getThreeVector3Normal(v1, v2, v3) {
  const direction = new THREE.Vector3()
  const triangle = _triangle
  triangle.set(v1, v2, v3)
  triangle.getNormal(direction)

  return direction
}

export function getThreeVector3Plane(v1, v2, v3) {
  const direction = new THREE.Vector3()
  const midpoint = new THREE.Vector3()

  const triangle = _triangle
  triangle.set(v1, v2, v3)
  triangle.getMidpoint(midpoint)
  triangle.getNormal(direction)

  const plane = new THREE.Plane()
  plane.setFromNormalAndCoplanarPoint(direction, midpoint)

  return plane
}
