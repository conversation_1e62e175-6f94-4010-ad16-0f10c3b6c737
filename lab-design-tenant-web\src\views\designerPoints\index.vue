<template>
  <div class="designer-points" v-loading="loadingList">
    <!-- 导航栏 -->
    <div class="points-header">
      <div class="time-search">
        <span class="label">{{lang('date')}}</span>
        <el-date-picker v-model="date" type="month" :picker-options="pickerOptions" format="yyyy-MM" value-format="yyyy-MM" placeholder="选择月" :clearable="false" @change="changeMonth"></el-date-picker>
        <span class="date-icon el-icon-date"></span>
      </div>
      <div class="points-btnlist">
        <el-button type="primary" v-if="!isCreatDesignerList" @click="startCount">{{lang('staticsBtn')}}</el-button>
        <el-button type="primary" v-if="isCreatDesignerList && !isPublished" @click="assignPoints">{{lang('allocation')}}</el-button>
        <el-button plain v-if="isCreatDesignerList && !isPublished" @click="startCount">{{lang('againCount')}}</el-button>
        <el-button type="primary" v-if="isCreatDesignerList && !isPublished" @click="publishPoints">{{lang('fabu')}}</el-button>
        <el-button plain v-if="isCreatDesignerList && isPublished" @click="rebackPoints">{{lang('backAll')}}</el-button>
        <el-button type="primary" v-if="isCreatDesignerList" @click="exportExcel">{{lang('export')}}</el-button>
      </div>
    </div>
    <!-- 内容 -->
    <div class="points-table" v-if="isCreatDesignerList">
      <!-- 搜索栏 -->
      <div class="search-list">
        <div class="one-search design-group">
          <span class="label">{{lang('designGroup')}}</span>
          <el-cascader v-model="searchData.deptIds" :options="designGroupList" @change="search('deptIds')" popper-class="design-group-cascader"
          :props="{ multiple: true, value: 'orgCode', label: 'orgName', children: 'sonList' }" 
          collapse-tags clearable></el-cascader>
        </div>
        <div class="one-search">
          <span class="label">{{lang('desinger')}}</span>
          <el-select v-model="searchData.userCodes" multiple filterable collapse-tags @change="search('userCodes')" :placeholder="lang('designPlaceholder')">
            <el-option v-for="item in designerList" :key="item.userCode" :label="item.userName" :value="item.userCode"></el-option>
          </el-select>
        </div>
        <div class="special-search">
          <el-select v-model="searchData.queryField" @change="search('queryField')" :placeholder="lang('tips')">
            <el-option v-for="item in completeList" :key="item.value" :label="lang(item.label)" :value="item.value"></el-option>
          </el-select>
          <el-select class="other-select" @change="search('queryField')" v-model="searchData.querySymbols" :placeholder="lang('tips')">
            <el-option v-for="item in biteList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-input :placeholder="lang('pleaseInput')" @change="search('queryField')" v-model="searchData.queryValue" @input="handleInput">
            <span slot="suffix" v-if="searchData.queryField == 'completionRate'" class="special-icon">%</span>
          </el-input>
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-list">
        <hg-table :header-data="headerData" :height="'auto'" class="user-table" :loading="tableLoading" :customNoData="true" @sortChange="sortChange" :data="pointsList" @update-selected-rows="selectTable">
          <template v-slot:no="{ row, index }">
            <span>{{ index + 1 }}</span>
          </template>
          <!-- 月总指标|月完成点数 -->
          <template #monthlyIndicatorPoints="scope">
            <span>{{ scope.row.monthlyIndicatorPoints }} | {{ getNum(scope.row.monthlyCompletionPoints) }}</span>
          </template>
          <!-- 完成率 -->
          <template #completionRate="scope">
            <span style="color: #E55353;" v-if="scope.row.completionRate < 1">{{ getRate(scope.row.completionRate) }}%</span>
            <span v-else>{{ getRate(scope.row.completionRate) }}%</span>
          </template>
          <!-- 超额点数 -->
          <template #excessPoints="scope">
            <span >{{ getNum(scope.row.excessPoints) }}</span>
            <!-- <span v-else>0.00</span> -->
          </template>
          <!-- 技能等级 -->
          <template #levelCode="scope">
            <span>{{ scope.row.levelCode }}{{lang('level')}}</span>
          </template>
          <!-- 操作 -->
          <template #other="scope">
            <el-tooltip class="item" effect="dark" :content="lang('scanPoint')" placement="bottom-start">
              <hg-icon style="font-size: 24px;" icon-name="icon-description" @click="scanDetails(scope.row)"></hg-icon>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="lang('board')" placement="bottom-start">
              <hg-icon v-if="isPublished" style="font-size: 24px;margin-left: 10px;" icon-name="icon-data_thresholding" @click="goPerson(scope.row)"></hg-icon>
            </el-tooltip>
          </template>
        </hg-table>
      </div>
      <div class="depart-pagination">
        <pagination :total="page.total" :disabled="tableLoading" :initPageIndex="page.pageNo" :initPageSize="page.pageSize" @onSearch="search"></pagination>
      </div>
    </div>

    <!-- 无内容显示 -->
    <div v-if="!isCreatDesignerList && !loadingList" class="points-table no-data">
      <img class="no-data-img" src="@/assets/images/designPoints/pic-notablet.png">
      <div><span>{{lang('nodata')}}</span><span class="statics-btn" @click="startCount">{{lang('nodataBtn')}}</span></div>
    </div>
    <pointAllocation :drawer.sync="drawer" :date="date" :designerList="designerList" @addSuccess="addSuccess"></pointAllocation>
    <statics-count :drawer.sync="staticsdrawer" :date="date" :isCreatDesignerList="isCreatDesignerList" @successCreat="successCreat"></statics-count>
    <pointsDetails :drawer.sync="pointDetailsDrawer" :rowData="rowData" :isHaveAllo="!isPublished" @allocatePoints="allocatePoints"></pointsDetails>
  </div>
</template>

<script>
import { getAllDesignGroups, getDesignerPointslist, publicList, withdraw, generated, downloadExcel } from '@/api/designPoints';
import { getUserList } from '@/api/order';
import { getDownloadUrl } from '@/api/file';
import hgTable from '@/components/HgTable';
import pagination from '@/components/Pagination';
import pointAllocation from './components/pointAllocation';
import staticsCount from './components/staticsCount';
import pointsDetails from './components/pointsDetails';
import { directDown, createIFrameDownLoad } from "@/public/utils/file.js";
import { getLang } from '@/public/utils';
import { server } from "@/config";
import { mapGetters } from 'vuex';
export default {
  name: "designerPoints",
  components: {
    hgTable,pagination,pointAllocation,staticsCount,pointsDetails
  },
  data() {
    return {
      pickerOptions: {
        disabledDate: (time) => {
          let nowYear = new Date().getFullYear();
          let nowMonth = new Date().getMonth() + 1;
          let nowYM = nowYear + '-' + (nowMonth >= 10 ? nowMonth : ('0' + nowMonth));
          let timeM = time.getMonth() + 1;
          let timeYM = time.getFullYear() + '-' + (timeM >= 10 ? timeM : ('0' + timeM));
          return timeYM > nowYM
        }
      },
      date: '',
      isCreatDesignerList: false,// 是否已经生成
      loadingList: true,
      isPublished: false, // 是否已经发布
      searchData: {
        deptIds: [],
        userCodes: [],
        queryField: '',
        querySymbols: '',
        queryValue: ''
      },
      designerList: [],
      designGroupList: [],
      completeList:[{value: 'completionRate',label: 'complete'},{value: 'excessPoints',label: 'overPoints'},{value: 'totalTimeoutQty',label: 'overTime'},{value: 'totalReturnQty',label: 'back'},{value: 'totalFreeQty',label: 'free'}],
      biteList: [{value: '>=',label: '>='},{value: '>',label: '>'},{value: '=',label: '='},{value: '<',label: '<'},{value: '<=',label: '<='}],
      tableLoading: false,
      page: {
        pageSize: 10,
        pageNo: 1,
        total: 10,
      },
      asc: '',
      sortField: '',
      pointsList: [],
      drawer: false,
      staticsdrawer: false,
      pointDetailsDrawer: false,
      rowData: {
        date: '',
        userCode: '',
        userName: ''
      }
    }
  },
  computed: {
    ...mapGetters(['orgCode', 'language']),
    headerData() {
      return [
        {
          prop: 'no',
          width: '80px',
          noTip: false,
          getLabel: () => {
            return this.lang('number');
          },
        },
        {
          prop: 'userName',
          minWidth: '25%',
          noTip: true,
          getLabel: () => {
            return this.lang('desinger');
          },
        },
        {
          prop: 'levelCode',
          minWidth: '25%',
          noTip: false,
          getLabel: () => {
            return this.lang('skillLevel');
          },
        },
        {
          prop: 'monthlyStandardPoints',
          minWidth: '25%',
          noTip: false,
          getLabel: () => {
            return this.lang('monthStard');
          },
        },
        {
          prop: 'monthlyIndicatorPoints',
          minWidth: '35%',
          noTip: true,
          getLabel: () => {
            return this.lang('monthAll');
          },
        },
        {
          prop: 'excessPoints',
          minWidth: '24%',
          noTip: true,
          sortable: true,
          getLabel: () => {
            return this.lang('overPoints');
          },
        },
        {
          prop: 'completionRate',
          minWidth: '30%',
          noTip: true,
          sortable: true,
          getLabel: () => {
            return this.lang('complete');
          },
        },
        {
          prop: 'totalTimeoutQty',
          minWidth: '20%',
          noTip: true,
          sortable: true,
          getLabel: () => {
            return this.lang('overTime');
          },
        },
        {
          prop: 'totalReturnQty',
          minWidth: '20%',
          noTip: true,
          sortable: true,
          getLabel: () => {
            return this.lang('back');
          },
        },
        {
          prop: 'totalFreeQty',
          minWidth: '20%',
          noTip: true,
          sortable: true,
          getLabel: () => {
            return this.lang('free');
          },
        },
        {
          prop: 'other',
          minWidth: '30px',
          noTip: true,
          getLabel: () => {
            return this.lang('operate');
          },
        },
      ];
    }
  },
  created () {
    this.setTime();
    this.isCreatDesignerPointer();
    this.getAllDesignGroups();
  },
  mounted () {

  },
  methods: {
    lang: getLang('designpoints'),
    handleInput(){
      // 只允许负号、小数点和数字
      let reg = /[^0-9.-]/g; // 匹配所有不合法字符
      this.searchData.queryValue = this.searchData.queryValue.replace(reg, '');

      // 处理负号和小数点的逻辑
      if (this.searchData.queryValue.split('.').length > 2) {
          // 如果有多个小数点，保留第一个
          this.searchData.queryValue = this.searchData.queryValue.replace(/\.(?=.*\.)/g, '');
      }
      if (this.searchData.queryValue.indexOf('-') > 0) {
          // 如果负号不在开头，移除负号
          this.searchData.queryValue = this.searchData.queryValue.replace(/-/g, '');
      }
    },
    // 判断是否已经生成统计
    async isCreatDesignerPointer(){
      this.loadingList = true;
      const {code, data} = await generated(this.date);
      if(code == 200){
        this.getDesignerPointslist();
        this.isCreatDesignerList = data.generated;
        this.isPublished = data.published;
      } else {
        this.isCreatDesignerList = false;
        this.isPublished = false;
      }
      this.loadingList = false;
    },
    // 改变日期
    changeMonth(){
      this.page.pageNo = 1;
      this.page.pageSize = 10;
      this.isCreatDesignerPointer();
    },
    /**
     * 设置默认时间
     */
     setTime() {
      // 获取当月第一天和最后一天
      const year = new Date().getFullYear();
      const month = new Date().getMonth();
      const endMonth = new Date(year, month, 0, 23, 59, 59);
      const formattedDate = endMonth.toISOString().slice(0, 7);
      this.date = formattedDate;
    },
    // 获取设计师组
    async getAllDesignGroups(){
      const { code, data } = await getAllDesignGroups();
      this.designGroupList = data;
      getUserList('').then((res)=>{
        this.designerList = res.data;
      })
    },
    // 改变搜索
    async search(type, searchData) {
      this.tableLoading = true;
      if(type == 'deptIds'){
        if(this.searchData.userCodes.length){
          this.searchData.userCodes = []
        }
      } else if(type == 'userCodes'){
        if(this.searchData.deptIds.length){
          this.searchData.deptIds = []
        }
      }
      if (type == 'page') {
        const { pageIndex, pageSize } = searchData;
        this.page.pageNo = pageIndex;
        this.page.pageSize = pageSize;
      } else {
        this.page.pageNo = 1;
        this.page.pageSize = 10;
      }
      this.getDesignerPointslist();
    },
    // 排序
    sortChange(data){
      this.asc = data.order == "ascending" ? true : false;
      this.sortField = data.prop;
      this.getDesignerPointslist();
    },
    // 获取列表
    async getDesignerPointslist(){
      // 设计组处理一下值
      let deptIds = []
      this.searchData.deptIds.forEach((item) => {
        deptIds = deptIds.concat(
          item.filter((it, index) => {
            return index == item.length - 1;
          })
        );
      })
      let queryValue = '';
      let queryField = '';
      let querySymbols = '';
      if(this.searchData.queryValue || this.searchData.queryValue === 0){
        queryValue = this.searchData.queryField != 'completionRate' ? this.searchData.queryValue : this.searchData.queryValue / 100;
        queryField = this.searchData.queryField;
        querySymbols = this.searchData.querySymbols;
      }
      let parame = {
        "asc": this.asc,
        "date": this.date, // 日期,格式（2024-03）
        "deptIds": deptIds, //部门ID 集合
        "pageNo": this.page.pageNo,
        "pageSize": this.page.pageSize,
        "queryField": queryField, // 查询字段 (completionRate: 完成率; excessPoints：超额点数; totalTimeoutQty: 超时订单总数; totalFreeQty: 免单订单总数; totalReturnQty: 免单订单总数)
        "querySymbols": querySymbols, // 查询的符号(>;>=;<=;<;=)
        "queryValue": queryValue, // 查询的值(x 由用户输入)
        "sortField": this.sortField, // 排序字段 (completionRate: 完成率; excessPoints：超额点数; totalTimeoutQty: 超时订单总数; totalFreeQty: 免单订单总数; totalReturnQty: 免单订单总数)
        "userCodes": this.searchData.userCodes  // 用户编码
      }
      const { code, data } = await getDesignerPointslist(parame);
      if(code == 200){
        this.pointsList = data.data;
        this.page.pageNo = data.pageNo;
        this.page.pageSize = data.pageSize;
        this.page.total = data.totalSize;
        this.tableLoading = false;
      }
    },
    getRate(value){
      return (value * 100).toFixed(2);
    },
    getNum(value){
      return value.toFixed(4)
    },
    // 选择某一行
    selectTable(){

    },
    // 开始统计
    startCount(){
      this.staticsdrawer = true;
    },
    // 点数分配
    assignPoints(){
      this.drawer = true;
    },
    // 详情的点数分配
    allocatePoints(){
      this.pointDetailsDrawer = false;
      this.drawer = true;
    },
    // 点数分配成功
    addSuccess(type){
      this.drawer = false;
      this.page.pageNo = 1;
      this.page.pageSize = 10;
      this.getDesignerPointslist();
    },
    // 生成成功
    successCreat(){
      this.staticsdrawer = false;
      this.page.pageNo = 1;
      this.page.pageSize = 10;
      this.isCreatDesignerPointer();
    },
    // 发布
    async publishPoints(){
      this.$confirm(this.lang('isFabu'), this.lang('systemTips'), {
        confirmButtonText: this.lang('submitBtn'),
        cancelButtonText: this.lang('cancel')
      }).then(async() => {
        const { code, data } = await publicList(this.date);
        if(code == 200){
          this.$message.success(this.lang('released'));
          this.isCreatDesignerPointer();
        }
      })
      .catch(action => {
        
      });
    },
    // 全部撤回
    async rebackPoints(){
      this.$confirm(this.lang('backtips'), this.lang('systemTips'), {
        confirmButtonText: this.lang('submitBtn'),
        cancelButtonText: this.lang('cancel')
      }).then(async() => {
        try {
          const {code, data} = await withdraw(this.date);
          if(code == 200){
            this.$message.success(this.lang('restacted'));
            this.isCreatDesignerPointer();
          }
        } catch (error) {
          this.$message.error(this.lang('overSix'))
        }
      })
      .catch(action => {
        
      });
    },
    // 查看明细
    scanDetails(row){
      this.rowData = {
        date: this.date,
        userCode: row.userCode,
        userName: row.userName
      }
      this.pointDetailsDrawer = true;
    },
    // 统计详情
    goPerson(row){
      this.$router.push({path: 'myDesignPoints', query: { date: this.date, userCode: row.userCode, userName: row.userName }})
    },
    // 导出excel
    async exportExcel(){
      this.$message.success(this.lang('downLoadWait'));
      // let url = `${server.designerPoints}/downloadExcel?month=${this.date}`;
      // directDown(url, `${this.date} ${this.lang('stats')}.xlsx`);
      const { code, data } = await downloadExcel(this.date);
      if(code == 200){
        const param = {
          s3FileId: data,
          filename: `${this.date} ${this.lang('stats')}`,
        };
        getDownloadUrl(param).then(res => {
          if(res.code === 200) {
            createIFrameDownLoad(res.data.url);
          }else {
            this.$hgOperateFail(this.$t('http.error.80080003'));
          }
        }).catch(err => {
          console.log('error:',err);
        });
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.designer-points {
  display: flex;
  flex-direction: column;
  height: 100%;
  /deep/.points-header {
    position: relative;
    height: 40px;
    .el-button.is-plain:focus{
      background: transparent;
    }
    .el-button--primary:focus{
      background: #3760EA;
      border-color: #3760EA;
    }
    .time-search{
      position: relative;
      width: 460px;
      display: flex;
      align-items: center;
      .el-input__prefix{
        display: none;
      }
      .label{
        margin-right: 10px;
      }
      .date-icon{
        position: absolute;
        right: 40px;
        font-size: 16px;
      }
    }
    .points-btnlist{
      position: absolute;
      right: 0px;
      top: 0;
    }
    .el-input{
      width: 400px;
      .el-input--prefix .el-input__inner{
        padding-left: 20px;
      }
    }
  }
  .points-table{
    flex: 1;
    display: flex;
    // height: 100%;
    flex-direction: column;
    background: #1B1D22;
    margin-top: 20px;
    .search-list{
      padding: 12px 24px;
      border-bottom: 1px solid #3D4047;
      /deep/.one-search{
        position: relative;
        display: inline-flex;
        align-items: center;
        margin-right: 20px;
        .el-input{
          width: 300px;
          background: #141519;
        }
        .label{
          margin-right: 10px;
        }
      }
      /deep/.special-search{
        display: inline-flex;
        margin-top: 10px;
        .el-select{
          width: 120px;
          margin-right: 10px;
        }
        .special-icon{
          display: inline-flex;
          height: 40px;
          width: 20px;
          align-items: center;
        }
        .el-input{
          width: 120px;
          background: #141519;
        }
        // .other-select{
        //   width: 80px;
        //   .el-input{ 
        //     width: 80px;
        //   }
        // }
      }
    }
    .table-list{
      flex: 1;
      .hg-table {
        height: 100%;
        overflow: auto;
      }
    }
    .design-group {
      /deep/.el-cascader__tags .el-tag {
        width: 65%;
        background: #1d1d1f;
        color: #fff;
        &:last-child {
          width: 25%;
        }
        &:first-child {
          width: 65%;
        }
        .el-tag__close {
          background-color: #1d1d1f;
          &::before{
            font-size: 16px;
            margin-top: 1px;
          }
        }
      }
    }
  }
  .no-data{
    display: flex;
    justify-content: center;
    align-items: center;
    .no-data-img{
      width: 300px;
      height: 200px;
      margin-bottom: 20px;
    }
    .statics-btn{
      text-decoration: underline;
      color: #5F8AFF;
      cursor: pointer;
    }
  }
}
</style>
<style lang="scss">
.design-group-cascader {
  box-shadow: 0px 0px 16px rgba(18, 19, 20, 0.32), 0px 8px 24px rgba(18, 19, 20, 0.2), 0px 12px 32px rgba(18, 19, 20, 0.12);
  border: solid 1px #1d1d1f;
  background: #1d1d1f;
  .el-cascader-panel {
    background: #1d1d1f;
  }
  .el-cascader-menu {
    border-right: solid 1px #38393d;
    &:last-child {
      border-right: none;
    }
  }
  .el-cascader-node:not(.is-disabled):hover,
  .el-cascader-node:not(.is-disabled):focus {
    background: #262629;
  }
  .is-empty {
    height: 80px;
  }
}
.design-group-cascader[x-placement^='bottom'] .popper__arrow::after {
  border-bottom-color: #1d1d1f;
}
.design-group-cascader[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #1d1d1f;
}
.el-month-table td.disabled .cell {
    background-color: #4b4c4f;
    cursor: not-allowed;
    color: #b1b7c1;
}
</style>
