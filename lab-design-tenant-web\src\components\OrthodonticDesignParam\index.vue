<template>
  <div class="orth-design-param">
    <div class="ipr-box" v-if="iprInfo">
      <div class="param-info">
        <p>{{ $t('component.ortho.iprTitle') }}</p>
        <div class="info">
          <div v-for="(key, kIndex) in Object.keys(iprInfo)" :key="kIndex" >
            <!-- 上下颌box -->
            <div :class="`ul-${key}`">
              <!-- 每个牙位 -->
              <div v-for="(item, index) in iprInfo[key]" :key="index" class="li-box">
                <span>{{ item.number }}</span>
                <div v-if="index < iprInfo[key].length - 1" class="button-box">
                  <ipr-popover :inputData="item" :disabled="disabled"></ipr-popover>
                  <p class="line"></p>
                </div>
              </div>
              
            </div>
            <!-- 中线 -->
            <div class="middle-line" v-if="kIndex === 0"><p></p><p></p></div>
          </div>
        </div>
      </div>
      <div :class="['open-tooth-canvas', showIPRTooth && 'is-open']">
        <p v-show="!showIPRTooth" @click="showIPRTooth = true">
          <span>{{ $t('component.ortho.btnShowTooth') }}</span>
          <hg-icon icon-name="hg-icon-viewer-next" iconfont-name="hg-common-iconfont"></hg-icon>
        </p>
        <div class="tooth-box">
          <div class="header">
            <span>{{ $t('component.ortho.toothTitle', [$t('component.ortho.iprTitle')]) }}</span>
            <hg-icon icon-name="hg-icon-im-close" iconfont-name="hg-common-iconfont" font-size="24px" @click="showIPRTooth = false"></hg-icon>
          </div>
          <!-- 这里是牙位图 -->
          <div class="tooth-canvas">
            <tooth-three-view :id="'ipr-tooth' + schemeId" :info="iprInfo" :mode="'ipr'"></tooth-three-view>
          </div>
        </div>
      </div>
    </div>
    
    <div class="addtional-box" v-if="additionInfo">
      <div class="param-info">
        <p>{{ $t('component.ortho.additionTitle') }}</p>
        <div class="info">
          <div v-for="(key, aIndex) in Object.keys(additionInfo)" :key="aIndex" >
            <div :class="['addition-ul',`addition-ul_${key}`]">
              <!-- 每个牙位 -->
              <div 
                v-for="(item, index) in additionInfo[key]" 
                :key="index" 
                :class="{'li-box': true, 'is-center': index+1 === Math.round(additionInfo[key].length/2)}">
                <addition-popover v-model="item.steps" :inputData="item" :disabled="disabled"></addition-popover>
              </div>
            </div>
            <!-- 中线 -->
          </div>
        </div>
      </div>
      <div :class="['open-tooth-canvas', showAddTooth && 'is-open']">
        <p v-show="!showAddTooth" @click="showAddTooth = true">
          <span>{{ $t('component.ortho.btnShowTooth') }}</span>
          <hg-icon icon-name="hg-icon-viewer-next" iconfont-name="hg-common-iconfont"></hg-icon>
        </p>
        <div class="tooth-box">
          <div class="header">
            <span>{{ $t('component.ortho.toothTitle', [$t('component.ortho.additionTitle')]) }}</span>
            <hg-icon icon-name="hg-icon-im-close" iconfont-name="hg-common-iconfont" font-size="24px" @click="showAddTooth = false"></hg-icon>
          </div>
          <!-- 牙位图 -->
          <div class="tooth-canvas">
            <tooth-three-view :id="'addition-tooth' + schemeId" :info="additionInfo" :mode="'addition'"></tooth-three-view>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import IprPopover from './components/IprPopover';
import AdditionPopover from './components/AdditionPopover';
import { initIPR, initAddData, } from './data.js';
import ToothThreeView from './components/ToothThreeView';

export default {
  components: { IprPopover, AdditionPopover, ToothThreeView },
  model: {
    prop: 'sourceData',
    event: 'update'
  },
  props: {
    disabled: Boolean,
    sourceData: {
      type: Object,
      default() {
        return null;
      }
    },
    // 方案信息
    schemeId: {
      type: [Number, String],
      default: 'new'
    }
  },
  data() {
    return {
      iprInfo: {
        upper: [],
        lower: [],
      },
      additionInfo: null,
      showIPRTooth: false,
      showAddTooth: false,
      iprThreeView: null,
      additionThreeView: null
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      if(this.sourceData && this.sourceData.iprInfo) {
        this.iprInfo = this.sourceData.iprInfo || initIPR();
        this.additionInfo = this.sourceData.additionInfo || initAddData();
      }else {
        this.iprInfo = initIPR();
        this.additionInfo = initAddData();
      }
    },

    onSave() {
      const data = { iprInfo: this.iprInfo, additionInfo: this.additionInfo };
      this.$emit('update', data);
    },
  },
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>

