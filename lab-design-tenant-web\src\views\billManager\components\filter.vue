<template>
  <div class="bill-filter">
    <el-form :inline="true" class="filter-list">
      <!-- <el-radio-group
        v-model="conditions.billType"
        class="bill-type"
        @change="typeChange"
      >
        <el-radio-button :label="1">{{ lang('day') }}</el-radio-button>
        <el-radio-button :label="3">{{ lang('week') }}</el-radio-button>
        <el-radio-button :label="2">{{ lang('month') }}</el-radio-button>
      </el-radio-group> -->
      <el-form-item :label="$t('heypoint.customer.customerNameNo')">
        <hg-input
          v-model="conditions.orgName"
          :placeholder="$t('heypoint.customer.customerNameNo')"
          clearable
          @change="searchData"
          @keyup.enter.native="searchData"
        ></hg-input>
      </el-form-item>
      <el-form-item :label="lang('billStatus')">
        <el-select
          :placeholder="lang('billStatus')"
          v-model="conditions.status"
          filterable
          @change="searchData"
        >
          <el-option
            v-for="item in settlementOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="lang('billDate')">
        <!-- <div v-show="conditions.billType === 1">
          <date-range-picker
            type="daterange"
            valueFormat="timestamp"
            format="yyyy-MM-dd"
            :clearable="false"
            v-model="conditions.dayTime"
            @change="searchData"
          ></date-range-picker>
        </div> -->
        <div>
          <el-date-picker
            v-model="conditions.monthTime"
            type="monthrange"
            align="right"
            unlink-panels
            :start-placeholder="$t('common.condition.startMonth')"
            :end-placeholder="$t('common.condition.endMonth')"
            :picker-options="pickerOptions"
            :clearable="false"
            @change="searchData"
          >
            <hg-icon
              slot="range-separator"
              icon-name="icon-arrow-time-lab"
            ></hg-icon>
          </el-date-picker>
        </div>
        <!-- <div v-show="conditions.billType === 3">
          <el-date-picker
            v-model="weekTime"
            popper-class="week"
            :picker-options="{'firstDayOfWeek': 1}"
            type="week"
            :format="startTime +' —> '+ endTime"
            :clearable="false"
            @change="changeweek"
            :placeholder="lang('selectTimeTip')">
          </el-date-picker>
        </div> -->
      </el-form-item>
      <el-form-item :label="lang('currency')">
        <el-select
          :placeholder="lang('leftDrawer.all')"
          v-model="conditions.currency"
          @change="searchData"
        >
          <el-option
            v-for="item in billMoneyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="lang('leftDrawer.saleOrderStatus')">
        <el-select
          :placeholder="lang('leftDrawer.all')"
          v-model="conditions.pushStatus"
          @change="searchData"
        >
          <el-option
            v-for="item in crmList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item :label="lang('leftDrawer.saleOrderStatus')">
        <el-select
          :placeholder="lang('leftDrawer.all')"
          v-model="conditions.pushStatus"
          @change="searchData"
        >
          <el-option
            v-for="item in saleOrderStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item :label="lang('leftDrawer.updateOrderStatus')">
        <el-select
          :placeholder="lang('leftDrawer.all')"
          v-model="conditions.updateOrderStatus"
          @change="searchData"
        >
          <el-option
            v-for="item in crmList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-radio v-model="conditions.isChangeCurrency" label="1" @click.native.prevent="cancelCurrency('1')">{{lang('currencyChange')}}</el-radio>
        <el-radio class="radio-width" v-model="conditions.notZero" label="1" @click.native.prevent="cancelSelect('1')">{{lang('hide')}}</el-radio>
      </el-form-item>
    </el-form>
    <slot></slot>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
export default {
  name: 'HeyPointCustomerFilter',
  components: {},
  props: {
    conditions: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      settlementOptions: [{
        label: this.lang('leftDrawer.all'),
        value: '',
      },
      {
        label: this.lang('leftDrawer.draft'),
        value: 2,
      },
      {
        label: this.lang('leftDrawer.unconfirmed'),
        value: 1,
      },
      {
        label: this.lang('leftDrawer.confirm'),
        value: 0,
      },
      {
        label: this.lang('leftDrawer.chanllenged'),
        value: 3,
      }],
      billMoneyOptions: [{
        label: this.lang('leftDrawer.all'),
        value: '',
      },
      {
        label: this.lang('leftDrawer.cny'),
        value: 1,
      },{
        label: this.lang('leftDrawer.usd'),
        value: 0,
      },{
        label: this.lang('leftDrawer.eur'),
        value: 2,
      },{
        label: this.lang('leftDrawer.jpy'),
        value: 3,
      },{
        label: this.lang('leftDrawer.aud'),
        value: 4,
      }],
      crmList: [
      {
        label: this.lang('leftDrawer.all'),
        value: '',
      },
      {
        value: 0,
        label: this.lang('leftDrawer.nosent')
      },{
        value: 1,
        label: this.lang('leftDrawer.sent')
      },{
        value: 2,
        label: this.lang('leftDrawer.faile')
      },{
        value: 3,
        label: this.lang('leftDrawer.deleteError')
      }],
      updateOrderStatus:[
       {
        value: 0,
        label: this.lang('leftDrawer.nosent')
      },{
        value: 1,
        label: this.lang('leftDrawer.sent')
      },{
        value: 2,
        label: this.lang('leftDrawer.faile')
      }
      ],
      startTime: '',
      endTime: '',
      weekTime:''
    };
  },
  computed: {
    pickerOptions() {
      return {
        shortcuts: [
          {
            text: this.$t('common.condition.lastMonth'),
            onClick(picker) {
              const start = new Date();
              const end = new Date();
              start.setMonth(start.getMonth() - 1);
              end.setMonth(end.getMonth() - 1);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: this.$t('common.condition.yearToDate'),
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: this.$t('common.condition.lastSixMonth'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 6);
              end.setMonth(end.getMonth() - 1);
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      };
    },
  },
  watch: {
    'conditions': {
      handler(newVal) {
        console.log('触发监听111', newVal);
        if (newVal.weekTime) {
          this.weekTime=newVal.weekTime[0]
          this.startTime=this.formatDateTime(newVal.weekTime[0])
          this.endTime=this.formatDateTime(newVal.weekTime[1])
          console.log('触发监听22', newVal,this.startTime);
        }
      },
      deep: true,
      immediate:true
    }
  },
  methods: {
    lang: getLang('bill'),
    /**
     * 
     */
     changeweek(val) {
       console.log('日期value',  this.conditions.weekTime);
       const year = new Date(val).getFullYear();
       console.log('year: ', year);
       const month = new Date(val).getMonth();
       const date = new Date(val).getDate();
       if (year<1972) {
        this.conditions.weekTime = null;
         this.searchData();
         return
       }
       const startDay = new Date(year, month, date - 1)
       const endDay=new Date(year,month,(date+5),23,59,59)
       this.startTime=this.formatDateTime(startDay)
       this.endTime=this.formatDateTime(endDay)
       this.conditions.weekTime[0] = startDay;
       this.conditions.weekTime[1] = endDay;
       console.log('日期value22',startDay,endDay);
       this.searchData();
    },
    formatDateTime(date) {
      if (date) {
        const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}-${this.pad(month)}-${this.pad(day)} `;
      }
      
    },
    pad(num) {
    return num.toString().padStart(2, '0');
    },

    /**
     * 搜索
     */
    searchData() {
      this.$emit('searchData', 'filter');
    },
    // 批量下载
    batchClick(){
      this.$emit('batchClick');
    },

    /**
     * 账单日期类型回调函数
     * @param val 账单日期类型
     */
    typeChange(val) {
      if (val) {
        // if (val === 1) {
        //   this.settlementOptions = this.allStatusOptions;
        // } else if (val === 2||val===3) {
        //   this.settlementOptions = this.allStatusOptions.filter(
        //     (item) => item.value === 2
        //   );
        //   if (this.conditions.billStatus) {
        //     this.conditions.billStatus = 2;
        //   }
        // }
        this.$emit('searchData', 'filter');
      }
    },
    // 单选按钮的取消选中
    cancelSelect(value){
      value === this.conditions.notZero ? (this.conditions.notZero = '') : (this.conditions.notZero = value);
      this.searchData();
    },
    // 仅币种变更
    cancelCurrency(value){
      value === this.conditions.isChangeCurrency ? (this.conditions.isChangeCurrency = '') : (this.conditions.isChangeCurrency = value);
      this.searchData();
    }
  },
};
</script>

<style lang="scss" scope>
.bill-filter {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  .el-form {
    .date-range-picker {
      background: $hg-background;
    }
    .el-form-item {
      margin-right: 24px;
      margin-bottom: 20px;
    }
    .bill-type {
      margin-right: 24px;
    }
  }
  .filter-list{
    margin-right: 160px;
  }
  .operate-btn {
    margin-bottom: 20px;
    .el-button {
      margin-right: 14px;
    }
  }
  .radio-width{
    margin-right: 60px;
  }
  .bill-down-btn{
    position: absolute;
    // right: 0;
    right:0px;
    top:65px
  }
  .bill-export-btn{
    position: absolute;
    right: 110px;
  }
}
</style>
<style lang="scss">
.bill-filter{
  .el-input{
    width: 200px;
    .el-input__prefix{
      margin-left: 4px;
      font-size: 16px;
      .el-input__inner{
      font-size: 14px;
      padding-left: 32px;
    }
    }
    
    
  }
  .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner{
    box-shadow: none;
  }
  .el-select > .el-input{
    width: 130px;
  }
  .el-select > .el-input .el-input__inner{
    width: 130px;
  }
}
.week{
  
  .el-date-table__row:hover,.current{
        // background-color: #606266;
        color:#606266 ;
  }
}

</style>