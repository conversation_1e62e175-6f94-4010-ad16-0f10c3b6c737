/**
 * @description 权限问题的映射(消息提示无权限访问)
 */
const AUTH_STATUS = {
  TOKEN_AUTHORITY_FAILURE_1: 70100002, // 无权限访问
  TOKEN_AUTHORITY_FAILURE_2: 70140002, // 无权限访问:${业务错误提示}
  TOKEN_AUTHORITY_FAILURE_3: 70140001, // null
};

/**
 * @description 访问异常
 */
const ACCESS_STATUS = {
  SERVICE_ERROR_2: 70100000, // 服务异常，请稍后再尝试
  REFERER_FAILURE: 70100003, // "访问透传信息失败"
  REFERER_NULL: 70120001, // 访问透传信息失败:Referer为空，请确认！
  CONNECT_FAILURE: 70100004, // "连接访问服务失败"
  BUSINESS_ERRO: 70100006, // ${业务错误提示}
  BILL_ERROR: 11010034, // 账单业务错误，创建crm订单失败
  BILL_OPERAGE_ERROR: 12000001, // 账单业务错误，创建crm订单失败
};

/**
 * @description 网络状态码的映射（消息提示访问失败）
 */
const NETWORK_STATUS = {
  FAILURE: 400, //"操作失败"
  LOSE_AUTHORIZATION: 401, // 校验不通过
  FORBIDDEN: 403, // 服务器拒绝处理
  NOT_FOUND: 404, // 找不到对应服务
  SERVICE_ERROR: 500, //"服务器内部错误"
  PARAM_ERROR: 501, //"请求参数错误",
  PERMISSION_DENIED: 502, // "无权限访问"
  DABS_ERROR: 503, //"数据库操作失败"
  REDIS_ERROR: 504, //"redis错误"
  SERVICE_FAILURE: 505, //"服务调用失败"
  LOGIN_FAIL: 506, //"登录失败"
};

/**
 * @description 登录问题的映射(弹窗提示异地登录或登录过期，请重新登录)
 */
const LOGIN_ERR_STATUS = {
  REMOTE_LOGIN: 70110006, // 校验失败:异地登录，请先注销！
  TOKEN_TIMEOUT: 70110001, // 校验失败:Token已过期
  TOKEN_CHECK_FAILURE: 70110002, // 校验失败:Token验证失败
  ACCESSTOKEN_TIMEOUT: 70110003, // 校验失败:accessToken已过期，请重新登录！
  REFRESHTOKEN_TIMEOUT: 70110004, // 校验失败:refreshToken已过期，请重新登录！
  ACCESSTOKEN_NULL: 70110005, // 校验失败:accessToken无效，请重新登录！
  USER_CANCEL_2: 70110007, // 校验用户信息失败
  USER_CANCEL_1: 70100001, // 校验用户信息失败
  USER_INFO_TIMEOUT: 70130001, // 服务异常，请稍后再尝试:用户信息已过期，请重新登录！
  TOKEN_NULL: 70130002, // 服务异常，请稍后再尝试: token为空，请重新登录！
};

module.exports = {
  SUCCESS: 200, // 响应成功

  /**
   * @description 异地登录，请重新登录！
   */
  remoteLoginStatus: LOGIN_ERR_STATUS.REMOTE_LOGIN, 

  /**
   * @description 请重新登录（状态码）
   */
  loginErrors: [
    LOGIN_ERR_STATUS.TOKEN_TIMEOUT,
    LOGIN_ERR_STATUS.TOKEN_CHECK_FAILURE,
    LOGIN_ERR_STATUS.ACCESSTOKEN_TIMEOUT,
    LOGIN_ERR_STATUS.ACCESSTOKEN_NULL,
    LOGIN_ERR_STATUS.REFRESHTOKEN_TIMEOUT,
    LOGIN_ERR_STATUS.USER_CANCEL_1,
    LOGIN_ERR_STATUS.USER_CANCEL_2,
    LOGIN_ERR_STATUS.USER_INFO_TIMEOUT,
    LOGIN_ERR_STATUS.TOKEN_NULL,
  ],

  /**
   * @description 权限问题，无权访问
   */
  authErrors: [
    AUTH_STATUS.TOKEN_AUTHORITY_FAILURE_1, 
    AUTH_STATUS.TOKEN_AUTHORITY_FAILURE_2, 
    AUTH_STATUS.TOKEN_AUTHORITY_FAILURE_3,
  ],

  /**
   * @description 4xx 5xx
   */
  networkStatusError: [
    NETWORK_STATUS.FAILURE,
    NETWORK_STATUS.SERVICE_ERROR,
    NETWORK_STATUS.PARAM_ERROR,
    NETWORK_STATUS.PERMISSION_DENIED,
    NETWORK_STATUS.DABS_ERROR,
    NETWORK_STATUS.REDIS_ERROR,
    NETWORK_STATUS.SERVICE_FAILURE,
    NETWORK_STATUS.LOGIN_FAIL,
  ],

  /**
   * @description 访问异常（状态码）
   */
  accessStatusError: [
    ACCESS_STATUS.REFERER_FAILURE, 
    ACCESS_STATUS.REFERER_NULL, 
    ACCESS_STATUS.CONNECT_FAILURE, 
    ACCESS_STATUS.BUSINESS_ERRO,
    ACCESS_STATUS.SERVICE_ERROR_2,
  ],
  /**
   * @description账单管理的错误码（状态码）
   */
  billStatusError: [
    ACCESS_STATUS.BILL_ERROR,
    ACCESS_STATUS.BILL_OPERAGE_ERROR
  ]
};
