//全局组件注册
// 全局组件命名规范为 hg-xxx 避免和其他UI库冲突
import Vue from 'vue'
import HgButton from './HgButton';
import HgIcon from './HgIcon';
import HgCard from './HgCard';
import SvgIcon from './SvgIcon';
import HgInput from './HgInput';

import I18nText from './I18nText';

Vue.component('hg-button',HgButton);
Vue.component('hg-icon', HgIcon);
Vue.component('hg-card', HgCard);
Vue.component('svg-icon', SvgIcon);
Vue.component('hg-input', HgInput);

Vue.use(I18nText);