import { createPromise } from '../promise'

export function beforeAop(object, property, aop) {
  const originProperty = object[property]

  if (!(originProperty instanceof Function) || !(aop instanceof Function)) {
    return
  }

  object[property] = function () {
    let aopResult = aop.apply(this, arguments)
    const isAopResultPromise = aopResult instanceof Promise

    if (isAopResultPromise) {
      return createPromise(async (resolve) => {
        aopResult = await aopResult

        let originResult = originProperty.apply(this, arguments)
        const isOriginResultPromise = originResult instanceof Promise

        if (isOriginResultPromise) {
          originResult = await originResult
        }

        const result = originResult === undefined ? aopResult : originResult
        resolve(result)
      })

    } else {
      let originResult = originProperty.apply(this, arguments)
      const isOriginResultPromise = originResult instanceof Promise

      if (isOriginResultPromise) {
        return createPromise(async (resolve) => {
          originResult = await originResult

          const result = originResult === undefined ? aopResult : originResult
          resolve(result)
        })
      } else {
        const result = originResult === undefined ? aopResult : originResult
        return result
      }
    }
  }
}

export function afterAop(object, property, aop) {
  const originProperty = object[property]

  if (!(originProperty instanceof Function) || !(aop instanceof Function)) {
    return
  }

  object[property] = function () {
    let originResult = originProperty.apply(this, arguments)

    const isOriginResultPromise = originResult instanceof Promise

    if (isOriginResultPromise) {
      return createPromise(async (resolve) => {
        originResult = await originResult

        let aopResult = aop.apply(this, arguments)
        const isAopResultPromise = aopResult instanceof Promise

        if (isAopResultPromise) {
          aopResult = await aopResult
        }

        const result = aopResult === undefined ? originResult : aopResult
        resolve(result)
      })
    } else {
      let aopResult = aop.apply(this, arguments)
      const isAopResultPromise = aopResult instanceof Promise

      if (isAopResultPromise) {
        return createPromise(async (resolve) => {
          aopResult = await aopResult

          const result = aopResult === undefined ? originResult : aopResult
          resolve(result)
        })
      } else {
        const result = aopResult === undefined ? originResult : aopResult
        return result
      }
    }
  }
}
