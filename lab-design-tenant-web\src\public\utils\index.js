/**
 * @description 自动批量引用文件
 * @param {Object} context 
 * @param {RegExp} reg 
 * @returns {Object} module 返回一个对象{ key: value}
 */
export const autoImportModule = context => {
  const contextKeys = context.keys();
  let module = {};
  contextKeys.forEach(key => {
    module = {
      ...module,
      ...context(key).default
    };
  });
  return module;
};

/**
 * @description 自动批量引入模块，根据文件名生成二级module
 * @param {Object} context 
 * @returns {Object} module 如： ./store/app.js ./store/user.const.js => { app: {}, user: {} }
 */
export const autoImportModuleFiles = context => {
  const contextkeys = context.keys();
  const modulesFiles = contextkeys.reduce((modules, modulePath) => {
    const fileName = modulePath.split('/').pop();
    const moduleName = fileName.split('.').shift();
    const value = context(modulePath);
    modules[moduleName] = value.default;
    return modules;
  }, {}); // 使用reduce 一定要给初始值，不然默认第一个modules为数组内部第一个元素
  return modulesFiles;
}


/**
 * 简单复制：当前复制是深复制，但无法对Date类型的值进行复制（会转成字符串）
 * @param {Oject} sourceObject  array也是oject的一种
 */
export const copy = (sourceObject) => {
  let result;
  if (sourceObject instanceof Object) {
    try {
      result = JSON.parse(JSON.stringify(sourceObject));
    } catch (error) {
      console.log('copy JSON.parse 转换JSON失败，sourceObject不是规范的JSON格式');
    }
  }
  return result;
};

// 字符串转JSON
export const parseJson = (str) => {
  let result = str;
  if (typeof str === 'string') {
    try {
      result = JSON.parse(str);
    } catch (error) {
      console.log('parseJson str入参不是完整JSON格式的字符串',str);
      return null;
    }
  }
  return result;
}

/**
 * 深拷贝
 * @param {*} obj 拷贝对象
 * @param {*} hash weak对象
 * @returns 深拷贝的对象
 */
export function deepClone(obj, hash = new WeakMap()) {
  //递归拷贝
  if (obj instanceof RegExp) return new RegExp(obj);
  if (obj instanceof Date) return new Date(obj);
  if (obj === null || typeof obj !== 'object') {
    //如果不是复杂数据类型，直接返回
    return obj;
  }
  if (hash.has(obj)) {
    return hash.get(obj);
  }
  /**
   * 如果obj是数组，那么 obj.constructor 是 [Function: Array]
   * 如果obj是对象，那么 obj.constructor 是 [Function: Object]
   */
  let t = new obj.constructor();
  hash.set(obj, t);
  for (let key in obj) {
    //如果 obj[key] 是复杂数据类型，递归
    if (Object.prototype.hasOwnProperty.call(obj, key)) { //是否是自身的属性
      if (obj[key] && typeof obj[key] === 'object') {
        t[key] = deepClone(obj[key], hash);
      } else {
        t[key] = obj[key];
      }

    }
  }
  return t;
}

/**
 * 获取数据类型
 * @param {*} data 
 * @returns 
 */
 export function getType(data) {
  return Object.prototype.toString.call(data).slice(8, -1);
}

/**
 * 判断引用类型
 * @param data 
 * @param type
 * @returns {boolean}
 */
export function isType(data, type) {
  return getType(data) === type.replace(/(^|\s)(\w)/g, ($1) => $1.toUpperCase())
}

/**
 * 判断是否为空值/空数组/空对象
 * @param data
 * @returns {boolean}
 */
export function isEmptyResult(data) {
  // 判断假值（除0外）和空数组/对象
  if (!data && data !== 0 && data !== false) {
    return true;
  }

  // 判断时间
  if (data instanceof Date) {
    return false;
  }

  if (Array.isArray(data) && data.length === 0) {
    return true;
  } else if (getType(data) === 'Object' && Object.keys(data).length === 0) {
    return true;
  }

  return false;
}

/** 
 * 翻译函数
 * @param cate 模块名
 */
export function getLang(cate) {
  if (!cate) {
    console.warn('缺少命名空间')
    return 'Without Cate';
  }
  return function (title, ...params) {
    if (title === undefined) {
      return ''
    }

    if (this) {
      return this.$t(`${cate}.${title}`, ...params);
    } 
    // else {
    //   return window.cnsumerVm.$t(`${cate}.${title}`, ...params);
    // }
  }
}

/**
 * 根据codes，返回对应的设计类型
 * @param {Array,string} codes
 * @param {Array} paramTypes : 设计类型一维扁平数组
 */
 export const getTypeName = (codes, paramTypes) => {
  if (!codes) {
    return '';
  }
  if(typeof codes === 'string') {
    codes = codes.split(',');
  }
  
  let zhList = [], enList = [];
  if (paramTypes instanceof Array){
    codes.forEach(code => {
      const item = paramTypes.find(item => item.designCode === Number(code));
      if(item) {
        zhList.push(item.cnName);
        enList.push(item.enName);
      }
      /* paramTypes.forEach(item => {
        if(code == item.designCode){
          zhList.push(item.cnName);
          enList.push(item.enName);
        }
      }); */
    });
  }

  return {
    zh: zhList,
    en: enList
  };
}

/**
 * 根据名字判断是否为stl
 * @param {*} filename
 */
export const isStl = (filename) => {
  const fileType = filename.split('.').pop();
  if(fileType.toLowerCase() === 'stl'){
    return true;
  }
  return false;
}

/**
 * 
 * @param {string} base64 
 * @param {string} fileName 
 * @returns 
 */
export function base64toFile(base64, fileName = 'hey.png') {//将base64转换为文件
  var arr = base64.split(','), mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
  while(n--){
      u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, {type:mime});
} 