import Vue from 'vue';
import CryptoJS from 'crypto-js'; //导入加密模块
const key = CryptoJS.enc.Utf8.parse('0123456789abcdef'); //十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse('abcdef0123456789'); //十六位十六进制数作为密钥偏移量
import moment from 'moment'

/**
 * 加密方法
 * @param {String} value 需要加密的字符
 * @returns {String} 加密后的字符
 */
const enCrypto = function (value) {
  const srcs = CryptoJS.enc.Utf8.parse(value);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
  return encrypted.ciphertext.toString().toUpperCase();
}
/**
 * 解密方法
 * @param {String} value 需要解密的字符
 * @returns {String} 解密后的字符
 */
const deCrypto = function (value) {
  const encryptedHexStr = CryptoJS.enc.Hex.parse(value);
  const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  const decrypt = CryptoJS.AES.decrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
  const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}

// 判断是否为空
const isEmpty = function (str) {
  if (str == null) {
    return true
  }
  str = str.toString();
  str = str.replace(/\+ /g, "");
  str = str.replace(/[ ]/g, "");
  str = str.replace(/[\r\n]/g, "");
  if (str == "") {
    return true
  } else {
    return false
  }
}
// 防抖
const Debounce = function (fn, delay) {
  // 记录上一次的延时器
  var timer = null;
  delay = delay || 200;
  return function () {
    var args = arguments;
    // 清除上一次延时器
    clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay);
  }
}
/**
 * 节流函数
 * @param {*} fn
 * @param {*} gapTime
 */
const throttle = function (fn, gapTime = 1000) {
  let _lastTime = null;

  return function (args) {
    let _nowTime = + new Date()
    if (_nowTime - _lastTime > gapTime || !_lastTime) {
      fn.apply(this, args);
      _lastTime = _nowTime
    }
  }
}

/**
 * 多层对象合并
 * @param {Object} obj1
 * @param {Object} obj2
 */
const MergeRecursive = function(target, sources) {
  let newobj = {};
  let keys1 = Object.keys(target);
  let keys2 = Object.keys(sources);
  for (const key of keys1) {
    if (typeof target[key] === 'object' && typeof sources[key] === 'object') {
      newobj[key] = MergeRecursive(target[key], sources[key])
    } else {
      let value = keys2.indexOf(key) >= 0 ? sources[key] : target[key]
      newobj[key] = value
    }
  }
  for (const key of keys2) {
    if (!newobj[key]) {
      newobj[key] = sources[key]
    }
  }
  return newobj
}

/**
 * 兼容处理JSON.parse报错
 * @param { String } objString
 */
const parseJson = function (objString, errRetutrn = {}) {
  try {
    return JSON.parse(objString)
  } catch (err) {
    return errRetutrn
  }
}

/**
 * 格式化日期
 * @param {*} timeStr
 * @param {*} format
 */
const formatDate = function (timeStr, format = 'YYYY-MM-DD') {
  moment.locale()
  return moment(timeStr).format(format)
}

/**
 * 存储localStorage
 */
const setStore = (name, content) => {
	if (!name) return;
	if (typeof content !== 'string') {
		content = JSON.stringify(content);
	}
	window.localStorage.setItem(name, content);
}

/**
 * 获取localStorage
 */
const getStore = name => {
	if (!name) return;
	var value = window.localStorage.getItem(name);
    if (value !== null) {
        try {
            value = JSON.parse(value);
        } catch (e) {
            value = value;
        }
    }
    return value;
}

/**
 * 删除localStorage
 */
const removeStore = name => {
	if (!name) return;
	window.localStorage.removeItem(name);
}

/**
 * 菜单数组去重方法(只适用于本系统的菜单处理)
 */
const repeatMenusFilter = (arr, key) => {
  if (!arr.length) return [];

	let mergeArr = [];
  // 多个角色，需要整合
  arr.forEach(item => {
    mergeArr = mergeArr.concat(item[key]);
  });
  // 对数组中的对象进行去重处理
  let filterArr = []
  let obj = {};
  filterArr = mergeArr.reduce((addArr,curItem) => {
    obj[curItem.name] ? "" : addArr.push(curItem);
    return addArr;
  },[])

  return filterArr;
}

/**
 * 根据不同语言切换不同的显示内容
 */
 const changeByLang = (zhText, enText) => {
  let text = Vue.prototype.i18n.locale === 'en' ? enText : zhText;
  return text;
}

export {
  isEmpty,
  Debounce,
  throttle,
  enCrypto,
  deCrypto,
  MergeRecursive,
  parseJson,
  formatDate,
  setStore,
  getStore,
  removeStore,
  repeatMenusFilter,
  changeByLang
}
