const config = require('@/config');
const staticResourcesUrl = config.staticResourcesUrl;

module.exports = {

  /**
   * 参数方案icon地址
   */
  PARAM_ICON_PATH: staticResourcesUrl + '/param_icon',

  COMPONENT_TYPE: {
    PIC: 1,   // 图片
    TEXT: 2,
    NUMBER: 3,
    INPUT_NUMBER: 4, // 参数 输入
    RADIO: 5,     // 参数&方案 单选
    NUMBER_RANGE: 6,  // 参数 取值范围
    TEXT_IMAGE_CHECKBOX: 7, // 参数 图片+文本
    SELECT: 8,  // 参数 下拉框
    IMAGE_CHECKBOX: 9,  // 方案 图片选择
    IMAGE_CHECKBOX_CARD: 10,  // 方案 图片选择+子集卡片
    RADIO_SELECT_CARD: 11, // 桩核-单选框+子集卡片选择
    RADIO_CHILD_CARD: 12, // 椅旁 单选+子集单选 
    CHECKBOX: 13, // 椅旁 复选框但只能单选
    RADIO_IMG_CARD: 14, // 单选 + 子集图片卡片
    TEXT_INPUT_CARD: 15, // 文本子集输入框-参数
    RADIO_CHILD_INPUT: 16, // 单选-并显示子集
  },

  // 参数checkbox取值：是、否
  SELECT_GROUP: {
    YES: 'yes',
    NO: 'no'
  },

  // 方案模型范围关联 映射表
  SELECT_SCHEME_MODEL_MAP: {
    'model.full': 'articulatorType.fullArch',
    'model.quadrant': 'articulatorType.quadrant',
  },

  /**
   * @description 软件类型i18n
   */
  SOFTWARE_I18N: {
    1: 'param.threeShape',
    2: 'param.exoCad',
    3: 'param.riosDesign',
  },

  /**
   * @description 设计软件类型
   */
  SOFTWARE: {
    THREE_SHAPE: 1,
    EXO_CAD: 2,
    RIOS_DESIGN: 3,
  },

  /**
   * @description 参数方案国际化配置
   */
  I18N_TITLE: {
    0: 'crown',
    21101: 'crown',
    21102: 'temporary',
    21104: 'inlay',
    21105: 'veneer',
    21201: 'coping',
    21202: 'anatomicalCoping',
    21303: 'fixedTray',
    21401: 'sectionedModel',
    21402: 'unsectionedModel',
    22101: 'fullRPD',
    22102: 'nesbit',
    22201: 'fullDentureCarded',
    22202: 'fullDenturePrinted',
    22203: 'tryInFull',
    22204: 'copyDentureFull',
    22301: 'partialDeneturePrinted',
    22302: 'tryInPartial',
    22303: 'copyDenturePartial',
    22401: 'removableModel',
    22501: 'removableTray',
    23104: 'anatomicBridge',
    23105: 'anatomicBridgeWax',
    23301: 'implantModel',
    23401: 'implantTray',
    24301: 'nightGuide',
    24302: 'splint',
    24303: 'bitePlate',
    24401: 'studyModel',
    24402: 'bracketRemoval',
    24403: 'segmentation',
    21301: 'postAndCore', // 桩核
    21304: 'postAndCore', // 标准桩核
    21305: 'postAndCore', // 解剖桩核
    21306: 'postAndCore', // 桩核一体全冠
    21307: 'postAndCore', // 桩核一体内冠
    21302: 'telescope', // 套筒冠
    22601: 'removableCrown', // 活动修复-牙冠
    22701: 'removableAntomicalCoping', // 活动修复-解剖型内冠
    24501: 'digitalModel', // 正畸&其他-数字模型
    23106: 'anatomicGingivaCutback', // 牙龈回切
    23201: 'customAbutment', // 个性化基台
    23203: 'screwRetained', // 螺丝固位冠
    24406: 'orthodonticBand', // 正畸带环
    23403: 'radiographicGuide', // 放射导板
    21103: 'marylandBridge', // 马里兰桥
    21111: 'snapSmile', // 超薄临时牙桥
    23103: 'maloBridge', // 马泷桥
    23404: 'bar', // 杆卡
    23204: 'positioningGuide', //基台定位器
    23601: 'singleToothGuide', // 单孔牙支持式导板
  },
}