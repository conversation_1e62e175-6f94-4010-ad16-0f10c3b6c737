<template>
  <div class="tooth-info">
    <div class="info-card" v-for="(item, index) in toothSchemefromToothDesign" :key="index">
      <!-- 冠类 TODO: 暂时新增活动修复的牙冠缺失位+解剖冠缺失位 -->
      <div v-if="[21201, 21202, 21205, 21101, 21102, 21104, 21105, 22601, 22701, 23201, 23203, 21108, 22602, 22702, 21111, 23204].includes(item.code)">
        <h3 class="icon-img"><img :src="item.iconUrl" alt="" style="vertical-align: middle;" />{{translateTextByLang(item)}}</h3>
        <div class="detail">
          <span class="tooth-number" v-for="(num, j) in item.tooth" :key="j"><img class="flag-img" :src="solidImg" alt="" /> {{$t('tooth.position.Dentition')}} : {{ num | showToothNumBySystemIdx }} </span>
          <span v-if="item.tooth2 && item.tooth2[0]">
            <span class="tooth-number" v-for="(num2, k) in item.tooth2[0].tooth" :key="k"><img class="flag-img" :src="dashImg" alt="" />{{$t('tooth.position.Dentition')}} : {{ num2 | showToothNumBySystemIdx }}</span>
          </span>
        </div>
      </div>

      <!-- 桥体 -->
      <div v-else-if="[21501, 23501, 21103, 23103].includes(item.code)">
        <h3 class="icon-img"><img :src="item.iconUrl" alt="" style="vertical-align: middle;" />{{ translateTextByLang(item) }} {{ item.tooth.join('-') | showToothNumBySystemIdx }}</h3>
        <div class="detail">
          <span v-if="item.tooth2 && item.tooth2[0]"
            ><span class="tooth-number" v-for="(subItem, k) in item.tooth2" :key="k">
              <img class="flag-img" :src="dashImg" alt="" v-if="[21106, 21107, 21203, 21204].includes(subItem.code)" /> <img class="flag-img" :src="solidImg" alt="" v-else />{{translateTextByLang(subItem)}} : {{ subItem.number | showToothNumBySystemIdx }}</span
            ></span
          >
        </div>
      </div>

      <!-- 分牙  分上下颌及显示牙号-->
      <div v-else-if="[24403].includes(item.code)">
        <h3 class="icon-img"><img :src="item.iconUrl" alt="" style="vertical-align: middle;" />{{ translateTextByLang(item) }}</h3>
        <div class="detail">
          <span class="tooth-number" v-if="isInclude(upperNumbers, item.tooth)">{{$t('tooth.position.upper')}} : {{ splitTooth(upperNumbers, item.tooth) | showToothNumBySystemIdx }}</span>
          <span class="tooth-number" v-if="isInclude(lowerNumbers, item.tooth)">{{$t('tooth.position.lower')}} : {{ splitTooth(lowerNumbers, item.tooth) | showToothNumBySystemIdx }}</span>
        </div>
      </div>

      <!-- 支架，托盘等 分上下颌-->
      <div v-else-if="[22501, 21303, 23401, 24102, 24301, 24302, 24402, 22101, 23106, 23403, 23404, 23204].includes(item.code)">
        <h3 class="icon-img"><img :src="item.iconUrl" alt="" style="vertical-align: middle;" />{{ translateTextByLang(item) }}</h3>
        <div class="detail">
          <span class="tooth-number" v-if="isInclude(upperNumbers, item.tooth)">
            <img class="flag-img" :src="mandImg" alt="" />
            {{$t('tooth.position.upper')}}
          </span>
          <span class="tooth-number" v-if="isInclude(lowerNumbers, item.tooth)">
            <img class="flag-img" :src="maxImg" alt="" />
            {{$t('tooth.position.lower')}}
          </span>
        </div>
      </div>

      <!-- 1/4支架-->
      <div v-else-if="[22102, 24406].includes(item.code)">
        <h3 class="icon-img"><img :src="item.iconUrl" alt="" style="vertical-align: middle;" />{{translateTextByLang(item)}}</h3>
        <div class="detail">
          <span class="tooth-number" v-for="(number, j) in item.tooth" :key="j">
            <img class="flag-img" :src="TextFilter2(number).imgUrl" alt="" />
            {{  TextFilter2(number).text }}
          </span>
        </div>
      </div>

      <!-- 只显示设计类型名字  eg：（设计类型名称）-->
      <div class="other-type" v-else-if="[21402, 21404, 22401, 23301, 24303, 24401, 24501, 22502].includes(item.code)">
        <h3 class="icon-img">
          <img :src="item.iconUrl" alt="" style="vertical-align: middle;" />
          {{ translateTextByLang(item) }}
        </h3>
      </div>

      <!-- 只显示设计类型名字和牙号并区分上下颌  eg：（设计类型名称）-->
      <!-- 全口义齿和局部义齿 -->
      <div v-else-if="[22201, 22202, 22203, 22204, 22301, 22302, 22303].includes(item.code)">
        <h3 class="icon-img">
          <img :src="item.iconUrl" alt="" style="vertical-align: middle;" />
          {{translateTextByLang(item)}}
        </h3>
        <div class="full-denture-detail">
          <div v-for="jaw in item.jawList" :key="jaw.jaw" class="full-denture-box">
            <div class="jaw-type" v-if="jaw.jaw === 'upper'">
              <img class="flag-img" :src="mandImg" alt="" />
              {{$t('tooth.position.upper')}}
            </div>
            <div class="jaw-type" v-if="jaw.jaw === 'lower'">
              <img class="flag-img" :src="maxImg" alt="" />
              {{$t('tooth.position.lower')}}
            </div>
            <span>
              {{ jaw.tooth.join('-') | showToothNumBySystemIdx }}
            </span>
          </div>
        </div>
        <!-- <img :src="item.iconUrl" alt="" style="vertical-align: middle;" />
        {{ translateTextByLang(item) }} -->
      </div>
      <!-- // 杂项其他不显示牙号 -->
      <div class="other-type" v-else-if="[25002,22503,23402,24405].includes(item.code)">
        <h3 class="icon-img">
          <img :src="item.iconUrl" alt="" style="vertical-align: middle;" />
          {{ translateTextByLang(item) }}
        </h3>
      </div>

      <!-- 单孔牙支持式导板 -->
      <div v-else-if="[23601].includes(item.code)">
        <h3 class="icon-img"><img :src="item.iconUrl" alt="" style="vertical-align: middle;" />{{ translateTextByLang(item) }} </h3>
        <div class="guide-box">
          <div class="upper guide-item " v-if="jawToothFilter(upperNumbers, item.tooth) && jawToothFilter(upperNumbers, item.tooth).length">
            <img class="flag-img" :src="solidImg" alt="" />
            <div class="name">{{ $t(`order.detail.upperJaw`) }}</div>
            <div class="tooth-list">{{ jawToothFilter(upperNumbers, item.tooth).join('-') | showToothNumBySystemIdx }}</div>
          </div>
          
          <div class="lower guide-item " v-if="jawToothFilter(lowerNumbers, item.tooth) && jawToothFilter(lowerNumbers, item.tooth).length">
            <img class="flag-img" :src="solidImg" alt="" />
            <div class="name">{{ $t(`order.detail.lowerJaw`) }}</div>
            <div class="tooth-list">{{ jawToothFilter(lowerNumbers, item.tooth).join('-') | showToothNumBySystemIdx }}</div>
          </div>
        </div>

        <div class="implant-box">
          <el-form :inline="true" :model="implantForm" :rules="rules" :disabled="disabledImplantSystem" ref="ruleForm" class="implant-form">
            <el-form-item :label="$t('order.detail.guide.implantSystem')" prop="implantSystem">
              <el-select
                class="implant-system"
                :placeholder="$t('order.detail.guide.implantSystem')"
                v-model="implantForm.implantSystem"
                @change="handleSystemChange"
                clearable
                filterable
              >
                <el-option
                  v-for="(item, idx) in systemOptions"
                  :key="idx"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('order.detail.guide.implantSeries')" prop="implantSeries">
              <el-select
                class="implant-syeries"
                :placeholder="$t('order.detail.guide.implantSeries')"
                v-model="implantForm.implantSeries"
                clearable
                filterable
                @change="handleSeriesChange"
              >
                <el-option
                  v-for="(item, idx) in seriesOptions"
                  :key="idx"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="" prop="isAtOnce">
              <el-radio
                :value="implantForm.isAtOnce"
                label="AtOnce"
                @click.prevent.native="InvertSelection()"
                >{{ $t('order.detail.guide.isAtOnce') }}</el-radio
              >
            </el-form-item>
          </el-form>


        </div>
      </div>

      <!-- 只显示设计类型名字和牙号  eg：（设计类型名称：1,2,3）-->
      <div class="other-type" v-else>
        <h3 class="icon-img">
          <img :src="item.iconUrl" alt="" style="vertical-align: middle;" />
          {{ translateTextByLang(item) }}: {{ String(item.tooth) | showToothNumBySystemIdx }}
        </h3>
      </div>

      <span class="delete-icon" @click="deleteDesign(item)">X</span>
    </div>
  </div>
</template>
<script>
import solidImg from '../img/solid.svg';
import dashImg from '../img/dash.svg';
import maxImg from '../img/maxilla.svg';
import mandImg from '../img/mandible.svg';
import topLeftImg from '../img/top_left.svg';
import topRightImg from '../img/top_right.svg';
import lowLeftImg from '../img/low_left.svg';
import lowRightImg from '../img/low_right.svg';
import { leftUpperNumber, rightUpperNumber, leftLowerNumber, rightLowerNumber, upperNumbers, lowerNumbers } from '../js/constant';
import app from '@/store/modules/app';

import { staticResourcesUrl } from '@/config';
import { ajaxGetBlob } from '@/public/utils/file';
import setting from '@/config/plantOptionsEnv';

export default {
  name: 'ToothInfo',
  components: {},
  props: {
    toothSchemefromToothDesign: {
      type: Array,
      default: () => [],
    },
    otherDesignerType: Array,
    implantSystemObj: Object
  },
  data() {
    return {
      solidImg: solidImg,
      dashImg: dashImg,
      maxImg,
      mandImg,
      topLeftImg,
      topRightImg,
      lowLeftImg,
      lowRightImg,
      upperNumbers: upperNumbers,
      lowerNumbers: lowerNumbers,
      detailPrefix: `${staticResourcesUrl}/tooth_detail_icon/`,
      plantOptionsData: [],
      implantForm: {
        implantSystem: '',
        implantSeries: '',
        isAtOnce: false,
      },
    };
  },
  computed: {
    rules() {
      return {
        implantSystem: [
          { required: true, message: this.$t('order.detail.info.placeholder'), trigger: 'change' }
        ],
        implantSeries: [],
        isAtOnce: []
      }
    },
    //种植体系统下拉选项
    systemOptions() {
      return this.plantOptionsData
        .map((ele) => ele.brand)
        .sort(
          (a, b) =>
            a.toUpperCase().charCodeAt(0) - b.toUpperCase().charCodeAt(0)
        ) //首字母排序
    },

    //种植体系列下拉选项
    seriesOptions() {
      return this.plantOptionsData
        .find((ele) => ele.brand === this.implantForm.implantSystem)
        ?.model.sort((a, b) => a.charCodeAt(0) - b.charCodeAt(0))
    },

    // 如果是其他设计师设计导板，当前设计师禁用种植体系统
    disabledImplantSystem() {
      return this.otherDesignerType.includes(23601)
    },
  },
  watch: {
    implantSystemObj: {
      handler(val) {
        console.log('implantSystemObj-val', val)
        if (val) {
          this.implantForm = {...this.implantForm, ...val}
        }
      },
      immediate: true
    }
  },
  created() {
    this.getPlantOptions();
  },
  methods: {
    //处理即拔即种反选
    InvertSelection() {
      this.implantForm.isAtOnce
        ? (this.implantForm.isAtOnce = '')
        : (this.implantForm.isAtOnce = 'AtOnce')
      this.$emit('updatePlantOptions', this.implantForm)
    },
    handleSystemChange() {
      this.implantForm.implantSeries = '' //重新选择种植系统重置种植系列值
      this.$emit('updatePlantOptions', this.implantForm)
    },

    handleSeriesChange() {
      this.$emit('updatePlantOptions', this.implantForm)
    },
    async getPlantOptions() {
      const plantOptionsData = localStorage.getItem('plantOptionsData');

      
      if (plantOptionsData) {
        this.plantOptionsData = JSON.parse(plantOptionsData)
        console.log('this.plantOptionsData',  this.plantOptionsData);
        return
      }
      // 种植体文件oss地址
      const url = setting.plantOptionsUrl;
      const xhrResponse = await ajaxGetBlob(url)
      const reader = new FileReader()
      reader.readAsText(xhrResponse, 'utf-8')
      let _this = this
      reader.onload = function (e) {
        _this.plantOptionsData = eval(reader.result.split('=')[1])
        localStorage.setItem('plantOptionsData', JSON.stringify(_this.plantOptionsData))
      }
    },
    /**
     * @description:  jawTooth过滤器
     * @param {type}
     * @return:
     */
    jawToothFilter(jawTooth, toothList) {
      return jawTooth.filter(item => toothList.includes(item))
    },
    TextFilter2(number) {
      let obj = {
        text: '',
        imgUrl: ''
      };
      if (leftUpperNumber.includes(number)) {
        // return '左上';
        obj.text = this.$t('tooth.position.upperLeft');
        obj.imgUrl = this.topLeftImg;
        // return this.$t('tooth.position.upperLeft')
      } else if (rightUpperNumber.includes(number)) {
        // return '右上';
        obj.text = this.$t('tooth.position.upperRight');
        obj.imgUrl = this.topRightImg;
        // return this.$t('tooth.position.upperRight')
      } else if (leftLowerNumber.includes(number)) {
        // return '左下';
        obj.text = this.$t('tooth.position.lowerLeft');
        obj.imgUrl = this.lowLeftImg;
        // return this.$t('tooth.position.lowerLeft')
      } else if (rightLowerNumber.includes(number)) {
        // return '右下';
        obj.text = this.$t('tooth.position.lowerRight');
        obj.imgUrl = this.lowRightImg;
        // return this.$t('tooth.position.lowerRight')
      }
      return obj;
    },

    resetImplantForm() {
      this.implantForm = {
        implantSystem: '',
        implantSeries: '',
        isAtOnce: false,
      }
    },
    deleteDesign(designItem) {
      let result = [designItem];
      /* if (designItem.tooth2 && designItem.tooth2.length) {
        result = result.concat([...designItem.tooth2]);
      } */
      this.$emit('deleteDesign', result);
      // this.$emit('updatePlantOptions', {})
    },

    isInclude(arr1, arr2) {
      //console.log(Object.prototype.toString(arr1), Object.prototype.toString(arr2));

      return arr1.some(ele => arr2.includes(ele));
    },

    splitTooth(arr1, arr2) {
      return arr2.filter(ele => arr1.includes(ele)).join('-');
    },
    /**
     * 根据中英文转换
     * @param {*} item
     */
    translateTextByLang(item) {
      if (!item) {
        return '';
      }
      // let lang = app.state.language;
      // if (lang == 'en') {
      //   return item.enName;
      // }
      // return item.zhName;
      return this.$t(`apiCommon.${item.code}`)
    },
  },
};
</script>
<style lang="scss" scoped>
.tooth-info {
  padding: 24px;
  .info-card {
    position: relative;
    padding: 20px 18px;
    border-radius: 4px;
    border: 1px solid #38393d;
    margin-bottom: 16px;
    font-size: 16px;
    color: #e4e8f7;
    &:hover {
      .delete-icon {
        display: block;
      }
    }
    .delete-icon {
      position: absolute;
      right: 6px;
      top: 6px;
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 50%;
      background-color: $hg-grey;
      color: #38393d;
      font-weight: 900;
      cursor: pointer;
      display: none;
    }

    h3 {
      margin-bottom: 18px;
    }

    .tooth-number {
      position: relative;
      display: inline-block;
      // width: 128px;
      // height: 40px;
      padding: 0 10px 0 34px;
      line-height: 40px;
      font-size: 14px;
      border-radius: 4px;
      margin-right: 12px;
      margin-bottom: 12px;
      text-align: center;
      color: #e4e8f7;
      background-color: #38393d;
      &:hover {
        .delete-icon {
          display: block;
        }
      }
      .flag-img {
        position: absolute;
        left: 10px;
        top: 8px;
        width: 24px;
        height: 24px;
      }
      .delete-icon {
        position: absolute;
        right: -6px;
        top: -6px;
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        border-radius: 50%;
        font-size: 12px;
        font-weight: 900;
        background-color: $hg-grey;
        color: #38393d;
        cursor: pointer;
        display: none;
      }
    }
    .full-denture-detail {
      .full-denture-box {
        margin-right: 12px;
        display: inline-block;
        background-color: #38393d;
        padding: 10px 12px;
        border-radius: 4px;
        .jaw-type {
          display: inline-block;
          position: relative;
          padding-left: 32px;
          height: 24px;
          line-height: 24px;
          .flag-img {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 24px;
            height: 24px;
          }
        }
      }
    }
    .icon-img {
      >img {
        margin-right: 10px;
      }
    }

    .guide-box {
      display: flex;
      .guide-item {
        padding: 10px 12px;
        border-radius: 4px;
        background: rgba(56, 57, 61, 0.40);
        display: flex;
        align-items: center;
        width: fit-content;
        margin-bottom: 16px;
        margin-right: 16px;
        .name {
          margin-left: 12px;
        }
        .tooth-list {
          margin-left: 12px;
        }
      }
    }
    .implant-box {
      display: flex;
      align-items: center;
      /deep/ .implant-form {
        .el-form-item {
          &.is-error {
            .el-input__inner {
              border-color: $hg-main-danger;
            }
          }
          .el-form-item__label {
            color: $hg-label;
          }
          .el-form-item__content {
            .el-radio__label {
              color: $hg-label;
            }
          }
        }
      }
      .implant-system {
        
      }
      .implant-syeries {
        margin: 0 24px;
      }
    }
  }
}
</style>
