<template>
  <div class="bill-details">
    <div class="customer-info">
      <div class="title">
        <div class="back">
          <hg-button type="secondary" size="medium" @click="back">
            <hg-icon icon-name="icon-arrow-back-lab"></hg-icon>
            <span>{{ $t('common.btn.back') }}</span>
          </hg-button>
          <div class="text">
            <span class="first">{{ `${lang('billManager')} ` }}</span>
            <span class="first">></span>
            <span class="second">{{ ` ${lang('billDetail')}` }}</span>
          </div>
        </div>
        <el-button type="primary" :loading="exportLoading" @click="exportBill">{{
          lang('exportBill')
        }}</el-button>
      </div>
      <div class="info-list">
        <div class="info-item" v-for="item in infoList" :key="item.name">
          <span>
            <span class="item-name">{{ lang(item.label) }}：</span>
            <span v-if="item.key == 'settlementType'">
              {{ item.value | settlementType }}
            </span>
            <span v-if="item.key == 'settlementCurrency'">
              {{ item.value | settlementCurrency }}
            </span>
            <span
              v-if="
                item.key != 'settlementType' && item.key != 'settlementCurrency'
              "
            >
              {{
                item.unit
                  ? item.type === 'number'
                    ? capitalize(item.value) + item.unit
                    : item.value + item.unit
                  : item.type === 'number'
                  ? '-' + capitalize(item.value)
                  : item.value
              }}
            </span>
          </span>
        </div>
      </div>
    </div>

    <hg-table
      class="bill-design-type-table"
      maxHeight="100%"
      :hasIndex="true"
      :data="tableInfo.data"
      :header-data="tableInfo.headerData"
      :loading="tableInfo.loading"
      v-bind="$attrs"
    >
      <!-- 客户名称 -->
      <template #designTypeName="{ row }">
        <span class="design-type-name">
          {{ language === 'zh' ? row.designTypeName : row.designTypeEnName }}
        </span>
      </template>
      <template #erpSkuCode="{ row }">
        <span class="design-type-name">
          {{ row.erpSkuCode ||'--' }}
        </span>
      </template>
      <template #number="{ row }">
        {{row.number | capitalize}}
      </template>
      <template #unitPoint="{ row }">
        {{row.unitPoint | capitalize}}
      </template>
      <template #totalPoint="{ row }">
        {{row.totalPoint | capitalize | addMinusSign}}
      </template>
      <template #giftPoints="{ row }">
        {{row.giftPoints | capitalize | addMinusSign}}
      </template>
      <template #rechargePoints="{ row }">
        {{row.rechargePoints | capitalize | addMinusSign}}
      </template>
      <template #creditScore="{ row }">
        {{row.creditScore | capitalize | addMinusSign}}
      </template>
      <!-- 折扣率 -->
      <template #discountRate="{ row }">
        <span>{{ `${row.discountRate}% off` }}</span>
      </template>
      <template #amountPayable="{ row }">
        {{row.amountPayable | capitalize}}
      </template>
    </hg-table>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
import {
  settlementType,
  settlementCurrency,
  capitalize,
  settlementCurrencyAbbreviation,
  addMinusSign
} from '@/filters/heypoint';
import { getBillDetails, getBillDesignType, getBillExcel } from '@/api/heypoint';
import hgTable from '@/components/HgTable';
import { mapGetters } from 'vuex';
import { directDown } from '@/public/utils/file';

export default {
  name: 'CustomerDetails',
  components: {
    hgTable,
  },
  filters: {
    settlementType,
    settlementCurrency,
    capitalize,
    addMinusSign,
  },
  data() {
    return {
      infoList: [
        {
          name: '客户名',
          label: 'customerName',
          value: null,
          key: 'orgName',
        },
        {
          name: '结算类型',
          label: 'settlementType',
          value: null,
          key: 'settlementType',
        },
        {
          name: '结算货币',
          label: 'settlementCurrency',
          value: null,
          key: 'settlementCurrency',
        },
        {
          name: '业务负责人',
          label: 'salesManager',
          value: null,
          key: 'businessName',
        },
        {
          name: '账单时间',
          label: 'billDate',
          value: null,
          key: 'date',
        },
        {
          name: '黑豆花费',
          label: 'heypointCost',
          value: null,
          key: 'total',
          type: 'number',
        },
        {
          name: '赠送黑豆花费',
          label: 'giveHeypoint',
          value: null,
          key: 'giftPoint',
          type: 'number',
        },
        {
          name: '充值黑豆花费',
          label: 'rechargeCost',
          value: null,
          key: 'rechargePoint',
          type: 'number',
        },
        {
          name: '信用值花费',
          label: 'useOverdraft',
          value: null,
          key: 'creditPoint',
          type: 'number',
        },
        {
          name: '折扣率',
          label: 'discountRate',
          value: null,
          unit: '% off',
          key: 'discountRate',
        },
        {
          name: '本账单花费金额',
          label: 'amountSpendOnThisBill',
          value: null,
          key: 'amountPayable',
          type: 'number',
          needCurrency: true,
          unit: '',
        },
        {
          name: '累计欠款金额',
          label: 'debt',
          value: null,
          key: 'fullPayment',
          needCurrency: true,
          type: 'number',
          unit: '',
        },
      ],
      detailInfo: {},
      tableInfo: {
        loading: false,
        data: [],
        headerData: [
          {
            prop: 'designTypeName',
            getLabel: () => {
              return this.lang('designType');
            },
          },
          {
            prop: 'erpSkuCode',
            getLabel: () => {
              return this.lang('materialNo');
            },
          },
          {
            prop: 'number',
            getLabel: () => {
              return this.lang('quantity');
            },
          },
          {
            prop: 'unitPoint',
            getLabel: () => {
              return this.lang('standardPrice');
            },
          },
          {
            prop: 'totalPoint',
            getLabel: () => {
              return this.lang('totalCost');
            },
          },
          {
            prop: 'giftPoints',
            getLabel: () => {
              return this.lang('giveHeypoint');
            },
          },
          {
            prop: 'rechargePoints',
            getLabel: () => {
              return this.lang('rechargeCost');
            },
          },
          {
            prop: 'creditScore',
            getLabel: () => {
              return this.lang('useOverdraft');
            },
          },
          {
            prop: 'discountRate',
            getLabel: () => {
              return this.lang('discountRate');
            },
          },
          {
            prop: 'exchangeRate',
            getLabel: () => {
              return this.lang('exchangeRate');
            },
          },
          {
            prop: 'amountPayable',
            getLabel: () => {
              return `${this.lang('amountPayable')}(${this.currency})`;
            },
          },
        ],
      },
      exportLoading: false
    };
  },
  computed: {
    ...mapGetters(['language']),
    currency() {
      const currency = this.detailInfo.settlementCurrency;
      const list = new Map([
        [0, 'USD'],
        [1, 'CNY'],
        [2, 'EUR'],
        [3, 'AUD']
      ]);
      return list.get(currency);
    },
  },
  created() {
    this.getbillDetails();
    this.getBillDetailsDesignType();
  },
  methods: {
    lang: getLang('bill.info'),
    capitalize,

    /**
     * 导出账单
     */
    async exportBill() {
      try {
        this.exportLoading = true;
        const { billNo } = this.$route.query;
        const params = {
          billNo,
          language: this.language,
          type: this.detailInfo.billType
        };
        const { data } = await getBillExcel(params);
        directDown(data, 'bill.xlsx');
      } catch (error) {
        console.error('error: ', error);
      } finally {
        this.exportLoading = false;
      }
    },

    /**
     * 获取账单设计类型
     */
    async getBillDetailsDesignType() {
      try {
        this.tableInfo.loading = true;
        const { billNo } = this.$route.query;
        const params = {
          billNo,
        };
        const { data } = await getBillDesignType(params);
        this.tableInfo.data = data;
      } catch (error) {
        console.log('error: ', error);
      } finally {
        this.tableInfo.loading = false;
      }
    },

    /**
     * 获取客户信息详情
     */
    async getbillDetails() {
      try {
        const { billNo } = this.$route.query;
        const params = {
          billNo,
        };
        const errArr = ['', undefined, null];
        const { data } = await getBillDetails(params);
        this.detailInfo = data;
        this.infoList.forEach((item) => {
          Object.keys(data).forEach((key) => {
            const value = data[key];
            if (key === item.key) {
              item.value = value;
              if (item.needCurrency) {
                item.unit = !errArr.includes(data.settlementCurrency)
                  ? ` ${settlementCurrencyAbbreviation(
                      data.settlementCurrency
                    )}`
                  : '';
              }
            }
          });
        });
      } catch (error) {
        console.error('error: ', error);
      }
    },

    /**
     * 返回事件
     */
    back() {
      this.$router.push({
        path: '/billManager',
        query: { ...this.$route.query },
      });
    },
  },
};
</script>

<style lang="scss">
.bill-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  .customer-info {
    background: $hg-main-black;
    padding: 16px 24px 24px 24px;
    position: relative;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 28px;
      .back {
        display: flex;
        align-items: center;
      }
      .hg-button {
        margin-right: 24px;
        .icon-arrow-back-lab {
          margin-right: 10px;
        }
      }
      .text {
        .first {
          color: $hg-secondary-text;
        }
        .second {
          color: $hg-label;
        }
      }
    }
    .info-list {
      display: flex;
      flex-wrap: wrap;
      .info-item {
        width: 24%;
        margin-bottom: 24px;
        .credit-value {
          width: 110px;
          .el-input-number__increase {
            .el-icon-arrow-up {
              bottom: 5px;
            }
          }
          .el-input-number__decrease {
            .el-icon-arrow-down {
              top: 5px;
            }
          }
        }
        .discount-value {
          width: 100px;
          .el-input-number__increase {
            .el-icon-arrow-up {
              bottom: 5px;
            }
          }
          .el-input-number__decrease {
            .el-icon-arrow-down {
              top: 5px;
            }
          }
        }
        .item-name {
          display: inline-block;
          min-width: 42px;
        }
        .remark-value {
          display: inline-block;
          max-height: 60px;
          overflow-y: auto;
        }
      }
      .info-item:not(:nth-child(4n)) {
        margin-right: calc(4% / 3);
      }
      .remark-item {
        display: flex;
        flex: 1;
        margin-right: 112px !important;
        > span {
          display: flex;
          width: 100%;
          .remark {
            display: inline-block;
            width: 96%;
          }
        }
      }
    }
    .edit-btn {
      display: flex;
      justify-content: flex-end;
      margin: -65px 0 0;
      .el-button {
        width: 88px;
        &.complete-edit {
          display: flex;
          justify-content: center;
          // background: #fff;
          // border: 1px solid $hg-border;
          // color: $mainColor;
        }
      }
    }
    .discount-tips {
      color: #dc5050;
      display: inline-block;
      width: 100%;
      font-size: 12px;
      &.is-edit {
        top: 28px;
      }
    }
    .return-btn {
      position: absolute;
      right: 0;
      top: 0;
      background: #fff;
      cursor: pointer;
      .icon-btn-return {
        font-size: 34px;
        // color: $mainColor;
      }
    }
  }
  .bill-design-type-table {
    flex: 1;
    margin-top: 20px;
    max-height: calc(100% - 216px - 20px);
  }
}
</style>