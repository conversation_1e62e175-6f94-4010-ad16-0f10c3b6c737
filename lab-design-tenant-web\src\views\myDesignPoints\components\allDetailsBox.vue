<template>
  <div class="all-details-box">
    <div class="left-details">
      <div class="left-box">
        <div class="img"><img src="@/assets/images/designPoints/monthlyIndicatorPoints.png" /></div>
        <div class="content"><p class="title">{{lang('monthPoints')}}</p><p class="num">{{allDetails.monthlyIndicatorPoints}}</p></div>
      </div>
      <div class="left-box special-box">
        <div class="img"><img src="@/assets/images/designPoints/monthlyCompletionPoints.png" /></div>
        <div class="content"><p class="title">{{lang('monthComplete')}}</p><p class="num">{{allDetails.monthlyCompletionPoints}}</p></div>
      </div>
      <div class="left-box special-box">
        <div class="img"><img src="@/assets/images/designPoints/excessPoints.png" /></div>
        <div class="content"><p class="title">{{lang('overPoints')}}</p><p class="num">{{allDetails.excessPoints}}</p></div>
      </div>
    </div>
    <div class="right-details">
      <div class="one-box">
        <div class="img"><img src="@/assets/images/designPoints/groupRank.png" /></div>
        <div class="content"><p class="title">{{lang('groupPaiming')}}</p><p class="num">{{allDetails.groupRank}}</p></div>
      </div>
      <div class="one-box" style="flex: 1.5;">
        <div class="img"><img src="@/assets/images/designPoints/completionRate.png" /></div>
        <div class="content"><p class="title">{{lang('complete')}}</p><p class="num" :style="allDetails.completionRate < 1 ? 'color: #BC4141;' : ''">{{getRate(allDetails.completionRate)}}%</p></div>
      </div>
      <div class="two-box">
        <p class="list"><span class="title">{{lang('backOrder')}}</span><span class="num">{{allDetails.totalReturnQty}}</span></p>
        <p class="list"><span class="title">{{lang('freeOrder')}}</span><span class="num">{{allDetails.totalFreeQty}}</span></p>
        <p class="list"><span class="title">{{lang('overOrder')}}</span><span class="red-num">{{allDetails.totalTimeoutQty}}</span></p>
      </div>
    </div>
  </div>
</template>

<script>
import { getLang } from '@/public/utils';
  export default {
    name: 'allDetailsBox',
    props: {
      allDetails: Object,
    },
    methods: {
      lang: getLang('designpoints'),
      getRate(value){
        return (value * 100).toFixed(2);
      },
    },
  }
</script>

<style lang="scss" scoped>
.all-details-box{
  position: relative;
  display: flex;
  margin-top: 24px;
  .left-details{
    flex: 3;
    display: flex;
    min-width: 530px;
    height: 128px;
    background: $hg-hover;
    border-radius: 4px;
    padding: 24px 16px; 
    .left-box{
      flex: 1;
      min-width: 160px;
      display: flex;
      align-items: center;
      padding-right: 20px;
      border-right: 1px solid #3D4048;
      &:last-child{
        border-right: 0;
      }
      .img{
        img{
          width: 40px;
          height: 40px;
        }
      }
      .content{
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .title{
          font-size: 14px;
          color: rgb(196, 200, 205);
          line-height: 20px;
        }
        .num{
          font-size: 32px;
          font-weight: 700;
          line-height: 40px;
        }
      }
    }
    .special-box{
      margin-left: 16px;
    }
  }
  .right-details{
    flex: 3;
    display: flex;
    // position: absolute;
    // right: 0;
    .one-box{
      flex: 1;
      display: flex;
      min-width: 177px;
      height: 128px;
      background: $hg-hover;
      border-radius: 4px;
      padding: 24px 16px; 
      align-items: center;
      margin-left: 20px;
      .img{
        img{
          width: 40px;
          height: 40px;
        }
      }
      .content{
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .title{
          font-size: 14px;
          color: rgb(196, 200, 205);
          line-height: 20px;
        }
        .num{
          font-size: 32px;
          font-weight: 700;
          line-height: 40px;
        }
      }
    }
    .two-box{
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 177px;
      height: 128px;
      background: $hg-hover;
      border-radius: 4px;
      padding: 24px 16px; 
      // justify-content: center;
      margin-left: 20px;
      .list{
        line-height: 28px;
        .title{
          display: inline-flex;
          width: 60%;
          color: #9EA2A8;
        }
        .num{

        }
        .red-num{
          color: #E55353;
        }
      }
    }
  }
}
</style>