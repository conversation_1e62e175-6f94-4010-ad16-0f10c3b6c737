// 以定义全局方法的方式自定义一个全局对话框插件
import msgDialogComponent from './Main.vue'

export default {
    install (Vue, options) {
        const msgDialogConstructor = Vue.extend(msgDialogComponent);
        const instance = new msgDialogConstructor();   //创建子实例
        instance.$mount(document.createElement('div')); //挂载实例到我们创建的DOM上
        document.body.appendChild(instance.$el);
        Vue.prototype.$Dialog = ({title=Vue.prototype.i18n.t('common.systemTip'), message, ensureBtnText=Vue.prototype.i18n.t('common.confirm'),cancelBtnText=Vue.prototype.i18n.t('common.cancel'),showConfirmButton=true,showCancelButton=true,updateShow=true,confirmAction=()=>{},cancelAction=()=>{},init=()=>{}}) => {
          instance.show = true;
          instance.title = title;
          instance.msg = message;
          instance.ensureBtnText = ensureBtnText;
          instance.cancelBtnText = cancelBtnText;
          instance.showConfirmButton = showConfirmButton;
          instance.showCancelButton = showCancelButton;
          instance.updateShow = updateShow;
          instance.confirmAction = confirmAction;
          instance.cancelAction = cancelAction;
          instance.init = init;
        }
    }
}
