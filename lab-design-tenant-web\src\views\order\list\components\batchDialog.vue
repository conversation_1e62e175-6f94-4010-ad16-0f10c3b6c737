//使用
<!-- 
  <batch-dialog ref="batchDialog" :assignType="assignType" :selectSelection="selectSelection" @batchList="handelbatchList"></batch-dialog>
  打开弹窗:this.$refs.batchDialog.batchPeopleDialog = true;
  assignType: 人物类型：IQC,DESIGNER,OQC   selectSelection：传进来选中的列表，订单详情用  handelbatchList：处理方法，抛出选中的item
 -->
<template>
  <div>
    <el-dialog :title="$t(title)" :visible.sync="batchPeopleDialog" width="520px" custom-class="batch-dialog">
      <div class="batch-content">
        <!-- 头部搜索 -->
        <div class="batch-header">
          <p class="batch-title">{{$t('orderList.order.select')}} {{ assignType === 'IQC' ? 'IQC' : assignType === 'DESIGNER' ? (language == 'zh' ? '设计师' : 'designer') : assignType === 'OQC' ? 'OQC' : '' }}</p>
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
              <hg-input v-model="batchSearch.nameOrGroupKeyword" :placeholder="$t('orderList.order.selectHolder')" @change="onSearch" clearable ></hg-input>
            </el-form-item>
            <el-form-item>
              <design-type-select class="searchData-order-design-type" v-model="batchSearch.designTypeCodes" @changeSearch="onSearch"></design-type-select>
            </el-form-item>
          </el-form>
        </div>
        <!-- 内容列表 IQC和设计师OQC区分开 -->
        <!-- IQC -->
        <div class="batch-list iqc-batchlist" v-loading="loadBatchList" v-if="assignType === 'IQC' && batchList.length > 0">
          <div class="batch-list-content">
            <div :class="['batch', nowSelectBatch.indexOf(bacthItm.userCode) != -1 ? 'now-select' : '']" v-for="(bacthItm, idx) in batchList" :key="idx" @click="selectBatch(bacthItm)">
              <div class="img-box">
                <img class="img" v-if="bacthItm.headImgurl" :src="bacthItm.headImgurl" alt="" />
                <span class="img img-text" v-else>{{ bacthItm.name[0] }}</span>
                <i class="on-line" :style="bacthItm.isOnline ? 'background:#72D143;' : 'background:#737680;'"></i>
              </div>
              <label
                :class="['el-upload-list__item-status-label', !(nowSelectBatch.indexOf(bacthItm.userCode) != -1) ? 'no-select-status' : '']"
                :style="nowSelectBatch.indexOf(bacthItm.userCode) != -1 ? 'display: block' : ''"
              >
                <i class="el-icon-upload-success el-icon-check"></i>
              </label>
              <div class="content-box">
                <p class="name">{{ bacthItm.name }}</p>
                <el-tooltip class="item" effect="dark" :disabled="bacthItm.zhRangeNames.length == 0 || bacthItm.enRangeNames.length == 0" 
                :content="language == 'zh' ? setRangName(bacthItm.zhRangeNames) : setRangName(bacthItm.enRangeNames)" placement="bottom-start">
                  <p class="range">{{$t('orderList.order.rang')}}：{{ language == 'zh' ? setRangName(bacthItm.zhRangeNames) : setRangName(bacthItm.enRangeNames) }}</p>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
        <!-- 设计师和OQC -->
        <div class="all-batch-list" v-loading="loadBatchList" v-else-if="(assignType === 'OQC' || assignType === 'DESIGNER') && batchList.length > 0">
          <div class="batch-list" v-for="(bacth, index) in batchList" :key="index">
            <div v-if="bacth.isRecommended" class="batch-list-title-tuijian">{{ bacth.groupName }}({{$t('orderList.order.rangFirst')}})</div>
            <div v-else class="batch-list-title">{{ bacth.groupName }}</div>
            <div class="batch-list-content">
              <div :class="['batch', nowSelectBatch.indexOf(bacthItm.userCode) != -1 ? 'now-select' : '']" v-for="(bacthItm, idx) in bacth.staffDtos" :key="idx" @click="selectBatch(bacthItm)">
                <div class="img-box">
                  <img class="img" v-if="bacthItm.headImgurl" :src="bacthItm.headImgurl" alt="" />
                  <span class="img img-text" v-else>{{ bacthItm.name[0] }}</span>
                  <i class="on-line" :style="bacthItm.isOnline ? 'background:#72D143;' : 'background:#737680;'"></i>
                </div>
                <label
                  :class="['el-upload-list__item-status-label', !(nowSelectBatch.indexOf(bacthItm.userCode) != -1) ? 'no-select-status' : '']"
                  :style="nowSelectBatch.indexOf(bacthItm.userCode) != -1 ? 'display: block' : ''"
                >
                  <i class="el-icon-upload-success el-icon-check"></i>
                </label>
                <div class="content-box">
                  <p class="name">{{ bacthItm.name }}</p>
                  <el-tooltip class="item" effect="dark" :disabled="bacthItm.zhRangeNames.length == 0 || bacthItm.enRangeNames.length == 0" 
                :content="language == 'zh' ? setRangName(bacthItm.zhRangeNames) : setRangName(bacthItm.enRangeNames)" placement="bottom-start">
                  <p class="range">{{$t('orderList.order.rang')}}：{{ language == 'zh' ? setRangName(bacthItm.zhRangeNames) : setRangName(bacthItm.enRangeNames) }}</p>
                </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-loading="loadBatchList" v-else>
          <el-empty></el-empty>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchPeopleDialog = false">{{$t('common.btn.cancel')}}</el-button>
        <el-button type="primary" @click="submitSelectPeople">{{$t('orderList.btnList.submit')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DesignTypeSelect from './design-type-select';
import { mapGetters } from 'vuex';
import { getBatchList } from '@/api/order';
import { batchAssign } from '../../../../api/order';
export default {
  name: 'batchDialog',
  components: { DesignTypeSelect },
  props: {
    title: {
      type: String,
      default: 'orderList.btnList.batchAll'
    },
    //打开批量弹窗类型 IQC,DESIGNER,OQC
    assignType: {
      type: String,
      default: 'DESIGNER',
    },
    // 选中的列表，批量处理
    selectSelection: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      batchPeopleDialog: false, //弹窗显示
      loadBatchList: true,
      batchSearch: {
        //搜索
        nameOrGroupKeyword: null,
        designTypeCodes: [],
      },
      batchList: [],
      nowSelectBatch: [], //当前选中的id
      nowSelectBatchItem: [], //当前选中的item,传回父组件进行批量处理
    };
  },
  computed: {
    ...mapGetters(['orgCode', 'language']),
  },
  watch: {
    batchPeopleDialog(newValue, oldValue) {
      if (newValue) {
        this.getBatchList();
      } else {
        this.batchSearch = {
          nameOrGroupKeyword: null,
          designTypeCodes: [],
        };
      }
    },
  },
  methods: {
    selectBatch(bacthItm) {
      if (this.nowSelectBatch.length > 0) {
        this.nowSelectBatch = [];
        this.nowSelectBatchItem = [];
      }
      this.nowSelectBatchItem.push(bacthItm);
      this.nowSelectBatch.push(bacthItm.userCode);
    },
    // 搜索
    onSearch(designType, valueList) {
      if (designType == 'designType') {
        this.batchSearch.designTypeCodes = valueList;
      }
      this.getBatchList();
    },
    // 获取指派角色列表
    getBatchList() {
      this.loadBatchList = true;
      // 重置选中的列表
      this.nowSelectBatch = [];
      this.nowSelectBatchItem = [];
      let orderCustomerOrgCode = null;
      //这里是订单列表的处理逻辑
      if (this.selectSelection && this.selectSelection.length > 0) {
        let codeArr = [];
        this.selectSelection.forEach((item) => {
          codeArr.push(item.orgCode);
        });
        codeArr = Array.from(new Set(codeArr));
        if (codeArr.length == 1) {
          orderCustomerOrgCode = codeArr[0];
        }
      }
      let designCodes = this.batchSearch.designTypeCodes;
      let nameOrGroupKeyword = this.batchSearch.nameOrGroupKeyword;
      let staffTypeCode = this.assignType == 'IQC' ? 1 : this.assignType == 'DESIGNER' ? 2 : this.assignType == 'OQC' ? 3 : null;
      getBatchList(designCodes, nameOrGroupKeyword, staffTypeCode, orderCustomerOrgCode).then((res) => {
        if (res.code === 200) {
          let batchList = res.data;
          // 当指派的是IQC时需要特殊处理数组
          if (this.assignType == 'IQC') {
            let IQCArr = [];
            batchList.forEach((item) => {
              IQCArr = IQCArr.concat(item.staffDtos);
            });
            this.batchList = IQCArr;
            this.loadBatchList = false;
          } else {
            this.batchList = batchList;
            this.loadBatchList = false;
          }
        } else {
          this.loadBatchList = false;
        }
      });
    },
    // 处理范围
    setRangName(arr) {
      let str = '';
      arr.forEach((it) => {
        str += it + '、';
      });
      if (str) str = str.slice(0, str.length - 1);
      return str;
    },

    // 提交选中的人
    submitSelectPeople() {
      //这里是订单列表的处理逻辑
      if (this.selectSelection && this.selectSelection.length > 0) {
        let orderAssignReqDtos = [];
        let staffTypeCode = this.assignType == 'IQC' ? 1 : this.assignType == 'DESIGNER' ? 2 : this.assignType == 'OQC' ? 3 : null;
        this.selectSelection.forEach((item) => {
          let obj = {
            orderCode: item.orderCode,
            userCode: this.nowSelectBatch[0],
          };
          orderAssignReqDtos.push(obj);
        });
        let data = {
          assignType: staffTypeCode,
          orderAssignReqDtos: orderAssignReqDtos,
        };
        batchAssign(data).then((res) => {
          if (res.code == 200) {
            this.batchPeopleDialog = false;
            this.$emit('handelbatchList', this.nowSelectBatchItem);
          }
        });
      } else {
        //其他使用组件时抛出函数，各自业务对应处理
        this.batchPeopleDialog = false;
        this.$emit('handelbatchList', this.nowSelectBatchItem);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.batch-dialog {
  .batch-content {
    .batch-header .batch-title {
      color: #fff;
      height: 28px;
    }
    /deep/.el-form-item{
      margin-right: 16px;
      &:last-child{
        margin-right: 0;
      }
    }
    /deep/.el-form--inline {
      .el-form-item__content {
        width: 226px;
      }
      .el-select {
        width: 240px;
      }
    }
    .all-batch-list {
      height: 360px;
      width: 496px;
      overflow: auto;
    }
    .iqc-batchlist {
      height: 360px;
      width: 496px;
      overflow: auto;
    }
    .batch-list {
      .batch-list-title {
        color: #fff;
        height: 28px;
        margin-top: 6px;
      }
      .batch-list-title-tuijian {
        color: #3760ea;
        height: 28px;
      }
      .batch-list-content {
        display: flex;
        flex-wrap: wrap;
        .batch {
          display: flex;
          padding: 20px;
          width: 226px;
          height: 112px;
          border: 1px solid #2d2f33;
          margin-right: 10px;
          margin-bottom: 10px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          .img-box {
            width: 48px;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            position: relative;
            .on-line {
              display: inline-block;
              position: absolute;
              width: 8px;
              height: 8px;
              border-radius: 50%;
              right: 0;
              top: 50px;
            }
          }
          .el-upload-list__item-status-label {
            display: none;
            position: absolute;
            right: -17px;
            top: -7px;
            width: 46px;
            height: 26px;
            background: $hg-main-blue;
            text-align: center;
            transform: rotate(45deg);
            box-shadow: 0 1px 1px #ccc;
            i {
              font-size: 12px;
              margin-top: 12px;
              transform: rotate(-45deg);
            }
            .el-icon-check {
              color: #fff;
            }
          }
          .img-box .img {
            width: 48px;
            height: 48px;
            display: inline-block;
            border-radius: 50%;
          }
          .img-text {
            background-color: #3054cc;
            width: 48px;
            height: 48px;
            line-height: 48px;
            display: block;
            text-align: center;
            color: #e4e8f7;
            font-weight: bold;
            font-style: normal;
          }
          .content-box {
            width: 128px;
            height: 100%;
            // display: flex;
            // flex-direction: column;
            // justify-content: center;
            // margin-right: 10px;
          }
          .name {
            color: #fff;
            margin-bottom: 8px;
            font-weight: 700;
            line-height: 24px;
          }
          .range {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: #fff;
            line-height: 20px;
            font-size: 14px;
            font-weight: 400;
          }
        }
        .now-select {
          border: 1px solid $hg-main-blue;
        }
        .batch:hover {
          background: $hg-hover;
          .no-select-status {
            display: block;
            background: #2d2f33;
          }
        }
      }
    }
  }
  .dialog-footer {
    .el-button {
      width: 104px;
      height: 40px;
    }
  }
}
/deep/.el-dialog__footer {
  border-top: 1px solid #38393d;
  padding: 22px;
  padding-top: 21px;
}
</style>
