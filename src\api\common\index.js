import http from '../request'

/** 获取时区信息列表*/
export const getTimezoneList = () => {
  return http({
    url: '/user-basic/timezone/v1/list',
    method: 'GET'
  })
}
/** 获取S3节点配置列表*/
export const getBucketCodeList = () => {
  return http({
    url: '/user-basic/bucket/v1/list',
    method: 'POST'
  })
}
/** 获取S3节点配置列表*/
export const getBusinessUserList = () => {
  return http({
    url: '/user-basic/labUser/v1/businessUsers',
    method: 'POST'
  })
}

/** 模拟发送统一的通知消息*/
export const sendNotice = (param) => {
  return http({
    url: '/design-notify-service/notify/v1/sendMsg',
    data: param,
    method: 'POST'
  })
}

/** 获取当前账号的所有未读通知消息*/
export const getUnreadNoticeList = (param) => {
  return http({
    url: '/design-notify-service/notify/v1/searchMsg',
    data: param,
    method: 'POST'
  })
}

/** 设置单条消息已读*/
export const setNoticeToRead = (param) => {
  return http({
    url: '/design-notify-service/notify/v1/readMsg?msgId=' + param,
    method: 'GET'
  })
}

/** 设置单条消息已读*/
export const setAllNoticeToRead = (param) => {
  return http({
    url: '/design-notify-service/notify/v1/readAllMsg?userId=' + param,
    method: 'GET'
  })
}

/** 设置单条消息已读*/
export const getAreaList = () => {
  return http({
    url: '/user-basic/noAuth/common/v1/getAreaList',
    method: 'GET'
  })
}
