import request from '../axios';
import { server } from '@/config';

const axios = request.axios;

//  获取设计师点数影响因素信息
export const getDesignerSetInfo = () => {
  return axios({
    url: `${server.designSet}/info`,
    method: 'get',
  });
 };

 //设置设计师点数影响因素
 export const setConfig = (data) => {
  return axios({
    url: `${server.designSet}/config`,
    method: 'post',
    data
  });
 };

 // 获取点数清单分页列表
 export const getList = (data) => {
  return axios({
    url: `${server.designSet}/list`,
    method: 'post',
    data
  });
 };

 // 批量设置物料点数
 export const setskuPoints = (data) => {
  return axios({
    url: `${server.designSet}/skuPoints`,
    method: 'post',
    data
  });
 };

 // 下载点数模板(返回文件流)
 export const exportExcel = () => {
  return axios({
    url: `${server.designSet}/export`,
    method: 'get',
    responseType: 'blob',
  });
 };

 // 导入导入SKU点数
 export const importExcel = (data) => {
  return axios({
    url: `${server.designSet}/import`,
    method: 'post',
    data,
    headers: {
      'content-type': 'multipart/form-data'
    }
  });
 };
 /** 客户流程配置功能——获取所有设计师小组
 * 无请求参数
 */
export const getAllDesignGroups = (param) => {
  return axios({
    url: `${server.designUser}/designerDept`,
    method: 'post'
  })
}

// 设计师点数列表
export const getDesignerPointslist = (data) => {
  return axios({
    url: `${server.designerPoints}/list`,
    method: 'post',
    data
  });
 };

// 导入设计师天数
export const importDayExcel = (data) => {
  return axios({
    url: `${server.designerPoints}/import`,
    method: 'post',
    data,
    headers: {
      'content-type': 'multipart/form-data'
    }
  });
 };
 // 根据月份获取工作天数标准getDesignerShifts
 export const getDesignerShifts = (month) => {
  return axios({
    url: `${server.designerPoints}/getDesignerShifts`,
    method: 'get',
    params: {
      month
    }
  });
 };
 //保存工作天数标准
 export const saveDesignerShifts = (data) => {
  return axios({
    url: `${server.designerPoints}/saveDesignerShifts`,
    method: 'post',
    data
  });
 };

 // 根据月份生成设计师统计积分
 export const generate = (data) => {
  return axios({
    url: `${server.designerPoints}/generate`,
    method: 'post',
    data
  });
 };
 //设计师积分发布
 export const publicList = (month) => {
  return axios({
    url: `${server.designerPoints}/publish`,
    method: 'get',
    params: {
      month
    }
  });
 };
  //设计师积分撤回
export const withdraw = (month) => {
  return axios({
    url: `${server.designerPoints}/withdraw`,
    method: 'get',
    params: {
      month
    }
  });
};

// 判断是否已经生成统计表
export const generated = (month) => {
  return axios({
    url: `${server.designerPoints}/generated`,
    method: 'get',
    params: {
      month
    }
  });
};
//根据订单号查询订单(点数分配)
export const orders = (data) => {
  return axios({
    url: `${server.designerPoints}/orders`,
    method: 'post',
    data
  });
};
// 查询客户编号
export const getorgSn = (sn) => {
  return axios({
    url: `${server.designerPoints}/orgSn`,
    method: 'get',
    params: {
      sn
    }
  });
}
// 获取点数详情分页列表
export const getDetailList = (data) => {
  return axios({
    url: `${server.designerPoints}/detailList`,
    method: 'post',
    data
  });
};

// 删除设计师点数
export const delOrders = (data) => {
  return axios({
    url: `${server.designerPoints}/del`,
    method: 'post',
    data
  });
};

// 确定点数分配或者直接添加
export const setOrders = (data) => {
  return axios({
    url: `${server.designerPoints}/set`,
    method: 'post',
    data
  });
};

// 设计师点数统计
export const getPointsStatistics = (data) => {
  return axios({
    url: `${server.pointsStatistics}/get`,
    method: 'post',
    data
  });
};

// 下载设计师统计表
export const downloadExcel = (date) => {
  return axios({
    url: `${server.designerPoints}/downloadExcel`,
    method: 'get',
    params: {
      month: date
    }
  });
}
// 月值班表模板下载
export const exportMonth = (data) => {
  return axios({
    url: `${server.designerPoints}/export`,
    method: 'get',
  });
}
// 点数设计下载模板
export const exportSku = (data) => {
  return axios({
    url: `${server.designSet}/export`,
    method: 'get',
  });
}

// 设计师点数详情下载
export const exportDetails = (date, userCode, userName) => {
  return axios({
    url: `${server.designerPoints}/downloadDetailExcel`,
    method: 'get',
    params: {
      month: date,
      userCode,
      userName
    }
  });
}

