<template>
  <tooth-drawer 
    ref="drawer" 
    :propToothDesign="curToothDesign" 
    :propCategoryCode="selectCode"
    :orgCode="orgCode"
    :deliveryCode="deliveryCode"
    :hasOtherCategory="hasOtherCategory"
    :otherDesignerType="otherDesignerType"
    :implantSystem="implantSystem"
    @updateToothDesign="closeToothDialog"
    @setCurrentInfo="setCurrentInfo"
    @resetImplantForm="resetImplantForm">
    <div slot="tab" class="tooth-drawer-tab">
      <div 
        v-for="item in categoryList" 
        :key="item.designCode"
        :class="['tooth-drawer-tab-li', disableSelect(item.designCode) && 'is-disabled']">
        <p :class="['tooth-drawer-tab-item', item.designCode === selectCode && 'is-active-tab']" @click="handleSelectCode(item.designCode)">
          <span class="tab-item-text">{{ $t(`apiCommon.${item.designCode}`) }}</span>
        </p>
      </div>
    </div>
  </tooth-drawer>
</template>

<script>
import { mapGetters } from 'vuex';
import ToothDrawer from './index';
import { copy } from './js/utils';

export default {
  components: { ToothDrawer },
  props: {
    propToothDesign: Array, // 编辑时传入的数据
    propCategoryCode: Number,
    orgCode: Number,
    deliveryCode: Number,
    designerTypes: Array,
    implantSystemObj: Object
  },
  data() {
    return {
      isShow: false,
      selectCode: 0,
      toothDesignMap: {}, // { 10001: { toothDesign: [], toothImage: null } }
      implantSystem: {}
    }
  },
  computed: {
    ...mapGetters(['designTypeTree', 'rootToLeafCodes', 'leafToRootCodes', 'userCode']),
    categoryList() {
      let categoryList = [];
      // 过滤联合修复及未知设计类型
      const filterCategory = [25001, 1]; 
      this.designTypeTree.forEach(item => {
        const { designCode, enName, level, cnName, iconUrl, hasParas } = item;
        if(!filterCategory.includes(designCode)) {
          categoryList.push({ designCode, enName, level, cnName, iconUrl, hasParas });
        }
      });
      return categoryList;
    },
    editToothList() {
      const keyList = Object.keys(this.toothDesignMap);
      let resultList = keyList.reduce((list, curKey) => {
        const { toothDesign } = this.toothDesignMap[curKey];
        list = list.concat(toothDesign);
        return list;
      }, []);
      return resultList;
    },
    // 当前是否选择了其他大类的品类
    hasOtherCategory() {
      const hasOther = Object.keys(this.toothDesignMap).some(key => this.toothDesignMap[key].toothDesign.length > 0 && Number(key) !== 24000);
      return hasOther;
    },

    curToothDesign() {
      // console.log('curToothDesign', this.toothDesignMap)
      // 有时候会没有拿到this.toothDesignMap，页面就报错了
      let obj = {
          '21000': {
              'toothDesign': [],
              'imageBase64': null
          },
          '22000': {
              'toothDesign': [],
              'imageBase64': null
          },
          '23000': {
              'toothDesign': [],
              'imageBase64': null
          },
          '24000': {
              'toothDesign': [],
              'imageBase64': null
          }
      }
      if(Object.keys(this.toothDesignMap).length === 0){
        this.toothDesignMap = obj;
      }
      return copy(this.toothDesignMap[this.selectCode].toothDesign);
    },
    otherDesignerType() {
      if (this.designerTypes) {
        const codeList = this.designerTypes.filter(item => item.designUser !== this.userCode).map(item => {
          const designTypes = JSON.parse(item.designTypes)
          if (designTypes) {
            return designTypes.map(item => item.code)
          }
          return null
        });
        return codeList.flat()
      }
      return []
    },
  },
  watch: {
    isShow(show) {
      if(show) {
        this.$refs.drawer.isShow = true;
      }
    },
    propCategoryCode: { // 初始化时默认选择
      immediate: true,
      handler(code) {
        if (code && code !== this.selectCode) {
          this.selectCode = code;
        } else {
          this.selectCode = this.designTypeTree[0].designCode;
        }
      }
    },
    propToothDesign: {
      immediate: true,
      handler(dataList) { // 编辑时-传递旧数据
        Object.keys(this.rootToLeafCodes).forEach(rootKey => {
          if(!this.toothDesignMap[rootKey]) {
            const data = {
              toothDesign: [],
              imageBase64: null,
            };
            this.$set(this.toothDesignMap, rootKey, data); // 激活双向绑定
          }
          const leafCodes = this.rootToLeafCodes[rootKey];
          const leafToothDesign = dataList.filter(item => leafCodes.includes(item.code));
          this.toothDesignMap[rootKey].toothDesign = copy(leafToothDesign);
        });
        this.selectCode = this.propCategoryCode;
      },
    },

    implantSystemObj: {
      handler(val) {
        console.log('implantSystemObj-val', val)
        if (val) {
          this.implantSystem = val;
        }
      },
      immediate: true
    }
    
  },

  created() {},

  methods: {
    /**
     * 获取当前一级设计类型的牙位图截图
     */
    getCurrentDesignImage() {
      if (this.$refs.drawer) {
        return { [this.selectCode]: this.$refs.drawer.threeView.getImage() }
      }
      return ''
    },
    // 一级类是否能切换
    disableSelect(code) {
      const dataList = this.$refs.drawer?.toothDesign || [];
      return code !== 24000 && dataList.some(item => item.code === 24102);
    },

    // 关闭牙位图时数据处理和传递
    closeToothDialog(toothDesign, base64, designCodes, implantForm = {}) {
      
      this.$refs.drawer.setCurrentInfo(); // 把当前选项卡的品类保存一下

      let infoMap = copy(this.toothDesignMap), totalList = [], imageMap = {}, toothDesignCodes = [];
      Object.keys(this.rootToLeafCodes).forEach(rootKey => {
        const { toothDesign, imageBase64 } = this.toothDesignMap[rootKey];
        if (imageBase64) {
          imageMap[rootKey] = imageBase64;
        }
        totalList = totalList.concat(toothDesign);
      });

      toothDesignCodes = totalList.map(item => item.code);
      
      const param = {infoMap, totalToothDesign: totalList, imageMap, toothDesignCodes, implantForm}; // infoMap的牙位图信息
      // 如果选择了单孔牙支持式导板，但没有选择种植体系统，则不能关闭侧拉弹窗
      if (toothDesignCodes.some(item => [23601].includes(item)) && !implantForm.implantSystem) {
        this.$message.error(this.$t('order.detail.guide.implantSystemTips'));
        return
      }
      this.$emit('closeToothDrawer', param);
      this.isShow = false;
      if (this.$refs.drawer) {
        this.$refs.drawer.isShow = false;
      }
    },

    // 切换一级类时保存前一个数据
    handleSelectCode(code) {
      if(this.selectCode !== code) {
        this.$refs.drawer.setCurrentInfo();// 把当前选项卡的品类保存一下
        this.selectCode = code;
      }
    },

    // 保存当前牙位信息-用于子组件传递数据回来
    setCurrentInfo(curToothDesign, curBase64, toothDesignCodes, implantForm) {
      this.toothDesignMap[this.selectCode].toothDesign = copy(curToothDesign);
      this.toothDesignMap[this.selectCode].imageBase64 = curBase64;
      console.log('implantForm.implantSystem', implantForm, implantForm.implantSystem)
      if (implantForm.implantSystem) {
        // this.toothDesignMap[this.selectCode].implantForm = implantForm;
        this.implantSystem = implantForm;
      }
    },

    resetImplantForm() {
      this.implantSystem = {}
    }

  }
}
</script>

<style lang="scss" scoped>
// 牙位图tab
.tooth-dailog {
  .tooth-drawer-tab {
    display: flex;
    margin-bottom: 16px;
    padding: 4px;
    border-radius: 4px;
    background: #2F3238;
    width: fit-content;

    .tooth-drawer-tab-li {
      cursor: pointer;

      .tooth-drawer-tab-item {  
        display: inline-block;  
        padding: 8px 0;   
        border-radius: 4px;

        .tab-item-text {
          display: inline-block;
          padding: 0 16px;
          color: #E1E8FF;
          border-right: 1px solid #5C6066;
        }

        &:hover {
          color: $hg-white;
          background: $hg-main-blue;
          .tab-item-text {
            border-right-color: $hg-main-blue;
          }
        }

        &:last-of-type {
          .tab-item-text {
            border-right: none;
          }
        }
      }

      .is-active-tab {
        color: $hg-white;
        background: $hg-main-blue;
        .tab-item-text {
          border-right-color: $hg-main-blue;
        }
      }
    }

    

    .tooth-drawer-tab-li.is-disabled {
      cursor: not-allowed;

      .tab-item-text {
        color: rgba(243, 245, 247, 0.24);
      }

      .tooth-drawer-tab-item {
        pointer-events: none;
        :hover {
          background: transparent;
          .tab-item-text {
            border-right-color: #5C6066;
          }
        }
      }
    }
  }
}
</style>