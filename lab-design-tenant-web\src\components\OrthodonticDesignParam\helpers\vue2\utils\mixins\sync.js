export function createSyncMixin(prop, privateField) {
  const unwatchList = []

  return {
    data() {
      const data = {}
      data[privateField] = this[prop]
      return data
    },

    created() {
      unwatchList.push(
        this.$watch(prop, () => {
          this[privateField] = this[prop]
        })
      )

      unwatchList.push(
        this.$watch(privateField, () => {
          this.$emit(`update:${prop}`, this[privateField], {
            sync: true
          })
        })
      )
    },

    beforeDestory() {
      for (const unwatch of unwatchList) {
        unwatch()
      }
    }
  }
}
