<template>
  <div class="cascader-box">
    <el-cascader
      ref="myCascader"
      v-model="selectedValue"
      :options="options"
      :props="propsObj"
      :show-all-levels="showAllLevels"
      :collapse-tags="collapseTags"
      @change="change"
      @expand-change="expandChange"
      @visible-change="visibleChange"
      @remove-tag="removeTag"
    />
  </div>
</template>
<script>
export default {
  name: 'VueCascader',
  props: {
    value: {
      type: [Number, String, Array],
      default: () => {
        return ''
      }
    },
    options: {
      type: Array,
      default: () => {
        return []
      }
    },
    propsObj: { // 可设置一些配置项，例如把value和label绑定到某个值上, 详情见elementui
      type: Object,
      default: () => {
        return {}
      }
    },
    showAllLevels: {
      type: Boolean,
      default: false
    },
    collapseTags: { // 折叠tag
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // selectedValue: this.value
    }
  },
  computed: {
    selectedValue: {
      get() {
        return this.value
      },
      set() {}
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    change(value) {
      // 当前选中的节点数组
      const checkedNodes = this.$refs.myCascader.getCheckedNodes()
      this.$emit('change', value, checkedNodes)
    },
    // 展开节点发生变化时触发
    expandChange(value) {
      this.$emit('expandChange', value)
    },
    // 下拉框出现/隐藏时触发（出现为true,隐藏为false）
    visibleChange(show) {
      this.$emit('visibleChange', show)
    },
    // 多选模式下移除tag时触发
    removeTag(value) {
      this.$emit('removeTag', value)
    }
  }
}
</script>
<style lang="scss" scoped>
.cascader-box {
  ::v-deep .el-cascader {
    width: 100%;
    .el-input__inner {
      background: transparent;
      &::placeholder {
        color: $hg-secondary-fontcolor;
      }
    }
    .el-input__suffix {
      right: 8px !important;
      color: $hg-secondary-fontcolor !important;
    }
    .el-cascader__tags {
      .el-tag {
        background: $hg-tag-bg-color;
        color: $hg-secondary-fontcolor;
        font-weight: 400;
        font-size: 14px;
        border-radius: 2px;
        .el-icon-close {
          color: $hg-secondary-fontcolor;
          width: 18px;
          height: 18px;
          font-size: 16px;
          font-weight: 400;
          &::before {
            margin-top: 2px;
          }
        }
      }
    }
  }
}
</style>
