<template>
  <div class="hg-table">
    <el-table
      v-loading="loading"
      element-loading-background="rgba(18,19,20, 0.8)"
      :default-sort="defaultSort"
      :data="data" 
      :max-height="maxHeight" 
      :height="height" 
      :header-row-class-name="headerRowClassName"
      :cell-class-name="cellClassName"
      :row-class-name="rowClassName"
      @row-click="rowClick"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @sort-change="handleSortChange">
      

      <!-- 勾选列 -->
      <el-table-column
        v-if="needSelect"
        type="selection"
        align="center"
        min-width="60%"
        :selectable="isSelectable"
        >
        </el-table-column>
      <!-- 序号 -->
      <el-table-column v-if="hasIndex" :label="$t('common.serialNo')" align="center" type="index" width="70"></el-table-column>
      <!-- 表头 start -->
      <el-table-column
        v-if="item.isHideHeader == 'hide' ? false : true"
        v-for="(item, index) in headerData"
        :key="index"
        :show-overflow-tooltip="!item.noTip"
        :sortable="item.sortable"
        :label="item.label || (item.getLabel && item.getLabel())"
        :align="item.align || 'left'"
        :prop="item.prop"
        :class-name="item.className"
        :width="item.width"
        :min-width="item.minWidth"
        >

        <!-- 可自定义内容的样式 -->
        <template v-slot="scope">
          <slot :name="item.prop" :row="scope.row" :index="scope.$index">{{ scope.row[item.prop] }}</slot>
        </template>
      </el-table-column>
      <!-- 表头 end -->
      <!-- 无数据样式 -->
      <div slot="empty" class="table-empty" v-if="customNoData">
        <img class="no-data-img" src="@/assets/images/inbox-blank.svg" />
        <span>{{$t('common.noData')}}</span>
      </div>
    </el-table>
  </div>
</template>

<script>
/**
 * 依赖于element-ui el-table
 */
export default {
  name: 'HgTable',

  props: {
    data: {   // 列表数据
      type: Array,
      require: true,
    },
    headerData: {     // 表头
      type: Array,
      require: true,
    },
    maxHeight: {      // 最大高度
      type: String,
      default: 'calc(100% - 58px)',
    },
    height: {         // 高度
      type: String,
      default: 'auto',
    },
    needSelect: Boolean,    // 是否需要勾选
    loading: {
      type: Boolean,
      default: false
    },
    defaultSort: {
      type: Object,
      default(){
        return {}
      }
    },
    hasIndex: {
      type: Boolean,
      default: false
    },
    customNoData: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      headerRowClassName: 'header-row-item',
      // 每个内容单元格的额外class
      cellClassName: 'cell-column-item',
      rowClassName: 'row-item',

      selectedRows: 0, // 被选中的行数
      selectList: [], // 被选中的数据
    };
  },

  watch:{
  },

  methods: {
    
    toggleCheckAll() {
      
    },

    isSelectable(row,index) {
      return true;
    },

    // 某一行点击事件
    rowClick(row,column,event) {
      this.$emit('enter-detail', row);
    },

    handleSelect(selection, row) {
      if(this.needSelect){
        this.$emit('update-selected-rows', selection.length, selection);
      }
    },

    handleSelectAll(selection) {
      if(this.needSelect){
        this.$emit('update-selected-rows', selection.length, selection);
      }
    },
    handleSortChange(scope){
      this.$emit('sortChange', scope);
    }
  }
};
</script>

<style lang="scss" scoped>
.hg-table {
  position: relative;
  display: flex;

  .el-table {
    border-radius: 4px;
    background: $hg-main-black;
    &::before {
      height: 0; // 去掉莫名切莫的线
    }
  }
  /deep/.el-table__fixed{
    &::before {
      height: 0; // 去掉莫名切莫的线
    }
  }
}

.hg-table > .el-table {
  /deep/.el-table__header-wrapper {
    .header-row-item {
      background: transparent;
      th {
        height: 48px;
        font-size: 12px;
        color: #C4C8CD;
        background: transparent;
        border-bottom: 1px solid $hg-border;

        // 第一个th 和 最后一个th
        &:first-of-type {
          padding-left: 12px;
        }

        &:nth-last-of-type(2) {
          div {
            padding-right: 18px;
          }
        }
      }

      th > div {
        line-height: 16px;
      }
    }

    .el-checkbox__input {
      height: 16px;
    }

    .el-checkbox__input .el-checkbox__inner {
      border: 1px solid $hg-border;
      background: transparent;
      width: 16px;
      height: 16px;
      &::after {
        transition: none;
      }
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      border-radius: 2px;
      border: 1px solid $hg-border;
      padding: 4px;
      background-color: transparent;

      &::after {
        top: 0;
        left: 0;
        margin-top: 3px;
        margin-left: 3px;
        transition: none;
        transform: none;
        width: 8px;
        height: 8px;
        border: none;
        background-color: $hg-main-blue;
      }
    }

    .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      top: 6px;
      background-color: $hg-main-blue;
    }
  }
  /deep/.el-table__fixed-header-wrapper {
    .header-row-item {
      background: transparent;
      th {
        height: 48px;
        font-size: 12px;
        color: $hg-disable;
        background: $hg-main-black;
        border-bottom: 1px solid $hg-border;
      }
    }
  }

  /deep/.el-table__body-wrapper {
    .row-item {
      background: transparent;
      &:hover {
        background-color: $hg-hover;
        td {
          background-color: $hg-hover;
        }
      }
    }

    .el-checkbox__input .el-checkbox__inner {
      width: 16px;
      height: 16px;
      border: 1px solid $hg-border;
      background: transparent;
      &::after {
        transition: none;
      }
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      border: none;
      background: $hg-main-blue;

      &::after {
        top: 2px;
        left: 6px;
        height: 8px;
        width: 4px;
        border-color: #123112;
      }
    }

    .cell-column-item {
      cursor: pointer;
      height: 56px;
      // color: $hg-secondary-text;
      color: $hg-label;
      font-size: 14px;
      border-bottom: 1px dashed #2d2f33;

      &:first-of-type {
        padding-left: 12px;
      }

      &:last-of-type {
        div {
          padding-right: 16px;
        }
      }
    }
  }
}

.hg-table > .el-table {
  /deep/.caret-wrapper {
    .sort-caret {
      border: solid 6px transparent;
    }
    .sort-caret.ascending {
      border-bottom-color: #9EA2A8;
      top: 3px;
    }
    .sort-caret.descending {
      border-top-color: #9EA2A8;
      bottom: 6px;
    }
  }

  /deep/.ascending .caret-wrapper {
    .sort-caret.ascending {
      border-bottom-color: $hg-label;
    }
  }

  /deep/.descending .caret-wrapper {
    .sort-caret.descending {
      border-top-color: $hg-label;
    }
  }
  .table-empty{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
</style>
