import {upperNumbers, lowerNumbers, upperLeftNumber, upperRightNumber, lowerLeftNumber, lowerRightNumber} from '@/public/constants';
import i18n from '@/public/i18n/index';
import { showToothNumBySystemIdx } from '@/filters';
import { deepClone, parseJson, copy } from '@/public/utils';
import store from '@/store';

/**
 * 获取组装好的牙位信息
 * @param {*} toothOrigin 牙位信息原始数据
 * @returns 
 */
export const getToothInfo = (originList = [], implantSystem) => {
  const toothOrigin = deepClone(originList);
  // 连接体21501
  const bridgeList = toothOrigin.filter(item => [21501,23501].includes(item.code));
  const bridgeToothPositionList = bridgeList.map(item => item.tooth).flat();
  const bridgeChildren = toothOrigin
    .filter(item => ![21501,23501].includes(item.code) && item.tooth && item.tooth.some(item => bridgeToothPositionList.includes(item)))
    .map(item => {
      return {
        ...item,
        tooth: item.tooth.filter(item => bridgeToothPositionList.includes(item))
      }
    });
  let notBridgeList = toothOrigin.filter(item => item.tooth && !item.tooth.every(item => bridgeToothPositionList.includes(item)));
  notBridgeList.forEach(item => {
    if(item.tooth && item.tooth.some(item => bridgeToothPositionList.includes(item))) { // 同类型，部分在桥体
      item.tooth = item.tooth.filter(tooth => !bridgeToothPositionList.includes(tooth));
    }
  });
  bridgeList.forEach(item => {
    item.children = [];
    bridgeChildren.forEach(child => {
      // if (child.tooth.every(toothPosition => item.tooth.includes(toothPosition))) {
      //   child = getToothPositionDisplay(child);
      //   item.children.push(child);
      // }
      if (item.tooth.some(bridgeTooth => child.tooth.includes(bridgeTooth))) {
        let newChild = deepClone(child);
        newChild.tooth = newChild.tooth.filter(childTooth => item.tooth.includes(childTooth));
        newChild = getToothPositionDisplay(newChild, implantSystem);
        item.children.push(newChild);
      }
    });
  });
  const toothList = [...bridgeList, ...notBridgeList];
  toothList.forEach(item => getToothPositionDisplay(item, implantSystem));
  return toothList;
}

/**
 * 获取牙位显示
 * @param {*} toothObj 牙位信息对象
 */
const getToothPositionDisplay = (toothObj = {}, implantSystem) => {
  const { code, tooth } = toothObj;
  let toothValue = '';
  let isJaw = false;

  let implantSystemObj
  if (implantSystem) {
    implantSystemObj = parseJson(implantSystem);
  }
  switch(code) {
    // 连接体21501 解剖型桥架（无蜡型）23104  解剖型桥架（有蜡型）23105
    case 21501:
    case 23104:
    case 23105:
    case 23501:
    case 21103:
    case 23103:
      toothValue = showToothNumBySystemIdx(tooth.join('-'));
      break;
    // 固定个性化托盘21303 活动托盘22501 种植托盘23401 半口支架22101 夜磨牙垫24301 咬合垫24302 托槽去除24402 分牙24403 隐形正畸(24101-24104)
    case 21303:
    case 22501:
    case 23401:
    case 22101:
    case 24301:
    case 24302:
    case 24402:
    case 24403:
    case 24101:
    case 24102:
    case 24103:
    case 24104:
    case 23106:
    case 23403:
    case 23404:
      if (tooth.some(item => upperNumbers.includes(item))) {
        toothValue += i18n.t('order.detail.upperJaw');
        isJaw = true;
        // toothValue = `${i18n.t('order.detail.upperJaw')}${tooth.length ? '(' + tooth.join(',') + ')' : ''}`;
      } 
      if (tooth.some(item => lowerNumbers.includes(item))) {
        toothValue += ' ' + i18n.t('order.detail.lowerJaw');
        // toothValue = `${i18n.t('order.detail.lowerJaw')}${tooth.length ? '(' + tooth.join(',') + ')' : ''}`;
        isJaw = true;
      }
      break;
    // 局部支架
    case 22102:
    case 24406:
      if (tooth.some(item => upperLeftNumber.includes(item))) {
        toothValue += i18n.t('order.detail.leftTop');
        isJaw = true;
      }
      if (tooth.some(item => upperRightNumber.includes(item))) {
        toothValue += ' ' + i18n.t('order.detail.rightTop');
        isJaw = true;
      }
      if (tooth.some(item => lowerLeftNumber.includes(item))) {
        toothValue += ' ' + i18n.t('order.detail.leftBottom');
        isJaw = true;
      }
      if (tooth.some(item => lowerRightNumber.includes(item))) {
        toothValue += ' ' + i18n.t('order.detail.rightBottom');
        isJaw = true;
      }
      break;
    case 22201:
    case 22202:
    case 22203:
    case 22204:
    case 22301:
    case 22302:
    case 22303:
      {
        const hasUp = tooth.some(item => upperNumbers.includes(item))
        const hasLow = tooth.some(item => lowerNumbers.includes(item))
        isJaw = true;
        if (hasUp && hasLow) {
          const upTooth = tooth.filter(item => upperNumbers.includes(item))
          const downTooth = tooth.filter(item => lowerNumbers.includes(item))
          toothValue += `${i18n.t('order.detail.upperJaw')} ${showToothNumBySystemIdx(upTooth.join('-'))} ${i18n.t('order.detail.lowerJaw')} ${showToothNumBySystemIdx(downTooth.join('-'))}`;
        } else {
          if (hasUp) {
            toothValue += `${i18n.t('order.detail.upperJaw')} ${showToothNumBySystemIdx(tooth.join('-'))}`;
          } else {
            toothValue += `${i18n.t('order.detail.lowerJaw')} ${showToothNumBySystemIdx(tooth.join('-'))}`;
          }
        }
      }
      break;
    case 22502: 
    case 25002:
    case 22503:
    case 23402:
    case 24405:
    case 21402:
    case 21404:
    case 22401:
    case 23301:
    case 24303:
    case 24401:
    case 24501:
      break; // 不用显示牙位
    // 单牙孔支持式导板
    case 23601:
      {
        const upTooth = tooth.filter(item => upperNumbers.includes(item))
        const downTooth = tooth.filter(item => lowerNumbers.includes(item))
        if (upTooth.length) {
          toothValue += `${i18n.t('order.detail.upperJaw')}: ${showToothNumBySystemIdx(upTooth.join('-'))}`;
        }
        if (downTooth.length) {
          toothValue += ` ${i18n.t('order.detail.lowerJaw')}: ${showToothNumBySystemIdx(downTooth.join('-'))}`;
        }
        if (implantSystemObj) {
          if (implantSystemObj.implantSystem) {
            toothValue += `, ${i18n.t('order.detail.guide.implantSystem')}: ${implantSystemObj.implantSystem}`;
          }
          if (implantSystemObj.implantSeries) {
            toothValue += `, ${i18n.t('order.detail.guide.implantSeries')}: ${implantSystemObj.implantSeries}`;
          }
          if (implantSystemObj.isAtOnce) {
            toothValue += `, ${i18n.t('order.detail.guide.isAtOnce')}`;
          }
        }
      }
      break;
    default:
      toothValue = showToothNumBySystemIdx(tooth.join(','));
      break;
  }
  toothObj.toothValue = toothValue;
  toothObj.isJaw = isJaw;
  return toothObj;
};

/**
 * 获取牙位相关信息[设计品类+牙位图+参数方案]的函数
 * @param {*} param0 
 * @returns {object} { 10002: { toothDesign: [], toothImage: '', parameterContent: '' }, 20001: {} }
 */
export const getToothInfoMap = ({sourceToothDesign = [], paramList = [], toothImage, categoryList, hasMulDesigner = false, isResponsibleDesigner = false, otherAssignDesignCodes = [], implantSystem}) => {
  // console.log('getToothInfoMap传入参数', sourceToothDesign, paramList, toothImage, categoryList);
  let resultMap = {};
  try {
    
    const rootToLeafCodes = store.getters.rootToLeafCodes || {};
    categoryList.forEach(codeStr => {
      const leafCodeList = rootToLeafCodes[codeStr] || []
      const designList = sourceToothDesign.filter(item => leafCodeList.includes(item.code));
      const params = paramList.filter(item => leafCodeList.includes(item.designCode));
      const resultData = {
        toothDesign: designList,
        toothImage: '',
        parameterContent: JSON.stringify(params),
        toothInfo: getToothInfo(copy(designList), implantSystem),
        toothImageBase64: null,
        editParamList: copy(params),
        hasMulDesigner,
        isResponsibleDesigner,
        myToothInfo: [],
        otherToothInfo: []
      };
      if (hasMulDesigner && isResponsibleDesigner) {
        const myDesignList = designList.filter(item => !otherAssignDesignCodes.includes(item.code))
        const otherDesignList = designList.filter(item => otherAssignDesignCodes.includes(item.code))
        resultData.myToothInfo = getToothInfo(copy(myDesignList), implantSystem)
        resultData.otherToothInfo = getToothInfo(copy(otherDesignList), implantSystem)
      }
      resultMap[codeStr] = resultData;
    });

    const firstCode = categoryList[0];
    if(firstCode) {
    
      const imageJson = parseJson(toothImage) || '';
      if(!imageJson && toothImage) { // 转化json失败，并且有数据，则说明是旧订单的。默认是只有一个大类，且归属于第一个
        resultMap[firstCode].toothImage = toothImage;

        /**
         * 兼容旧数据中的[其他]类型
         * 放在这里是因为，只有toothImage不是json格式的订单需要兼容--前提是没有BUG
         */
        if(categoryList.length === 1 && sourceToothDesign.some(item => item.code === 25002)) { 
          let dataList = resultMap[firstCode].toothDesign;
          dataList = dataList.filter(item => item.code !== 25002); // 把旧的除掉
  
          let oldOtherDesign = sourceToothDesign.filter(item => item.code === 25002);
          oldOtherDesign = getOtherNewCode(Number(firstCode), oldOtherDesign);
          
          const newToothDesign = dataList.concat(oldOtherDesign);
          resultMap[firstCode].toothDesign = newToothDesign;
          resultMap[firstCode].toothInfo = getToothInfo(copy(newToothDesign));
        }
  
      }else if(imageJson) {
        Object.keys(imageJson).forEach(codeStr => {
          resultMap[codeStr].toothImage = imageJson[codeStr];
        });
      }
    }

    console.log('resultMap', resultMap);

  } catch (error) {
    console.log('getToothInfoMap', error);
    return resultMap;
  }
  return resultMap;
};

/**
 * 兼容旧数据，需要把旧数据中的code重置为新的
 * 通过数据可以得知，旧数据中的四大类下的other都为[designCode: 25002, parentCode: 24400]
 * 本方法需要把这两个值同时更新
 */
const getOtherNewCode = (curCategoryCode, oldOtherDesign = []) => {
  let newOtherList = oldOtherDesign;
  let curNewOtherItem;
  
  const dataList = store.state.user.rootToLeafList;
  const node = dataList.find(item => item.designCode === curCategoryCode);
  if(node) {
    // console.log('找到所有叶子节点了吗', node.leafList);
    curNewOtherItem = node.leafList.find(item => item.cnName === '其他');
  }

  
  if(curNewOtherItem) {
    const { designCode, parentCode } = curNewOtherItem;
    newOtherList.map(item => {
      item.code = designCode;
      item.pidCode = parentCode;
      return item;
    });
  }
  console.log('getOtherNewCode?', curNewOtherItem);
  return newOtherList;
};

/**
 * 备案：设置根节点
 */
export const setRootCode = (sourceDataList) => {
  const leafToRootCodes = store.getters.leafToRootCodes || {};
  let resultList = sourceDataList.map(data => {
    const rootCode = leafToRootCodes[data.code];
    data.rootCode = rootCode;
    return data;
  });

  return resultList;
};