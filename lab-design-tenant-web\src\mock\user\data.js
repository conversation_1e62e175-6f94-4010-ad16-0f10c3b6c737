//组织管理树
const orgList = [
  {
    code: 200060,
    name: '<PERSON><PERSON><PERSON><PERSON> 设计中心',
    count: 184,
    type: 0,
    org_code: 200060,
    status: 1,
    is_passed: 1,
    children: [
      {
        code: 300089,
        name: '设计一组',
        count: 477,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: [
          {
            code: 300165,
            name: '支架1',
            count: 21,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: null,
          },
          {
            code: 300166,
            name: '固定修复',
            count: 36,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: [
              {
                code: 300168,
                name: '固定修复一组',
                count: 19,
                type: 1,
                org_code: 200060,
                status: 1,
                is_passed: 1,
                children: null,
              },
              {
                code: 300169,
                name: '固定修复二组',
                count: 0,
                type: 1,
                org_code: 200060,
                status: 1,
                is_passed: 1,
                children: null,
              },
              {
                code: 300170,
                name: '固定修复三组',
                count: 17,
                type: 1,
                org_code: 200060,
                status: 1,
                is_passed: 1,
                children: null,
              },
            ],
          },
          {
            code: 300167,
            name: '托盘',
            count: 11,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: null,
          },
        ],
      },
      {
        code: 300037,
        name: '测试设计二组',
        count: 6,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300038,
        name: '测试设计三组',
        count: 8,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300107,
        name: '设计二组',
        count: 6,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300111,
        name: '开发调试组',
        count: 7,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300115,
        name: 'Hdesign',
        count: 5,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300122,
        name: '广州医疗设计组',
        count: 4,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300123,
        name: 'zyl测试组',
        count: 5,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300131,
        name: '销售部',
        count: 19,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: [
          {
            code: 300132,
            name: '海外-美国',
            count: 2,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: null,
          },
          {
            code: 300133,
            name: '海外-欧洲',
            count: 1,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: null,
          },
          {
            code: 300134,
            name: '中国大陆',
            count: 5,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: null,
          },
          {
            code: 300171,
            name: '开发测试业务',
            count: 10,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: null,
          },
        ],
      },
      {
        code: 300138,
        name: '翔越设计一组',
        count: 4,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300147,
        name: 'test ortho',
        count: 4,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: [
          {
            code: 300148,
            name: 'test1.1',
            count: 2,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: [
              {
                code: 300151,
                name: '55',
                count: 1,
                type: 1,
                org_code: 200060,
                status: 1,
                is_passed: 1,
                children: null,
              },
            ],
          },
          {
            code: 300149,
            name: 'test 2.1',
            count: 1,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: [
              {
                code: 300150,
                name: '15626417367',
                count: 1,
                type: 1,
                org_code: 200060,
                status: 1,
                is_passed: 1,
                children: null,
              },
            ],
          },
        ],
      },
      {
        code: 300155,
        name: '开发调试2组',
        count: 1,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300159,
        name: '开发一级部门',
        count: 5,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: [
          {
            code: 300160,
            name: '开发二级部门',
            count: 2,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: null,
          },
        ],
      },
      {
        code: 300161,
        name: '设计三组',
        count: 1,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300164,
        name: '设计五组',
        count: 3,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300174,
        name: '测试管理员',
        count: 1,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300175,
        name: 'UAT测试设计组',
        count: 4,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300176,
        name: '旭设计组',
        count: 4,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300177,
        name: 'Alex部门',
        count: 4,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: null,
      },
      {
        code: 300178,
        name: '磊设计组',
        count: 6,
        type: 1,
        org_code: 200060,
        status: 1,
        is_passed: 1,
        children: [
          {
            code: 300179,
            name: '磊二级设计组',
            count: 4,
            type: 1,
            org_code: 200060,
            status: 1,
            is_passed: 1,
            children: null,
          },
        ],
      },
    ],
  },
]
// 人员列表
const userList = {
  count: 184,
  list: [
    {
      user_code: 101086,
      real_name: '设计师',
      roles: '设计师',
      scopes:
        '牙冠/冠桥（普通）、临冠/临桥（普通）、套筒冠（普通）、贴面（普通）、数字模型（普通）、嵌体/高嵌体（普通）、正畸模型（普通）、内冠（普通）、解剖型内冠（普通）、标准桩核（普通）、解剖型桩核（困难）、桩核冠（困难）、半口支架（专家）、1/4支架（普通）、个性化托盘（普通）、活动义齿（成品牙）（普通）、活动义齿（设计牙）（普通）、试戴义齿（普通）、咬合垫（普通）、咬合板（普通）、夜磨牙垫（普通）、其他（普通）、种植全冠（普通）、种植解剖型内冠（普通）、全冠桥架（困难）',
      is_admin: 0,
      is_dept_admin: 0,
      role_codes: [500003],
      design_codes: [
        20000,
        20001,
        20003,
        20004,
        20005,
        20007,
        20008,
        20013,
        20015,
        20017,
        20018,
        20019,
        20038,
        20039,
        20040,
        20053,
        20060,
        20061,
        20062,
        20063,
        20064,
        20065,
        20091,
        20094,
        20095,
        20096,
      ],
      design_levels: [
        {
          design_code: 20000,
          name: '牙冠/冠桥',
          level_code: [1],
        },
        {
          design_code: 20001,
          name: '临冠/临桥',
          level_code: [1],
        },
        {
          design_code: 20003,
          name: '套筒冠',
          level_code: [1],
        },
        {
          design_code: 20004,
          name: '贴面',
          level_code: [1],
        },
        {
          design_code: 20005,
          name: '数字模型',
          level_code: [1],
        },
        {
          design_code: 20007,
          name: '嵌体/高嵌体',
          level_code: [1],
        },
        {
          design_code: 20008,
          name: '正畸模型',
          level_code: [1],
        },
        {
          design_code: 20013,
          name: '内冠',
          level_code: [1],
        },
        {
          design_code: 20015,
          name: '解剖型内冠',
          level_code: [1],
        },
        {
          design_code: 20017,
          name: '标准桩核',
          level_code: [1],
        },
        {
          design_code: 20018,
          name: '解剖型桩核',
          level_code: [2],
        },
        {
          design_code: 20019,
          name: '桩核冠',
          level_code: [2],
        },
        {
          design_code: 20038,
          name: '桩核冠',
          level_code: [],
        },
        {
          design_code: 20039,
          name: '半口支架',
          level_code: [3],
        },
        {
          design_code: 20040,
          name: '1/4支架',
          level_code: [1],
        },
        {
          design_code: 20053,
          name: '个性化托盘',
          level_code: [1],
        },
        {
          design_code: 20060,
          name: '活动义齿（成品牙）',
          level_code: [1],
        },
        {
          design_code: 20061,
          name: '活动义齿（设计牙）',
          level_code: [1],
        },
        {
          design_code: 20062,
          name: '试戴义齿',
          level_code: [1],
        },
        {
          design_code: 20063,
          name: '咬合垫',
          level_code: [1],
        },
        {
          design_code: 20064,
          name: '咬合板',
          level_code: [1],
        },
        {
          design_code: 20065,
          name: '夜磨牙垫',
          level_code: [1],
        },
        {
          design_code: 20091,
          name: '其他',
          level_code: [1],
        },
        {
          design_code: 20094,
          name: '种植全冠',
          level_code: [1],
        },
        {
          design_code: 20095,
          name: '种植解剖型内冠',
          level_code: [1],
        },
        {
          design_code: 20096,
          name: '全冠桥架',
          level_code: [2],
        },
      ],
      login_status: 0,
    },
    {
      user_code: 101087,
      real_name: 'OQC',
      roles: 'OQC',
      scopes:
        '牙冠/冠桥（普通）、临冠/临桥（普通）、套筒冠（普通）、贴面（普通）、数字模型（普通）、嵌体/高嵌体（普通）、正畸模型（普通）、内冠（普通）、解剖型内冠（普通）、标准桩核（普通）、解剖型桩核（困难）、桩核冠（困难）、半口支架（专家）、1/4支架（普通）、个性化托盘（普通）、活动义齿（成品牙）（普通）、活动义齿（设计牙）（普通）、试戴义齿（普通）、咬合垫（普通）、咬合板（普通）、夜磨牙垫（普通）、其他（普通）、种植全冠（普通）、种植解剖型内冠（普通）、全冠桥架（困难）',
      is_admin: 0,
      is_dept_admin: 0,
      role_codes: [500004],
      design_codes: [
        20000,
        20001,
        20003,
        20004,
        20005,
        20007,
        20008,
        20013,
        20015,
        20017,
        20018,
        20019,
        20038,
        20039,
        20040,
        20053,
        20060,
        20061,
        20062,
        20063,
        20064,
        20065,
        20091,
        20094,
        20095,
        20096,
      ],
      design_levels: [
        {
          design_code: 20000,
          name: '牙冠/冠桥',
          level_code: [1],
        },
        {
          design_code: 20001,
          name: '临冠/临桥',
          level_code: [1],
        },
        {
          design_code: 20003,
          name: '套筒冠',
          level_code: [1],
        },
        {
          design_code: 20004,
          name: '贴面',
          level_code: [1],
        },
        {
          design_code: 20005,
          name: '数字模型',
          level_code: [1],
        },
        {
          design_code: 20007,
          name: '嵌体/高嵌体',
          level_code: [1],
        },
        {
          design_code: 20008,
          name: '正畸模型',
          level_code: [1],
        },
        {
          design_code: 20013,
          name: '内冠',
          level_code: [1],
        },
        {
          design_code: 20015,
          name: '解剖型内冠',
          level_code: [1],
        },
        {
          design_code: 20017,
          name: '标准桩核',
          level_code: [1],
        },
        {
          design_code: 20018,
          name: '解剖型桩核',
          level_code: [2],
        },
        {
          design_code: 20019,
          name: '桩核冠',
          level_code: [2],
        },
        {
          design_code: 20038,
          name: '桩核冠',
          level_code: [],
        },
        {
          design_code: 20039,
          name: '半口支架',
          level_code: [3],
        },
        {
          design_code: 20040,
          name: '1/4支架',
          level_code: [1],
        },
        {
          design_code: 20053,
          name: '个性化托盘',
          level_code: [1],
        },
        {
          design_code: 20060,
          name: '活动义齿（成品牙）',
          level_code: [1],
        },
        {
          design_code: 20061,
          name: '活动义齿（设计牙）',
          level_code: [1],
        },
        {
          design_code: 20062,
          name: '试戴义齿',
          level_code: [1],
        },
        {
          design_code: 20063,
          name: '咬合垫',
          level_code: [1],
        },
        {
          design_code: 20064,
          name: '咬合板',
          level_code: [1],
        },
        {
          design_code: 20065,
          name: '夜磨牙垫',
          level_code: [1],
        },
        {
          design_code: 20091,
          name: '其他',
          level_code: [1],
        },
        {
          design_code: 20094,
          name: '种植全冠',
          level_code: [1],
        },
        {
          design_code: 20095,
          name: '种植解剖型内冠',
          level_code: [1],
        },
        {
          design_code: 20096,
          name: '全冠桥架',
          level_code: [2],
        },
      ],
      login_status: 0,
    },
  ],
}
export default {
  orgList,
  userList,
}
