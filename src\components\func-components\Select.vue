<template>
  <div class="select-box">
    <el-select
      popper-class="hg-select-box"
      :value="selectedValue"
      :multiple="isMultiple"
      :placeholder="placeholder ? placeholder : $t('common.selectPlaceholder')"
      :filterable="filterable"
      :disabled="disabled"
      @focus="focus"
      @change="change"
    >
      <el-option
        v-for="(item, idx) in selectOptions"
        :key="item.label + item.value + idx"
        :label="getSelectLabel(item)"
        :value="item.value"
      />
    </el-select>
  </div>
</template>
<script>
export default {
  name: "Select",
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    isMultiple: { // 是否多选
      type: Boolean,
      default: false
    },
    value: {
      // type: String,
      default: ""
    },
    selectOptions: {
      type: Array,
      default: () => []
    },
    filterable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedValue: this.value,
    };
  },
  created() {
  },
  watch: {
    value(val) {
      this.selectedValue = val;
    },
  },
  methods: {
    // 获取下拉列表内容中英文名称
    getSelectLabel(item) {
      let result = ''
      if(item.i18nCode){
        result = item.label
      } else {
        result = this.$i18n.locale == "en" && item.lable_en ? item.lable_en : item.label;
      }
      return result;
    },
    focus() {
      this.$emit('focus');
    },
    change(value) {
      let selectedValue = '';
      if (this.selectedValue) {
        selectedValue = this.selectedValue.toString().indexOf('+') !== -1 ? this.selectedValue.split('+')[1] : this.selectedValue; // 针对区号的情况
      }
      this.selectedValue = value != selectedValue ? value : this.selectedValue;
      this.$emit('change', value);
    },

  },
};
</script>
<style lang="scss" scoped>
.select-box {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  ::v-deep .el-select {
    width: 100%;
    .el-input {
      width: 100%;
      display: block;
      .el-input__inner {
        width: 100%;
        border: 1px solid $hg-border-color;
        border-radius: $hg-border-radius2;
        box-sizing: border-box;
        background-color: transparent;
        height: 40px;
        line-height: 40px;
        color: $hg-primary-fontcolor;
        font-weight: normal;
        padding: 0 24px;
        &::placeholder {
          color: $hg-secondary-fontcolor;
        }
      }
      &:hover {
        .el-input__inner {
          border: 1px solid $hg-disable-fontcolor;
        }
      }
      &.is-focus {
        .el-input__inner {
          border-color: $hg-primary-fontcolor;
        }
      }
      .el-input__suffix {
        right: 8px !important;
        .el-select__caret{
          color: $hg-secondary-fontcolor;
        }
      }
    }
    .el-select__tags {
      padding: 0 8px;
      .el-tag {
        height: 32px;
        line-height: 32px;
        border-radius: 2px;
        background: $hg-hover-bg-color;
        border: none;
        padding: 0 8px;
        color: $hg-primary-fontcolor;
        .el-icon-close {
          background-color: transparent;
          right: 0;
          font-size: 20px;
          color: $hg-secondary-fontcolor;
          margin-left: 8px;
        }
      }
    }
  }
}

</style>
<style lang="scss">
.hg-select-box {
  @include bg-box-shadow();
}
</style>
