<template>
  <el-dialog
    custom-class="order-detail-page  union-normal-problem"
    append-to-body 
    width="706px" 
    :title="$t('common.systemTips')"
    :visible="isShow" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :before-close="handleClose">
    <div class="title">
      <span class="require">{{ '* ' }}</span>
      {{ $t('order.detail.tips.unionNormalProblem') }}
    </div>
    <div class="content" v-loading="loadingList">
      <el-collapse v-model="activeNames">
        <el-collapse-item :id="order.orderNo" v-for="order in orderDesigners" :name="order.orderNo" :key="order.orderNo">
          <template slot="title">
            <div class="header-title"><span class="name">{{order.orderNo}}<span v-if="order.isWithdraw" class="reback-tips">{{$t('order.detail.tips.unionRecall')}}</span></span><span class="time">{{ order.createdTime | dateFormatInHtml }}</span></div>
          </template>
          <div>
            <div class="check-item" v-for="designer in order.children" :key="designer.id">
              <el-checkbox :disabled="order.isWithdraw || designer.isCommonQuestions" v-model="designer.selectDesigner"></el-checkbox>
              <div class="design-info">
                <div :class="['designer-name', (order.isWithdraw || designer.isCommonQuestions) ? 'disable-name' : '']">{{designer.designUserName}}<span v-if="designer.isCommonQuestions" class="reback-tips">{{$t('order.detail.tips.unionIssues')}}</span></div>
                <div :class="['design-type', (order.isWithdraw || designer.isCommonQuestions) ? 'disable-name' : '']">{{$getI18nText(designer.designTypeName)}}</div>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div slot="footer" class="footer">
      <!-- <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" class="select-all">{{ $t('userList.list.selectAll') }}</el-checkbox> -->
      <div class="order-design-btn">
        <hg-button type="secondary" @click="handleClose">{{ $t('common.btn.cancel') }}</hg-button>
        <hg-button :loading="isLoading" :disabled="isSelectOrder.length == 0" @click="handleConfirm">{{ $t('common.btn.confirm') }}</hg-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { saveUnionCommonQuestions } from '@/api/order/operate'
import { getorderDesigners, saveNewCommonQuestions } from '@/api/order'
export default {
  name: 'MarkNormalProblem',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    lastDesignerTypes: Array,
    orderCode: String
  },
  data() {
    return {
      isLoading: false,
      loadingList: false,
      activeNames: [],
      orderDesigners: []
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        this.loadingList = true;
        this.getorderDesigners()
      }
    },
    orderDesigners(){
      
    }
  },
  computed: {
    ...mapGetters(['oneDesignList', 'language', 'oneDesignSkuList']),
    // 是否选择了设计师
    isSelectOrder(){
      let selectId = []
      this.orderDesigners.find((item) => {
        item.children.forEach((it) => {
          if(!it.isCommonQuestions && it.selectDesigner){
            selectId.push(it.id)
          }
        })
      })
      return selectId
    }
  },
  methods: {
    // 获取设计师轮次信息
    async getorderDesigners(){
      try {
        const { code, data } = await getorderDesigners(this.orderCode);
        if(code == 200){
          this.orderDesigners = this.transformData(data);
          this.loadingList = false;
          this.$nextTick(() => {
            this.orderDesigners.forEach((item) => {
              this.activeNames.push(item.orderNo)
            })
          })
        }
      } catch (error) {
        this.loadingList = false;
      }
    },
    transformData(inputArray) {
      const result = inputArray.reduce((acc, item) => {
        // 查找是否已经存在相同的 orderNo
        let existingGroup = acc.find(group => group.orderNo === item.orderNo);

        if (!existingGroup) {
          // 如果不存在，创建一个新的组
          existingGroup = {
            orderNo: item.orderNo,
            createdTime: item.createdTime,
            isWithdraw: item.isWithdraw,
            children: []
          };
          acc.push(existingGroup);
        }
        item.selectDesigner = item.isCommonQuestions ? true : false;
        item = this.handelDetails(item)
        // 将当前项添加到 children 中
        existingGroup.children.push(item);
        return acc;
      }, []);

      return result;
    },
    handelDetails(data){
      data.designTypeName = {zh: '', en: ''};
      if(data.designTypes){
        let designTypes = JSON.parse(data.designTypes);
        designTypes.forEach((design) => {
          let node = this.oneDesignSkuList.find((it) => {return it.skuCode == design})
          if(node){
            data.designTypeName.zh += node.zhName + '、';
            data.designTypeName.en += node.enName + '、'
          }
        })
        data.designTypeName.zh = data.designTypeName.zh.slice(0, data.designTypeName.zh.length - 1)
        data.designTypeName.en = data.designTypeName.en.slice(0, data.designTypeName.en.length - 1)
      }
      return data
    },
    handleClose() {
      this.$emit('update:isShow', false)
    },
    async handleConfirm() {
      try {
        this.isLoading = true
        const params = {
          ids: this.isSelectOrder,
          orderCode: this.orderCode
        }
        const { code } = await saveNewCommonQuestions(params)
        if (code === 200) {
          this.$emit('update:isShow', false)
          this.$emit('markSuccess')
        }
      } catch (error) {
        console.log('error: ', error);
      } finally {
        this.isLoading = false
      }
    },
  }
}
</script>

<style lang="scss">
.union-normal-problem {
  .el-dialog__header {
    background: $hg-hover;
  }
  .el-dialog__body {
    background: $hg-hover;
    .title {
      color: $hg-label;
      font-size: 14px;
      .require {
        color: #E55353;
      }
    }
    .content {
      margin-top: 16px;
      min-height: 200px;
      max-height: 290px;
      // padding: 0 16px;
      overflow-y: auto;
      border-radius: 4px;
      background: #27292E;
      border: 1px solid #3A3A3A;
      .el-collapse-item__header{
        position: relative;
        border-bottom: 1px solid #3A3A3A;
        padding: 0 16px;
        color: #F9FAFB;
        .header-title{
          width: 100%;
          padding-left: 20px;
          display: flex;
          justify-content: space-between;
          .reback-tips{
            display: inline-flex;
            margin-left: 10px;
            height: 24px;
            padding: 2px 4px;
            align-items: center;
            border-radius: 3px;
            background: #D0302F;
          }
        }
        .el-icon-arrow-right{
          position: absolute;
          left: 0px;
        }
      }
      .el-collapse-item__content{
        padding: 16px;
        background: $hg-main-black;
        color: #F9FAFB;
      }
      .check-item {
        display: flex;
        align-items: center;
        border-bottom: 1px dashed #3A3A3A;
        line-height: 40px;
        // margin-bottom: 16px;
        .el-checkbox {
          margin-right: 8px;
        }
        .el-checkbox__input.is-disabled .el-checkbox__inner{
          background-color: transparent;
          border-color: #FFFFFF52;
        }
        .design-info{
          display: flex;
          width: 100%;
          align-items: center;
          justify-content: space-between;
        }
        .designer-name {
          color: $hg-label;
          font-size: 14px;
          line-height: 20px;
          .reback-tips{
            display: inline-flex;
            margin-left: 10px;
            // height: 20px;
            padding: 2px 4px;
            align-items: center;
            color: #fff;
            border-radius: 3px;
            background: #D0302F;
          }
        }
        .design-type {
          max-width: 350px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          line-height: 16px;
          color: $hg-label;
        }
        .disable-name{
          color: #FFFFFF52;
        }
      }
    }
  }
  .el-dialog__footer {
    background: $hg-hover;
    .select-all {
      color: $hg-label;
      margin-bottom: 16px;
    } 
    .footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .hg-button {
        width: 96px;
        &:first-of-type {
          margin-right: 20px;
        }
      }
    }
  }
}
</style>