import { deepCompare } from './compare'

export function booleanCalculate(left, operator, right) {
  switch (operator) {
    // eslint-disable-next-line
    case '==': return left == right
    case '===': return left === right
    // eslint-disable-next-line
    case '!=': return left != right
    case '!==': return left !== right
    case '>': return left > right
    case '>=': return left >= right
    case '<': return left < right
    case '<=': return left <= right
    case 'equal': return deepCompare(left, right)
    case '!equal': return !deepCompare(left, right)
    default: return true
  }
}

export function multipleBooleanCalculate(satisfy, arr) {
  switch (satisfy) {
    case '&&': {
      return arr.every((item) => {
        const [left, operator, right] = item
        return booleanCalculate(left, operator, right)
      })
    }

    case '||': {
      return arr.some((item) => {
        const [left, operator, right] = item
        return booleanCalculate(left, operator, right)
      })
    }
  }
}

// 只要有一项匹配上即可
export function includes(object, queryParams) {
  for (const key in queryParams) {
    const value = queryParams[key]
    const field = object[key]
    if (value !== undefined && field !== undefined) {
      return value === field
    }

    if ((value === undefined && field) || (value && field === undefined)) {
      continue
    }
  }
  return false
}

// 字段空值也匹配(理解为全匹配)
export function voidMatch(object, queryParams) {
  for (const key in queryParams) {
    const value = queryParams[key]
    if (value === '') {
      continue
    }
    if (object[key] !== value) {
      return false
    }
  }
  return true
}

// 字段严格相等
export function strictMatch(object, queryParams) {
  for (const key in queryParams) {
    const value = queryParams[key]
    if (object[key] !== value) {
      return false
    }
  }
  return true
}

// 匹配
export function match(object, queryParams, strict) {
  return strict ? strictMatch(object, queryParams) : voidMatch(object, queryParams)
}
