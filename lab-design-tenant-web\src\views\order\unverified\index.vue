<template>
  <div class="order-list">

    <div class="header-search">
      <filter-component 
        :searchData="searchData" 
        :btnDisabled="btnDisabled"
        :btnLoading="btnLoading"
        @onSearch="onSearch" 
        @handleBatchDownload="handleBatchDownload"></filter-component>
    </div>

    <div class="depart-table">
      <hg-table
        :header-data="tableHeader"
        :height="'auto'"
        :needSelect="true"
        :data="dataList"
        :defaultSort="defaultSort"
        :loading="tableLoading"
        @sortChange="sortTable"
        @update-selected-rows="handleSelect"
        @enter-detail="handleToDetail">
        <!-- 订单编号 -->
        <template #orderNo="scope">
          <el-tooltip :disabled="scope.row.orderNo.length <= 17" :content="scope.row.orderNo" placement="top">
            <span class="order-no">
              <img v-show="scope.row.isExpedited && ![8, 11].includes(scope.row.status)" class="jiaji" src="@/assets/images/order/badge_ASAP.png">
              {{ handleOrderNumber(scope.row.orderNo) }}
            </span>
          </el-tooltip>
        </template>
        <!-- 创建时间 -->
        <template #createdTime="scope">
          <span>{{ scope.row.createdTime | dateFormatInHtml }}</span>
        </template>
        <!-- 倒计时 -->
        <template #timeCost="scope">
          <count-down 
            :completeTime="scope.row.completeTime" 
            :createdTime="scope.row.createdTime" 
            :stamp="scope.row.deliveryTime" 
            :orderState="scope.row.status"> </count-down>
        </template>
        <!-- 完成时间 -->
        <template #completeTime="scope">
          <span v-if="scope.row.completeTime > 0">{{ scope.row.completeTime | dateFormatInHtml }}</span>
          <span v-else>--</span>
        </template>
        <!-- 订单状态 -->
        <template #status="scope">
          <span class="table-content-status-name">
            {{ $t(`design_tenant_order_status.${scope.row.statusName.en}`) }}
          </span>
        </template>
        <!-- 当前处理人 -->
        <template #designBy="scope">
          <span>{{ scope.row.designBy || $t('orderList.order.unknown') }}</span>
        </template>
        <!-- 设计类型 -->
        <!-- <template #designTypeCodes="scope">
          <span>{{ scope.row.designeName ? $getI18nText(scope.row.designeName) : '' }}</span>
        </template> -->
        <template #designTypeCodes="scope">
          <!-- <span>{{ scope.row.designeName ? $getI18nText(scope.row.designeName) : '' }}</span> -->
          <span>{{scope.row.toothInfoStr || $t('orderList.order.unknownorder')}}</span>
        </template>
        <!-- 设计软件 -->
        <template #designSoftware="scope">
          <span class="order-no">
            <img v-if="scope.row.designSoftwareCode == 1001" src="@/assets/images/order/icon_3Shape.png">
            <img v-else-if="scope.row.designSoftwareCode == 1002" src="@/assets/images/order/icon_exocad.png">
            {{scope.row.designSoft || $t('orderList.order.unknownorder')}}
          </span>
        </template>
        <!-- 订单类型 -->
        <template #designCategory="scope">
          <div :class="{'table-order-type': true, 'is-union': scope.row.isUnion}">
            <span> {{ $getI18nText(scope.row.designCategory) }} </span>
          </div>
        </template>
      </hg-table>
    </div>
    <div class="depart-pagination">
      <pagination 
        showTotal
        :total="page.total" 
        :disabled="tableLoading" 
        :initPageIndex="page.pageIndex" 
        :initPageSize="page.pageSize" 
        @onSearch="searchByPage"></pagination>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import HgTable from '@/components/HgTable';
import CountDown from '@/components/CountDown';
import Pagination from '@/components/Pagination';
import FilterComponent from './components/FilterComponent';
import { ORDER_TYPES, ROUTE_NAME, ROLE_CODE, FILE_TYPES, ROUTE_PATH, UNION_TYPE_CODE } from '@/public/constants';
import { getOrderList } from '@/api/order';
import { getOrderStatus } from '@/api/common';
import { getTypeName, copy } from '@/public/utils';
import { getFileNameNoSuffix, createIFrameDownLoad } from '@/public/utils/file';
import { getDownloadUrl } from '@/api/file';
import { getToothInfo } from '@/public/utils/order';

export default {
  name: 'Unauthorized',
  components: { HgTable, CountDown, Pagination, FilterComponent,  },
  data() {
    return {
      statusList: [],
      tableHeader: [
        {
          prop: 'orderNo',
          width: '180px',
          noTip: true,
          getLabel: () => {
            return this.$t('orderList.order.orderNo');
          },
        },
        {
          prop: 'fileName',
          minWidth: '60px',
          noTip: false,
          getLabel: () => {
            return this.$t('orderList.order.orderfiles');
          },
        },
        {
          prop: 'orgName',
          minWidth: '40px',
          sortable: 'custom',
          getLabel: () => {
            return this.$t('orderList.order.orgName');
          },
        },{
          prop: 'designCategory',
          minWidth: '70px',
          getLabel: () => {
            return this.$t('orderList.order.orderType');
          },
        },
        {
          prop: 'designTypeCodes',
          minWidth: '80px',
          getLabel: () => {
            return this.$t('orderList.order.designTypeCodes');
          },
        },
        {
          prop: 'designSoftware',
          minWidth: '60px',
          noTip: false,
          getLabel: () => {
            return this.$t('order.detail.info.software');
          },
        },
        {
          prop: 'designBy',
          minWidth: '50px',
          sortable: 'custom',
          getLabel: () => {
            return this.$t('orderList.order.designBy');
          },
        },
        {
          prop: 'createdTime',
          minWidth: '70px',
          sortable: 'custom',
          getLabel: () => {
            return this.$t('orderList.searchList.creatTime');
          },
        },
        {
          prop: 'timeCost',
          minWidth: '60px',
          getLabel: () => {
            return this.$t('orderList.order.timeCost');
          },
        },
        // {
        //   prop: 'completeTime',
        //   minWidth: '60px',
        //   sortable: 'custom',
        //   align: 'center',
        //   getLabel: () => {
        //     return this.$t('orderList.searchList.finishTime');
        //   },
        // },
        {
          prop: 'status',
          minWidth: '60px',
          noTip: true,
          sortable: 'custom',
          getLabel: () => {
            return this.$t('orderList.searchList.statusType');
          },
        },
      ],

      // 搜索条件
      searchData: {
        asc: true,  // 升序
        sortField: '', // 排序字段
        keywords: '', // 关键字
        designTypeCodeTree: [], //类型
        createRangeTime: [0, 0],
        designCategoryCode: '', //一级类
      },
      // 分页器
      page: {
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      },
      defaultSort: {},
      dataList:[], 
      tableLoading: false,

      selectDataList: [],
      btnLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'designTypeTree', 'roles', 'oneDesignList', 'userCode',]),
    btnDisabled() {
      if(this.selectDataList.length > 0) {

        //[待退回][已退回]只有管理员、系统运营、设计运营、IQC、OQC还可以下载文件
        const canDonwloadCode = [ROLE_CODE.ADMIN, ROLE_CODE.SYSTEM_OPER, ROLE_CODE.DESIGN_OPERATE, ROLE_CODE.IQC, ROLE_CODE.OQC];

        // 不能操作的订单列表
        const canNotOperateList = this.selectDataList.filter(data => {
          const { status } = data;

          if([ORDER_TYPES.PENDING_RETURN, ORDER_TYPES.RETURNED].includes(status)) { // 如果是待退回、已退回的订单
            return !this.roles.some(roleItem => canDonwloadCode.includes(roleItem.roleCode)); // 当前角色不包含可操作的角色列表
          }else {
            return false;
          }
        });

        if(canNotOperateList.length > 0) {
          return true;
        }
        
        return false;
      }else {
        return true;
      }
    },
  },
  // 进入前
  beforeRouteEnter(to, from, next){
    next(vm => {
      if(from.path === ROUTE_PATH.ORDER_DETAIL) {
        const { searchData, pageData } = vm.$store.state.order.unauthSearchData;
        vm.searchData = searchData || vm.searchData;
        vm.page = pageData || vm.page;
        
        const { sortField, asc } = vm.searchData;
        if(sortField) {
          const sortOrder = asc ? 'ascending' : 'descending' ;
          const sortProp = sortField;
          vm.defaultSort = {
            order: sortOrder,
            prop: sortProp
          }
        }

      }

      vm.init();
      getOrderStatus().then(res => {
        vm.statusList = res.data;
      });
    });
  },
  // 离开前
  beforeRouteLeave(to, from, next) {
    let data = {
      searchData: null,
      pageData: null,
    };
    if(to.path === ROUTE_PATH.ORDER_DETAIL) { // 离开前保存当前的tab和查询条件
      data.searchData = this.searchData;
      data.pageData = this.page;
    }

    this.$store.commit('UPDATE_UNAUTH_SEARCH_DATA', data);
    next();
  },
  // mounted() {
  //   this.init();

  //   getOrderStatus().then(res => {
  //     this.statusList = res.data;
  //   });
  // },
  methods: {
    
    // 条件检索都是从第一页开始
    onSearch() {
      this.page.pageIndex = 1;
      this.init();
    },

    searchByPage(type, page) {
      const { pageIndex, pageSize } = page;
      this.page.pageIndex = pageIndex;
      this.page.pageSize = pageSize;
      this.init();
    },

    init() {

      const staticParam = { // 要传，但不需要变化的字段
        status: 0, // 订单状态 全部查询
        completeStartTime: 0,
        completeEndTime: 0,
        externalReturn: -1,
        internalReturn: -1,
        unauthorized: 1, // 未认证的订单
      };
      const { pageSize, pageIndex } = this.page;
      const { asc, sortField, keywords, designCategoryCode, designTypeCodeTree, createRangeTime, } = this.searchData;
      const bindParam = { asc, sortField, keywords, designCategoryCode, };
      const designTypeCodeItem = this.getDesignTypeCodes(designTypeCodeTree);
      const { designCategoryCode: resultCatgoryCode, designTypeCodes } = designTypeCodeItem;

      const searchCateList = resultCatgoryCode.split(',');
      let searchDesignCategory = resultCatgoryCode;
      let designTypeList = designTypeCodes.split(','); 
      let isUnion = 0;
      const hasUnion = searchCateList.includes(String(UNION_TYPE_CODE));
      if(hasUnion) {
        isUnion = 1;
        const exceptUnion = searchCateList.filter(code => Number(code) !== UNION_TYPE_CODE);
        searchDesignCategory = exceptUnion.join();
        designTypeList = designTypeList.filter(code => Number(code) !== UNION_TYPE_CODE);
      }

      const param = Object.assign({}, bindParam, staticParam,
        { 
          pageNo: pageIndex, 
          pageSize, 
          designTypeCodes: designTypeList.join(),
          startTime: createRangeTime[0],
          endTime: createRangeTime[1],
          isUnion: isUnion,
          designCategoryCode: searchDesignCategory,
        }, );

      this.tableLoading = true;
      getOrderList(param).then(res => {
        let dataList = res.data.data ? res.data.data : [];
        this.page.total = res.data.totalSize || 0;
        this.page.pageIndex = res.data.pageNo || 1;
        this.page.pageSize = res.data.pageSize || 10;
        this.dataList = this.handelStatus(dataList);
      }).finally(() => {
        this.tableLoading = false;
      });
    },

    // 提取里面的三级二级作为designTypeCodes, 一级作为designCategoryCode
    getDesignTypeCodes(treeData) {

      const dataList = copy(treeData);

      let result = {
        designCategoryCode: '',
        designTypeCodes: '',
      };

      let categoryList = dataList.map(item => item[0]);
      let codeList = [];
      dataList.forEach(item => {
        if(item.length > 1) {
          codeList.push(item.pop());
        }
      });

      categoryList = Array.from(new Set(categoryList));
      codeList = Array.from(new Set(codeList));

      result.designCategoryCode = categoryList.join(',');
      result.designTypeCodes = codeList.join(',');
      return result;
    },

    // 处理列表的状态显示
    handelStatus(list) {
      list.forEach((item) => {
        // 当item.statusName为null时
        if (item.statusName) {
          // 根据返回的statusName重新设置status，用来做后面的判断
          let newStatus = this.statusList.find((it) => {
            return item.statusName && (item.statusName.cn == it.cnName || item.statusName.en == it.enName);
          });
          if (newStatus) {
            item.status = Number(newStatus.statusCode);
          }
        }
        // 订单文件拿orderLists fileType=1第一项
        if (item.orderLists) {
          let file = item.orderLists.filter((item) => item.fileType === 1)[0];
          item.fileName = file && file.fileName ? file.fileName : item.fileName;
        }
        // 处理当前的设计软件
        if(item.designSoftwareCode && item.softwareVersion){
          item.designSoft = this.$t(`design_software.${item.designSoftwareCode}`) + '(' + this.$t(`design_software.${item.softwareVersion}`) + ')'
        } else if(item.designSoftwareCode && !item.softwareVersion){
          item.designSoft = this.$t(`design_software.${item.designSoftwareCode}`)
        }

        // 处理当前的设计类型
        item.toothInfoStr = this.getToothDesign(item.toothDesign);
        //处理当前处理人
        item.designBy = this.getHandler(item.status, item);
        item.designeName = getTypeName(item.designTypeCodes, this.oneDesignList);

        const categoryList = item.designCategory.split(',');
        if(categoryList.length < 2) {
          const data = this.designTypeTree.find(dItem => dItem.designCode === Number(item.designCategory));
          if(data) {
            item.designCategory = { zh: data.cnName, en: data.enName };
          }
        }else {
          item.isUnion = 1;
          item.designCategory = { zh: '联合修复', en: 'Combined Restorations' };
        }
      });
      return list;
    },

    // 获取当前处理人
    getHandler(orderState, data) {
      // 待翻译、待退回
      if (orderState === ORDER_TYPES.PENDING_TRANSLATE || orderState === ORDER_TYPES.PENDING_RETURN) {
        return data.iqcUser;
      }
      // 待设计、设计中
      if (orderState === ORDER_TYPES.PENDING_DESIGN || orderState === ORDER_TYPES.DESIGNING) {
        return data.designUser;
      }
      // 待审核
      if (orderState === ORDER_TYPES.PENDING_REVIEW) {
        return data.oqcUser;
      }
      // 待确认、 已完成、已退回
      const clientType = [ORDER_TYPES.PENDING_CONFIRM, ORDER_TYPES.COMPLETED, ORDER_TYPES.RETURNED, ORDER_TYPES.APPLY_FREE];
      if (clientType.indexOf(orderState) > -1) {
        return this.$t('orderList.order.dealWithClient');
      }
    },

    // 获取当前的设计类型
    getToothDesign(toothDesign){
      let toothInfo = toothDesign && JSON.parse(toothDesign);
      toothInfo = getToothInfo(toothInfo) // 这一步是根据选择的通用符转换牙位号
      let toothInfoStr = '';
      toothInfo.forEach((it) => {
        if(it.children && it.children.length > 0){
          it.children.forEach((child) => {
            let name = this.$t(`apiCommon.${child.code}`);
            if(child.toothValue && child.code != 25001){
              toothInfoStr += name + '(' + child.toothValue + '),'
            } else {
              // 未知类型只要有一个就可以了
              if(this.language == 'zh' && toothInfoStr.indexOf('未知') == -1){
                toothInfoStr += name + ','
              } else if(this.language == 'en' && toothInfoStr.indexOf('Unknown') == -1){
                toothInfoStr += name + ','
              }
            }
          })
        }
        if([25001].includes(it.code)){
          let name = this.$t(`apiCommon.${it.code}`);
          // 未知类型只要有一个就可以了
          if(this.language == 'zh' && toothInfoStr.indexOf('未知') == -1){
            toothInfoStr += name + ','
          } else if(this.language == 'en' && toothInfoStr.indexOf('Unknown') == -1){
            toothInfoStr += name + ','
          }
        } else if([25002].includes(it.code)){
          let name = this.$t(`apiCommon.${it.code}`);
          toothInfoStr += name + ','
        }else {
          let name = this.$t(`apiCommon.${it.code}`);
          if(it.toothValue && it.toothValue.length > 0){
            toothInfoStr += name + '(' + it.toothValue + '),'
          } else {
            toothInfoStr += name + ','
          }
        }
      })
      toothInfoStr = toothInfoStr.slice(0, toothInfoStr.length - 1);
      return toothInfoStr
    },


    // 列表排序
    sortTable(row) {
      this.searchData.asc = row.order === 'descending' ? false : true;
      this.searchData.sortField = row.prop ? row.prop : '';
      this.onSearch();
    },

    // 只允许下载，batchDownload有则可以下载
    handleSelect(length, selection) {
      console.log(length, selection);
      this.selectDataList = selection;
    },

    handleBatchDownload() {
      console.log('开始下载');
      this.btnLoading = true;
      let requestList = [];
      this.selectDataList.forEach(item => {
        const { orderLists, orgCode, fileName, orderNo } = item;
        const originFile = orderLists.find(file => file.fileType === FILE_TYPES.ORIGIN_FILE);
        if(originFile) {
          const requestItem = new Promise(resolve => {
            const param = {
              filename: getFileNameNoSuffix(fileName),
              orgCode: orgCode,
              s3FileId: originFile.s3FileId
            };

            getDownloadUrl(param, true).then(res => {
              resolve({url: res.data.url});
            }).catch(err => {
              resolve({orderNumber: orderNo});
            });
          });

          requestList.push(requestItem);
        }
      });

      // 这里下载不会对订单造成状态变更
      Promise.all(requestList).then(resList => {
        const failList = resList.filter(item => item.orderNumber).map(item => item.orderNumber);
        if(failList.length > 0) {
          this.$hgOperateFail(this.$t('file.tips.fileExpireByList',[failList.join('、')]));
        }
        const fileUrlList = resList.filter(item => item.url).map(item => item.url);
        fileUrlList.forEach((url, index) => {
          createIFrameDownLoad(url, index * 500);
        });
      }).finally(() => {
        this.btnLoading = false;
      });
    },

     //长度超过 17 订单号中间显示星号
    handleOrderNumber(orderNumber) {
      const orderLength = orderNumber.length;
      if (orderLength <= 17) {
        return orderNumber;
      } else {
        const start = orderNumber.substr(0, 4);
        const end = orderNumber.substr(orderLength - 4);
        const newNumber = start + '**********' + end;
        return newNumber;
      }
    },
    // 点击行跳转详情
    handleToDetail(row) {
      let orderCode = row.orderCode;
      this.$router.push({ name: ROUTE_NAME.ORDER_DETAIL, query: { id: orderCode } });
    },
  }
}
</script>

<style lang="scss" scoped>
.order-list {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .depart-table {
    flex: 1;
    background: #1D1D1F;
    
    .hg-table {
      height: 100%;
      /deep/.el-table {
        // max-height: 100% !important;
      }
      .order-no{
        display: flex;
        align-items: center;
        img{
          width: 20px;
          height: 20px;
          margin-right: 6px;
        }
      }
    }
    .table-high-light {
      float: left;
      width: auto;
      max-width: calc(100% - 41px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .table-content-status-name {
      display: inline-block;
      min-width: 82px;
      height: 30px;
      line-height: 30px;
      border-radius: 20px;
      background: #BFC6D933;
      color: #BFC6D9;
      font-size: 12px;
      text-align: center;
      padding: 0 9px;
    }

    .is-designing {
      background: #5b87f7;
      color: #ffffff;
    }

    .table-order-type {
      &>span {
        display: inline-block;
        padding: 4px 8px;
      }
    }

    .table-order-type.is-union {
      &>span {
        border-radius: 24px;
        border: 1px solid #9EA2A8;
        background: rgba(61, 64, 71, 0.48);
      }
    }
    
  }
  .depart-pagination {
    z-index: 1;
    position: absolute;
    bottom: 0;
    height: 58px;
    width: 100%;
  }
}
/deep/.el-form {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .el-form-item {
    margin-right: 24px;
  }
}
</style>
