<template>
  <div class="hg-img-checkbox-card">
    <div class="img-checkbox-box">
      <div 
        v-for="(item, index) in data.child" 
        :key="item.name+index"
        :class="{'img-checkbox-card-item': true,'is-active': selectValue === item.name}"  
        @click="handleChange(item.name)">
        <span>{{ getI18nName(item, i18nTitle, $getI18nText) }}</span>
        <hg-pic :iconPath="item.iconPath"></hg-pic>
      </div>
    </div>

    <div class="children-card">
      <div
        v-for="(child, cIndex) in childrenList" 
        :key="child.name+cIndex" 
        :class="{'child-item': true, 'is-active': selectChildValue === child.name}" 
        @click="handleChangeChild(child.name)">
        <span>{{ getI18nName(child, i18nTitle, $getI18nText) }}</span>
        <hg-pic :iconPath="child.iconPath"></hg-pic>
      </div>
    </div>

  </div>
</template>

<script>
import { SELECT_SCHEME_MODEL_MAP } from '../utils/constant';
import HgPic from './HgPic';
import { getI18nName } from '../utils';

export default {
  components: { HgPic },
  inject: ['getTupdateTimes'],
  model: {
    prop: 'value',
    event: 'update',
  },

  props: {
    value: {
      type: [Number,String],
      required: true,
    },
    data: {
      type: Object,
      required: true,
      default(){
        return {
          value: null,
          child: [],
        }
      }
    },
    i18nTitle: { // i18n需要拼接成i18n国际化文本，数据库就不存太长
      type: String,
      default: '',
    },
    parentItem: { // 关联父级对象
      type: Object,
      default() {
        return null;
      }
    },
    eventDisable: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      selectValue: '', // 当前data的value
      selectChildValue: '', // 当前data的value-item 选中的value
      
      oldChildValueList: [], // 数据初始时每个子集的value
    }
  },

  computed: {
    childrenList(){  // 子集组合

      const { child } = this.data;
      if(!child || child.length === 0) {
        return [];
      }
      
      const selectChildIndex = child.findIndex(item => item.name === this.selectValue);
      if(selectChildIndex > -1) {
        return child[selectChildIndex].child;
      }

      return [];
    },
    updateTimes() { // 保存更新的次数
      return this.getTupdateTimes ? this.getTupdateTimes() : 0;
    }
  },

  watch: {
    parentItem: {  // 关联父级value发生变化
      deep: true,
      handler(item) {
        const parentValue = SELECT_SCHEME_MODEL_MAP[item.value];
        if(parentValue) {
          // 把旧的child-value更新回默认值
          const oldChild = this.oldChildValueList.find(item => item.parent === this.selectValue);
          if(oldChild) {
            this.updateChildItemValue(this.selectValue, oldChild.value);
          }

          this.selectValue = parentValue;
          this.$emit('update', parentValue);
        }
      }
    },

    selectValue(value) { // value发生变化
      const oldChild = this.oldChildValueList.find(item => item.parent === value);
      if(oldChild) {
        this.updateChildItemValue(value, oldChild.value);
        this.selectChildValue = oldChild.value;
      }
    },

    updateTimes(value) {
      if(value> 0) {
        this.initOldValue();
      }
    }
  },

  mounted() {
    // 初始化value 和 子集value
    if(this.data && this.data.child && this.data.child.length > 0) {
      this.initOldValue();
    }
  },
  
  methods: {
    getI18nName,
    // value变化事件
    handleChange(value) {
      if(this.eventDisable) return;

      if(this.parentItem) return; //有父级的组件由父级值做决定

      if(this.selectValue !== value) {

        // 把旧的child-value更新回默认值
        const oldChild = this.oldChildValueList.find(item => item.parent === this.selectValue);
        if(oldChild) {
          this.updateChildItemValue(this.selectValue, oldChild.value);
        }

        this.selectValue = value;
        this.$emit('update', value);
      }
    },

    // 子集value变化事件
    handleChangeChild(value) {
      if(this.eventDisable) return;
      
      if(this.selectChildValue !== value) {
        this.selectChildValue = value;
        this.updateChildItemValue(this.selectValue, value);
        
      }
    },

    // 更新selectChildValue的值
    setSelectChildValue(parentValue) {
      const selectChildIndex = this.data.child.findIndex(item => item.name === parentValue);
      if(selectChildIndex > -1) {
        const childItem = this.data.child[selectChildIndex];
        this.selectChildValue = childItem.value;
      }
    },

    // 更新child的value
    updateChildItemValue(parentValue, childValue){
      const selectChildIndex = this.data.child.findIndex(item => item.name === parentValue);
      if(selectChildIndex > -1) {
        const childItem = this.data.child[selectChildIndex];
        childItem.value = childValue;
      }
    },

    // 加载旧值
    initOldValue() {
      this.oldChildValueList = [];
      const { value, child } = this.data;
      this.selectValue = value;
      this.setSelectChildValue(value);

      child.forEach(item => {
        this.oldChildValueList.push({
          parent: item.name,
          value: item.value
        });
      });
    },

  }
}
</script>

<style lang="scss" scoped>
.hg-img-checkbox-card {
  position: relative;
  display: flex;
  flex-direction: column;
  font-size: 12px;

  .img-checkbox-box {
    display: flex;
    flex-wrap: wrap;
  }

  .img-checkbox-card-item {
    .hg-pic {
      cursor: pointer;
      margin-top: 4px;
    }
  }

  .is-active {
    color: $hg-secondary-primary;

    .hg-pic {
      border: 2px solid $hg-main-blue;
      /deep/.hg-pic-check {
        display: inline-block;
        border-color: $hg-main-blue $hg-main-blue transparent transparent;
  
        .el-icon-check {
          color:$hg-label;
        }
      }
    }
  }

  .children-card {
    display: flex;
    flex-wrap: wrap;
    margin-top: 24px;
    padding: 0 24px 24px 24px;
    background: $hg-hover;
    border-radius: 2px;

    .child-item {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      margin: 24px 24px 0 0;
      width: 176px;

      span {
        flex: 1;
        display: flex;
        flex-direction: column-reverse;
        line-height: 16px;
      }

      .hg-pic {
        margin-top: 4px;
      }
    }
    
  }
}
</style>