<template>
  <Popup :show="show" :popup-title="popupTitle" :is-use-ele="true" :loading="loading" @cancel="cancel" @submit="submitForm">
    <div slot="popupContent" class="add-customer-box custom-form">
      <el-form ref="addCustomerRuleForm" :model="newCustomerObj" :rules="rules">
        <!-- 公司名称 -->
        <el-form-item :label="$t('customer.companyName')" prop="orgName" class="add-customer-label">
          <hg-input v-model="newCustomerObj.orgName" :placeholder="$t('customer.companyNamePlaceholder')" />
          <CRMList :searchName="newCustomerObj.orgName" @update="setBasicInfo">
            <el-button class="hg-button" :disabled="!(newCustomerObj.orgName && newCustomerObj.orgName.replace(/^\s+/,''))" slot="reference">{{ $t('customer.searchCRM') }}</el-button>
          </CRMList>
        </el-form-item>
        <!-- 编码 -->
        <el-form-item :label="$t('customer.orgSn')" prop="orgSn" class="add-customer-label">
          <el-input v-model="newCustomerObj.orgSn" :disabled="disableCode" type="text" :placeholder="$t('customer.orgSnPlaceholder')" />
        </el-form-item>
        <!-- 时区 -->
        <el-form-item :label="$t('timezone.timezone')" prop="tzCode" class="add-customer-label">
          <div class="input-box"><Select :select-options="timezoneList" :value="newCustomerObj.tzCode" @change="changeTimezone" /></div>
        </el-form-item>
        <!-- 区域 -->
        <el-form-item :label="$t('customer.area')" prop="areaCode" class="add-customer-label">
          <div class="input-box">
            <el-cascader style="width: 100%;" v-model="newCustomerObj.areaCode" :props="{label: 'label', value: 'areaCode'}" :options="areaList" @change="changeArea"></el-cascader>
          </div>
        </el-form-item>
        <!-- 助记码 -->
        <el-form-item :label="$t('customer.memorySn')" class="add-customer-label">
          <el-input v-model="newCustomerObj.memorySn" :disabled="disableCode" type="text" :placeholder="$t('customer.memoryTips')" />
        </el-form-item>
        <!-- S3节点配置 -->
        <!-- <el-form-item :label="$t('customer.s3Setting')" prop="bucketCode" class="add-customer-label">
          <div class="input-box"><Select :select-options="bucketList" :value="newCustomerObj.bucketCode" @change="changeBucket" /></div>
        </el-form-item> -->
        <!-- 客户级别 -->
        <el-form-item :label="$t('customer.customLevel')" prop="customLevel" class="add-customer-label">
          <div class="input-box"><Select :select-options="customLevelList" :value="newCustomerObj.customLevel" @change="changeCustomLevel" /></div>
        </el-form-item>
        <!-- 客户类型 -->
        <el-form-item :label="$t('customer.customType')" prop="customType" class="add-customer-label">
          <div class="input-box"><Select :select-options="customTypeList" :value="newCustomerObj.customType" @change="changeCustomType" /></div>
        </el-form-item>
        <!-- 公司地址 -->
        <el-form-item :label="$t('customer.companyAddress')" class="add-customer-label">
          <el-input v-model="newCustomerObj.orgAddress" type="text" :placeholder="$t('customer.addressPlaceholder')" />
        </el-form-item>
        <!-- 负责人 -->
        <el-form-item :label="$t('customer.leader')" class="add-customer-label">
          <el-input v-model="newCustomerObj.orgLeader" type="text" :placeholder="$t('customer.leaderPlaceholder')" />
        </el-form-item>
        <!-- 邮箱 -->
        <el-form-item :label="$t('customer.email')" prop="email" class="add-customer-label">
          <el-input v-model="newCustomerObj.email" type="text" :placeholder="$t('customer.emailPlaceholder')" />
        </el-form-item>
        <!-- 手机号码 -->
        <el-form-item :label="$t('customer.phone')" prop="mobile" class="add-customer-label">
          <div class="area-code">
            <Select :select-options="countryListArrayComputed" :value="newCustomerObj.mobilePrefix" :placeholder="$t('customer.areaCode')" @change="changeAreaCode" />
          </div>
          <el-input v-model="newCustomerObj.mobile" type="text" :placeholder="$t('customer.phonePlaceholder')" />
        </el-form-item>
        <!-- 币种 -->
        <el-form-item :label="$t('customer.currencyType')" prop="settlementCurrency" class="add-customer-label">
          <div class="input-box"><Select :select-options="currencyList" :value="newCustomerObj.settlementCurrency" @change="changeCurrencyType" /></div>
        </el-form-item>
        <!-- 结算方式 -->
        <el-form-item :label="$t('customer.settleTypes')" class="add-customer-label">
          <el-radio v-model="newCustomerObj.settlementType" :label="0" @change="changeSttleTypes">{{ $t('customer.settleByMonth') }}</el-radio>
          <el-radio v-model="newCustomerObj.settlementType" disabled :label="1" @change="changeSttleTypes">{{ $t('customer.settleByDeposit') }}</el-radio>
        </el-form-item>
        <!-- 信用值 -->
        <el-form-item :label="$t('customer.creditValue')" prop="creditScore" class="add-customer-label">
          <div class="input-box"><el-input-number v-model="newCustomerObj.creditScore" :min="0" controls-position="right" /></div>
        </el-form-item>
        <!-- 折扣率 -->
        <el-form-item :label="$t('customer.discountRate')" prop="discountRate" class="add-customer-label">
          <el-input v-model="newCustomerObj.discountRate" type="number">
            <span slot="suffix">% off</span>
          </el-input>
        </el-form-item>
        <!-- 关联业务人员 -->
        <el-form-item :label="$t('customer.saleman')" class="add-customer-label">
          <div class="input-box">
            <Select :placeholder="$t('customer.salemanPlaceholder')" :select-options="businessUserList" :value="newCustomerObj.businessUserCodes" :is-multiple="true" @focus="showBusinessUserList" @change="changeBusinessUser" />
          </div>
        </el-form-item>
        <!-- <el-form-item :label="$t('customer.password')" prop="password" class="add-customer-label">
          <el-input type="text" :placeholder="$t('customer.passwordPlaceholder')" v-model="newCustomerObj.password"></el-input>
        </el-form-item> -->
      </el-form>
    </div>
  </Popup>
</template>

<script>
import Popup from '@/components/func-components/Popup'
import Select from '@/components/func-components/Select'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { refreshLabel } from '@/assets/script/refreshLabel.js'
import { getTimezoneList, getBucketCodeList, getBusinessUserList, getAreaList } from '@/api/common'
import { getAllCurrency } from '@/api/customer'
import { myTimeFormat } from '@/assets/script/formatDate.js'
import { getStore } from '@/assets/script/storage.js'
import CRMList from './CRMList';
import HgInput from '@/components/func-components/HgInput'

export default {
  name: 'AddCustomer',
  components: {
    Popup,
    Select,
    CRMList,
    HgInput
  },
  props: {
    popupTitle: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    areaCodeArr: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    var checkMobile = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.PHONE_RULE.test(value)) {
          return callback(new Error(this.$t('customer.phoneErro')))
        }
      }
      callback()
    }
    var checkEmail = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.EMAIL_RULE.test(value)) {
          return callback(new Error(this.$t('customer.emailErro')))
        }
      }
      callback()
    }
    // var checkPassword = (rule, value, callback) => {
    //   if (value !== '' && !COMMON_CONSTANTS.PASSWORD_RULE.test(value)) {
    //     return callback(new Error(this.$t('customer.passwordErro')))
    //   } else {
    //     callback()
    //   }
    // }
    var checkOrgSn = (rule, value, callback) => {
      if (value !== '' && !COMMON_CONSTANTS.CUSTOMER_CODE_RULE.test(value)) {
        return callback(new Error(this.$t('customer.orgSnErro')))
      } else {
        callback()
      }
    }
    var checkDiscountRate = (rule, value, callback) => {
      const reg = /^[0-9]*[1-9]*$/
      value = Number(value)
      if (value !== '' && (!reg.test(value) || value > 100 || value < 0)) {
        return callback(new Error(this.$t('customer.discountRateErro')))
      } else {
        callback()
      }
    }
    return {
      newCustomerObj: {
        orgName: '',
        orgSn: '', // 客户编码
        customLevel: 0, // 默认为普通等级
        customType: 0, // 默认为非直营客户
        orgAddress: '',
        orgLeader: '',
        email: '',
        mobile: '',
        mobilePrefix: '+86', // 区号
        // password: '',
        tzCode: getStore('userInfo').timezone.tzCode,
        // bucketCode: 3000, // S3节点区域，默认为阿里云oss的bucket
        areaCode: '',
        settlementCurrency: '', // 币种
        settlementType: 0, // 0是月结，1是充值
        discountRate: 0, // 折扣率
        creditScore: 1000000, // 信用值
        businessUserCodes: [], // 关联业务人员，多选
        memorySn: ''
      },
      rules: {
        orgName: [
          { required: true, message: this.$t('customer.companyNamePlaceholder') }
        ],
        orgSn: [
          { required: true, message: this.$t('customer.orgSnPlaceholder') },
          { validator: checkOrgSn, trigger: 'blur' }
        ],
        tzCode: [
          { required: true, message: this.$t('timezone.timezoneErr') }
        ],
        areaCode: [
          { required: true, message: this.$t('customer.selectArea') }
        ],
        // bucketCode: [
        //   { required: true, message: this.$t('customer.s3Placeholder') }
        // ],
        email: [
          { required: true, message: this.$t('customer.emailPlaceholder') },
          { validator: checkEmail, trigger: 'blur' }
        ],
        mobile: [
          { validator: checkMobile, trigger: 'blur' }
        ],
        discountRate: [
          { validator: checkDiscountRate, trigger: 'blur' }
        ],
        settlementCurrency: [
          { required: true, message: this.$t('customer.currencyTypePlaceholder') }
        ],
        creditScore: [
          { required: true, message: this.$t('customer.creditPlaceholder') }
        ]
        // password: [
        //   { required: true, message: this.$t('customer.passwordPlaceholder'), trigger: 'blur' },
        //   { validator: checkPassword, trigger: 'blur' }
        // ],
      },
      // 客户等级列表
      customLevelList: [
        {
          value: 0,
          label: this.$t('customer.normal')
        },
        {
          value: 1,
          label: 'VIP'
        },
        {
          value: 2,
          label: 'VVIP'
        }
      ],
      // 客户类型列表
      customTypeList: [
        {
          value: 0,
          label: this.$t('customer.undirectSales')
        },
        {
          value: 1,
          label: this.$t('customer.directSales')
        },
        {
          value: 2,
          label: this.$t('customer.testUser')
        },
        {
          value: 3,
          label: this.$t('customer.disaster')
        },
        {
          value: 4,
          label: this.$t('customer.agencyuser')
        }
      ],
      // 时区列表
      timezoneList: [],
      areaList: [],
      // oss区域列表
      bucketList: [],
      businessUserList: [],
      // 币种列表
      currencyList: [],
      loading: false,
      disableCode: false
    }
  },
  computed: {
    countryListArrayComputed() { // 根据目前的中英文状态返回相对应的中英文区号
      const countryListArrayNew = []
      this.areaCodeArr.forEach((item) => {
        if (this.$i18n.locale === 'zh') {
          item.label = item.countryName + ' +' + item.mobilePrefix
        } else {
          item.label = item.countryEn + ' +' + item.mobilePrefix
        }
        item.value = item.mobilePrefix
        countryListArrayNew.push(item)
      })
      return countryListArrayNew
    }
  },
  watch: {
    show(val) {
      if (val) {
        refreshLabel('add-customer-label')
        // this.getBucketCodeListFunc()
        Promise.allSettled([this.getTimezoneListFunc(), this.getAreaList(), this.getAllCurrencyFunc(), this.getBusinessUserListFunc()])
      } else {
        this.resetForm('addCustomerRuleForm')
        this.disableCode = false
      }
    }
  },
  mounted() {
    // this.$on('isLoading', () => {
    //   this.loading = false
    // })
  },
  methods: {
    cancel() {
      this.loading = false
      this.$emit('update:show', false)
    },
    setBasicInfo(info) {
      this.newCustomerObj.orgSn = info.orgSn;
      this.newCustomerObj.orgName = info.orgName;
      this.newCustomerObj.orgAddress = info.orgAddress;
      this.newCustomerObj.memorySn = info.memorySn;
      this.disableCode = true;
    },
    submitForm() {
      this.$refs['addCustomerRuleForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.newCustomerObj.creditScore > 1000000) {
            this.newCustomerObj.creditScore = 1000000
          }
          let data = Object.assign({}, this.newCustomerObj);
          if(this.disableCode) {
            data.isBackupCrmOrg = 1;
          }
          let newUserObj = {
            ...data,
            areaCode: data.areaCode.join(',')
          }
          this.$emit('submit', newUserObj)
        } else {
          this.loading = false
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.newCustomerObj.orgAddress = ''
      this.newCustomerObj.orgLeader = ''
      this.newCustomerObj.creditScore = 1000000 // 信用值
      this.newCustomerObj.settlementType = 0 // 结算方式
      this.newCustomerObj.businessUserCodes = [] // 选中的业务人员
      this.newCustomerObj.memorySn = ''
    },
    // 获取币种列表
    getAllCurrencyFunc() {
      getAllCurrency().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.currencyList = res.data
            this.currencyList.forEach((item) => {
              item.label = item.currency
              item.lable_en = item.currencyEn
              item.value = item.settlementCurrency
            })
          }
        }
      })
    },
    // 获取业务人员列表
    getBusinessUserListFunc() {
      return new Promise((resolve) => {
        getBusinessUserList().then((res) => {
          if (res.code === 200) {
            if (res.data != null && res.data.length) {
              this.businessUserList = res.data
              this.businessUserList.forEach((item) => {
                item.label = item.realName
                item.value = item.userCode
              })
            }
          }
        })
      })
    },
    // 获取时区信息列表
    getTimezoneListFunc() {
      getTimezoneList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.timezoneList = res.data
            this.timezoneList.forEach((item) => {
              /* let utc = myTimeFormat(Math.abs(item.utc * 60 * 1000), ':')
              utc = item.utc < 0 ? `-${utc}` : `+${utc}`
              item.label = this.$t(`timezone.${item.countryCode}`, { utc: utc }) */
              item.label = this.$i18n.locale === 'zh' ? item.tzNameCn : item.tzNameEn;
              item.value = item.tzCode
            })
          }
        }
      })
    },
    // 获取区域列表
    async getAreaList(){
      const loop = (arr) => {
        arr.forEach((item) => {
          item.label =  this.$i18n.locale === 'zh' ? item.nameCn : item.name;
          if(item.children){
            loop(item.children)
          }
        })
        return arr
      }
      const { code, data } = await getAreaList();
      if(code == 200){
        this.areaList = loop(data);
        console.log(this.areaList)
      }
    },
    // 选择时区
    changeTimezone(value) {
      this.newCustomerObj.tzCode = value
    },
    // 选择区域
    changeArea(value){
      this.newCustomerObj.areaCode = value
    },
    // 获取S3节点配置列表
    getBucketCodeListFunc() {
      getBucketCodeList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.bucketList = res.data
            this.bucketList.forEach((item) => {
              item.label = item.aliasCn
              item.lable_en = item.aliasEn
              item.value = item.bucketCode
            })
          }
        }
      })
    },
    // 选择S3节点配置
    changeBucket(value) {
      this.newCustomerObj.bucketCode = value
    },
    // 选择客户等级
    changeCustomLevel(value) {
      this.newCustomerObj.customLevel = value
    },
    // 选择客户类型
    changeCustomType(value) {
      this.newCustomerObj.customType = value
    },
    // 选择区号
    changeAreaCode(value) {
      // select获取到的值是value，但是显示的是label，所以将显示的值变成数字类型，就能只获取到区号，然后在区号前面加上'+'号
      this.newCustomerObj.mobilePrefix = '+' + Number(value)
    },
    // 选择币种
    changeCurrencyType(value) {
      this.newCustomerObj.settlementCurrency = value
    },
    // 选择结算方式
    changeSttleTypes() {
    },
    // 打开关联业务人员的下拉框
    showBusinessUserList() {
      // this.getBusinessUserListFunc()
    },
    // 选择关联业务人员
    changeBusinessUser(value) {
      this.newCustomerObj.businessUserCodes = value
    }
  }
}
</script>

<style lang="scss" scoped>
.add-customer-box {
  .input-box {
    width: 320px;
    ::v-deep .el-input-number {
      width: 100%;
      .el-input__inner {
        text-align: left;
      }
      .el-input-number__decrease, .el-input-number__increase {
        background: transparent;
        border-color: $hg-border-color;
      }
    }
  }
  .area-code {
    width: 96px;
    margin-right: 12px;
  }
}
</style>
<style lang="scss">
.custom-form .el-form .el-form-item .el-form-item__content .el-input .el-input__suffix{
  right: 10px;
}
</style>
