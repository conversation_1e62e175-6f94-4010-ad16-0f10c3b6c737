<template>
  <Popup :show="show" :popup-title="popupTitle" :is-use-ele="true" :loading="loading" @cancel="cancel" @submit="submitForm('addOrgRuleForm')">
    <div slot="popupContent" class="add-org-box custom-form">
      <el-form ref="addOrgRuleForm" :model="orgObj" :rules="rules">
        <el-form-item prop="orgName" class="add-org-label">
          <template slot="label">
            <span :title="$t('org.departmentName')">{{ $t('org.departmentName') }}</span>
          </template>
          <el-input v-model="orgObj.orgName" type="text" :placeholder="$t('org.deptNamePlaceholder')" :title="orgObj.orgName ? '' : $t('org.deptNamePlaceholder')" />
        </el-form-item>
        <el-form-item class="add-org-label">
          <template slot="label">
            <span :title="$t('org.parentDept')">{{ $t('org.parentDept') }}</span>
          </template>
          <el-input v-model="orgObj.parentName" type="text" :disabled="true" />
        </el-form-item>
      </el-form>
    </div>
  </Popup>
</template>

<script>
import Popup from '@/components/func-components/Popup'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { refreshLabel } from '@/assets/script/refreshLabel.js'

export default {
  name: 'AddOrg',
  components: {
    Popup
  },
  props: {
    popupTitle: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    parentOrg: {
      type: Object,
      default: {}
    }
  },
  data() {
    var checkDeptName = (rule, value, callback) => {
      if (value !== '') {
        if (value.trim().length === 0) {
          return callback(new Error(this.$t('org.deptNamePlaceholder')))
        }
        if (!COMMON_CONSTANTS.NAME_RULE.test(value)) {
          return callback(new Error(this.$t('org.deptNameErro')))
        }
      }
      callback()
    }
    return {
      orgObj: {
        children: [],
        isClient: '',
        isDept: 1,
        isLeaf: 1,
        depth: '',
        orgCode: '',
        orgName: '',
        parentName: '',
        parentCode: '',
        parentCodes: '',
        sortNo: '',
        status: 0,
        tenantCode: ''
      },
      rules: {
        orgName: [
          { required: true, message: this.$t('org.deptNamePlaceholder') },
          { validator: checkDeptName, trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  watch: {
    show(val) {
      if (val) {
        refreshLabel('add-org-label')
      } else {
        this.reset()
      }
    },
    parentOrg: {
      handler(newVal, oldVal) {
        if (Object.keys(newVal).length) {
          this.orgObj.parentName = newVal.data.orgName
          this.orgObj.parentCode = newVal.data.orgCode
          this.orgObj.depth = newVal.data.depth + 1
          this.orgObj.isClient = newVal.data.isClient
          this.orgObj.parentCodes = newVal.data.parentCodes ? newVal.data.parentCodes + `${newVal.data.orgCode}` : `${newVal.data.orgCode}`
          this.orgObj.sortNo = newVal.data.sortNo
          this.orgObj.tenantCode = newVal.data.tenantCode
        }
      },
      deep: true
    }
  },
  mounted() {
  },
  methods: {
    reset() {
      this.orgObj = {
        children: [],
        isClient: '',
        isDept: 1,
        isLeaf: 1,
        depth: '',
        orgCode: '',
        orgName: '',
        parentName: '',
        parentCode: '',
        parentCodes: '',
        sortNo: '',
        status: 0,
        tenantCode: ''
      }
      this.$emit('update:parentOrg', {})
      this.resetForm('addOrgRuleForm')
    },
    cancel() {
      this.loading = false
      this.$emit('update:show', false)
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$emit('submit', this.orgObj)
        } else {
          this.loading = false
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
