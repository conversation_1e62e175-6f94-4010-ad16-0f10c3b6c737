const plugins = ['@vue/babel-plugin-transform-vue-jsx']
// 生产环境移除console
const isDev = process.env.VUE_APP_ENVIRONMENT === 'dev' || process.env.NODE_ENV === 'local';
if(!isDev) {
  plugins.push('transform-remove-console');
}

module.exports = {
  presets: [
    '@vue/cli-plugin-babel/preset',
    ['@babel/preset-env', { 'modules': false }]
  ],
  plugins: [
    ...plugins,
    [
      'component',
      {
        libraryName: 'element-ui',
        styleLibraryName: 'theme-chalk'
      }
    ]
  ]
};
