import { getAllDesignTypes, getDesignSkuAll } from '@/api/common';
import { copy } from '@/public/utils';

const state = {
  authList: [],
  roles: [],
  username: '',
  userId: 0,
  userCode: 0,
  orgCode: 0,
  menus: [],
  designTypeTree: [], // 返回全部设计类型
  oneDesignList: [],  // 一维设计类型数组
  aiFunctionsList: [],
  rootToLeafList: {},
  rootToLeafCodes: {}, // 一级类code和三级类code的关系
  leafToRootCodes: {},

  designTypeSkuTree: [], //返回全部设计类型包含skucode的
  oneDesignSkuList: [],  // 一维设计类型包含skucode的数组
};

const mutations = {
  // 初始化用户信息
  INIT_USER_INFO: (state, userInfo) => {
    state.username = userInfo.realName;
    state.userId = userInfo.userId;
    state.userCode = userInfo.userCode;
    state.orgCode = userInfo.orgCode;
  },
  // 初始化用户角色
  INIT_USER_ROLE: (state, roles) => {
    state.roles = roles;
  },
  // 初始化权限列表
  INIT_AUTH_LIST: (state, authList) => {
    state.authList = authList;
  },
  // 初始化菜单列表
  INIT_MENU_LIST: (state, menuList) => {
    state.menus = menuList;
  },
  // 加载树结构的设计类型
  INIT_DESIGN_TYPE_TREE: (state, designTree) => {
    state.designTypeTree = designTree;
  },
  // 加载所有级别的设计类型
  INIT_ONE_DIMENSIONAL_DESIGN_TYPE: (state, designList) => {
    state.oneDesignList = designList;
  },
  // 初始化ai工具的functions
  INIT_AI_FUNCTIONS_LIST: (state, aiFunctionsList) => {
    state.aiFunctionsList = aiFunctionsList;
  },
  INIT_ROOT_TO_LEAF: (state, {list, codes, leafCodes}) => {
    state.rootToLeafList = list;
    state.rootToLeafCodes = codes;
    state.leafToRootCodes = leafCodes;
  },

  // 加载树结构的设计类型（含sku）
  INIT_DESIGN_TYPE_SKU_TREE: (state, designSkuTree) => {
    state.designTypeSkuTree = designSkuTree;
  },
  // 加载所有级别的设计类型（含sku）
  INIT_ONE_DIMENSIONAL_DESIGN_SKU_TYPE: (state, designTreeList) => {
    state.oneDesignSkuList = designTreeList;
  },
};

const actions = {
  initDesignTypeTree: ({commit}) => {
    getAllDesignTypes().then(res => {
      if(res.code === 200) {
        const designTree = res.data || [];
        const designList = getOneDimensional(designTree);
        const rootToLeafList = getRootToLeaf(designTree);
        let rootToLeafCodes = {};
        let leafToRootCodes = {};
        rootToLeafList.forEach(item => {
          const { designCode, leafList } = item;
          const leafCodes = leafList.map(item => item.designCode);
          rootToLeafCodes[designCode] = leafCodes;
          
          leafCodes.forEach(item => { leafToRootCodes[item] = designCode;});
        });

        commit('INIT_DESIGN_TYPE_TREE', designTree);
        commit('INIT_ONE_DIMENSIONAL_DESIGN_TYPE', designList);
        commit('INIT_ROOT_TO_LEAF', {list: rootToLeafList, codes: rootToLeafCodes, leafCodes: leafToRootCodes});
      }
    });
  },

  // 设计类型含sku
  initDesignTypeSkuTree: ({commit}) => {
    getDesignSkuAll().then(res => {
      if(res.code === 200) {
        const loop = function(arr){
          arr.forEach((item) => {
            if(!item.skuCode){
              item.skuCode = item.designCode;
            }
            if(item.children && item.children.length > 0){
              loop(item.children)
            }
          })
          return arr
        }
        let designSkuTree = res.data || [];
        // 这里做一步将所有空缺sku的都填入designcode
        designSkuTree = loop(designSkuTree)
        const designSkuList = getSkuOneDimensional(designSkuTree);
        commit('INIT_DESIGN_TYPE_SKU_TREE', designSkuTree);
        commit('INIT_ONE_DIMENSIONAL_DESIGN_SKU_TYPE', designSkuList);
      }
    });
  },
  updateDesignStatus: ({commit}, isDesign) => {
    commit('UPDATE_DESIGN_STATUS', isDesign);
  },

};

const getOneDimensional = (dataList) => {
  let designList = [];

  dataList.forEach(node => {
    const { cnName, enName, iconUrl, designCode, children, level, parentCode, hasParas } = node;
    const item = { cnName, enName, iconUrl, designCode, level, parentCode, hasParas };
    designList.push(item);
    if(children && children.length > 0) {
      designList = designList.concat(getOneDimensional(children));
    }
  });
  return designList;
};

// 获取sku设计类型一维数组
const getSkuOneDimensional = (dataList) => {
  let designList = [];

  dataList.forEach(node => {
    const { zhName, enName, designCode, children, skuCode } = node;
    const item = { zhName, enName, designCode, skuCode };
    designList.push(item);
    if(children && children.length > 0) {
      designList = designList.concat(getSkuOneDimensional(children));
    }
  });
  return designList;
};

const getRootToLeaf = (dataList) => {
  let resultList = [];
  dataList.forEach(node => {
    const { cnName, enName, iconUrl, designCode, children, level, parentCode, hasParas } = node;
    if(designCode !== 25001) {
      let result = { cnName, enName, iconUrl, designCode, level, parentCode, hasParas, leafList: [] };
      result.leafList = children.reduce((list, curItem) => {
        const curChild = copy(curItem.children) || [];
        list = list.concat(curChild);
        return list;
      },[]);
      resultList.push(result);
      }
  });
  return resultList;
};

export default {
  state,
  actions,
  mutations
}