<template>
  <div>
    <el-drawer
      :title="$t('customer.crmPrice')"
      custom-class="crm-drawer"
      :visible.sync="crmDrawer"
    >
      <div class="table-list">
        <!-- 列表 -->
        <div class="depart-table" v-if="!isShowAsync">
          <new-table class="user-table" :hasIndex="true" :loading="tableLoading" :data="newMountedList" :header-data="headerData">
            <template #designName="{ row }">
              <span>{{row.designName}}</span>
            </template>
            <template #erpSkuCode="{ row }">
              <span>{{row.erpSkuCode}}</span>
            </template>
            <template #price="{ row }">
              <span>{{row.price}}</span>
            </template>
          </new-table>
          <div class="depart-pagination">
            <Pagination :total="page.total" :page-size="page.pageSize"
            :pageSizes="[10, 20, 50, 100]" :page-no="page.pageNo" @changePageSize="changePageSize" @changePageNo="changePageNo" />
          </div>
        </div>
        <!-- 同步后的结果展示 -->
        <div class="depart-table" v-else>
          <new-table class="user-table" :hasIndex="true" :loading="asyncCrmLoading" :data="newCrmList" :header-data="asynsCrmHeader">
            <template #designName="{ row }">
              <span>{{row.designName}}</span>
            </template>
            <template #erpSkuCode="{ row }">
              <span>{{row.erpSkuCode}}</span>
            </template>
            <template #price="{ row }">
              <span>{{row.price}}</span>
            </template>
            <template #result="scope">
              <span v-if="scope.row.result === '成功'">{{$t('customer.success')}}</span>
              <span class="error-tips" v-else>{{$t('customer.fail')}}</span>
            </template>
          </new-table>
          <div class="depart-pagination">
            <Pagination :total="crmpage.total" :page-size="crmpage.pageSize"
            :pageSizes="[10, 20, 50, 100]" :page-no="crmpage.pageNo" @changePageSize="changeCrmPageSize" @changePageNo="changeCrmPageNo" />
          </div>
        </div>
        <!-- 按钮组 -->
        <div class="btn-list">
          <el-button v-if="!isShowAsync" style="background: transparent;" plain @click="crmDrawer = false">{{$t('customer.cancle')}}</el-button>
          <el-button v-if="!isShowAsync" type="primary" @click="submitPoint">{{$t('customer.asycBtn')}}</el-button>
          <el-button v-if="isShowAsync" type="primary" @click="knowPrice">{{$t('customer.konw')}}</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import newTable from '@/components/func-components/newTable.vue'
import Pagination from '@/components/func-components/Pagination'
import { getCrmList, asyncCrmInTable } from '@/api/customer'
import { getStore } from '@/assets/script/utils.js'
export default {
  name: "leftDrawer",
  components: {
    newTable,
    Pagination
  },
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShowAsync: false,
      page: {
        pageSize: 20,
        pageNo: 1,
        total: 0,
      },
      crmpage: {
        pageSize: 20,
        pageNo: 1,
        total: 0,
      },
      tableLoading: false,
      asyncCrmLoading: false,
      importTableList: [],
      newMountedList: [],
      asyncCrmList: [],
      newCrmList: [],
      headerData: [
        {
          prop: 'designName',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.designApplication');
          },
        },
        {
          prop: 'erpSkuCode',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.artNo');
          },
        },
        {
          prop: 'price',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.unitPrice') + this.getCurrency(this.newMountedList[0]);
          },
        },
      ],
      asynsCrmHeader: [
        {
          prop: 'designName',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.designApplication');
          },
        },
        {
          prop: 'erpSkuCode',
          minWidth: '30%',
          getLabel: () => {
            return this.$t('customer.artNo');
          },
        },
        {
          prop: 'price',
          minWidth: '20%',
          getLabel: () => {
            return this.$t('customer.unitPrice') + this.getCurrency(this.newMountedList[0]);
          },
        },
        {
          prop: 'result',
          minWidth: '20%',
          getLabel: () => {
            return this.$t('customer.importedStatus');
          },
        },
      ],
    };
  },
  computed: {
    crmDrawer: {
      get() {
        return this.drawer;
      },
      set(val) {
        this.$emit("update:drawer", val);
      },
    },
    language() {
      return getStore('lang')
    }
  },
  watch: {
    crmDrawer(newValue, oldValue) {
      if(!newValue){
        this.$emit('submitPrice');
        this.isShowAsync = false;
        this.newMountedList = [];
        this.newCrmList = []
      } else {
        this.getCrmList()
      }
    }
  },
  mounted () {

  },
  methods: {
    // 获取crm列表
    async getCrmList(){
      try {
        this.tableLoading = true;
        const customerCode = this.$route.query.orgCode
        const { code, data } = await getCrmList({orgCode: customerCode});
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.page.total = data.length;
        this.importTableList = data
        this.newMountedList = this.importTableList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize);
        this.tableLoading = false;
      } catch (error) {
        if(error.code == '60010045'){
          this.$message.error(this.$t('customer.obtainPrice'))
          this.tableLoading = false;
          this.crmDrawer = false;
        }
      }
    },

    changePageSize(val) {
      this.page.pageSize = val
      this.page.pageNo = 1
      this.newMountedList = this.asyncCrmList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize)
    },
    changeCrmPageSize(val) {
      this.page.pageSize = val
      this.page.pageNo = 1
      this.newCrmList = this.importTableList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize)
    },
    changePageNo(val) {
      this.page.pageNo = val;
      this.newMountedList = this.importTableList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize)
    },
    changeCrmPageNo(val) {
      this.page.pageNo = val;
      this.newCrmList = this.asyncCrmList.slice((this.page.pageNo - 1) * this.page.pageSize, this.page.pageNo * this.page.pageSize)
    },
    // 确认按钮
    async submitPoint(){
      try {
        this.isShowAsync = true;
        this.asyncCrmLoading = true;
        const customerCode = this.$route.query.orgCode
        const { code, data } = await asyncCrmInTable({orgCode: customerCode})
        if(code == 200){
          this.crmpage.pageNo = 1;
          this.crmpage.pageSize = 10;
          this.crmpage.total = data.length;
          this.asyncCrmList = data
          this.newCrmList = this.asyncCrmList.slice((this.crmpage.pageNo - 1) * this.crmpage.pageSize, this.crmpage.pageNo * this.crmpage.pageSize);
          this.asyncCrmLoading = false;
        }
      } catch (error) {
        if(error.code == '60010046'){
          this.$confirm(this.$t('customer.syncFaild'), this.$t('customer.remainding'), {
            confirmButtonText: this.$t('common.confirm'),
            cancelButtonText: this.$t('common.cancel'),
            type: 'warning',
          }).then(() => {
            this.tableLoading = false;
            this.crmDrawer = false;
            this.isShowAsync = false;
            this.asyncCrmLoading = false;
          }).catch(() => {
            this.tableLoading = false;
            this.crmDrawer = false;
            this.isShowAsync = false;
            this.asyncCrmLoading = false;
          });
        }
      }
    },
    knowPrice(){
      // this.$emit('submitPrice');
      this.crmDrawer = false;
      this.isShowAsync = false;
    },
    // 币种
    getCurrency(row){
      if(!row || (!row.currency && row.currency !== 0)) return ''
      let obj = {
        0: '$',
        1: '¥',
        2: '€',
        3: '¥ JPY',
        4: '¥'
      }
      return obj[row.currency]
    }
  },
};
</script>

<style lang="scss">
.crm-drawer {
  width: 800px !important;
  background-color: #1B1D22;

  .draw-title {
    color: #e4e8f7;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: left;
  }
  .el-drawer__header {
    border-bottom: 1px solid #38393d;
    padding: 18px 24px;
    margin-bottom: 0;
    color: #F3F5F7;
  }
  .el-drawer__body {
    padding: 24px;
    overflow: hidden;
  }
  .point-upload-box {
    width: 100%;
    height: 656px;
    background: #27292E;
    padding: 20px;
  }
  .el-upload {
    width: 100%;
  }
  .table-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 60px);
    .import-title {
      line-height: 40px;
      .success {
        color: #00b860;
        margin: 0 8px;
      }
      .error {
        color: #e55353;
        margin: 0 8px;
      }
    }
    .depart-table {
      position: relative;
      // flex: 1;
      height: calc(100% - 20px);
      width: 100%;
      .hg-table {
        height: 100%;
        // height: calc(100% - 30px);
        overflow: hidden;
        .el-table {
          // height: calc(100% - 60px)!important;
          // max-height: 100% !important;
        }
      }
      .table-high-light {
        float: left;
        width: auto;
        max-width: calc(100% - 41px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .expedited-time {
        line-height: 40px;
      }
      .error-tips{
        color: #E55353;
      }
    }
    .depart-pagination {
      z-index: 1;
      position: absolute;
      bottom: 0;
      right: 0;
      height: 60px;
      width: 100%;
    }
    .btn-list{
      position: absolute;
      bottom: 24px;
      right: 10px;
      display: flex;
      justify-content: flex-end;
      width: 100%;
      height: 50px;
      padding: 10px 24px 0 0;
      border-top: 1px solid #3D4047;
    }
  }
  .el-button.is-plain:focus{
    background: transparent;
  }
  .el-button--primary:focus{
    background: #3760EA;
    border-color: #3760EA;
  }
}
</style>
