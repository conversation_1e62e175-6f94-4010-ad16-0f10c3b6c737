<template>
  <el-dialog
    custom-class="recharge-dialog"
    append-to-body
    top="8vh"
    width="608px"
    :title="lang('paidCredit')"
    :visible.sync="isShow"
    :close-on-click-modal="false"
    @close="onClose('rechargeForm')">
    <el-form
      ref="rechargeForm"
      :model="rechargeForm"
      :rules="rules"
      :label-position="'left'"
      label-width="200px"
      class="recharge-form">
      <el-form-item :label="$t('heypoint.customer.customerName') + '：'" prop="userName">
        {{detailInfo.orgName}}
      </el-form-item>
      <el-form-item :label="lang('settlementType') + '：'" prop="settlementType">
        {{detailInfo.settlementType | settlementType}}
      </el-form-item>
      <el-form-item :label="lang('nowBalance') + '：'" prop="balance">
        {{detailInfo.rechargeBalance | capitalize}}
      </el-form-item>
      <el-form-item :label="lang('availableOverdraft') + '：'" prop="availableCredit">
        {{detailInfo.availableCredit | capitalize}}
      </el-form-item>
      <el-form-item :label="lang('overdraftLimit') + '：'" prop="creditScore">
        {{detailInfo.creditScore | capitalize}}
      </el-form-item>
      <el-form-item :label="lang('nowLimit') + '：'" prop="rechargeNum" class="recharge-num">
        <el-input-number v-model.number="rechargeForm.rechargeNum" :precision="2" :step="1"></el-input-number>
      </el-form-item>
      <el-form-item :label="lang('remark') + '：'" prop="remark" class="remark-item">
        <el-input
        style="margin-top: 10px;"
          v-model="rechargeForm.remark"
          type="textarea"
          :placeholder="lang('enterReson')"
          maxlength="100"
          show-word-limit>
        </el-input>
      </el-form-item>
    </el-form>
    <div class="tips">{{lang('confirm')}}</div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose('rechargeForm')">{{lang('close')}}</el-button>
      <el-button type="primary" :loading="rechargeLoading" @click="confirm('rechargeForm')">{{lang('submit')}}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { rechargeOrGift } from '@/api/heypoint';
import {
  settlementType,
  capitalize,
} from '@/filters/heypoint';
import { getLang } from '@/public/utils';
import { accSub } from '@/public/utils/math'
export default {
  name: 'RechargeDialog',
  props: {
    isShow: {
      default: false
    },
    detailInfo: {
      default: () => {}
    }
  },
  filters: {
    settlementType,
    capitalize
  },
  data() {
    const validateRechargeNum = (rule, value, callback) => {
      console.log('value: ', value);
      if (value === '' || value == undefined) {
        callback(new Error(this.lang('refill')));
      } else {
        if (value === 0) {
          callback(new Error(this.lang('zero')));
        } else if (value > accSub(this.detailInfo.creditScore, this.detailInfo.availableCredit)) {
          callback(new Error(this.lang('chargeLimitTips')));
        }
        callback();
      }
    };
    return {
      rechargeForm: {
        rechargeNum: null,
        remark: null,
      },
      rules: {
        rechargeNum: [
          { required: true, validator: validateRechargeNum, trigger: 'blur'}
        ]
      },
      rechargeLoading: false
    }
  },
  computed: {
    // ...mapGetters(['uid']),
  },
  methods: {
    lang: getLang('heypoint.customer.operate'),
    /**
     * 关闭弹窗
     * @param formName 表单名称
     */
    onClose(formName) {
      this.$refs[formName].resetFields();
      this.$emit('update:isShow', false);
      this.$emit('refreshInfo');
    },

    /**
     * 提交充值
     * @param formName 表单名称
     */
    confirm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            this.rechargeLoading = true;
            const { orgCode } = this.$route.query;
            const params = {
              operationType: 1,
              orgCode: Number(orgCode),
              orgName: this.detailInfo.orgName,
              num: this.rechargeForm.rechargeNum,
              remark: this.rechargeForm.remark,
            };
            const { code, msg } = await rechargeOrGift(params);
            if (code === 200) {
              this.onClose('rechargeForm');
            } else {
              this.$message({
                type: 'error',
                message: msg
              });
            }
          } catch (error) {
            console.error('error: ', error);
          } finally {
            this.rechargeLoading = false;
          }
        }
      });
    },
    //结算类型
    settlementType(param) {
      if (param === undefined) return
      var monthly = this.lang('monthly');
      var prepaid = this.lang('prepaidMonth');
      const typeMap = {
        0: monthly,
        1: prepaid
      }
      return typeMap[param]
    },
  }
}
</script>

<style lang="scss">
.recharge-dialog {
  .el-dialog__body {
    padding: 40px 64px 0;
    .recharge-form {
      color: $hg-label;
      .el-form-item {
        &.recharge-num {
          margin-bottom: 24px;
          .el-input-number {
            width: 320px;
            background: $hg-main-black;
          }
        }
        &.remark-item {
          .el-form-item__content {
            width: 320px;
            .el-textarea__inner {
              width: 320px;
              height: 90px;
            }
            .el-input__count {
              height: 20px;
              line-height: 20px;
            }
          }
        }
        &.is-error {
          margin-bottom: 22px;
        }
      }
    }
    .tips {
      width: 640px;
      word-break: break-word;
      color: #DC5050;
      margin: 10px 0 14px 0;
    }
  }
  .el-dialog__footer {
    text-align: right;
    .dialog-footer {
      .el-button {
        width: 112px;
        height: 40px;
        padding: 0;
      }
    }
  }
}
</style>
