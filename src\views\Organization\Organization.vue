<template>
  <OrgContent :show.sync="showOrg"></OrgContent>
</template>

<script>
import OrgContent from "./OrgContent.vue";

export default {
  name: "Organization",
  components: {
    OrgContent
  },
  data() {
    return {
      showOrg: false,
    }
  },
  computed: {
  },
  watch: {
  },
  created() {

  },
  mounted() {
    this.showOrg = true;
  },
  methods:{
  },
  destroyed() {
    this.showOrg = false;
  }
};
</script>

<style lang="scss" scoped>
</style>
