import * as THREE from 'three';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { topBoxNumber, bottomBoxNumber, toothNumbers, iprtopBoxNumber, iprbottomBoxNumber } from './constant';
import { assignColors, iprAssignColors } from './utils';
import { staticResourcesUrl } from '@/config';
import { deepClone } from '@/public/utils/index';
// import data from './data.json'
// import iprdata from './iprdata.json'
import SpriteText from '@/components/OrthodonticDesignParam/helpers/three/object/features/SpriteText/index'
import {
	createSphereHelper,
	getObjectCenter
} from '@/components/OrthodonticDesignParam/helpers/three/utils'
import {
  disposeObject,
} from '@/components/OrthodonticDesignParam/helpers/three/utils/index'
let commonMaterial = new THREE.MeshPhongMaterial({
	color: 0xcecece,
});

export default class ThreeView {
	constructor(dom, mode) {
		this.dom = dom;
		this.mode = mode;
		this.iprData = null;
		this.additionData = null;
		this.allSelectTooth = [];
		this.ToothGroup = new THREE.Group(); //牙齿模型mesh组
		this.fileCardGroup = new THREE.Group();//附件操作的所有mesh
		this.iprCardGroup = new THREE.Group();//ipr操作的所有mesh
		this.additionSpriteTextGroup = new THREE.Group();
		this.iprSpriteTextGroup = new THREE.Group();
		this.loader = new STLLoader();
		this.toothList = [];
		this.object = new THREE.Sprite(new THREE.SpriteMaterial({
			side: THREE.DoubleSide,
			transparent: false
		}))
		this.init();
		this.animate();
		// this.loadModel();
	}

	async _getData(data){
		if(this.mode === 'addition'){
			this.additionData = deepClone(data);
		} else if(this.mode === 'ipr'){
			this.iprData = deepClone(data)
		}
		const newData = deepClone(data);
		await this.loadModel(newData);
	}

	updateToothAndCard(data){
		if(this.mode === 'addition'){
			this.updateAdditionToothAndCard(data);
		} else if(this.mode === 'ipr'){
			this.updateIprToothAndCard(data)
		}
	}

	// 更新牙位图 ipr
	updateIprToothAndCard(data){
		const newData = deepClone(data);
		disposeObject(this.iprCardGroup)
		disposeObject(this.iprSpriteTextGroup)
		let iprCardGroup = new THREE.Group();
		this.iprSpriteTextGroup = new THREE.Group();
		this.iprCardGroup = iprCardGroup;
		this.scene.add(this.iprCardGroup)
		this.scene.add(this.iprSpriteTextGroup)
		this.initIprSelectStatus(newData)
	}
	// 更新牙位图 附件
	updateAdditionToothAndCard(data){
		const newData = deepClone(data);
		disposeObject(this.fileCardGroup)
		disposeObject(this.additionSpriteTextGroup)
		let fileCardGroup = new THREE.Group();
		this.additionSpriteTextGroup = new THREE.Group();
		this.fileCardGroup = fileCardGroup;
		this.scene.add(this.fileCardGroup)
		this.scene.add(this.additionSpriteTextGroup)
		this.initSelectStatus(newData)
	}

	/**
	 * 初始化threeJS
	 */
	init() {
		const fov = 13,
			near = 0.1,
			far = 10000,
			domWidth = this.dom.offsetWidth,
			domHeight = this.dom.offsetHeight;
		this.scene = new THREE.Scene();
		this.scene.background = new THREE.Color(0xffffff);
		this.camera = new THREE.PerspectiveCamera(fov, domWidth / domHeight, near, far);

		this.camera.position.set(170, 80, 820);
		this.camera.lookAt(new THREE.Vector3(170, 95, 0));
		this.scene.add(this.camera);

		this.scene.add(new THREE.AmbientLight(0x333333));
		let light = new THREE.SpotLight(0xffffff, 1);
		light.position.set(200, 95, 800);
		this.scene.add(light);
		this.renderer = new THREE.WebGLRenderer({
			antialias: true,
			preserveDrawingBuffer: true,
		});
		this.renderer.setPixelRatio(window.devicePixelRatio);
		this.renderer.setSize(domWidth, domHeight);
		this.renderer.gammaInput = true;
		this.renderer.gammaOutput = true;
		this.renderer.shadowMap.enabled = true;
		this.scene.add(this.ToothGroup);

		this.renderer.render(this.scene, this.camera);
		this.camera.aspect = parseInt(this.renderer.domElement.style.width) / parseInt(this.renderer.domElement.style.height);
		this.camera.updateProjectionMatrix();
		this.dom && this.dom.appendChild(this.renderer.domElement);
		this.camera.aspect = parseInt(this.renderer.domElement.style.width) / parseInt(this.renderer.domElement.style.height);
		this.camera.updateProjectionMatrix();
		this.scene.add(this.fileCardGroup)
		this.scene.add(this.iprCardGroup)
		this.scene.add(this.additionSpriteTextGroup)
		this.scene.add(this.iprSpriteTextGroup)
	}

	async loadModel(data) {
		const path = `${staticResourcesUrl}/tooth_model/`;
		this.loader.setPath(path);
		const GumsArr = ['upper', 'lower'];
		const bracketArr = ['bracket_up_half', 'bracket_low_half'];
		const bracketWideArr = ['bracket_up_half_l', 'bracket_low_half_l'];

		let curIndex = 0;
		let total = toothNumbers.length + GumsArr.length + bracketArr.length + bracketWideArr.length;
		const handle = () => {
			curIndex++;
			if (curIndex === total) {
				// this.$emit('loaded',true)
			}
		};

		// 加载完牙齿后进行操作
		const renderTooth = async () => {
			let arr = []
			this.ToothGroup.updateMatrixWorld()
			toothNumbers.forEach((item, idx) => {
				const i = idx + 1;
				arr.push(new Promise((resolve, reject) => {
					this.loader.load(
						'unn' + i + '.stl',
						geometry => {
							let mesh = new THREE.Mesh(
								geometry,
								new THREE.MeshPhongMaterial().copy(commonMaterial)
							);
							mesh.name = toothNumbers[i - 1];
							//绑定盒子模型
							mesh.geometry.computeBoundingBox();
							let centerId = new THREE.Vector3();
							// 将该向量设置为a + b。
							centerId.addVectors(mesh.geometry.boundingBox.min, mesh.geometry.boundingBox.max);
							// 将该向量与所传入的标量s进行相乘。
							centerId.multiplyScalar(0.5);
							// 将该向量乘以四阶矩阵m（第四个维度隐式地为1），and divides by perspective.
							// 世界矩阵
							// 一个对象的世界矩阵.matrixWorld是该对象本地矩阵及其所有所有祖宗对象本地矩阵的乘积，或者每一个对象的世界矩阵是对象本地矩阵和父对象的世界矩阵的乘积。
							// centerId.applyMatrix4(mesh.matrixWorld);
							
							this.allSelectTooth.push(mesh)
							this.ToothGroup.add(mesh);
							centerId.applyMatrix4(mesh.matrixWorld);
							mesh.geometry.center();
							mesh.position.set(centerId.x, centerId.y, centerId.z);
							handle();
							resolve(mesh);
						},
						progress => { },
						err => {
							console.error(err);
						}
					);
				}))
			})
			const meshs = await Promise.all(arr)
			// 附件操作
			if (this.mode === 'addition') {
				this.initSelectStatus(data)
			} else if (this.mode === 'ipr') {
				this.initIprSelectStatus(data)
			}
		};
		const renderFloor = () => {
			GumsArr.forEach(ele => {
				this.loader.load(`${ele}.stl`, geometry => {
					let mesh1 = new THREE.Mesh(geometry, new THREE.MeshPhongMaterial().copy(new THREE.MeshPhongMaterial({
						color: 0xe17d79,
					})));
					mesh1.name = ele;
					mesh1.position.z = -5;
					this.scene.add(mesh1);
					handle();
				});
			});
		};
		renderTooth();
		renderFloor();
	}

	disposeChild(mesh) {
		if (mesh instanceof THREE.Mesh) {
			if (mesh.geometry?.dispose) {
				mesh.geometry.dispose(); //删除几何体
			}
			if (mesh.material?.dispose) {
				mesh.material.dispose(); //删除材质
			}
			if (mesh.material?.texture?.dispose) {
				mesh.material.texture.dispose();
			}
		}
		if (mesh instanceof THREE.Group) {
			mesh.clear();
		}
		if (mesh instanceof THREE.Object3D) {
			mesh.clear();
		}
	}
	/**
	 * 获取模型截图
	 */
	getImage() {
		this.selectedTooths = [];
		this.clearSelectBorder();
		//  截图
		this.toothImageBase64 = this.renderer.domElement.toDataURL('image/png', 1);
		return this.toothImageBase64;
	}

	/**
	 * 清除选中边框样式
	 */
	clearSelectBorder() {
		for (let i = this.scene.children.length - 1; i >= 0; i--) {
			if (this.scene.children[i].name === 'outline') {
				const mesh = this.scene.children[i];
				this.disposeChild(mesh);
				this.scene.remove(mesh);
			}
		}
		this.renderer.render(this.scene, this.camera);
	}
	/**
	 * 附件操作
	 * 初始化选择状态 设置选中的牙齿出现边框
	 */
	initSelectStatus(data) {
		new Promise((resolve, reject) => {
			this.clearSelectBorder();
			const { upper, lower } = data;
			let toothList = assignColors(upper.concat(lower))
			this.toothList = toothList
			for (let i = 0, len = this.toothList.length; i < len; i++) {
				this.allSelectTooth[i].material.color.set(0xcecece)
				if (this.toothList[i].color && this.toothList[i].steps.length) {
					let mesh = this.allSelectTooth[i];
					let centerPoint = getObjectCenter(mesh)
					this.toothList[i].centerPoint = centerPoint
					mesh.material.color.set(this.toothList[i].color)
					let m = new THREE.MeshBasicMaterial({ color: this.toothList[i].color });
					var cube = new THREE.Mesh(mesh.geometry, m);
					cube.name = 'outline';
					// this.scene.add(cube);
					cube.scale.set(1.1, 1.1, 1.05);
					cube.position.set(mesh.position.x, mesh.position.y, mesh.position.z - 1.5);
					this.creatTextPlane(this.toothList[i])
					const sphere = this.creatPoint(this.toothList[i])
					const line = this.creatLine(this.toothList[i])
					this.fileCardGroup.add(sphere)
					this.fileCardGroup.add(cube)
					this.fileCardGroup.add(line)
				}
			}
			resolve()
		})
	}

	creatPoint(data) {
		const group = new THREE.Group()
		group.position.copy(data.endpoint)
		const sphere = createSphereHelper({
			size: 2,
			color: data.color,
		})
		group.add(sphere)
		return group
	}
	// 跟中牙齿中心点画出一条外射的线
	creatLine(data) {
		const group = new THREE.Group()

		const points = [];
		const centerPint = new THREE.Vector3();
		const endpoint = new THREE.Vector3();
		centerPint.copy(data.centerPoint)
		endpoint.copy(data.endpoint)
		points.push(centerPint)
		points.push(endpoint)
		const geometry = new THREE.BufferGeometry().setFromPoints(points)

		// 创建线段材质
		const material = new THREE.LineBasicMaterial({ color: data.color });

		// 创建线段对象并添加到场景
		const line = new THREE.Line(geometry, material);
		group.add(line)
		return group
	}

	// 生成附件文本框
	creatTextPlane(data) {
		const lang = window.localStorage.getItem('lang')
		const endPoint = data.endpoint
		const spriteText = new SpriteText()
		let boxWidth = (topBoxNumber.includes(data.number) || bottomBoxNumber.includes(data.number)) ? 6 : 20
		let str = ''
		let fontSize = 15
		let size = 1.5
		if (data.steps.length) {
			data.steps.forEach((it, index) => {
				if((topBoxNumber.includes(data.number) || bottomBoxNumber.includes(data.number))){
					if(lang == 'zh'){
						str += it + '步、'
						boxWidth = 20
					} else {
						index == 0 ? str +=  'Tray ' + it + ',' : str +=  '\nTray ' + it + ','
						fontSize = 18
						size = 1.8
						boxWidth = 40
					}
				} else{
					if(lang == 'zh'){
						str += it + '步、'
					} else {
						str +=  'Tray ' + it + ',';
						boxWidth = 50
					} 
				}
				
			})
		}
		let text = ''
		if(lang === 'zh'){
			text = `牙位：${data.number}\n粘接：${str.slice(0, str.length - 1)}`
		} else {
			text = `Tooth：${data.number}\nBond at：${str.slice(0, str.length - 1)}`
		}

		spriteText.setProperties({
			text: text,
			textHeight: 0.06,
			fontWeight: 500,
			fontSize: fontSize,
			color: 'rgba(0, 0, 0, 1)',
			borderWidth: 0.8,
			borderColor: data.orgincolor,
			borderRadius: 3,
			size: size,
			padding: [6, 4],
			center: data.center,
			boxWidth: 70,
			otherboxWidth: boxWidth
		})
		const spriteTextObject = spriteText.object
		spriteTextObject.position.copy(endPoint)
		this.additionSpriteTextGroup.add(spriteTextObject)

	}

	// 生成ipr文本框
	creatIprPlane(data) {
		const endPoint = data.endpoint
		const spriteText = new SpriteText()
		const lang = window.localStorage.getItem('lang')
		let text = ''
		let fontSize = 16
		let size = 1.5
		let otherboxWidth = 40
		if (lang == 'zh'){
			text = `牙位：${data.number}-${data.nextNumber}\n${data.step}步前，片切${data.cutValue}mm`
		} else {
			if (iprtopBoxNumber.includes(data.number) || iprbottomBoxNumber.includes(data.number)){
				text = `Tooth：${data.number}-${data.nextNumber}\nBefore Tray ${data.step}.\nReduce ${data.cutValue} mm`
				fontSize = 18
				size = 1.8
				otherboxWidth = 40
			} else {
				text = `Tooth：${data.number}-${data.nextNumber}\nBefore Tray ${data.step}. Reduce ${data.cutValue} mm`
				otherboxWidth = 106
			}
		}
		spriteText.setProperties({
			text: text,
			textHeight: 0.06,
			fontWeight: 500,
			fontSize: fontSize,
			color: 'rgba(0, 0, 0, 1)',
			borderWidth: 0.8,
			borderColor: data.orgincolor,
			borderRadius: 3,
			size: size,
			padding: [6, 4],
			center: data.center,
			boxWidth: 70,
			otherboxWidth: otherboxWidth
		})
		const spriteTextObject = spriteText.object
		spriteTextObject.position.copy(endPoint)
		this.iprSpriteTextGroup.add(spriteTextObject)
	}

	/**
	 * ipr操作
	 */
	initIprSelectStatus(data) {
		new Promise((resolve, reject) => {
			const { upper, lower } = data
			let toothList = iprAssignColors(upper.concat(lower))
			for (let i = 0, len = toothList.length; i < len; i++) {
				// 有间隙的牙齿
				if (toothList[i].color && toothList[i].nextNumber && toothList[i].step && toothList[i].cutValue) {
					let mesh = this.allSelectTooth.find((item) => { return item.name === toothList[i].number });
					let nextMesh = this.allSelectTooth.find((item) => { return item.name === toothList[i].nextNumber });
					let center = getObjectCenter(mesh)
					let nextCenter = getObjectCenter(nextMesh)
					const centerPoint = this.getPointInCenter(center, nextCenter)
					toothList[i].centerPoint = centerPoint
					this.creatIprPlane(toothList[i])
					const sphere = this.creatPoint(toothList[i])
					const arrow = this.creatArrow(toothList[i])
					this.iprCardGroup.add(sphere)
					this.iprCardGroup.add(arrow)
				}
			}
			resolve()
		})
	}

	// 根据两个点取中线点
	getPointInCenter(point1, point2) {
		// 创建线段对象
		const line = new THREE.Line3(point1, point2);

		// 找到线段的中心点
		const center = line.getCenter(new THREE.Vector3());
		center.z += 9
		return center
	}

	// 创建箭头哦
	creatArrow(data) {
    // 创建Vector3对象，代表方向
		const centerPint = new THREE.Vector3();
		const endpoint = new THREE.Vector3();
		centerPint.copy(data.centerPoint)
		endpoint.copy(data.endpoint)
    const length = endpoint.distanceTo(centerPint);
		const arrow = new THREE.ArrowHelper(new THREE.Vector3().subVectors(centerPint, endpoint).normalize(), endpoint, length, data.color, 4, 3);
    return arrow

  }
	/**
	 * 动画效果
	 */
	animate() {
		this.timer = requestAnimationFrame(this.animate.bind(this));
		this.renderer.render(this.scene, this.camera);
	}
	/**
	 * 清除占用内存
	 */
	destroyed() {
		this.scene.traverse(item => {
			this.disposeChild(item);
		});
		THREE.Cache.clear();
		this.scene.clear();
		this.renderer.dispose();
		this.renderer.forceContextLoss();
		cancelAnimationFrame(this.timer);
	}
}